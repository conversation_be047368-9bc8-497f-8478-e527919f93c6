/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import com.yeepay.g3.app.yop.portal.dto.spi.SpiApiModel;
import com.yeepay.g3.app.yop.portal.enums.ApiVersionEnum;
import com.yeepay.g3.app.yop.portal.exception.YopPortalException;
import com.yeepay.g3.app.yop.portal.vo.page.SpiApiQueryParam;
import com.yeepay.g3.utils.common.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/4 6:22 下午
 */
public class SpiQueryServiceTest extends SpringBootTests {

    @Autowired
    private SpiQueryService spiQueryService;

    /**
     * 查询spi关联的api:传参正确
     */
    @Test
    public void testListApiContacted() {
        SpiApiQueryParam spiApiQueryParam = new SpiApiQueryParam();
        String spiName = "test-wdc.self-settle.result";
        spiApiQueryParam.setSpiName(spiName);
        List<SpiApiModel> spiApiModels = spiQueryService.listApiContacted(spiApiQueryParam);
        Assert.assertTrue(CollectionUtils.isNotEmpty(spiApiModels));

        // 根据api名称模糊查询-能检索出结果
        spiApiQueryParam.setApiName("产品");
        List<SpiApiModel> spiApiModelWithApiName = spiQueryService.listApiContacted(spiApiQueryParam);
        Assert.assertTrue(CollectionUtils.isNotEmpty(spiApiModelWithApiName));

        // 根据api名称模糊查询-不能检索出结果
        spiApiQueryParam.setApiName("error-api-title");
        List<SpiApiModel> spiApiModelWithErrorApiName = spiQueryService.listApiContacted(spiApiQueryParam);
        Assert.assertTrue(CollectionUtils.isEmpty(spiApiModelWithErrorApiName));

        // 根据api uri查询-能检索出结果
        spiApiQueryParam.setApiName(null);
        spiApiQueryParam.setApiUri("/rest/v1.0/test-wdc/product-query/query-for-doc");
        List<SpiApiModel> spiApiModelWithApiUri = spiQueryService.listApiContacted(spiApiQueryParam);
        Assert.assertTrue(CollectionUtils.isNotEmpty(spiApiModelWithApiUri));

        // 根据api uri查询-不能检索出结果
        spiApiQueryParam.setApiUri("/error/uri");
        List<SpiApiModel> spiApiModelWithErrorApiUri = spiQueryService.listApiContacted(spiApiQueryParam);
        Assert.assertTrue(CollectionUtils.isEmpty(spiApiModelWithErrorApiUri));

        // 根据api版本进行查询-V1(旧版)
        spiApiQueryParam.setApiUri(null);
        spiApiQueryParam.setApiVersion(ApiVersionEnum.V1);
        List<SpiApiModel> spiApiModelV1 = spiQueryService.listApiContacted(spiApiQueryParam);
        Assert.assertTrue(CollectionUtils.isNotEmpty(spiApiModelV1));

        // 根据api版本进行查询-V2(新版)
        spiApiQueryParam.setApiUri(null);
        spiApiQueryParam.setApiVersion(ApiVersionEnum.V2);
        List<SpiApiModel> spiApiModelV2 = spiQueryService.listApiContacted(spiApiQueryParam);
        Assert.assertTrue(CollectionUtils.isEmpty(spiApiModelV2));
    }

    /**
     * 查询spi关联的api:缺少必要参数spiName
     */
    @Test(expected = YopPortalException.class)
    public void testListApiContactedMissSpiName() {
        SpiApiQueryParam spiApiQueryParam = new SpiApiQueryParam();
        spiQueryService.listApiContacted(spiApiQueryParam);
    }

}
