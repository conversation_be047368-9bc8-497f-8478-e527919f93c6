package com.yeepay.g3.app.yop.portal.utils;

import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.net.NetworkInterface;
import java.nio.BufferUnderflowException;
import java.nio.ByteBuffer;
import java.security.SecureRandom;
import java.text.ParseException;
import java.util.Date;
import java.util.Enumeration;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * title: Id生成器<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-01 15:03
 */
public class IdGenerator {

    private static final Logger LOGGER = LoggerFactory.getLogger(IdGenerator.class);

    private static final String DATE_TIME_FORM = "yyMMddHHmmss";

    private static final int LOW_ORDER_THREE_BYTES = 0x00ffffff;

    private static final int MACHINE_IDENTIFIER;
    private static final short PROCESS_IDENTIFIER;
    private static final AtomicInteger NEXT_COUNTER = new AtomicInteger(new SecureRandom().nextInt());

    private static final char[] HEX_CHARS = new char[]{
            '0', '1', '2', '3', '4', '5', '6', '7',
            '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    public static final int ORDER_ID_MIN_LENGTH = 28;


    static {
        try {
            MACHINE_IDENTIFIER = createMachineIdentifier();
            PROCESS_IDENTIFIER = createProcessIdentifier();
        } catch (Exception e) {
            LOGGER.error("OrderIdGenerator init fails.", e);
            throw new YeepayRuntimeException(e);
        }
    }


    public static String generate() {
        Date date = new Date();
        String dateStr = DateFormatUtils.format(date, DATE_TIME_FORM);
        ByteBuffer buffer = ByteBuffer.allocate(8);
        buffer.put(int2(MACHINE_IDENTIFIER));
        buffer.put(int1(MACHINE_IDENTIFIER));
        buffer.put(int0(MACHINE_IDENTIFIER));
        buffer.put(short1(PROCESS_IDENTIFIER));
        buffer.put(short0(PROCESS_IDENTIFIER));
        int counter = NEXT_COUNTER.getAndIncrement();
        buffer.put(int2(counter));
        buffer.put(int1(counter));
        buffer.put(int0(counter));
        return dateStr + toHexStr(buffer.array());
    }

    public static Date analysisCreatedDate(String orderId) {
        if (StringUtils.length(orderId) < ORDER_ID_MIN_LENGTH) {
            throw new YeepayRuntimeException("Illegal OrderId Length:{0}", orderId);
        }
        try {
            return DateUtils.parseDate(StringUtils.substring(orderId, 0, 12), DATE_TIME_FORM);
        } catch (ParseException e) {
            throw new YeepayRuntimeException("Illegal OrderId Prefix:{0}", orderId);
        }
    }

    private static String toHexStr(byte[] array) {
        char[] chars = new char[16];
        int i = 0;
        for (byte b : array) {
            chars[i++] = HEX_CHARS[b >> 4 & 0xF];
            chars[i++] = HEX_CHARS[b & 0xF];
        }
        return new String(chars);
    }


    private static int createMachineIdentifier() {
        // build a 2-byte machine piece based on NICs info
        int machinePiece;
        try {
            StringBuilder sb = new StringBuilder();
            Enumeration<NetworkInterface> e = NetworkInterface.getNetworkInterfaces();
            while (e.hasMoreElements()) {
                NetworkInterface ni = e.nextElement();
                sb.append(ni.toString());
                byte[] mac = ni.getHardwareAddress();
                if (mac != null) {
                    ByteBuffer bb = ByteBuffer.wrap(mac);
                    try {
                        sb.append(bb.getChar());
                        sb.append(bb.getChar());
                        sb.append(bb.getChar());
                    } catch (BufferUnderflowException shortHardwareAddressException) { //NOPMD
                        // mac with less than 6 bytes. continue
                    }
                }
            }
            machinePiece = sb.toString().hashCode();
        } catch (Throwable t) {
            // exception sometimes happens with IBM JVM, use random
            machinePiece = (new SecureRandom().nextInt());
            LOGGER.warn("Failed to get machine identifier from network interface, using random number instead", t);
        }
        machinePiece = machinePiece & LOW_ORDER_THREE_BYTES;
        return machinePiece;
    }

    // Creates the process identifier.  This does not have to be unique per class loader because
    // NEXT_COUNTER will provide the uniqueness.
    private static short createProcessIdentifier() {
        short processId;
        try {
            String processName = java.lang.management.ManagementFactory.getRuntimeMXBean().getName();
            if (processName.contains("@")) {
                processId = (short) Integer.parseInt(processName.substring(0, processName.indexOf('@')));
            } else {
                processId = (short) java.lang.management.ManagementFactory.getRuntimeMXBean().getName().hashCode();
            }

        } catch (Throwable t) {
            processId = (short) new SecureRandom().nextInt();
            LOGGER.warn("Failed to get process identifier from JMX, using random number instead", t);
        }

        return processId;
    }

    private static byte int2(final int x) {
        return (byte) (x >> 16);
    }

    private static byte int1(final int x) {
        return (byte) (x >> 8);
    }

    private static byte int0(final int x) {
        return (byte) (x);
    }

    private static byte short1(final short x) {
        return (byte) (x >> 8);
    }

    private static byte short0(final short x) {
        return (byte) (x);
    }
}
