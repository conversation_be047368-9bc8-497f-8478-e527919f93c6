package com.yeepay.g3.app.yop.portal.regression;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.yeepay.g3.facade.yop.sys.dto.RegressionAssertionDTO;
import com.yeepay.g3.sdk.yop.client.YopResponse;
import com.yeepay.g3.utils.common.encrypt.Digest;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.script.*;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/8/5 下午4:12
 */
@Component
public class GroovyAssertEngine implements AssertEngine {

    private static final Logger logger = LoggerFactory.getLogger(GroovyAssertEngine.class);

    private static ScriptEngine scriptEngine = new ScriptEngineManager().getEngineByName("groovy");

    /*cache some compiled scripts*/
    private Cache<String, CompiledScript> compiledScriptLoadingCache = CacheBuilder.newBuilder().maximumSize(200)
            .concurrencyLevel(4).expireAfterWrite(30, TimeUnit.MINUTES).build();

    public ExecutionResult runAssert(ExecutionResult executionResult) {
        YopResponse response = executionResult.getResponse();
        for (RegressionAssertionDTO assertion : executionResult.getAssertionList()) {
            String expression = assertion.getExpression();
            String md5 = Digest.md5Digest(expression);
            CompiledScript compiledScript;
            try {
                compiledScript = compiledScriptLoadingCache.get(md5, new CompiledScriptCallable(expression));
            } catch (Exception e) {
                logger.error("error compile/get groovy script", e);
                assertion.setResult(e.getMessage());
                executionResult.setSuccess(false);
                return executionResult;
            }
            try {
                Bindings bindings = new SimpleBindings();
                bindings.put("response", response);
                bindings.put("bizResponse", response.getResult());
                compiledScript.eval(bindings);
            } catch (Throwable e) {
                logger.error("error compile/get groovy script", e);
                assertion.setStatus("failure");
                assertion.setResult(e.getMessage());
                executionResult.setSuccess(false);
                return executionResult;
            }
            assertion.setStatus("success");
        }
        executionResult.setSuccess(true);
        return executionResult;
    }

    private static class CompiledScriptCallable implements Callable<CompiledScript> {

        private String expression;

        public CompiledScriptCallable(String expression) {
            this.expression = expression;
        }

        @Override
        public CompiledScript call() throws Exception {
            return ((Compilable) scriptEngine).compile(expression);
        }
    }

}
