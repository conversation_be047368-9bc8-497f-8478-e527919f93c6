package com.yeepay.g3.app.yop.portal.service;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/8/30 10:57 上午
 */
public interface CustomerService {
    /**
     * 查询管理员邮箱：非易宝商户查询所有非冻结状态的操作员邮箱
     *
     * @param customerNo
     * @return
     */
    List<String> getAdminEmail(String customerNo);
}
