package com.yeepay.g3.app.yop.portal.utils.validate;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * title: 系统参数工具类<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-13 16:35
 */
public class SystemParameterUtils {

    public static Map<String, String> getSystemParameters() {
        List<String> config = (List<String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_SYSTEM_PARAMS_CONFIG);
        Map<String, String> result = Maps.newHashMapWithExpectedSize(config.size());
        config.forEach(configValue -> {
            String[] items = StringUtils.split(configValue, ",");
            if (items.length == 3) {
                result.put(items[0], items[2]);
            }
        });
        return result;
    }

}
