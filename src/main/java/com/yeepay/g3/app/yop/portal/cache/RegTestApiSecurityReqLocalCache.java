/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqDTO;
import com.yeepay.g3.facade.yop.sys.facade.SecurityReqQueryFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 19/05/23 10:12
 */
@Component("regTestApiSecurityReqLocalCache")
public class RegTestApiSecurityReqLocalCache {

    private static final String SEPARATOR = "$";
    public static final int CACHE_MAX_SIZE = 500;
    public static final int CACHE_EXPIRE_AFTER_WRITE = 5;
    private static final Logger LOGGER = LoggerFactory.getLogger(RegTestApiSecurityReqLocalCache.class);

    private SecurityReqQueryFacade securityReqQueryFacade = RemoteServiceFactory.getService(SecurityReqQueryFacade.class);

    private LoadingCache<String, Map<String, SecurityReqDTO>> regTestApiSecurityReqLocalCache = CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE)
            .expireAfterWrite(CACHE_EXPIRE_AFTER_WRITE, TimeUnit.MINUTES)
            .recordStats()
            .build(getCacheLoader());

    private CacheLoader getCacheLoader() {
        return new CacheLoader<String, Map<String, SecurityReqDTO>>() {
            @Override
            public Map<String, SecurityReqDTO> load(String key) throws Exception {
                String[] array = StringUtils.split(key, SEPARATOR);
                String apiUri = array[0];
                String apiGroup = array[1];
                LOGGER.info("load api security req by :{} ", key);
                Map<String, SecurityReqDTO> apiMap = new HashMap<>();
                try {
                    apiMap = securityReqQueryFacade.findByApiUri(apiUri);
                    if (apiMap == null || apiMap.size() == 0) {
                        apiMap = securityReqQueryFacade.findByApiGroup(apiGroup);
                    }
                } catch (Exception e) {
                    LOGGER.error("load api security req by apiuri failed", e);
                }
                return apiMap;
            }
        };
    }

    public Map<String, SecurityReqDTO> get(String key) {
        try {
            return regTestApiSecurityReqLocalCache.get(key);
        } catch (Exception e) {
            LOGGER.error("error when get local cache", e);
            return null;
        }
    }

}
