/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.UserVO;
import com.yeepay.g3.app.yop.portal.vo.page.AclUserPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.List;

/**
 * title: 用户搜索服务<br>
 * description: 用户搜索服务接口<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-26
 */
public interface UserSearchService {

    /**
     * 搜索用户（新方法，返回UserVO列表）
     *
     * @param keyword 搜索关键词
     * @param limit 返回数量限制，默认10
     * @return 用户列表
     */
    List<UserVO> searchUsersForVO(String keyword, Integer limit);
}