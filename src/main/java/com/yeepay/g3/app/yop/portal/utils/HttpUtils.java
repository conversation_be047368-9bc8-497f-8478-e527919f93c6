/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.g3.app.yop.portal.dto.AuditContentDTO;
import com.yeepay.g3.app.yop.portal.shiro.utils.Constants;
import com.yeepay.g3.app.yop.portal.shiro.utils.OAuth2TokenUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.yop.perm.dto.AclAuditRecordDTO;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-12-03 11:36
 */
@Slf4j
public class HttpUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtils.class);

    private static final ObjectMapper OBJECT_MAPPER = JsonMapper.nonEmptyMapper().getMapper();

    public static void post(HttpServletRequest request, String content, String url, AclAuditRecordDTO record, String operator) throws Exception {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        AuditContentDTO auditContentDTO = JsonMapper.nonEmptyMapper().fromJson(content, AuditContentDTO.class);
        HttpEntity entity = null;
        HttpHeaders headers = new HttpHeaders();
        Enumeration reqHeaders = request.getHeaderNames();
        while (reqHeaders.hasMoreElements()) {
            String name = (String) reqHeaders.nextElement();
            headers.add(name, request.getHeader(name));
        }

        headers.set("authorization", "Bearer " + OAuth2TokenUtils.generate(record.getOriginator(), null, OperatorTypeEnum.SP_BASED, null, null));
        headers.add(Constants._AUDIT_RECORD_CODE, record.getCode());
        headers.add(Constants._OPERATOR, operator);
        if (StringUtils.startsWith(auditContentDTO.getContentType(), "application/json")) {
            headers.setContentType(MediaType.APPLICATION_JSON);
            entity = new HttpEntity(auditContentDTO.getBody(), headers);
        } else {
            LinkedMultiValueMap body = new LinkedMultiValueMap();
            for (String key : auditContentDTO.getParams().keySet()) {
                body.put(key, Arrays.asList(auditContentDTO.getParams().get(key)));
            }
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            entity = new HttpEntity(body, headers);
        }
        ResponseEntity result = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);

        String resultBody = (String) result.getBody();
        JsonNode rootNode = OBJECT_MAPPER.readTree(resultBody);
        String status = rootNode.path("status").asText();
        if (status.equals("error")) {
            LOGGER.error("error when operate audit resource ,error:" + result.getBody());
            throw new Exception("操作异常:" + rootNode.path("message").asText());
        }
    }

    /**
     * 发起http post请求
     *
     * @param url
     * @param content
     * @param headers
     * @return
     */
    public static String postJson(String url, String content, Map<String, String> headers) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach((k, v) -> httpHeaders.add(k, v));
        }
        HttpEntity entity = new HttpEntity(content, httpHeaders);
        ResponseEntity result = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        return (String) result.getBody();
    }

    /**
     * 获取接口返回的结果(GET).
     *
     * @param getUrl        请求接口的url
     * @param requestParam  请求接口的参数
     * @param requestHeader 请求接口的Header
     * @return 请求接口的返回值
     * @throws IOException the io exception
     */
    public static String getGetResponseMess(String getUrl, Map<Object, Object> requestParam, Map<Object, Object> requestHeader) throws IOException {

        String param = "";
        if (requestParam != null) {
            for (Map.Entry<Object, Object> entry : requestParam.entrySet()) {
                if ("".equals(param) || param == "") {
                    param = entry.getKey() + "=" + URLEncoder.encode(entry.getValue().toString(), "utf-8");
                } else {
                    param = param + "&" + entry.getKey() + "=" + URLEncoder.encode(entry.getValue().toString(), "utf-8");
                }
            }
        }
        getUrl = getUrl + "?" + param;
        URL url = new URL(getUrl);
        // 将url 以 open方法返回的urlConnection  连接强转为HttpURLConnection连接  (标识一个url所引用的远程对象连接)
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        if (requestHeader != null) {
            // 设置 Header 信息
            for (Map.Entry<Object, Object> entry : requestHeader.entrySet()) {
                connection.setRequestProperty(entry.getKey().toString(), entry.getValue().toString());
            }
        }
        connection.connect();

        // 获取输入流
        BufferedReader br;
        if (connection.getResponseCode() == 200) {
            br = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
        } else {
            br = new BufferedReader(new InputStreamReader(connection.getErrorStream(), "UTF-8"));
        }
        String line;
        StringBuilder sb = new StringBuilder();
        while ((line = br.readLine()) != null) {
            sb.append(line);
        }
        br.close();
        connection.disconnect();
        return sb.toString();
    }

}
