package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.ApiCommon;
import com.yeepay.g3.app.yop.portal.validation.group.ApiEdit;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 17:19
 */
public class ApiDefineBasicVO implements Serializable {

    private static final long serialVersionUID = 3140232895777234634L;

    @NotNull(message = "${api.basic.apiId}", groups = ApiEdit.class)
    private Long apiId;

    @NotNull(message = "${api.basic.apiUri}", groups = ApiCommon.class)
    private String apiUri;

    @NotNull(message = "${api.basic.apiType}", groups = ApiCommon.class)
    private String apiType;

    @NotNull(message = "${api.basic.apiGroup}", groups = ApiCommon.class)
    private String apiGroup;

    @NotNull(message = "${api.basic.apiTitle}", groups = ApiCommon.class)
    private String apiTitle;

    private String description;

    @NotEmpty(message = "${api.basic.methods}", groups = ApiCommon.class)
    private List<String> methods;

    private List<String> tags;

    public Long getApiId() {
        return apiId;
    }

    public void setApiId(Long apiId) {
        this.apiId = apiId;
    }

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public String getApiType() {
        return apiType;
    }

    public void setApiType(String apiType) {
        this.apiType = apiType;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    public String getApiTitle() {
        return apiTitle;
    }

    public void setApiTitle(String apiTitle) {
        this.apiTitle = apiTitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getMethods() {
        return methods;
    }

    public void setMethods(List<String> methods) {
        this.methods = methods;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
