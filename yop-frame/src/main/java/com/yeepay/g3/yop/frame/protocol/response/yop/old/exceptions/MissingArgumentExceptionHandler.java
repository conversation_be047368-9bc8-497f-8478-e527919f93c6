package com.yeepay.g3.yop.frame.protocol.response.yop.old.exceptions;

import com.yeepay.g3.yop.frame.chain.interceptor.annotations.ExceptionHandler;
import com.yeepay.g3.yop.frame.context.Context;
import com.yeepay.g3.yop.frame.context.ContextUtils;
import com.yeepay.g3.yop.frame.error.YopError;
import com.yeepay.g3.yop.frame.exception.validation.MissingArgumentException;
import com.yeepay.g3.yop.frame.protocol.response.yop.old.AbstractExceptionYopMarshaller;
import com.yeepay.g3.yop.frame.response.Response;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/1/26 下午12:53
 */
@ExceptionHandler({MissingArgumentException.class})
public class MissingArgumentExceptionHandler extends AbstractExceptionYopMarshaller {

    @Override
    protected YopError getError(Context context, Response response) {
        YopError error = new YopError();
        error.setCode("99001006");
        error.setMessage(String.format("应用(%s)缺少必要的参数:%s", ContextUtils.getAppId(context), ((MissingArgumentException) response.getException()).getArgumentName()));
        return error;
    }
}
