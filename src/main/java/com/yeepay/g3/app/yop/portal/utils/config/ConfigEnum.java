/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.utils.config;

import com.google.common.collect.Maps;
import com.yeepay.boot.components.config.ConfigKey;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import static com.yeepay.g3.app.yop.portal.utils.Constants.*;

/**
 * Title: 配置信息
 * Description: 描述
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 *
 * <AUTHOR>
 * @version 0.1, 14-5-18 17:35
 */
public enum ConfigEnum implements ConfigKey {

    // 租户登录地址
    YOP_PORTAL_TENANT_LOGIN_URL("YOP_PORTAL_TENANT_LOGIN_URL", "http://open.yeepay.com/login"),

    YOP_PORTAL_LOGIN_URL("YOP_PORTAL_LOGIN_URL", "http://employee.yeepay.com:8001/employee-boss/loginout/showLogin"),

    YOP_PORTAL_RESOURCE_PERM_NEW("YOP_PORTAL_RESOURCE_PERM_NEW", "{\"/signin/sso/boss3g\":\"boss3g\",\"/rest/config\":\"anon\",\"/apis/**\":\"anon\",\"/rest/project-version/**\":\"anon\",\"/rest/**/commons/**\":\"oauth2\",\"/rest/**\":\"oauth2,log\",\"/static/**\":\"anon\",\"/**\":\"anon\"}"),

    // 结果通知接收端等待时间
    YOP_PORTAL_NOTIFY_RECEIVE_SLEEP_MILLIS("YOP_PORTAL_NOTIFY_RECEIVE_SLEEP_MILLIS", 100L),
    /**
     * 对接产品分类
     */
    YOP_DOCKING_PRODUCT_CATEGORY("YOP_DOCKING_PRODUCT_CATEGORY", new HashMap<String, String>() {
    }),
    /**
     * 结果通知错误码解决方案
     */
    YOP_NOTIFY_ORDER_ERROR_CODE_SOLUTION("YOP_NOTIFY_ORDER_ERROR_CODE_SOLUTION", new HashMap<String, String>() {

    }),
    /**
     * 租户code
     */
    YOP_PORTAL_TENANT_CODES("YOP_PORTAL_TENANT_CODES", new HashMap<String, String>() {{
        put("YEEPAY", "易宝");
        put("SUNRISE", "日晟外综服");
    }}),
    /**
     * 商户提供方code与租户code对应
     */
    YOP_PORTAL_CUSTOMER_PROVIDER_CODES_TENANT_CODES("YOP_PORTAL_CUSTOMER_PROVIDER_CODES_TENANT_CODES", new HashMap<String, String>() {{
        put("KJ", "YEEPAY");
        put("PAYPLUS", "YEEPAY");
        put("AIR", "YEEPAY");
        put("YEEPAY", "YEEPAY");
        put("YOP", "YEEPAY");
        put("SUNRISE", "SUNRISE");
    }}),

    /**
     * soa服务信息查询域名
     */
    YOP_PORTAL_SOA_SERVICE_HOST("YOP_PORTAL_SOA_SERVICE_HOST", "https://boss3g.yeepay.com"),
    /**
     * 通知订单的错误码
     */
    YOP_NOTIFY_ORDER_ERROR_CODE("YOP_NOTIFY_ORDER_ERROR_CODE", new HashMap<String, String>() {
    }),

    /**
     * （流程魔方中）产品-API审核流程编号
     */
    YOP_PORTAL_PRODUCT_API_EXAMINE_PROCESS_NO("YOP_PORTAL_PRODUCT_API_EXAMINE_PROCESS_NO", "WUTestProcess"),

    /**
     * 中台产品（产品码）
     * api审核时中台产品需要过中台同事审核
     */
    YOP_PORTAL_MIDDLE_GROUND_PRODUCTS("YOP_PORTAL_MIDDLE_GROUND_PRODUCTS", new ArrayList<>()),

    /**
     * 运行模式
     */
    YOP_PORTAL_PRODUCTION_MODE("YOP_PORTAL_PRODUCTION_MODE", "PRO"),

    YOP_PORTAL_FORBID_POST("YOP_PORTAL_FORBID_POST", false),

    YOP_PORTAL_RESOURCE_PERM("YOP_PORTAL_RESOURCE_PERM", "{\"/signin/sso/boss3g\":\"boss3g\",\"/rest/config\":\"anon\",\"/apis/**\":\"anon\",\"/rest/project-version/**\":\"anon\",\"/rest/**/commons/**\":\"oauth2\",\"/rest/**\":\"oauth2,log\",\"/static/**\":\"anon\",\"/**\":\"anon\"}"),

    YOP_PORTAL_REGRESSION_TEST_ENVS("YOP_PORTAL_REGRESSION_TEST_ENVS", new HashMap<String, String>() {{
        put("NC", "内测");
        put("PRO", "线下生产");
        put("HW", "华为云");
    }}),

    // api参数自动解析环境枚举
    YOP_PORTAL_API_PARAM_ANALYZE_ENV("YOP_PORTAL_API_PARAM_ANALYZE_ENV", new HashMap<String, String>() {
        {
            put("NC", "内测");
            put("PRODUCT", "生产");
        }
    }),

    YOP_PORTAL_REGRESSION_TEST_SDK_CONFIG("YOP_PORTAL_REGRESSION_TEST_SDK_CONFIG", new HashMap<String, String>() {
        {
            put("appKey", "_regression_test");
            put("cert", "secret");
            put("testServerRoot", "http://172.17.102.173:8064/yop-center/");
            put("proServerRoot", "http://10.151.30.80:18064/yop-center/");
        }
    }),

    // yop-center服务端口
    YOP_AUTO_GENERATE_API_PARAM_LEVEL("yop.auto.generate.api.param.level", 1),

    YOP_PUBLIC_KEY("yop.public.key", new HashMap<String, String>() {
        {
            put("RSA2048", "");
            put("RSA4096", "");
        }
    }),

    YOP_PORTAL_IGNORE_PARAM_LOG("YOP_PORTAL_IGNORE_PARAM_LOG", new ArrayList<String>() {
        {
            add("HttpServletRequest");
            add("BindingResult");
        }
    }),

    YOP_DEVELOPER_CENTER_HOST("YOP_DEVELOPER_CENTER_HOST", new String("https://mp.yeepay.com")),

    YOP_API_RECORD_FORMAT("yop.api.record.format", "JSON"),

    YOP_PORTAL_HIRE_STATUS_SWITCH("YOP_PORTAL_HIRE_STATUS_SWITCH", true),

    YOP_PORTAL_HIRE_STATUS_ACCEPT("YOP_PORTAL_HIRE_STATUS_ACCEPT", new ArrayList<String>() {
        {
            add("TRAINEE");
            add("PROBATION");
            add("REGULAR");
        }
    }),

    YOP_MBR_DOC_URL_PREFIX("YOP_MBR_DOC_URL_PREFIX", "http://localhost:8080/doc/"),

    YOP_BOSS_LOGIN_URL("YOP_BOSS_LOGIN_URL", "http://employee.yeepay.com:8001/employee-boss/loginout/showLogin"),

    YOP_BOSS_LOGOUT_URL("YOP_BOSS_LOGOUT_URL", "http://employee.yeepay.com:8001/employee-boss/loginout/logout"),

    YOP_ERROR_CODES("YOP_ERROR_CODES", new HashMap<String, String>() {
        {
            put("40020", "服务不可用");
            put("40021", "授权权限不足");
            put("40029", "访问受限");
            put("40041", "缺少必填参数");
            put("40042", "非法的参数");
            put("40044", "业务处理失败");
            put("40047", "鉴权认证失败");
            put("40049", "SDK 故障");
        }
    }),

    YOP_INVOKE_LOG_LINK_LOG_CENTER("YOP_INVOKE_LOG_LINK_LOG_CENTER", ""),

    YOP_INVOKE_LOG_LINK_CALL_CHAIN("YOP_INVOKE_LOG_LINK_CALL_CHAIN", ""),

    YOP_PORTAL_CONFIG_PLATFORM_TEST("YOP_PORTAL_CONFIG_PLATFORM_TEST", new HashMap<String, String>() {
        {
            put("tips.app.create", "注意：一个测试商编，只能创建一个回归测试应用，回归测试应用创建统一由YOP运营人员创建，请将测试商编、回归测试应用名称发邮件给舒伟");
            put("email.app.create", "<EMAIL>");
        }
    }),

    YOP_PORTAL_STATIC_RESOURCE_CONFIG("YOP_PORTAL_STATIC_RESOURCE_CONFIG", new HashMap<String, String>() {
        {
            put("version", "20180705161500");
            put("host", "http://localhost:8088/yop-portal/");
        }
    }),

    YOP_PORTAL_BASE_URL("YOP_PORTAL_BASE_URL", "http://localhost:8088"),

    YOP_ACL_DISABLE_REASON("YOP_ACL_DISABLE_REASON", new ArrayList<String>() {
        {
            add("该资源正在进行升级，暂停使用。给您带来不便望请谅解。");
            add("该资源已迁移到旧版运营后台，暂停使用。给您带来便望请谅解。");
        }
    }),

    YOP_PORTAL_REBUILD_TYPE("YOP_PORTAL_REBUILD_TYPE", new HashMap<String, String>() {
        {
            put("UPGRADE_NC", "仅升级内测环境(仅生产环境有效,以便内测环境验证)");
            put("SYNC_NC_TO_PRO", "同步内测到生产(仅生产环境有效,请保证项目已经对外)");
            put("UPGRADE_ALL", "升级所有环境(审慎!生产环境仅限紧急情况下使用)");
            put("UPDATE_ALL", "同步所有环境(解决部分节点不一致问题)");
        }
    }),

    YOP_PORTAL_OAUTH2_EXPIRED_SECONDS("YOP_PORTAL_OAUTH2_EXPIRED_SECONDS", 86400L),

    YOP_INVOKE_LOG_REQUEST_BODY_MASK("YOP_INVOKE_LOG_REQUEST_BODY_MASK", new HashMap<String, Boolean>()),

    YOP_INVOKE_LOG_DEFAULT_REQUEST_BODY("YOP_INVOKE_LOG_DEFAULT_REQUEST_BODY", "该api分组不允许查看请求报文。请联系SP标记敏感字段，YOP审核通过后可开启查看功能。"),

    YOP_INVOKE_LOG_TABLE_PARTITION("YOP_INVOKE_LOG_TABLE_PARTITION", Boolean.FALSE),

    YOP_REBUILD_CLASS_LOADER_NOTIFIER_SWITCH("YOP_REBUILD_CLASS_LOADER_NOTIFIER_SWITCH", Boolean.TRUE),

    YOP_CEPH_CONFIG("YOP_CEPH_CONFIG", new HashMap<String, String>() {
        {
            put(CEPH_BUCKET_NAME, "yop_doc");
            put(CEPH_YCS_URL, "qargwapi.tc.yp:30231");
            put(CEPH_TOKEN, "6e86bb08946647fd9f50b7e83e8dbfb4cbfe3dc97d4af8a5e32193ea840d03422885ac8be11bd977be2242e1a220e68deaa24e13c8d4890709678251283aeed3ecf8a25f32b0419391fdeae33e7c19c5");
            put(CEPH_BATCH_POST_ADDRESS, "http://************:30231/api/v1/storage/buckets/");
            put(CEPH_STATIC_URL, "http://qastaticres.yeepay.com/");
            put(CEPH_NGINX_ACCESS_PREFIX, "/");
        }
    }),

    YOP_ATTACHMENT_BASE_CONFIG("YOP_ATTACHMENT_BASE_CONFIG", new HashMap<String, String>() {
        {
            put(ATTACHMENT_MAX_FILE_SIZE, "2MB");
        }
    }),

    YOP_ATTACHMENT_EXT_CONFIG("YOP_ATTACHMENT_EXT_CONFIG", new HashMap<String, String>() {
        {
            put(ATTACHMENT_SUPPORTED_TYPES,
                    "[" +
                            "{\"code\":\"EXCEL\",\"formats\":[\"xls\",\"xlsx\",\"numbers\"],\"name\":\"表格\"}," +
                            "{\"code\":\"DOCUMENT\",\"formats\":[\"pdf\",\"doc\",\"docx\",\"txt\",\"ppt\",\"pages\",\"keynote\"],\"name\":\"文档\"}," +
                            "{\"code\":\"IMAGE\",\"formats\":[\"jpg\",\"png\",\"gif\",\"jpeg\",\"bmp\"],\"name\":\"图片\"}" +
                            "]");
        }
    }),

    YOP_PORTAL_PAGE_SIZE("YOP_PORTAL_PAGE_SIZE", new HashMap<String, Long>() {
        {
            put("app", 10L);
            put("cert", 10L);
            put("cert-change-log", 10L);
            put("product", 10L);
            put("product-api", 10L);
            put("product-authz", 10L);
            put("product-changes", 10L);
            put("product-authz-changes", 10L);
            put("isv", 10L);
            put("isv-changes", 10L);
            put("isv-oper", 10L);
            put("api", 10L);
            put("api-change-record", 10L);
            put("api-deploy-record", 10L);
            put("doc", 10L);
            put("doc-page-history", 10L);
            put("doc-publish", 10L);
            put("doc-faq", 10L);
            put("whitelist", 10L);
            put("concurrent", 10L);
            put("custom-solution", 10L);
            put("custom-solution-related-api", 10L);
            put("custom-solution-unrelated-api", 10L);
        }
    }),

    YOP_SECURITY_DEF_CONFIG("YOP_SECURITY_DEF_CONFIG", new HashMap<String, String>() {
        {
            put("YOP-OAUTH2", "{\"type\":\"oauth2\",\"tokenUrl\":\"https://open.yeepay.com/yop-center/rest/v1.0/oauth2/token\",\"flow\":\"password\",\"scopes\":{\"write:all\":\"modify all info\",\"read:all\":\"read all info\"},\"desc\":\"适用于移动端的 OAuth2.0，暂时不支持scope\"}");
            put("YOP-HMAC-AES128", "{\"type\":\"YOP-HMAC\",\"name\":\"YOP-HMAC-AES128\",\"in\":\"query\",\"desc\":\"对称加密AES，密钥长度128。适用于一些安全需求一般的场景，比如个人身份认证，安全性较非对称加密会低一些，但是效率更高。在后端应用平均响应时间为10ms的测试环境中，tps为1270笔/s。\"}");
            put("YOP-HMAC-AES256", "{\"type\":\"YOP-HMAC\",\"name\":\"YOP-HMAC-AES256\",\"in\":\"query\",\"desc\":\"对称加密AES，密钥长度256\"}");
            put("YOP-RSA2048-SHA256", "{\"type\":\"YOP-RSA-SHA\",\"name\":\"YOP-RSA2048-SHA256\",\"in\":\"query\",\"extensions\":[{\"name\":\"authority\",\"type\":\"array\",\"title\":\"证书颁发机构\",\"optionValues\":[{\"name\":\"CFCA\",\"title\":\"CFCA证书\"}],\"defaultValue\":[]},{\"name\":\"needEncrypt\",\"type\":\"boolean\",\"title\":\"加密\",\"defaultValue\":false},{\"name\":\"forceEncryptAppDate\",\"type\":\"date\",\"title\":\"强制加密起始时间\",\"remark\":\"注：创建时间大于等于该时间的应用，调用网关时必须加密。\"}]}");
        }
    }),

    YOP_SCHEMA_TYPE_FORMAT("YOP_SCHEMA_TYPE_FORMAT",
            "{\"string\":[\"-\",\"byte\",\"binary\",\"date\",\"date-time\",\"password\",\"email\",\"mobile\",\"idcard\",\"bankcard\",\"cvv\",\"uuid\"],\"integer\":[\"-\",\"int32\",\"int64\"],\"number\":[\"-\",\"float\",\"double\"],\"boolean\":[\"-\"],\"array\":[\"-\"],\"object\":[\"-\",\"map\"]}"),

    YOP_NOTIFY_PROTOCOLS("YOP_NOTIFY_PROTOCOLS", new HashMap<String, String>() {
        {
            put("YOP#V1", "YOP协议V1版");
            put("YOP#V2", "YOP协议V2版(推荐)");
        }
    }),

    YOP_PORTAL_CUSTOMER_PROVIDER_CODES("YOP_PORTAL_CUSTOMER_PROVIDER_CODES", new HashMap<String, String>() {
        {
            put("YEEPAY", "易宝");
            put("PAYPLUS", "钱麦");
            put("KJ", "跨境");
            put("AIR", "航旅");
            put("YOP", "开放平台");
            put("SUNRISE", "日晟外综服");
        }
    }),

    YOP_SYSTEM_PARAMS_CONFIG("YOP_SYS_PARAMS_CONFIG", new ArrayList<String>() {
        {
            add("#context.appkey#,应用标示appKey,x-yop-appkey");
            add("#context.customerNo#,商户编号customerNo,x-yop-customerno");
            add("#context.oauth2.userid#,oauth2token中还原出的userid,x-yop-oauth2-userid");
            add("#context.productCode#,产品编码,x-yop-product-code");
            add("#header.requestId#,唯一请求标识,x-yop-request-id");
            add("#header.date#,请求发起时间,x-yop-request-date");
            add("#header.requestIp#,调用发起者IP,x-yop-request-ip");
            add("#header.requestSource#,调用来源,x-yop-request-source");
            add("#header.content.sha256#,变量x-yop-content-sha256,x-yop-content-sha256");
        }
    }),

    YOP_GIT_SYNC_CLIENT_CONFIG("YOP_GIT_SYNC_CLIENT_CONFIG", new HashMap<String, String>() {
        {
            put("baseUrl", "http://gitlab.yeepay.com/api/v4/");
        }
    }),

    YOP_GIT_SYNC_MODE("YOP_GIT_SYNC_MODE", "ACTIVE"),

    YOP_GIT_SYNC_PRODUCTION_COMMIT_CONFIG("YOP_GIT_SYNC_PRODUCTION_COMMIT_CONFIG", new HashMap<String, String>() {
        {
            put("baseUrl", "https://boss3g.yeepay.com/yop-portal/");
            put("username", "git");
            put("password", "qweasd");
        }
    }),

    YOP_REF_DOCS_LIMIT("YOP_REF_DOCS_LIMIT", new ArrayList<String>() {
        {
            add("platform");
        }
    }),

    YOP_IMG_COMPRESS_CONFIG("YOP_IMG_COMPRESS_CONFIG", new HashMap<String, Object>() {
        {
            put(COMPRESS_SWITCH, true);
            put(COMPRESS_THRESHOLD, 1024 * 1024);
            put(COMPRESS_SUPPORTED_TYPES, "png,jpg|jpeg");
            put("cmd_png", "magick convert $srcFile -strip -quality 85% $targetFile");
            put("cmd_jpg|jpeg", "magick convert $srcFile -sampling-factor 4:2:0 -strip -quality 85% -interlace JPEG -colorspace sRGB $targetFile");
        }
    }),

    YOP_PORTAL_API_ROUTE_IMPLICIT_PARAM_IN("YOP_PORTAL_API_ROUTE_IMPLICIT_PARAM_IN", new HashMap<String, String>() {
        {
            put("DUBBO", "RPC_CONTEXT");
            put("HTTP", "HEADER");
        }
    }),

    YOP_MBR_DOC_ACCESS_CONFIG("YOP_MBR_DOC_ACCESS_CONFIG", new HashMap<String, Object>() {
        {
            put(YOP_MBR_DOC_ACCESS_MBR_PREFIX_CFG_KEY, "https://open.yeepay.com");
            put(YOP_MBR_DOC_ACCESS_PREVIEW_PREFIX_CFG_KEY, "http://*************:9001");
        }
    }),

    YOP_PORTAL_SDK_CONFIG("YOP_PORTAL_SDK_CONFIG", new HashMap<String, String>() {
        {
            put("appId", "yop-boss");
            put("secretKey", "PdZ74F6sxapgOWJ31QKmYw==");
        }
    }),

    // 开发者中心Basic认证方式的用户名:密码
    YOP_DEVELOPER_KEY_TOOLS_BASIC("YOP_DEVELOPER_KEY_TOOLS_BASIC", "keytools:keytools"),

    // 根据查询条件批量重发每次查询的pageSize
    YOP_PORTAL_QUERY_RESEND_PAGE_SIZE("YOP_PORTAL_QUERY_RESEND_PAGE_SIZE", 200L),
    YOP_PORTAL_QUERY_RESEND_INTERVAL_MILLIS("YOP_PORTAL_QUERY_RESEND_INTERVAL_MILLIS", 1000L),

    YOP_DOC_FAQ_RELATE_QUERY_LIMIT("YOP_DOC_FAQ_RELATE_QUERY_LIMIT", 50L),

    // yop-notifier黑名单列表
    YOP_NOTIFIER_BLACKLIST("YOP_NOTIFIER_BLACKLIST", new HashMap<>()),
    YOP_CUSTOMER_TENANT_DEFAULT("YOP_CUSTOMER_TENANT_DEFAULT", "YEEPAY"),//境外配置为KJ

    // 用户搜索服务配置
    YOP_USER_SEARCH_CONFIG("YOP_USER_SEARCH_CONFIG", new HashMap<String, String>() {
        {
            put("serviceUrl", "https://uiaservice.yeepay.com/yuia-service-boss/meta/users");
            put("systemCode", "fmc-boss");
            put("signature", "aDhMd3XCjk9LV8/efoZSuQ==");
        }
    });

    private static Map<String, ConfigEnum> valueMap = Maps.newHashMap();

    static {
        for (ConfigEnum item : ConfigEnum.values()) {
            valueMap.put(item.configKey, item);
        }
    }

    private String configKey;
    private Object defaultValue;

    ConfigEnum(String configKey, Object defaultValue) {
        this.configKey = configKey;
        this.defaultValue = defaultValue;
    }

    public String getConfigKey() {
        return configKey;
    }

    public Object getDefaultValue() {
        return defaultValue;
    }

}
