/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.perm.enums.AuditStatusEnum;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Size;
import java.util.Date;

/**
 * title:申请单 <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/11/29 19:47
 */
public class AuditRequisitionPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    @Size(min = 1, max = 32)
    private String code;

    @Size(min = 1, max = 32)
    private String originator;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdEndDate;

    private AuditStatusEnum status;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOriginator() {
        return originator;
    }

    public void setOriginator(String originator) {
        this.originator = originator;
    }

    public Date getCreatedStartDate() {
        return createdStartDate;
    }

    public void setCreatedStartDate(Date createdStartDate) {
        this.createdStartDate = createdStartDate;
    }

    public Date getCreatedEndDate() {
        return createdEndDate;
    }

    public void setCreatedEndDate(Date createdEndDate) {
        this.createdEndDate = createdEndDate;
    }

    public AuditStatusEnum getStatus() {
        return status;
    }

    public void setStatus(AuditStatusEnum status) {
        this.status = status;
    }
}
