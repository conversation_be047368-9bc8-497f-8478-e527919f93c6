package com.yeepay.g3.app.yop.portal.vo;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-05-29 11:53
 */
public class SecurityReqChangeVo extends BaseVO {

    private static final long serialVersionUID = -1;

    private String apiUri;

    private String apiId;

    private String apiGroupCode;

    private Long securityReqVersion;

    private List<NewSecurityReqVO> securities;

    public String getApiId() {
        return apiId;
    }

    public void setApiId(String apiId) {
        this.apiId = apiId;
    }

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public Long getSecurityReqVersion() {
        return securityReqVersion;
    }

    public void setSecurityReqVersion(Long securityReqVersion) {
        this.securityReqVersion = securityReqVersion;
    }

    public List<NewSecurityReqVO> getSecurities() {
        return securities;
    }

    public void setSecurities(List<NewSecurityReqVO> securities) {
        this.securities = securities;
    }
}
