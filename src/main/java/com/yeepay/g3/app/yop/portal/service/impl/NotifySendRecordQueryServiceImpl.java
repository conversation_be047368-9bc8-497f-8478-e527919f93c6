/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.service.AppQueryService;
import com.yeepay.g3.app.yop.portal.service.NotifySendRecordQueryService;
import com.yeepay.g3.app.yop.portal.service.SpiService;
import com.yeepay.g3.app.yop.portal.utils.DateUtils;
import com.yeepay.g3.app.yop.portal.utils.MapUtils;
import com.yeepay.g3.app.yop.portal.vo.NotifySendRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.PageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.notify.NotifySendRecordVO;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.monitor.enums.OrderStatusEnum;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.query.QueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/28 20:41
 */
@Component
@Slf4j
public class NotifySendRecordQueryServiceImpl implements NotifySendRecordQueryService {
    @Resource(name = "notifySendRecordQueryService")
    private QueryService queryService;
    @Autowired
    private AppQueryService appQueryService;
    @Autowired
    private SpiService spiService;

    @Override
    public PageQueryResult<NotifySendRecordVO> listSendRecord(NotifySendRecordQueryParam notifySendRecordQueryParam, PageQueryParam pageQueryParam) {
        Map<String, Object> paramMap = MapUtils.objectToMap(notifySendRecordQueryParam);
        paramMap.put("_startIndex", pageQueryParam.getPageNo() <= 1 ? 0 : (pageQueryParam.getPageNo() - 1) * pageQueryParam.getPageSize());
        paramMap.put("_maxSize", pageQueryParam.getPageSize());
        List<Map> list = queryService.query("listSendRecord", paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return new PageQueryResult<>();
        }
        PageQueryResult<NotifySendRecordVO> result = new PageQueryResult<>();
        List<NotifySendRecordVO> data = new ArrayList<>(list.size());
        // spi标题需要单独查询，若本次查询中有多条记录使用了相同的spi则缓存其spi名称减少多余的开销
        Map<String, String> spiTitleCache = new HashMap<>();
        list.forEach(map -> data.add(convertToNotifySendRecordVO(map, spiTitleCache)));
        result.setItems(data);
        result.setPageNo(pageQueryParam.getPageNo());
        return result;
    }

    private NotifySendRecordVO convertToNotifySendRecordVO(Map<String, Object> dataMap, Map<String, String> spiTitleCache) {
        NotifySendRecordVO notifySendRecordVO = new NotifySendRecordVO();
        notifySendRecordVO.setId((String) dataMap.get("id"));
        notifySendRecordVO.setNotificationId((String) dataMap.get("notification_id"));
        notifySendRecordVO.setNotifyMerchantNo((String) dataMap.get("real_notify_merchant_no"));
        notifySendRecordVO.setAppId((String) dataMap.get("real_app_id"));
        notifySendRecordVO.setUrl((String) dataMap.get("real_url"));
        notifySendRecordVO.setNotifyRule((String) dataMap.get("real_notify_rule"));
        notifySendRecordVO.setErrorCode((String) dataMap.get("error_code"));
        notifySendRecordVO.setErrorMsg(StringUtils.isEmpty((String) dataMap.get("error_msg")) ? notifySendRecordVO.getErrorCode() : (String) dataMap.get("error_msg"));
        notifySendRecordVO.setOrderDate(DateUtils.toDate(dataMap.get("order_datetime")));
        notifySendRecordVO.setSendDate(DateUtils.toDate(dataMap.get("send_datetime")));
        notifySendRecordVO.setStatus(OrderStatusEnum.parse((String) dataMap.get("status")));
        String spiName = (String) dataMap.get("spi_name");

        if (StringUtils.isNotEmpty(spiName)) {
            notifySendRecordVO.setSpiName(spiName);
            String spiTitle = spiTitleCache.computeIfAbsent(spiName, key -> spiService.findSpiTitle(key));
            notifySendRecordVO.setSpiTitle(spiTitle);
        }
        return notifySendRecordVO;
    }
}
