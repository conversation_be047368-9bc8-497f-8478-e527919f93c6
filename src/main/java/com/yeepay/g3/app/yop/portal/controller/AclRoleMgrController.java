package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.cache.SpLocalCache;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.AclRoleQueryService;
import com.yeepay.g3.app.yop.portal.service.AclUserQueryService;
import com.yeepay.g3.app.yop.portal.utils.MapUtils;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.page.AclRolePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.AclUserPageQueryParam;
import com.yeepay.g3.core.yop.utils.bean.BeanConvertUtils;
import com.yeepay.g3.facade.yop.perm.dto.AclIspRoleAuthResponse;
import com.yeepay.g3.facade.yop.perm.dto.AclResourceAuthTreeDTO;
import com.yeepay.g3.facade.yop.perm.dto.AclRoleDTO;
import com.yeepay.g3.facade.yop.perm.dto.IspInfoDTO;
import com.yeepay.g3.facade.yop.perm.enums.OperatorPositionEnum;
import com.yeepay.g3.facade.yop.perm.enums.OperatorStatusEnum;
import com.yeepay.g3.facade.yop.perm.enums.RoleTypeEnum;
import com.yeepay.g3.facade.yop.perm.facade.AclPrivilegeMgrFacade;
import com.yeepay.g3.facade.yop.perm.facade.AclRoleMgrFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.yeepay.g3.app.yop.portal.utils.Constants.ALL_SP_INFO_KEY;


/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午7:34
 */

@Controller
@RequestMapping("/rest/acl/role")
public class AclRoleMgrController {

    private AclRoleMgrFacade aclRoleMgrFacade = RemoteServiceFactory.getService(AclRoleMgrFacade.class);
    private AclPrivilegeMgrFacade aclPrivilegeMgrFacade = RemoteServiceFactory.getService(AclPrivilegeMgrFacade.class);

    @Autowired
    private AclUserQueryService aclUserQueryService;

    @Autowired
    private AclRoleQueryService aclRoleQueryService;

    @Autowired
    private SpLocalCache spLocalCache;

    @RequestMapping(value = "/commons/list", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage roleCodes() {
        List<AclRoleVO> aclRoleVOS;
        if (ShiroUtils.isPlatformOperator()) {
            aclRoleVOS = aclRoleQueryService.list();
        } else {
            aclRoleVOS = aclRoleQueryService.listForSp(ShiroUtils.getShiroUser().getSpScopes());
        }
        return new ResponseMessage("result", aclRoleVOS);
    }

    @RequestMapping(value = "/commons/users", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage users(String roleCode) {
        return new ResponseMessage("result", aclUserQueryService.pageCommonListForRole(roleCode));
    }

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage queryRoleList(@RequestParam(required = false) RoleTypeEnum type,
                                         @RequestParam(required = false) String spCode,
                                         @RequestParam(required = false) String roleCode,
                                         @RequestParam(required = false) String roleName,
                                         @RequestParam(required = false, value = "_pageNo") Integer pageNo,
                                         @RequestParam(required = false, value = "_pageSize") Integer pageSize) {

        AclRolePageQueryParam param = AclRolePageQueryParam.Builder.anAclRolePageQueryParam()
                .withType(type)
                .withSpCode(spCode)
                .withRoleCode(roleCode)
                .withRoleName(roleName)
                .withPageNo(pageNo)
                .withPageSize(pageSize)
                .build();
        if (ShiroUtils.isPlatformOperator()) {
            return new ResponseMessage("result", aclRoleQueryService.pageQuery(param));
        } else {
            return new ResponseMessage("result", aclRoleQueryService.pageQueryForSp(param, ShiroUtils.getShiroUser().getSpScopes()));
        }
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage queryRoleDetail(@RequestParam("roleCode") String roleCode) {
        AclRoleDTO aclRole = aclRoleMgrFacade.findByRoleCode(roleCode);
        List<AclResourceAuthTreeDTO> resourceTrees = aclPrivilegeMgrFacade.findAclResourceTreeByRoleCode(roleCode);
        AclRoleVO aclRoleVO = BeanConvertUtils.convert(aclRole, AclRoleVO.class);
        aclRoleVO.setResources(resourceTrees);
        if (!RoleTypeEnum.TEMPLET.equals(aclRoleVO.getRoleType())) {
            Map<String, IspInfoDTO> allSpCodes = spLocalCache.get(ALL_SP_INFO_KEY);
            IspInfoDTO ispInfoDTO = allSpCodes.get(aclRoleVO.getSpCode());
            if (ispInfoDTO != null) {
                aclRoleVO.setSpName(ispInfoDTO.getSpName());
            }
        }
        return new ResponseMessage("result", aclRoleVO);
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody AclRoleRequestParamVO role) {
        AclRoleDTO aclRoleDTO = BeanConvertUtils.convert(role, AclRoleDTO.class);
        aclRoleMgrFacade.create(aclRoleDTO);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody AclRoleRequestParamVO role) {
        AclRoleDTO aclRole = BeanConvertUtils.convert(role, AclRoleDTO.class);
        aclRoleMgrFacade.update(aclRole);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/auth/add", method = RequestMethod.POST)
    public ResponseMessage authUser(@RequestBody AclRoleAuthRequestParam authParam) {
        aclRoleMgrFacade.batchAddUser(authParam.getCode(), authParam.getOptCodes());
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/auth/delete", method = RequestMethod.POST)
    public ResponseMessage unauthUser(@RequestBody AclRoleAuthRequestParam authParam) {
        aclRoleMgrFacade.batchDeleteUser(authParam.getCode(), authParam.getOptCodes());
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/type", method = RequestMethod.GET)
    public ResponseMessage queryRoleType() {
        Map<String, String> typeMap = RoleTypeEnum.getDisplayValueMap();
        List<TypeVO> roleTypes = new ArrayList<>();
        if (ShiroUtils.getShiroUser().isPlatformOperator()) {
            roleTypes = MapUtils.mapToTypeVOs(typeMap);
        } else {
            TypeVO typeVO = new TypeVO();
            typeVO.setCode(RoleTypeEnum.SP_BASED.getValue());
            typeVO.setName(RoleTypeEnum.SP_BASED.getDisplayName());
            roleTypes.add(typeVO);
        }
        return new ResponseMessage("result", roleTypes);
    }

    @ResponseBody
    @RequestMapping(value = "/by-spcodes", method = RequestMethod.GET)
    public ResponseMessage queryRoleBySpCodes(@RequestParam List<String> spCodes) {
        List<AclIspRoleAuthResponse> responses = aclRoleMgrFacade.findBySpCodes(spCodes);
        return new ResponseMessage("result", BeanConvertUtils.convertList(responses, AclIspRoleAuthVO.class));
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam String roleCode) {
        aclRoleMgrFacade.delete(roleCode);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/by-optcode", method = RequestMethod.POST)
    public ResponseMessage queryByOptCode(@RequestParam String optCode) {
        List<AclIspRoleAuthResponse> aclRoles = aclRoleMgrFacade.findByOperatorCode(ShiroUtils.getOperatorCode(), optCode);
        return new ResponseMessage("result", aclRoles);
    }

    @ResponseBody
    @RequestMapping(value = "/check-duplicate", method = RequestMethod.GET)
    public ResponseMessage checkRoleCode(@RequestParam String roleCode) {
        if (aclRoleMgrFacade.findByRoleCode(roleCode) != null) {
            return new ResponseMessage("result", true);
        }
        return new ResponseMessage("result", false);

    }

    @ResponseBody
    @RequestMapping(value = "/users", method = RequestMethod.GET)
    public ResponseMessage queryUsers(@RequestParam String roleCode,
                                      @RequestParam(required = false) OperatorPositionEnum position,
                                      @RequestParam(required = false) String name,
                                      @RequestParam(required = false) OperatorStatusEnum status,
                                      @RequestParam(required = false, value = "_pageNo") Integer pageNo,
                                      @RequestParam(required = false, value = "_pageSize") Integer pageSize) {

        AclUserPageQueryParam param = AclUserPageQueryParam.Builder.anAclUserPageQueryParam()
                .withPosition(position)
                .withName(name)
                .withStatus(status)
                .withPageNo(pageNo)
                .withPageSize(pageSize)
                .build();

        return new ResponseMessage("result", aclUserQueryService.pageListForRole(param, roleCode));
    }
}
