/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yeepay.g3.facade.yop.perm.dto.AclIspRoleAuthResponse;
import com.yeepay.g3.facade.yop.perm.enums.OperatorPositionEnum;
import com.yeepay.g3.facade.yop.perm.enums.OperatorStatusEnum;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/10/25 19:33
 */
public class IspOperatorVO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 用户编码
     */
    @JsonProperty("optCode")
    private String operatorCode;

    /**
     * 用户名称
     */
    @JsonProperty("optName")
    private String operatorName;

    /**
     * 用户状态
     */
    private OperatorStatusEnum status;

    /**
     * 用户邮箱
     */
    @JsonProperty("email")
    private String operatorEmail;

    /**
     * 用户职位
     */
    private OperatorPositionEnum position;

    /**
     * 用户类型
     */
    @JsonProperty("type")
    private OperatorTypeEnum operatorType;

    private List<AclIspRoleAuthVO> perms;

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public OperatorStatusEnum getStatus() {
        return status;
    }

    public void setStatus(OperatorStatusEnum status) {
        this.status = status;
    }

    public String getOperatorEmail() {
        return operatorEmail;
    }

    public void setOperatorEmail(String operatorEmail) {
        this.operatorEmail = operatorEmail;
    }

    public OperatorPositionEnum getPosition() {
        return position;
    }

    public void setPosition(OperatorPositionEnum position) {
        this.position = position;
    }

    public OperatorTypeEnum getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(OperatorTypeEnum operatorType) {
        this.operatorType = operatorType;
    }

    public List<AclIspRoleAuthVO> getPerms() {
        return perms;
    }

    public void setPerms(List<AclIspRoleAuthVO> perms) {
        this.perms = perms;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
