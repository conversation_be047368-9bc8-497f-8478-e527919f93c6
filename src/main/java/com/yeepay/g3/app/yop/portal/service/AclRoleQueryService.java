package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.AclRoleVO;
import com.yeepay.g3.app.yop.portal.vo.page.AclRolePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.AclRolePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.List;
import java.util.Set;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午5:11
 */
public interface AclRoleQueryService {

    List<AclRoleVO> list();

    List<AclRoleVO> listForSp(Set<String> spCodes);

    PageQueryResult<AclRolePageItem> pageQuery(AclRolePageQueryParam param);

    PageQueryResult<AclRolePageItem> pageQueryForSp(AclRolePageQueryParam param, Set<String> spCodes);
}
