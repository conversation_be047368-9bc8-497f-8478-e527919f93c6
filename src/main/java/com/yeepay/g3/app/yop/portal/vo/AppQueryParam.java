/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.sys.enums.AppStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/8 15:05
 */
@Data
@Accessors(chain = true)
public class AppQueryParam extends BaseVO {
    private String customerNo;
    private List<String> types;
    private AppStatusEnum status;
}
