/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.sys.enums.ProductApiTypeEnum;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/5 下午4:54
 */
public class ProductApiVO implements Serializable {

    private static final long serialVersionUID = -1L;

    @NotNull
    @Size(min = 1, max = 32)
    private String productCode;

    private Long sceneId;

    @NotNull
    private ProductApiTypeEnum type;

    @NotNull
    @Size(min = 1)
    private List<String> values;

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Long getSceneId() {
        return sceneId;
    }

    public void setSceneId(Long sceneId) {
        this.sceneId = sceneId;
    }

    public ProductApiTypeEnum getType() {
        return type;
    }

    public void setType(ProductApiTypeEnum type) {
        this.type = type;
    }

    public List<String> getValues() {
        return values;
    }

    public void setValues(List<String> values) {
        this.values = values;
    }
}
