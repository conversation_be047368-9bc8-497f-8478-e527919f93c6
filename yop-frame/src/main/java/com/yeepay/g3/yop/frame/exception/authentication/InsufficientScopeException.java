package com.yeepay.g3.yop.frame.exception.authentication;

import com.yeepay.g3.yop.frame.CharacterConstants;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/1/25 下午6:12
 */
public class InsufficientScopeException extends OAuth2Exception {
    @Override
    public String getCode() {
        return super.getCode() + CharacterConstants.DOT + "insufficient-scope";
    }
}
