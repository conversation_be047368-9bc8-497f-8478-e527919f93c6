/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * title: 自定义解决方案-添加api<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/20
 */
@Data
public class CustomSolutionRelateApiVO implements Serializable {

    private static final long serialVersionUID = -1L;

    @NotNull
    private String solutionCode;

    @NotNull
    private List<String> apiIds;
}
