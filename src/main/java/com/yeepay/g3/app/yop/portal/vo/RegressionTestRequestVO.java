/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.yeepay.g3.facade.yop.api.enums.HttpMethodType;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/20 上午10:28
 */
public class RegressionTestRequestVO implements Serializable {

    private static final long serialVersionUID = -1;

    private String appKey;
    private String cert;
    private String signAlg;
    private String serverRoot;
    private String resourcePath;
    private Multimap<String, String> formParam;
    private String jsonParam;
    private boolean isJsonRequest = false;
    private boolean isNeedEncrypt = false;
    private String encryptPriKey;
    private String accessToken;
    private HttpMethodType[] supportMethods;


    private RegressionTestRequestVO() {
    }

    public String getAppKey() {
        return appKey;
    }

    public String getCert() {
        return cert;
    }

    public String getSignAlg() {
        return signAlg;
    }

    public String getServerRoot() {
        return serverRoot;
    }

    public String getResourcePath() {
        return resourcePath;
    }

    public String getJsonParam() {
        return jsonParam;
    }

    public Multimap<Object, Object> getFormParam() {
        return ArrayListMultimap.create(formParam);
    }

    public boolean isJsonRequest() {
        return isJsonRequest;
    }

    public boolean isNeedEncrypt() {
        return isNeedEncrypt;
    }

    public String getEncryptPriKey() {
        return encryptPriKey;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public HttpMethodType[] getSupportMethods() {
        return supportMethods;
    }

    public void setSupportMethods(HttpMethodType[] supportMethods) {
        this.supportMethods = supportMethods;
    }

    public static class Builder {
        private String appKey;
        private String cert;
        private String signAlg;
        private String serverRoot;
        private String resourcePath;
        private Multimap<String, String> formParam = ArrayListMultimap.create();
        private String jsonParam;
        private boolean isJsonRequest = false;
        private boolean isNeedEncrypt = false;
        private String encryptPriKey;
        private String accessToken;
        private HttpMethodType[] supportMethods;

        private Builder() {
        }

        public static Builder anRegressionTestRequestVO() {
            return new Builder();
        }

        public Builder withAppKey(String appKey) {
            this.appKey = appKey;
            return this;
        }

        public Builder withCert(String cert) {
            this.cert = cert;
            return this;
        }

        public Builder withSignAlg(String signAlg) {
            this.signAlg = signAlg;
            return this;
        }

        public Builder withServerRoot(String serverRoot) {
            this.serverRoot = serverRoot;
            return this;
        }

        public Builder withResourcePath(String resourcePath) {
            this.resourcePath = resourcePath;
            return this;
        }

        public Builder addFormParam(String key, String value) {
            this.formParam.put(key, value);
            return this;
        }

        public Builder withJsonParam(String jsonParam) {
            this.jsonParam = jsonParam;
            return this;
        }

        public Builder isJsonRequest() {
            this.isJsonRequest = true;
            return this;
        }

        public Builder isNeedEncrypt() {
            this.isNeedEncrypt = true;
            return this;
        }

        public Builder withEncryptPriKey(String encryptPriKey) {
            this.encryptPriKey = encryptPriKey;
            return this;
        }

        public Builder withAccessToken(String accessToken) {
            this.accessToken = accessToken;
            return this;
        }

        public Builder withSupportMethods(HttpMethodType[] supportMethods) {
            this.supportMethods = supportMethods;
            return this;
        }

        public RegressionTestRequestVO build() {
            RegressionTestRequestVO vo = new RegressionTestRequestVO();
            vo.appKey = this.appKey;
            vo.cert = this.cert;
            vo.signAlg = this.signAlg;
            vo.serverRoot = this.serverRoot;
            vo.resourcePath = this.resourcePath;
            vo.formParam = ArrayListMultimap.create(this.formParam);
            vo.jsonParam = this.jsonParam;
            vo.isJsonRequest = this.isJsonRequest;
            vo.isNeedEncrypt = this.isNeedEncrypt;
            vo.encryptPriKey = this.encryptPriKey;
            vo.accessToken = this.accessToken;
            vo.supportMethods = this.supportMethods;
            return vo;
        }
    }

}
