/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.config.ra;

import cfca.ra.toolkit.RAClient;
import cfca.ra.toolkit.exception.RATKException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/3/24 12:05 下午
 */
@Configuration
@Profile("cfca")
public class RAConfig {

    @Autowired
    private RAClientConfig raClientConfig;

    @Bean("raClient")
    public RAClient getRAClient() throws RATKException {
        RAClient client = null;
        switch (raClientConfig.getType()) {
            case "1":
                // 初始化为http连接方式，指定url
                client = new RAClient(raClientConfig.getUrl(), raClientConfig.getConnectTimeout(), raClientConfig.getReadTimeout());
                break;
            case "2":
                // 初始化为https连接方式，指定url，另需配置ssl的证书及信任证书链
                client = new RAClient(raClientConfig.getUrl(), raClientConfig.getConnectTimeout(), raClientConfig.getReadTimeout());
                client.initSSL(raClientConfig.getKeyStorePath(), raClientConfig.getKeyStorePassword(), raClientConfig.getTrustStorePath(), raClientConfig.getTrustStorePassword());
                break;
            case "3":
                // 初始化为socket 连接方式，指定ip和端口
                client = new RAClient(raClientConfig.getIp(), raClientConfig.getPort(), raClientConfig.getConnectTimeout(), raClientConfig.getReadTimeout());
                break;
            case "4":
                // 初始化为ssl socket 连接方式，指定ip和端口，另需配置ssl的证书及信任证书链
                client = new RAClient(raClientConfig.getIp(), raClientConfig.getPort(), raClientConfig.getConnectTimeout(), raClientConfig.getReadTimeout());
                client.initSSL(raClientConfig.getKeyStorePath(), raClientConfig.getKeyStorePassword(), raClientConfig.getTrustStorePath(), raClientConfig.getTrustStorePassword());
                // 如需指定ssl协议、算法、证书库类型，使用如下方式
                // client.initSSL(keyStorePath, keyStorePassword, trustStorePath, trustStorePassword, "SSL", "IbmX509", "IbmX509", "JKS", "JKS");
                break;
            default:
                break;
        }
        return client;
    }

}
