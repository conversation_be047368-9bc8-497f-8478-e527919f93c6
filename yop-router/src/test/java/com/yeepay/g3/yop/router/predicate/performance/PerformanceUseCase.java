/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.router.predicate.performance;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/14 6:30 PM
 */
@Data
@Builder
public class PerformanceUseCase {

    private String name;

    private int time;

    private Map<String, Object> param;

    private Map<String, String> expression;

}
