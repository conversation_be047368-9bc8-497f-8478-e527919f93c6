<?xml version="1.0" encoding="UTF-8"?>
<Server port="8005" shutdown="SHUTDOWN">
  <Listener className="org.apache.catalina.core.AprLifecycleListener" SSLEngine="on" />
  <Listener className="org.apache.catalina.core.JreMemoryLeakPreventionListener" />
  <Listener className="org.apache.catalina.mbeans.GlobalResourcesLifecycleListener" />
  <Listener className="org.apache.catalina.core.ThreadLocalLeakPreventionListener" />

  <GlobalNamingResources>
    <Resource name="UserDatabase" auth="Container"
              type="org.apache.catalina.UserDatabase"
              description="User database that can be updated and saved"
              factory="org.apache.catalina.users.MemoryUserDatabaseFactory"
              pathname="conf/tomcat-users.xml" />
  </GlobalNamingResources>

  <Service name="Catalina">
    <Executor name="tomcatThreadPool"
              maxThreads="2000" maxQueueSize="2000" maxIdleTime="30000" prestartminSpareThreads="true"/>
    <Connector executor="tomcatThreadPool" port="8080" protocol="org.apache.coyote.http11.Http11NioProtocol"
               connectionTimeout="20000"
               redirectPort="8443" URIEncoding="utf-8"
               enableLookups="false"
               minProcessors="10" maxProcessors="150"
               acceptCount="2000"
               maxKeepAliveRequests="8000" keepAliveTimeout="65000"
               maxHttpHeaderSize="8192"
               disableUploadTimeout="true"
               compression="on"
               compressionMinSize="2048"
               noCompressionUserAgents="gozilla, traviata"
               compressableMimeType="text/html,text/xml,text/javascript,application/javascript,text/css,text/plain,application/octet-stream"
               maxPostSize="6391456"/>
    <Engine name="Catalina" defaultHost="localhost">
      <Realm className="org.apache.catalina.realm.LockOutRealm">
        <Realm className="org.apache.catalina.realm.UserDatabaseRealm"
               resourceName="UserDatabase"/>
      </Realm>
      <Host name="localhost"  appBase="webapps"
            unpackWARs="true" autoDeploy="false" workDir="${workdir}">
        <Valve className="com.yeepay.g3.utils.common.tomcat.ThreadContextValve"/>
        <Valve className="com.yeepay.infra.AccessLogValve" 
               pattern="%{X-Forwarded-For}i %h %l %u %D %t &quot;%m %U %{_method}i&quot; %s %b"/>
      </Host>
    </Engine>
  </Service>
</Server>
