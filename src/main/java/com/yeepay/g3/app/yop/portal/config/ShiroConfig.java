package com.yeepay.g3.app.yop.portal.config;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroTenantRealm;
import org.apache.shiro.cache.ehcache.EhCacheManager;
import org.apache.shiro.session.mgt.SessionManager;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.servlet.Cookie;
import org.apache.shiro.web.servlet.SimpleCookie;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017/7/27 下午12:35
 */
@Configuration
public class ShiroConfig {

    @Bean(name = "shiroEhcacheManager")
    public EhCacheManager getEhCacheManager() {
        EhCacheManager em = new EhCacheManager();
        em.setCacheManagerConfigFile("classpath:yop-portal-spring/ehcache-shiro.xml");
        return em;
    }

    @Bean(name = "sessionIdCookie")
    public Cookie getSessionIdCookie() {
        SimpleCookie cookie = new SimpleCookie();
        cookie.setName("yop_sid");
        cookie.setHttpOnly(true);
        cookie.setMaxAge(1);
        return cookie;
    }

    @Bean(name = "lifecycleBeanPostProcessor")
    public LifecycleBeanPostProcessor getLifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }

    @Bean
    public DefaultAdvisorAutoProxyCreator getDefaultAdvisorAutoProxyCreator() {
        DefaultAdvisorAutoProxyCreator daap = new DefaultAdvisorAutoProxyCreator();
        daap.setProxyTargetClass(true);
        return daap;
    }

    @Bean(name = "securityManager")
    public DefaultWebSecurityManager getDefaultWebSecurityManager(SessionManager sessionManager, ShiroRealm shiroRealm, ShiroTenantRealm shiroTenantRealm) {
        DefaultWebSecurityManager dwsm = new DefaultWebSecurityManager();
        dwsm.setRealms(Arrays.asList(shiroRealm, shiroTenantRealm));
        dwsm.setCacheManager(getEhCacheManager());
        dwsm.setSessionManager(sessionManager);
        return dwsm;
    }

    @Bean(name = "sessionManager")
    public SessionManager getSessionManager(Cookie sessionIdCookie) {
        DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
        sessionManager.setSessionIdCookie(sessionIdCookie);
        sessionManager.setGlobalSessionTimeout(30 * 60_000);
        sessionManager.setDeleteInvalidSessions(true);
        // StatusLess 下无需管理 Session
        sessionManager.setSessionValidationSchedulerEnabled(false);
        return sessionManager;
    }

    @Bean
    public AuthorizationAttributeSourceAdvisor getAuthorizationAttributeSourceAdvisor(DefaultWebSecurityManager defaultWebSecurityManager) {
        AuthorizationAttributeSourceAdvisor aasa = new AuthorizationAttributeSourceAdvisor();
        aasa.setSecurityManager(defaultWebSecurityManager);
        return new AuthorizationAttributeSourceAdvisor();
    }

    //判断URL是否需要登录/权限等工作
    @Bean(name = "shiroFilter")
    public ShiroFilterFactoryBean getShiroFilterFactoryBean(DefaultWebSecurityManager defaultWebSecurityManager, List<CustomShiroFilter> customShiroFilters) {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(defaultWebSecurityManager);
        shiroFilterFactoryBean.setLoginUrl("/signin/sso/*");
        shiroFilterFactoryBean.setSuccessUrl("/");
        Map<String, Filter> filters = Maps.newHashMapWithExpectedSize(customShiroFilters.size());
        customShiroFilters.forEach(filter -> filters.put(filter.shiroName(), filter));
        shiroFilterFactoryBean.setFilters(filters);
        return shiroFilterFactoryBean;
    }

}
