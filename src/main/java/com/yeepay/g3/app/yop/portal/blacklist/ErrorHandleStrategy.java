/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.blacklist;

import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/6/15 9:54 下午
 */
@Component
public class ErrorHandleStrategy implements HandleStrategy {
    private final String name = "ERROR";

    @Override
    public void handle(String appId, String url) {
        throw new YeepayRuntimeException("该url被黑名单拦截, url:{0}, appId:{1}", url, appId);
    }

    @Override
    public String name() {
        return name;
    }


    @PostConstruct
    public void register() {
        HandleStrategyFactory.register(this);
    }
}
