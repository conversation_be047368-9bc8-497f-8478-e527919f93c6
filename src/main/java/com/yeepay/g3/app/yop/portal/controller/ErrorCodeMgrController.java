package com.yeepay.g3.app.yop.portal.controller;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yeepay.g3.app.yop.portal.dto.ErrcodeApiBatchAddDTO;
import com.yeepay.g3.app.yop.portal.dto.ErrorCodeType;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.ApiErrcodeService;
import com.yeepay.g3.app.yop.portal.service.ApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.service.ErrorCodeQueryMgrService;
import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.PlatefromErrorCodeSimpleInfo;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.facade.yop.sys.dto.ErrorCodeDTO;
import com.yeepay.g3.facade.yop.sys.dto.ErrorCodeSolutionDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiErrcodeDTO;
import com.yeepay.g3.facade.yop.sys.enums.ErrorCodeTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.ApiErrcodeFacade;
import com.yeepay.g3.facade.yop.sys.facade.ErrorCodeFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import jodd.util.CsvUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.io.BufferedReader;
import java.io.Serializable;
import java.io.StringReader;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午3:41
 */

@Controller
@RequestMapping("/rest/api-group/error-code")
public class ErrorCodeMgrController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ErrorCodeMgrController.class);
    private static final String[] ERROR_CODE_IMPORT_HEADER = {"type", "apiGroup", "errorCode", "subErrorCode", "subErrorMsg"};
    private static final int MAX_IMPORT_SIZE = 200;

    @Autowired
    private ErrorCodeQueryMgrService errorCodeQueryMgrService;

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    @Autowired
    private ApiErrcodeService apiErrcodeService;

    private ErrorCodeFacade errorCodeFacade = RemoteServiceFactory.getService(ErrorCodeFacade.class);

    private ApiErrcodeFacade apiErrcodeFacade = RemoteServiceFactory.getService(ApiErrcodeFacade.class);

    private static final PageQueryResult<ErrorCodeItem> EMPTY_RESULT = new PageQueryResult<ErrorCodeItem>() {
        {
            setPageNo(0);
            setItems(new ArrayList<>());
            setTotalPageNum(0);
        }
    };

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage queryErrorCodes(@RequestParam(required = false) ErrorCodeTypeEnum type,
                                           @RequestParam(required = false) String apiGroupCode,
                                           @RequestParam(required = false) String apiUri,
                                           @RequestParam(required = false) String errorCode,
                                           @RequestParam(required = false) String subErrorCode,
                                           @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                           @RequestParam(value = "_pageSize", required = false) Integer pageSize) {
        List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
        OperatorTypeEnum operatorType = ShiroUtils.getOperatorType();
        if (OperatorTypeEnum.SP_BASED.equals(operatorType) && apiGroupCodes.size() == 0) {
            return new ResponseMessage("result", EMPTY_RESULT);
        }

        ErrorCodePageQueryParam.Builder builder = ErrorCodePageQueryParam.Builder
                .createBuilder()
                .withType(type)
                .withApiGroupCode(apiGroupCode)
                .withApiUri(apiUri)
                .withErrorCode(errorCode)
                .withSubErrorCode(subErrorCode)
                .withPageNo(pageNo)
                .withPageSize(pageSize);
        if (OperatorTypeEnum.SP_BASED.equals(operatorType) && apiGroupCodes.size() > 0) {
            builder.withApiGroupCodes(apiGroupCodes);
        }
        return new ResponseMessage("result", errorCodeQueryMgrService.queryErrorCodes(builder.build()));
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage queryDetail(@RequestParam Long id) {
        ErrorCodeDTO errorCodeDTO = errorCodeFacade.findById(id);
        return new ResponseMessage("result", errorCodeDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/group-common/list", method = RequestMethod.GET)
    public ResponseMessage queryGroupCommon(@RequestParam String apiGroupCode) {
        List<ErrorCodeDTO> errorCodeDTOList = errorCodeFacade.findGroupCommon(apiGroupCode);
        return new ResponseMessage("result", errorCodeDTOList);
    }


    @ResponseBody
    @RequestMapping(value = "/api-specific/list", method = RequestMethod.GET)
    public ResponseMessage queryApiSpecific(@RequestParam String apiUri) {
        List<ErrorCodeDTO> errorCodeDTOList = errorCodeFacade.findApiSpecific(apiUri);
        return new ResponseMessage("result", errorCodeDTOList);
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody ErrorCodeDTO errorCodeDTO) {
        ErrorCodeTypeEnum errorCodeType = errorCodeDTO.getType();
        checkUserPerm(true, errorCodeType, errorCodeDTO.getApiGroupCode(), ShiroUtils.getShiroUser());
        if (errorCodeFacade.create(errorCodeDTO)) {
            return new ResponseMessage("result", "success");
        } else {
            return new ResponseMessage("result", "failure");
        }
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody ErrorCodeDTO errorCodeDTO) {
        ErrorCodeTypeEnum errorCodeType = errorCodeDTO.getType();
        checkUserPerm(false, errorCodeType, errorCodeDTO.getApiGroupCode(), ShiroUtils.getShiroUser());
        if (errorCodeFacade.update(errorCodeDTO)) {
            return new ResponseMessage("result", "success");
        } else {
            return new ResponseMessage("result", "failure");
        }
    }


    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam Long id, @RequestParam(required = false) boolean force) {
        ErrorCodeDTO errorCodeDTO = errorCodeFacade.findById(id);
        ErrorCodeTypeEnum errorCodeType = errorCodeDTO.getType();
        checkUserPerm(false, errorCodeType, errorCodeDTO.getApiGroupCode(), ShiroUtils.getShiroUser());
        List<String> apis = apiErrcodeService.checkRelatedApis(id);
        if (CollectionUtils.isNotEmpty(apis)) {
            if (!force) {
                return new ResponseMessage(ResponseMessage.Status.WARNING, "存在关联apis:" + apis.stream().collect(Collectors.joining(",")));
            } else {
                LOGGER.warn("delete errcodes when exists related apis, errcodeId:{}, apis:{}", id, apis);
            }
        }
        errorCodeFacade.delete(id);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/commons/error-code-type/list", method = RequestMethod.GET)
    public ResponseMessage queryErrorCodeType() {
        Map<String, ErrorCodeTypeEnum> errorCodeTypeMap = ErrorCodeTypeEnum.getValueMap();
        List<ErrorCodeType> errorCodeTypes = new ArrayList<>();
        OperatorTypeEnum type = ShiroUtils.getOperatorType();
        for (Map.Entry<String, ErrorCodeTypeEnum> entry : errorCodeTypeMap.entrySet()) {
            if (!OperatorTypeEnum.SP_BASED.equals(type) || !ErrorCodeTypeEnum.PLATFORM_COMMON.equals(entry.getValue())) {
                ErrorCodeType errorCodeType = new ErrorCodeType();
                String key = entry.getKey();
                errorCodeType.setValue(key);
                errorCodeType.setDisplayName(errorCodeTypeMap.get(key).getDisplayName());
                errorCodeTypes.add(errorCodeType);
            }
        }
        return new ResponseMessage("result", errorCodeTypes);
    }


    @ResponseBody
    @RequestMapping(value = "/commons/list", method = RequestMethod.GET)
    public ResponseMessage queryPlateformErrorCode() {
        List<ErrorCodeDTO> errorCodes = errorCodeFacade.findPlateformCommon();
        Map<String, List<ErrorCodeDTO>> map = new HashMap<>();
        for (ErrorCodeDTO errorCode : errorCodes) {
            String errorCodeValue = errorCode.getErrorCode();
            if (map.get(errorCodeValue) == null) {
                List<ErrorCodeDTO> errorCodeDTOList = new ArrayList<>();
                errorCodeDTOList.add(errorCode);
                map.put(errorCodeValue, errorCodeDTOList);
            } else {
                map.get(errorCodeValue).add(errorCode);
            }
        }
        Map<String, String> codeMap = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_ERROR_CODES);
        List<PlatefromErrorCodeSimpleInfo> codeList = new ArrayList<>();
        for (Map.Entry<String, List<ErrorCodeDTO>> entry : map.entrySet()) {
            PlatefromErrorCodeSimpleInfo code = new PlatefromErrorCodeSimpleInfo();
            code.setCode(entry.getKey());
            code.setMsg(codeMap.get(entry.getKey()));
            code.setSubCodes(convert(entry.getValue()));
            codeList.add(code);
        }
        Collections.sort(codeList, new Comparator<PlatefromErrorCodeSimpleInfo>() {
            @Override
            public int compare(PlatefromErrorCodeSimpleInfo code1, PlatefromErrorCodeSimpleInfo code2) {
                return Integer.parseInt(code1.getCode()) - Integer.parseInt(code2.getCode());
            }
        });

        return new ResponseMessage("result", codeList);
    }

    @ResponseBody
    @RequestMapping(value = "/solution", method = RequestMethod.GET)
    public ResponseMessage querySolution(@RequestParam String apiGroupCode,
                                         @RequestParam String apiUri,
                                         @RequestParam String errorCode,
                                         @RequestParam String subErrorCode) {
        ErrorCodeSolutionDTO errorCodeDTO = errorCodeFacade.findErrorSolution(apiGroupCode, apiUri, errorCode, subErrorCode);
        return new ResponseMessage("result", errorCodeDTO);
    }

    private List<PlatefromErrorCodeSimpleInfo> convert(List<ErrorCodeDTO> errorCodes) {
        List<PlatefromErrorCodeSimpleInfo> errorCodeSimpleInfos = new ArrayList<>();
        for (ErrorCodeDTO errorCode : errorCodes) {
            PlatefromErrorCodeSimpleInfo errorCodeSimpleInfo = new PlatefromErrorCodeSimpleInfo();
            errorCodeSimpleInfo.setCode(errorCode.getSubErrorCode());
            errorCodeSimpleInfo.setMsg(errorCode.getSubErrorMsg());
            errorCodeSimpleInfos.add(errorCodeSimpleInfo);
        }
        return errorCodeSimpleInfos;
    }


    private void checkUserPerm(boolean isCreate, ErrorCodeTypeEnum errorCodeType, String apiGroupCode, ShiroRealm.ShiroUser shiroUser) {
        if (isCreate && ErrorCodeTypeEnum.API_SPECIFIC.equals(errorCodeType)) {
            throw new IllegalArgumentException("不允许添加该类型错误码:API_SPECIFIC");
        }
        if (ErrorCodeTypeEnum.PLATFORM_COMMON.equals(errorCodeType) && shiroUser.isSpOperator()) {
            throw new IllegalArgumentException("用户没有权限操作平台类型错误码");
        }
        if (StringUtils.isNotBlank(apiGroupCode) && shiroUser.isSpOperator()) {
            Set<String> apiGroupCodes = shiroUser.getApiGroupScopes();
            if (CollectionUtils.isEmpty(apiGroupCodes) || !apiGroupCodes.contains(apiGroupCode)) {
                throw new IllegalArgumentException("用户没有权限操作该分组的错误码," + apiGroupCode);
            }
        }
    }

    /**
     * 导入表头：type,apiGroup,errorCode,subErrorCode,subErrorMsg
     *
     * @param csvText
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(value = "batch-import", method = RequestMethod.POST)
    public ResponseMessage batchImport(@RequestParam("csvText") String csvText) throws Exception {
        CheckUtils.notEmpty(csvText, "csvText");
        try (BufferedReader reader = new BufferedReader(new StringReader(csvText))) {
            String header;
            header = reader.readLine();
            validateImportHeaderLine(header);

            List<CsvLine> lines = Lists.newArrayListWithExpectedSize(MAX_IMPORT_SIZE);
            List<ImportFailResult> results = Lists.newArrayListWithExpectedSize(MAX_IMPORT_SIZE);
            AtomicInteger i = new AtomicInteger(2);
            reader.lines().forEachOrdered(line -> {
                if (i.get() > MAX_IMPORT_SIZE + 1) {
                    throw new IllegalArgumentException("please make sure your csv-text no more than 200 lines");
                }
                if (StringUtils.isNotBlank(line) && StringUtils.isNotBlank(line.replace(",", ""))) {
                    lines.add(new CsvLine(i.get(), line));
                } else {
                    results.add(new ImportFailResult(i.get(), "空白行"));
                }
                i.getAndIncrement();
            });

            for (Map.Entry<Integer, ErrorCodeDTO> entry : parseCsvText(lines, results, ShiroUtils.getShiroUser()).entrySet()) {
                try {
                    if (!errorCodeFacade.create(entry.getValue())) {
                        results.add(new ImportFailResult(entry.getKey(), "数据库已存在相同错误码"));
                    }
                } catch (Exception e) {
                    LOGGER.error("import errorcode fail", e);
                    if (StringUtils.startsWith(e.getMessage(), "orgExceptionType:")) {
                        results.add(new ImportFailResult(entry.getKey(), StringUtils.truncate(e.getMessage(), 0, 80)));
                    } else {
                        results.add(new ImportFailResult(entry.getKey(), e.getMessage()));
                    }
                }
            }
            Collections.sort(results);
            return new ResponseMessage("result", results);
        }
    }

    private void validateImportHeaderLine(String header) {
        String[] cells = CsvUtil.toStringArray(header);
        boolean checkFailed = false;
        if (cells.length == ERROR_CODE_IMPORT_HEADER.length) {
            for (int i = 0; i < ERROR_CODE_IMPORT_HEADER.length; i++) {
                if (!ERROR_CODE_IMPORT_HEADER[i].equals(cells[i])) {
                    checkFailed = true;
                    break;
                }
            }
        } else {
            checkFailed = true;
        }
        if (checkFailed) {
            throw new IllegalArgumentException("please refer to the csv template to make fields in the order " + Arrays.toString(ERROR_CODE_IMPORT_HEADER));
        }
    }

    private Map<Integer, ErrorCodeDTO> parseCsvText(List<CsvLine> lines, List<ImportFailResult> results, ShiroRealm.ShiroUser shiroUser) {
        Map<Integer, ErrorCodeDTO> dtoMap = Maps.newHashMapWithExpectedSize(lines.size());
        Set<Object> lineKeys = Sets.newHashSetWithExpectedSize(lines.size());
        boolean spOperator = shiroUser.isSpOperator();
        lines.forEach(line -> {
            String[] cells = CsvUtil.toStringArray(line.getLineText());
            if (cells.length < ERROR_CODE_IMPORT_HEADER.length) {
                results.add(new ImportFailResult(line.lineNo, "字段列数不符合要求，请参考模板"));
            } else {
                for (int i = 0; i < cells.length; i++) {
                    if (null != cells[i]) {
                        cells[i] = cells[i].trim();
                    }
                }
                if (spOperator) {
                    cells[2] = "40044";
                }
                fillInMap(line.getLineNo(), cells, dtoMap, lineKeys, results, shiroUser);
            }
        });
        return dtoMap;
    }


    private void fillInMap(int lineNo, String[] cells, Map<Integer, ErrorCodeDTO> dtoMap, Set<Object> lineKeys, List<ImportFailResult> results, ShiroRealm.ShiroUser shiroUser) {
        ImportFailResult blankCells = checkBlankCell(lineNo, cells);
        if (null != blankCells) {
            results.add(blankCells);
        } else {
            ErrorCodeTypeEnum type = ErrorCodeTypeEnum.parse(cells[0]);
            if (null != type) {
                try {
                    checkUserPerm(true, type, cells[1], shiroUser);
                    String key = cells[0] + cells[2] + cells[3];
                    if (lineKeys.add(key)) {
                        ErrorCodeDTO dto = new ErrorCodeDTO();
                        dto.setType(type);
                        dto.setApiGroupCode(cells[1]);
                        dto.setErrorCode(cells[2]);
                        dto.setSubErrorCode(cells[3]);
                        dto.setSubErrorMsg(cells[4]);
                        dtoMap.put(lineNo, dto);
                    } else {
                        results.add(new ImportFailResult(lineNo, "重复行"));
                    }
                } catch (Exception ex) {
                    results.add(new ImportFailResult(lineNo, ex.getMessage()));
                }
            } else {
                results.add(new ImportFailResult(lineNo, "type值不合法"));
            }
        }
    }

    //type,apiGroup,errorCode,subErrorCode,subErrorMsg
    private ImportFailResult checkBlankCell(int lineNo, String[] cells) {
        List<Integer> cellIndex = new ArrayList<>(4);
        if (StringUtils.isBlank(cells[0])) {
            cellIndex.add(0);
        } else if (StringUtils.isBlank(cells[2])) {
            cellIndex.add(2);
        } else if (StringUtils.isBlank(cells[3])) {
            cellIndex.add(3);
        }
        if (StringUtils.isBlank(cells[1])) {
            if (ErrorCodeTypeEnum.GROUP_COMMON.getValue().equals(cells[0])) {
                cellIndex.add(1);
            }
        }
        if (!cellIndex.isEmpty()) {
            String blankCells = cellIndex.stream().map(i -> ERROR_CODE_IMPORT_HEADER[i]).collect(Collectors.joining(","));
            return new ImportFailResult(lineNo, blankCells + " 不能为空");
        }
        return null;
    }

    private class CsvLine {
        private int lineNo;
        private String lineText;

        public CsvLine(int lineNo, String lineText) {
            this.lineNo = lineNo;
            this.lineText = lineText;
        }

        public int getLineNo() {
            return lineNo;
        }

        public void setLineNo(int lineNo) {
            this.lineNo = lineNo;
        }

        public String getLineText() {
            return lineText;
        }

        public void setLineText(String lineText) {
            this.lineText = lineText;
        }
    }

    private class ImportFailResult implements Serializable, Comparable<ImportFailResult> {
        private static final long serialVersionUID = -1L;

        private int lineNo;
        private String reason;

        public int getLineNo() {
            return lineNo;
        }

        public void setLineNo(int lineNo) {
            this.lineNo = lineNo;
        }

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        public ImportFailResult(int lineNo, String reason) {
            this.lineNo = lineNo;
            this.reason = reason;
        }

        @Override
        public int compareTo(ImportFailResult that) {
            return this.lineNo - that.lineNo;
        }
    }

    @ResponseBody
    @RequestMapping(value = "api/list", method = RequestMethod.GET)
    public ResponseMessage relatedApiList(ErrcodeApiPageQueryParam pageQueryParam) {
        PageQueryResult<ErrcodeApiPageItem> errcodeApis = apiErrcodeService.findRelatedApisPage(pageQueryParam);
        return new ResponseMessage("page", errcodeApis);
    }

    @ResponseBody
    @RequestMapping(value = "api/batch-create", method = RequestMethod.POST)
    public ResponseMessage batchCreateRelatedApi(@RequestBody ErrcodeApiBatchAddDTO batchAdd) {
        apiErrcodeFacade.batchCreate(batchAdd.getApiIds().stream().map(apiId -> {
            final ApiErrcodeDTO apiErrcode = new ApiErrcodeDTO();
            apiErrcode.setApiId(apiId);
            apiErrcode.setErrcodeId(batchAdd.getErrcodeId());
            return apiErrcode;
        }).collect(Collectors.toList()));
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "api/batch-delete", method = RequestMethod.POST)
    public ResponseMessage batchDeleteRelatedApi(@RequestBody BatchDeleteRelatedApi relatedApi) {
        if (null != relatedApi && CollectionUtils.isNotEmpty(relatedApi.getIds())) {
            apiErrcodeFacade.batchDelete(relatedApi.getIds());
        }
        return new ResponseMessage();
    }

    private static class BatchDeleteRelatedApi {
        private List<Long> ids;

        public List<Long> getIds() {
            return ids;
        }

        public void setIds(List<Long> ids) {
            this.ids = ids;
        }
    }

}
