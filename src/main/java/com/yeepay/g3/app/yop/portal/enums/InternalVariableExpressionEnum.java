package com.yeepay.g3.app.yop.portal.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/9 16:04
 */
public enum InternalVariableExpressionEnum {
    CONTEXT_APPKEY("#context.appkey#", "应用标识appKey"),
    CONTEXT_CUSTOMERNO("#context.customerNo#", "商户编号customerNo"),
    CONTEXT_OAUTH2_USERID("#context.oauth2.userid#", "oauth2_token中还原出的userid"),
    HEADER_X("#header.X#", "平台内置报文头"),
    HEADER_REQUESTID("#header.requestId#", "唯一请求标识"),
    HEADER_DATE("#header.date#", "请求发起时间"),
    HEADER_REQUESTIP("#header.requestIp#", "调用发起者IP"),
    HEADER_REQUESTSOURCE("#header.requestSource#", "调用来源"),
    HEADER_META_X("#header.meta.X#", "用户自定义报文头"),
    HEADER_CONTENT_SHA256("#header.content.sha256#", "变量x-yop-content-sha256");

    private static final Map<String, InternalVariableExpressionEnum> VALUE_MAP;

    static {
        VALUE_MAP = Maps.newHashMap();
        for (InternalVariableExpressionEnum authType : InternalVariableExpressionEnum.values()) {
            VALUE_MAP.put(authType.getValue(), authType);
        }
    }

    private String value;

    private String displayName;

    InternalVariableExpressionEnum(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static Map<String, InternalVariableExpressionEnum> getValueMap() {
        return VALUE_MAP;
    }

    public static InternalVariableExpressionEnum parse(String value) {
        return VALUE_MAP.get(value);
    }

    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }
}
