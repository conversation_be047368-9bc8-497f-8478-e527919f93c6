package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.YosStoreConfigCreate;
import com.yeepay.g3.app.yop.portal.validation.group.YosStoreConfigEdit;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

public class YosStoreConfigVO implements Serializable {

    private static final long serialVersionUID = -1;

    @NotNull(message = "{yosStoreConfig.id}", groups = YosStoreConfigEdit.class)
    private Long id;

    @NotNull(message = "{yosStoreConfig.apiGroupCode}", groups = YosStoreConfigCreate.class)
    private String apiGroupCode;

    @NotNull(message = "{yosStoreConfig.storeType}", groups = YosStoreConfigCreate.class)
    private String storeType;

    @NotNull(message = "{yosStoreConfig.secretKey}", groups = YosStoreConfigCreate.class)
    private String secretKey;

    @NotNull(message = "{yosStoreConfig.bucket}", groups = YosStoreConfigCreate.class)
    private String bucket;

    @NotNull(message = "{yosStoreConfig.fileName}", groups = YosStoreConfigCreate.class)
    private String fileName;

    private StoreConfigVO storeConfig;

    @NotNull(message = "{yosStoreConfig.isDefault}", groups = YosStoreConfigCreate.class)
    private Boolean isDefault;

    @NotNull(message = "{yosStoreConfig.version}", groups = YosStoreConfigEdit.class)
    private Long version;

    private Date createdDate;

    private Date lastModifiedDate;

    private Boolean force;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public String getStoreType() {
        return storeType;
    }

    public void setStoreType(String storeType) {
        this.storeType = storeType;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public StoreConfigVO getStoreConfig() {
        return storeConfig;
    }

    public void setStoreConfig(StoreConfigVO storeConfig) {
        this.storeConfig = storeConfig;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public Boolean getForce() {
        return force;
    }

    public void setForce(Boolean force) {
        this.force = force;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
