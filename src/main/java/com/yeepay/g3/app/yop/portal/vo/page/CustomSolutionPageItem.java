/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * title: 自定义解决方案-分页item<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/19
 */
@Data
public class CustomSolutionPageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private String solutionCode;

    private String solutionName;

    private String operatorCode;

    private String description;

    private String spCode;

    private String spName;

    private Long version;

    private Date createdDate;

    private Date lastModifiedDate;

    private boolean hasDoc;
}
