/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.cache;


import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeepay.g3.facade.yop.perm.dto.AclAuditDTO;
import com.yeepay.g3.facade.yop.perm.dto.AclAuditStageDTO;
import com.yeepay.g3.facade.yop.perm.facade.AclAuditFacade;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * title:根据资源id查询其最大步骤的审核角色及资源（操作需要审核的资源时，需要判断是否是最后一步审核） <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-28 14:46
 */
@Component("aclAuditMaxStageCache")
public class AclAuditMaxStageCache {

    private static final Logger LOGGER = LoggerFactory.getLogger(AclAuditMaxStageCache.class);

    private AclAuditFacade aclAuditFacade = RemoteServiceFactory.getService(AclAuditFacade.class);

    public LoadingCache<Long, AclAuditStageDTO> aclAuditCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(121, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, AclAuditStageDTO>() {
                @Override
                public AclAuditStageDTO load(Long key) throws Exception {
                    try {
                        AclAuditStageDTO stageDTO = new AclAuditStageDTO();
                        AclAuditDTO aclAuditDTO = aclAuditFacade.findAudit(key);
                        Map<Integer, AclAuditStageDTO> stageDTOMap = new HashMap<>();
                        if (aclAuditDTO != null && CollectionUtils.isNotEmpty(aclAuditDTO.getAclAuditStages())) {
                            int stage = 1;
                            stageDTO = aclAuditDTO.getAclAuditStages().get(0);
                            for (AclAuditStageDTO aclAuditStageDTO : aclAuditDTO.getAclAuditStages()) {
                                if (aclAuditStageDTO.getStage() > stage) {
                                    stageDTO = aclAuditStageDTO;
                                }
                            }
                        }
                        return stageDTO;
                    } catch (Exception e) {
                        LOGGER.error("load aclAuditDTO by resourceId failed.", e);
                        return null;
                    }
                }
            });

}
