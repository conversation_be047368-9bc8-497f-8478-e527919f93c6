/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import lombok.Data;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/7 17:55
 */
@Data
public class SpiSubscribePageItem implements Serializable {
    private static final long serialVersionUID = -1L;
    private Long id;
    private String customerNo;
    private String appId;
    private String spiName;
    private String url;
}
