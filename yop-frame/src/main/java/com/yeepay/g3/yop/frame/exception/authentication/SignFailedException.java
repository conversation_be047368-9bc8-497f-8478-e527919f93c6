package com.yeepay.g3.yop.frame.exception.authentication;

import com.yeepay.g3.yop.frame.CharacterConstants;

/**
 * title: 签名失败异常<br/>
 * description: isv.authentication.digest.signature-failure<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/8/8 下午6:14
 */
public class SignFailedException extends SignException {

    private static final long serialVersionUID = -1L;

    @Override
    public String getCode() {
        return super.getCode() + CharacterConstants.DOT + "signature-failure";
    }
}
