package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.perm.enums.ResourceStatusEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * title:用户拥有权限的资源-按钮 <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2016<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/7/23 15:52
 */
public class AclButtonVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private String url;

    private ResourceStatusEnum status;

    private Boolean needAudit;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public ResourceStatusEnum getStatus() {
        return status;
    }

    public void setStatus(ResourceStatusEnum status) {
        this.status = status;
    }

    public Boolean getNeedAudit() {
        return needAudit;
    }

    public void setNeedAudit(Boolean needAudit) {
        this.needAudit = needAudit;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
