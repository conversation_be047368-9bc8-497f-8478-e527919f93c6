package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.doc.enums.v2.DocStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-02-22 17:12
 */
@Data
public class DocArrangeVo implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;
    private String docNo;
    private String title;
    private DocStatusEnum status;
    private Integer seq;
}
