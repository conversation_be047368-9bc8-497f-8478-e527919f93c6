/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.service.TenantService;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/10/20 16:40
 */
@Component
@Slf4j
public class TenantServiceImpl implements TenantService {
    @Override
    public List<String> getProviderCodesByTenantCodes(Set<String> tenantCodes) {
        if (CollectionUtils.isEmpty(tenantCodes)) {
            return Collections.emptyList();
        }
        List<String> list = new ArrayList<>();
        //查租户codes支持的服务提供方codes
        Map<String, String> configParam = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_CUSTOMER_PROVIDER_CODES_TENANT_CODES);
        for (Map.Entry<String, String> entry : configParam.entrySet()) {
            for (String tenantCode : tenantCodes) {
                if (entry.getValue().equals(tenantCode)) {
                    String providerCode = entry.getKey();
                    list.add(providerCode);
                }
            }
        }
        return list;
    }
}
