package com.yeepay.g3.app.yop.portal.filter;

import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.catalina.connector.RequestFacade;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URL;
import java.util.Map;

@Component
public class OptionsFilter implements Filter {

    private static final Logger LOGGER = LoggerFactory.getLogger(OptionsFilter.class);

    private static final String OPTIONS = "OPTIONS";

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void destroy() {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) throws IOException, ServletException {
        // 特殊处理预检请求
        HttpServletResponse res = WebUtils.toHttp(response);
        if (ConfigUtils.isProductionMode()) {
            Map<String, String> configMap = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_STATIC_RESOURCE_CONFIG);
            String baseUrl = configMap.get("host");
            URL url = new URL(baseUrl);
            String protocol = url.getProtocol();
            String host = url.getHost();
            int port = url.getPort();
            if (port != -1) {
                baseUrl = protocol + "://" + host + ":" + port;
            } else {
                baseUrl = protocol + "://" + host;
            }
            res.setHeader("Access-Control-Allow-Origin", baseUrl);
        } else {
            res.setHeader("Access-Control-Allow-Origin", "*");
        }
        res.setHeader("Access-Control-Allow-Methods", "POST,GET");
        res.setHeader("Access-Control-Allow-Headers", "authorization,content-type,x-yop-appkey,x-requested-with,x-yop-cause,yuiassotoken,login_user,token");

        HttpServletRequest req = WebUtils.toHttp(request);
        if (StringUtils.equals(req.getMethod(), OPTIONS)) {
            return;
        }
        LOGGER.info("uri:{}", ((RequestFacade) request).getRequestURI());
        if ("/yop/cas".equals(((RequestFacade) request).getRequestURI())) {
            System.out.println();
        }
        chain.doFilter(request, response);
    }
}
