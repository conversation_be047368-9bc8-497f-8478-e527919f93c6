/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import cn.hutool.core.thread.NamedThreadFactory;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.controller.AttachmentController;
import com.yeepay.g3.app.yop.portal.fileupload.FileUploadFactory;
import com.yeepay.g3.app.yop.portal.fileupload.FileUploadStrategy;
import com.yeepay.g3.app.yop.portal.service.AttachmentService;
import com.yeepay.g3.app.yop.portal.utils.Constants;
import com.yeepay.g3.app.yop.portal.utils.RuntimeUtils;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.utils.storage.RemoteStorageConfig;
import com.yeepay.g3.app.yop.portal.utils.storage.RemoteStorageUtils;
import com.yeepay.g3.app.yop.portal.vo.AttachmentReqVo;
import com.yeepay.g3.core.yop.utils.Encodes;
import com.yeepay.g3.core.yop.utils.concurrent.sequence.SnowflakeIdWorker;
import com.yeepay.g3.core.yop.utils.concurrent.sequence.UUIDExt;
import com.yeepay.g3.facade.yop.doc.dto.AttachmentDTO;
import com.yeepay.g3.facade.yop.doc.dto.AttachmentUploadRequest;
import com.yeepay.g3.facade.yop.doc.enums.StoreTypeEnum;
import com.yeepay.g3.facade.yop.doc.facade.AttachmentFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static com.yeepay.g3.app.yop.portal.utils.Constants.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/15 11:09
 */
@Component
public class AttachmentServiceImpl implements AttachmentService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AttachmentController.class);
    private static final JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();
    private static final String EXT_SEPERATOR = ".";
    private static final int MAX_FILE_NAME_LENGTH = 30;
    private static final ExecutorService CLEAN_TMP_FILE_EXECUTOR = Executors.newSingleThreadExecutor(new NamedThreadFactory("cleanTmpFile-", false));
    private AttachmentFacade attachmentFacade = RemoteServiceFactory.getService(AttachmentFacade.class);

    @Override
    public String create(Map<String, String> cephConfig, AttachmentUploadRequest uploadRequest, AttachmentReqVo reqVo, boolean persis, String strategyName) throws IOException {
        validate(reqVo, false);
        AttachmentDTO attachmentDTO = parseFileInfo(reqVo, false);
        cephConfig = getCephConfig();
        uploadRequest = toUploadRequest(cephConfig, reqVo, attachmentDTO, strategyName);
        if (persis) {
            attachmentFacade.save(uploadRequest);
        }
        return attachmentDTO.getFileId();
    }

    @Override
    public void update(Map<String, String> cephConfig, AttachmentUploadRequest uploadRequest, AttachmentReqVo reqVo, boolean persis, String strategyName) throws IOException {
        validate(reqVo, true);
        if (StringUtils.isNotBlank(reqVo.getFileName()) || null != reqVo.getFileStream()) {
            AttachmentDTO attachmentDTO = parseFileInfo(reqVo, true);
            cephConfig = getCephConfig();
            uploadRequest = toUploadRequest(cephConfig, reqVo, attachmentDTO, strategyName);
            if (persis) {
                attachmentFacade.update(uploadRequest);
            }
        }
    }

    @Override
    public void deleteCephFile(Map<String, String> cephConfig, AttachmentUploadRequest uploadRequest) {
        try {
            if (null != cephConfig && null != uploadRequest && null != uploadRequest.getValue()) {
                String storePath = uploadRequest.getValue().getStorePath();
                if (StringUtils.isNotBlank(storePath)) {
                    final RemoteStorageConfig storageConfig = getStorageConfig(cephConfig);
                    RemoteStorageUtils.delete(storageConfig, convertAttachmentStorePathToFileKey(storageConfig, storePath));
                }
            }
        } catch (Exception ex) {
            //log by CephUtil
        }
    }

    private String convertAttachmentStorePathToFileKey(RemoteStorageConfig storageConfig, String storePath) {
        final String bucketPrefix = storageConfig.getBucket() + "/";
        if (storePath.startsWith(bucketPrefix)) {
            return StringUtils.substringAfter(storePath, bucketPrefix);
        }
        return storePath;
    }

    private String convertFileKeyToAttachmentStorePath(RemoteStorageConfig storageConfig, String fileKey) {
        final String bucketPrefix = storageConfig.getBucket() + "/";
        return bucketPrefix + RemoteStorageUtils.getRealFileKey(storageConfig, fileKey);
    }

    private RemoteStorageConfig getStorageConfig(Map<String, String> cephConfig) {
        return RemoteStorageConfig.builder()
                .serverUrl(cephConfig.get(Constants.CEPH_YCS_URL))
                .secretKey(cephConfig.get(Constants.CEPH_TOKEN))
                .accessKey(cephConfig.get(Constants.CEPH_ACCESS_KEY))
                .bucket(cephConfig.get(Constants.CEPH_BUCKET_NAME))
                .subBucket(cephConfig.get(Constants.CEPH_SUB_BUCKET_NAME))
                .cannedAcl(cephConfig.getOrDefault(Constants.CEPH_BUCKET_ACL, Constants.CEPH_BUCKET_DEFAULT_ACL))
                .build();
    }


    private void validate(AttachmentReqVo reqVo, boolean isUpdate) {
        CheckUtils.notNull(reqVo, "reqVo");
        if (isUpdate) {
            CheckUtils.notNull(reqVo.getId(), "id");
        } else {
            CheckUtils.notNull(reqVo.getFileStream(), "fileStream");
        }
        if (null != reqVo.getFileStream()) {
            Map<String, String> baseConfig = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_ATTACHMENT_BASE_CONFIG);
            validateFileSize(reqVo.getFileStream().getSize(), parseSize(baseConfig.getOrDefault(Constants.ATTACHMENT_MAX_FILE_SIZE, "2MB")));
        }
    }

    private void validateFileSize(long uploadSize, long supportSize) {
        if (uploadSize > supportSize) {
            throw new IllegalArgumentException("file size must no more than " + supportSize + "!!!");
        }
    }

    private long parseSize(String maxFileSize) {
        maxFileSize = maxFileSize.toUpperCase(Locale.ENGLISH);
        if (maxFileSize.endsWith("KB")) {
            return Long.valueOf(maxFileSize.substring(0, maxFileSize.length() - 2)) * 1024L;
        } else {
            return maxFileSize.endsWith("MB") ? Long.valueOf(maxFileSize.substring(0, maxFileSize.length() - 2)) * 1024L * 1024L : Long.valueOf(maxFileSize);
        }
    }


    private AttachmentDTO parseFileInfo(AttachmentReqVo reqVo, boolean isUpdate) {
        AttachmentDTO attachmentDTO = new AttachmentDTO();
        if (null != reqVo.getFileStream()) {
            Map<String, String> fileTypeConfig = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_ATTACHMENT_EXT_CONFIG);
            String origFileName = parseFileName(reqVo), extName = parseExtName(origFileName), fileType = parseFileType(extName, fileTypeConfig.get(Constants.ATTACHMENT_SUPPORTED_TYPES));
            attachmentDTO.setFileName(origFileName);
            attachmentDTO.setFileType(fileType);
        } else {
            attachmentDTO.setFileName(reqVo.getFileName());
        }
        if (isUpdate) {
            AttachmentDTO oldFileDTO = attachmentFacade.findById(reqVo.getId());
            attachmentDTO.setId(oldFileDTO.getId());
            attachmentDTO.setBizCode(oldFileDTO.getBizCode());
            attachmentDTO.setFileId(oldFileDTO.getFileId());
            attachmentDTO.setStoreType(oldFileDTO.getStoreType());
            attachmentDTO.setFileType(oldFileDTO.getFileType());
            attachmentDTO.setVersion(oldFileDTO.getVersion());
            if (StringUtils.isNotBlank(attachmentDTO.getFileName()) && !attachmentDTO.getFileName().contains(EXT_SEPERATOR)) {
                attachmentDTO.setFileName(attachmentDTO.getFileName() + parseExtName(oldFileDTO.getFileName()));
            }
            if (null == reqVo.getFileStream()) {
                attachmentDTO.setStorePath(oldFileDTO.getStorePath());
            }
        } else {
            attachmentDTO.setFileId(getFileId());
            attachmentDTO.setBizCode(reqVo.getBizCode());
            attachmentDTO.setStoreType(StoreTypeEnum.CEPH.name());
            attachmentDTO.setVersion(0L);
        }
        return attachmentDTO;
    }

    private String getFileId() {
        SnowflakeIdWorker idWorker = new SnowflakeIdWorker();
        long next = idWorker.next();
        return Encodes.encodeBase62(next);
    }


    private Map<String, String> getCephConfig() {
        return (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CEPH_CONFIG);
    }


    private AttachmentUploadRequest toUploadRequest(Map<String, String> cephConfig, AttachmentReqVo reqVo, AttachmentDTO attachmentDTO, String strategyName) throws IOException {
        AttachmentUploadRequest uploadRequest = new AttachmentUploadRequest();
        if (null != reqVo.getFileStream()) {
            FileUploadStrategy strategy = FileUploadFactory.get(strategyName);
            if (Objects.isNull(strategy)) {
                LOGGER.info("file upload strategyName: {} not found, use default strategy", strategyName);
                strategy = FileUploadFactory.get("general");
            }
            LOGGER.info("file upload strategyName: {}", strategyName);
            String fileName = strategy.resolveFileName(attachmentDTO.getBizCode(), attachmentDTO.getFileId(), parseExtName(attachmentDTO.getFileName()));
            attachmentDTO.setStorePath(uploadToCeph(cephConfig, fileName, reqVo.getFileStream().getSize(), reqVo.getFileStream().getInputStream()));
        }
        uploadRequest.setOperator(ShiroUtils.getShiroUser().getUsername());
        uploadRequest.setValue(attachmentDTO);
        return uploadRequest;
    }

    private String parseExtName(String origName) {
        return EXT_SEPERATOR + StringUtils.substringAfterLast(origName, EXT_SEPERATOR);
    }


    private String uploadToCeph(Map<String, String> cephConfig, String fileName, long fileSize, InputStream inputStream) {
        try {
            String fileExt = StringUtils.substringAfterLast(fileName, EXT_SEPERATOR),
                    srcFilePath = COMPRESS_WORK_DIR + PATH_SEPARATOR + UUIDExt.randomV4UUID() + EXT_SEPERATOR + fileExt,
                    targetFilePath = COMPRESS_WORK_DIR + PATH_SEPARATOR + UUIDExt.randomV4UUID() + EXT_SEPERATOR + fileExt;
            boolean compressed = compressIfNecessary(COMPRESS_WORK_DIR, srcFilePath, targetFilePath, fileSize, fileExt, inputStream);
            if (compressed) {
                final byte[] srcFileBytes = Files.readAllBytes(Paths.get(srcFilePath)),
                        targetFileBytes = Files.readAllBytes(Paths.get(targetFilePath));
                if (srcFileBytes.length > targetFileBytes.length) {
                    LOGGER.info("success to compress file, {}->{}", srcFileBytes.length, targetFileBytes.length);
                    fileSize = targetFileBytes.length;
                    inputStream = new ByteArrayInputStream(targetFileBytes);
                } else {
                    inputStream = new ByteArrayInputStream(srcFileBytes);
                }
                CLEAN_TMP_FILE_EXECUTOR.submit(new AttachmentServiceImpl.CleanFileTask(Arrays.asList(srcFilePath, targetFilePath)));
            }
        } catch (Exception e) {
            LOGGER.error("error to compress file, ex:", e);
        }
        final RemoteStorageConfig storageConfig = getStorageConfig(cephConfig);
        RemoteStorageUtils.upload(storageConfig, fileName, inputStream);
        return convertFileKeyToAttachmentStorePath(storageConfig, fileName);

    }

    private boolean compressIfNecessary(String workDirPath, String srcFilePath, String targetFilePath, long originFileSize, String originFileExt, InputStream originInputStream) throws Exception {
        final Map<String, Object> compressConfig = (Map<String, Object>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_IMG_COMPRESS_CONFIG);
        if (MapUtils.isNotEmpty(compressConfig) && Boolean.valueOf((String) compressConfig.get(COMPRESS_SWITCH))) {
            String supportedTypes = (String) compressConfig.get(COMPRESS_SUPPORTED_TYPES);
            Long compressThreshold = Long.valueOf((String) compressConfig.get(COMPRESS_THRESHOLD));
            if (StringUtils.isNotBlank(supportedTypes) && null != compressThreshold && originFileSize > compressThreshold) {
                String foundSupportedType = null;
                //格式："png,jpg|jpeg"
                for (String type : StringUtils.split(supportedTypes, COMPRESS_SUPPORTED_TYPES_SEPERATOR)) {
                    if (originFileExt.equalsIgnoreCase(type)) {
                        foundSupportedType = type;
                        break;
                    } else if (StringUtils.contains(type, COMPRESS_SUPPORTED_TYPES_ALIAS_SEPERATOR)) {
                        for (String typeAlias : StringUtils.split(type, COMPRESS_SUPPORTED_TYPES_ALIAS_SEPERATOR)) {
                            if (originFileExt.equalsIgnoreCase(typeAlias)) {
                                foundSupportedType = type;
                                break;
                            }
                        }
                    }
                }
                if (null != foundSupportedType) {
                    String compressCmd = (String) compressConfig.get(COMPRESS_CMD_PREFIX + foundSupportedType);
                    if (StringUtils.isNotBlank(compressCmd)) {
                        final File workDir = new File(workDirPath);
                        if (!workDir.exists()) {
                            workDir.mkdirs();
                        }
                        Files.copy(originInputStream, Paths.get(srcFilePath));
                        compressCmd = compressCmd.replace(COMPRESS_SRC_FILE_VARIABLE, srcFilePath).replace(COMPRESS_TARGET_FILE_VARIABLE, targetFilePath);
                        StopWatch watch = new StopWatch();
                        try {
                            watch.start();
                            final String execResult = RuntimeUtils.exec(compressCmd, workDir);
                            if (StringUtils.isNotBlank(execResult)) {
                                LOGGER.info("fail to compress img, execResult:{}", execResult);
                            }
                            return new File(targetFilePath).exists();
                        } finally {
                            watch.stop();
                            LOGGER.info("compress file, elapsedTime:{}ms.", watch.getTime(TimeUnit.MILLISECONDS));
                        }
                    }
                }
            }
        }
        return false;
    }

    private String parseFileName(AttachmentReqVo reqVo) {
        String origName = reqVo.getFileStream().getOriginalFilename();
        if (StringUtils.isBlank(reqVo.getFileName())) {
            reqVo.setFileName(origName);
        }
        String realExt = parseExtName(origName);
        if (!reqVo.getFileName().endsWith(realExt)) {
            reqVo.setFileName(reqVo.getFileName() + realExt);
        }
        if ((reqVo.getFileName().length() - realExt.length()) > MAX_FILE_NAME_LENGTH) {
            throw new IllegalArgumentException("fileName should be less than " + MAX_FILE_NAME_LENGTH + " chars.");
        }
        return reqVo.getFileName();
    }

    private String parseFileType(String extName, String supportedTypes) {
        try {
            if (StringUtils.isNotBlank(supportedTypes)) {
                List array = JSON_MAPPER.fromJson(supportedTypes, List.class);
                for (int i = 0; i < array.size(); i++) {
                    Map type = (Map) array.get(i);
                    List formats = (List) type.get("formats");
                    for (int j = 0; j < formats.size(); j++) {
                        String supportExt = (String) formats.get(j);
                        if (!supportExt.startsWith(EXT_SEPERATOR)) {
                            supportExt = EXT_SEPERATOR + supportExt;
                        }
                        if (supportExt.equals(extName)) {
                            return type.get("code").toString();
                        }
                    }
                }
            }
        } catch (Exception ex) {
            LOGGER.warn("wrong config str for supportedTypes.", ex);
        }
        throw new IllegalArgumentException("file type " + extName + " is not supported!!!");
    }

    private class CleanFileTask implements Runnable {
        List<String> files;

        public CleanFileTask(List<String> files) {
            this.files = files;
        }

        @Override
        public void run() {
            try {
                if (CollectionUtils.isNotEmpty(files)) {
                    for (String file : files) {
                        Files.deleteIfExists(Paths.get(file));
                    }
                }
            } catch (Exception e) {
                LOGGER.error("error to clean tmp files, ex:", e);
            }
        }
    }

}
