package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;

/**
 * title: 基于 RouteId的鉴权拦截器<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/11/30 18:39
 */
@Component
public class RouteIdBasedAuthorizationFilter extends CacheAbstractAuthorizationFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(RouteIdBasedAuthorizationFilter.class);

    // 根据routeId获得spCode
    @Override
    protected String[] getSpCodes(String[] params) {
        String[] spCodes;
        if (params == null || params.length < 1) {
            spCodes = null;
        } else {
            spCodes = new String[params.length];
            try {
                for (int i = 0; i < params.length; i++) {
                    spCodes[i] = routeLocalCache.get(Long.valueOf(params[i]));
                }
            } catch (ExecutionException e) {
                LOGGER.info("params are invalid " + e);
            }
            LOGGER.debug("spCodes by routeId:{}", spCodes);
        }
        return spCodes;
    }

    @Override
    public String shiroName() {
        return "route_id_based";
    }
}
