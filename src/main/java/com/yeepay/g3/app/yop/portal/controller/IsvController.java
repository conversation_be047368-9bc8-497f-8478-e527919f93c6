/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.CustomerChangeQueryService;
import com.yeepay.g3.app.yop.portal.service.CustomerQueryService;
import com.yeepay.g3.app.yop.portal.service.TenantService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.validation.group.CustomerCreate;
import com.yeepay.g3.app.yop.portal.validation.group.CustomerEdit;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.CustomerVO;
import com.yeepay.g3.app.yop.portal.vo.page.CustomerChangePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.CustomerPageQueryParam;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.enums.CustomerOperateTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.CustomerStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.CustomerTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.CustomerFacade;
import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/18 下午2:58
 */
@Controller
@RequestMapping("/rest/isv")
public class IsvController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppController.class);

    private CustomerFacade customerFacade = RemoteServiceFactory.getService(CustomerFacade.class);

    @Autowired
    private CustomerQueryService customerQueryService;

    @Autowired
    private CustomerChangeQueryService customerChangeQueryService;

    @Autowired
    private TenantService tenantService;

    @ResponseBody
    @RequestMapping(value = "/exists", method = RequestMethod.GET)
    public ResponseMessage exists(@RequestParam(value = "customerNo") String customerNo) {
        try {
            CustomerDTO customer = customerFacade.find(customerNo);
            return new ResponseMessage("result", null != customer);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when exists customerNo[" + customerNo + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/generate-no", method = RequestMethod.GET)
    public ResponseMessage generateNo() {
        try {
            return new ResponseMessage("result", System.currentTimeMillis() + RandomStringUtils.randomNumeric(3));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when generate customerNo", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody @Validated(CustomerCreate.class) CustomerVO customerVO) {
        try {
            CustomerDTO customerDTO = new CustomerDTO();
            BeanUtils.copyProperties(customerVO, customerDTO);
            customerDTO.setStatus(CustomerStatusEnum.VERIFIED);
            CustomerCreateRequest createRequest = new CustomerCreateRequest(ShiroUtils.getOperatorCode(), StringUtils.defaultIfEmpty(customerVO.getCause(), "通过运营后台操作"));
            createRequest.setCustomer(customerDTO);
            customerFacade.create(createRequest);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when customer product with param:" + customerVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@Validated CustomerPageQueryParam param,
                                @RequestParam(value = "_pageNo", required = false) Integer _pageNo) {
        param.setPageNo(_pageNo);
        Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
        Long appPageSize = pageSizeMap.get("isv");
        param.setPageSize(appPageSize.intValue());
        if (ShiroUtils.isPlatformOperator()) {
            return new ResponseMessage("page", customerQueryService.pageQuery(param));
        } else {
            //如果是sp用户，按租户查询数据
            List<String> providerCodes = tenantService.getProviderCodesByTenantCodes(ShiroUtils.getShiroUser().getTenantScopes());
            if (CollectionUtils.isEmpty(providerCodes)) {
                return new ResponseMessage("page", null);
            }
            return new ResponseMessage("page", customerQueryService.pageQueryForSp(param, providerCodes));
        }
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam(value = "customerNo") String customerNo) {
        return new ResponseMessage("result", customerFacade.find(customerNo));
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody @Validated(CustomerEdit.class) CustomerVO customerVO) {
        CustomerDTO customerDTO = new CustomerDTO();
        BeanUtils.copyProperties(customerVO, customerDTO);
        CustomerUpdateRequest request = new CustomerUpdateRequest(ShiroUtils.getOperatorCode(), StringUtils.defaultIfEmpty(customerVO.getCause(), "通过运营后台操作"));
        request.setCustomer(customerDTO);
        customerFacade.update(request);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/frozen", method = RequestMethod.POST)
    public ResponseMessage frozen(@RequestBody @Validated(CustomerEdit.class) CustomerVO customerVO) {
        CustomerFrozenRequest request = new CustomerFrozenRequest(ShiroUtils.getOperatorCode(), customerVO.getCause());
        request.setCustomerNo(customerVO.getCustomerNo());
        customerFacade.frozen(request);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/unfrozen", method = RequestMethod.POST)
    public ResponseMessage unfrozen(@RequestBody @Validated(CustomerEdit.class) CustomerVO customerVO) {
        CustomerUnfrozenRequest request = new CustomerUnfrozenRequest(ShiroUtils.getOperatorCode(), customerVO.getCause());
        request.setCustomerNo(customerVO.getCustomerNo());
        customerFacade.unfrozen(request);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/change", method = RequestMethod.GET)
    public ResponseMessage change(@Validated CustomerChangePageQueryParam param,
                                  @RequestParam(value = "_pageNo", required = false) Integer _pageNo) {
        param.setPageNo(_pageNo);
        Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
        Long appPageSize = pageSizeMap.get("isv-changes");
        param.setPageSize(appPageSize.intValue());
        Date createdEndDate = param.getCreatedEndDate();
        if (null != createdEndDate) {
            param.setCreatedEndDate(new Date(createdEndDate.getTime() + 86400000));
        }
        if (ShiroUtils.isPlatformOperator()) {
            return new ResponseMessage("page", customerChangeQueryService.pageQuery(param));
        } else {
            //如果是sp用户，按租户查询数据
            List<String> providerCodes = tenantService.getProviderCodesByTenantCodes(ShiroUtils.getShiroUser().getTenantScopes());
            if (CollectionUtils.isEmpty(providerCodes)) {
                return new ResponseMessage("page", null);
            }
            return new ResponseMessage("page", customerChangeQueryService.pageQueryForSp(param, providerCodes));
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/operate-type", method = RequestMethod.GET)
    public ResponseMessage changeType() {
        List<CommonsVO> list = new ArrayList<>();
        CustomerOperateTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/provider-code", method = RequestMethod.GET)
    public ResponseMessage providerCode() {
        List<CommonsVO> list = new ArrayList<>();
        Map<String, String> map = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_CUSTOMER_PROVIDER_CODES);
        if (ShiroUtils.isPlatformOperator()) {
            map.forEach((key, value) -> list.add(new CommonsVO(key, value)));
            return new ResponseMessage("result", list);
        } else {
            //如果是sp用户，按租户查询数据
            List<String> providerCodes = tenantService.getProviderCodesByTenantCodes(ShiroUtils.getShiroUser().getTenantScopes());
            if (CollectionUtils.isEmpty(providerCodes)) {
                return new ResponseMessage("result", list);
            }
            providerCodes.forEach(providerCode -> list.add(new CommonsVO(providerCode, map.get(providerCode))));
            return new ResponseMessage("result", list);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/type", method = RequestMethod.GET)
    public ResponseMessage type() {
        List<CommonsVO> list = new ArrayList<>();
        CustomerTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/status", method = RequestMethod.GET)
    public ResponseMessage status() {
        List<CommonsVO> list = new ArrayList<>();
        CustomerStatusEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }
}
