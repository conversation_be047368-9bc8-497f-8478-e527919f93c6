package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.ApiOperationTypeEnum;

import java.util.Date;
import java.util.List;

/**
 * title: Api发布记录查询参数<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-02-13 14:58
 */
public class ApiPublishRecordPageQueryParam extends BasePageQueryParam {
    private static final long serialVersionUID = -1L;

    private String apiGroup;

    private List<String> apiGroupCodes;

    private String path;

    private String method;

    private ApiOperationTypeEnum opType;

    private Date createdStartDate;

    private Date createdEndDate;

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    public ApiPublishRecordPageQueryParam withApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public List<String> getApiGroupCodes() {
        return apiGroupCodes;
    }

    public void setApiGroupCodes(List<String> apiGroupCodes) {
        this.apiGroupCodes = apiGroupCodes;
    }

    public ApiPublishRecordPageQueryParam withApiGroupCodes(List<String> apiGroupCodes) {
        this.apiGroupCodes = apiGroupCodes;
        return this;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public ApiPublishRecordPageQueryParam withPath(String path) {
        this.path = path;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public ApiPublishRecordPageQueryParam withMethod(String method) {
        this.method = method;
        return this;
    }

    public ApiOperationTypeEnum getOpType() {
        return opType;
    }

    public void setOpType(ApiOperationTypeEnum opType) {
        this.opType = opType;
    }

    public ApiPublishRecordPageQueryParam withOpType(ApiOperationTypeEnum opType) {
        this.opType = opType;
        return this;
    }

    public Date getCreatedStartDate() {
        return createdStartDate;
    }

    public void setCreatedStartDate(Date createdStartDate) {
        this.createdStartDate = createdStartDate;
    }

    public ApiPublishRecordPageQueryParam withStartDate(Date startDate) {
        this.createdStartDate = startDate;
        return this;
    }

    public Date getCreatedEndDate() {
        return createdEndDate;
    }

    public void setCreatedEndDate(Date createdEndDate) {
        this.createdEndDate = createdEndDate;
    }

    public ApiPublishRecordPageQueryParam withEndDate(Date endDate) {
        this.createdEndDate = endDate;
        return this;
    }
}
