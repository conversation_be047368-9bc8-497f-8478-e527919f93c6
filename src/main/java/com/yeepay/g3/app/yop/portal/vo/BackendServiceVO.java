package com.yeepay.g3.app.yop.portal.vo;

import java.io.Serializable;

/**
 * title: BackendServiceVO<br/>
 * description: BackendServiceVO<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午7:32
 */
public class BackendServiceVO implements Serializable {

    private static final long serialVersionUID = 6318473265309553326L;

    private String name;

    private String type;

    private String basePath;

    private Long connectionTimeout;

    private Long readTimeout;

    public String getName() {
        return name;
    }

    public BackendServiceVO setName(String name) {
        this.name = name;
        return this;
    }

    public String getType() {
        return type;
    }

    public BackendServiceVO setType(String type) {
        this.type = type;
        return this;
    }

    public String getBasePath() {
        return basePath;
    }

    public BackendServiceVO setBasePath(String basePath) {
        this.basePath = basePath;
        return this;
    }

    public Long getConnectionTimeout() {
        return connectionTimeout;
    }

    public BackendServiceVO setConnectionTimeout(Long connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
        return this;
    }

    public Long getReadTimeout() {
        return readTimeout;
    }

    public BackendServiceVO setReadTimeout(Long readTimeout) {
        this.readTimeout = readTimeout;
        return this;
    }

    @Override
    public String toString() {
        return "BackendServiceVO{" +
                "name='" + name + '\'' +
                ", connectionTimeout=" + connectionTimeout +
                ", readTimeout=" + readTimeout +
                '}';
    }
}
