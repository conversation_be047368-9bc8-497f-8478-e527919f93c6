/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import com.yeepay.g3.app.yop.portal.vo.ApiRelationVO;
import com.yeepay.g3.app.yop.portal.vo.SimpleApiVO;
import com.yeepay.g3.utils.common.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/28 5:12 下午
 */
public class ApiRelationServiceTest extends SpringBootTests {
    @Autowired
    private ApiRelationService apiRelationService;

    @Test
    public void testApis() {
        List<SimpleApiVO> result = apiRelationService.apis("test", "d96891f30b4844b290349a9fd0551c4e");
        Assert.assertTrue(CollectionUtils.isNotEmpty(result));
    }

    @Test
    public void testList() {
        List<ApiRelationVO> apiRelationVOS = apiRelationService.list("f8a573822acb43079afa2d4817c53677");
        Assert.assertTrue(CollectionUtils.isNotEmpty(apiRelationVOS));
    }

    @Test
    public void testDetail() {
        ApiRelationVO detail = apiRelationService.detail(13L);
        Assert.assertTrue(null != detail);
    }
}
