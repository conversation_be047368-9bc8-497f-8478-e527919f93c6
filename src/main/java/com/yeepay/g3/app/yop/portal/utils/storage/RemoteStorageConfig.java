/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.utils.storage;

import com.amazonaws.services.s3.model.CannedAccessControlList;
import lombok.Builder;
import lombok.Getter;

/**
 * title: 远端存储配置类<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/1/30
 */
@Builder
@Getter
public class RemoteStorageConfig {

    /**
     * 桶名
     */
    private String bucket;

    /**
     * 子桶名(境外共用一个桶)
     */
    private String subBucket;

    /**
     * 服务地址(http)
     */
    private String serverUrl;

    /**
     * 访问标识
     */
    private String accessKey;

    /**
     * 访问密钥
     */
    private String secretKey;

    /**
     * 读写权限
     * @see CannedAccessControlList
     */
    private String cannedAcl;
}
