package com.yeepay.g3.app.yop.portal.vo.page;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * ApiRouteDeployRecordPageItem
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.v3.generators.java.SpringCodegen", date = "2020-11-03T07:28:20.629Z[GMT]")


public class ApiRouteDeployRecordPageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    @JsonProperty("id")
    private Long id = null;

    @JsonProperty("operator")
    private String operator = null;

    @JsonProperty("opType")
    private String opType = null;

    @JsonProperty("cause")
    private String cause = null;

    @JsonProperty("createdDate")
    private Date createdDate = null;

    public ApiRouteDeployRecordPageItem id(Long id) {
        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @Schema(description = "")

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ApiRouteDeployRecordPageItem operator(String operator) {
        this.operator = operator;
        return this;
    }

    /**
     * 操作人
     *
     * @return operator
     **/
    @Schema(description = "操作人")

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public ApiRouteDeployRecordPageItem opType(String opType) {
        this.opType = opType;
        return this;
    }

    /**
     * 操作类型
     *
     * @return opType
     **/
    @Schema(description = "操作类型")

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public ApiRouteDeployRecordPageItem cause(String cause) {
        this.cause = cause;
        return this;
    }

    /**
     * 操作原因
     *
     * @return cause
     **/
    @Schema(description = "操作原因")

    public String getCause() {
        return cause;
    }

    public void setCause(String cause) {
        this.cause = cause;
    }

    public ApiRouteDeployRecordPageItem createdDate(Date createdDate) {
        this.createdDate = createdDate;
        return this;
    }

    /**
     * 操作时间
     *
     * @return createdDate
     **/
    @Schema(description = "操作时间")

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiRouteDeployRecordPageItem apiRouteDeployRecordPageItem = (ApiRouteDeployRecordPageItem) o;
        return Objects.equals(this.id, apiRouteDeployRecordPageItem.id) &&
                Objects.equals(this.operator, apiRouteDeployRecordPageItem.operator) &&
                Objects.equals(this.opType, apiRouteDeployRecordPageItem.opType) &&
                Objects.equals(this.cause, apiRouteDeployRecordPageItem.cause) &&
                Objects.equals(this.createdDate, apiRouteDeployRecordPageItem.createdDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, operator, opType, cause, createdDate);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiRouteDeployRecordPageItem {\n");

        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    operator: ").append(toIndentedString(operator)).append("\n");
        sb.append("    opType: ").append(toIndentedString(opType)).append("\n");
        sb.append("    cause: ").append(toIndentedString(cause)).append("\n");
        sb.append("    createdDate: ").append(toIndentedString(createdDate)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
