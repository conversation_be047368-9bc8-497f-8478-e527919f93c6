/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.ProductStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.ProductTypeEnum;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Size;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/11/29 19:47
 */
public class AuditRecordPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    @Size(min = 1, max = 32)
    private String code;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date arrivedStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date arrivedEndDate;

    private String operator;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Date getArrivedStartDate() {
        return arrivedStartDate;
    }

    public void setArrivedStartDate(Date arrivedStartDate) {
        this.arrivedStartDate = arrivedStartDate;
    }

    public Date getArrivedEndDate() {
        return arrivedEndDate;
    }

    public void setArrivedEndDate(Date arrivedEndDate) {
        this.arrivedEndDate = arrivedEndDate;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
