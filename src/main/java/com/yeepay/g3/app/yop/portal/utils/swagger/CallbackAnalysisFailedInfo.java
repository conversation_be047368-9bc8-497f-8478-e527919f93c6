package com.yeepay.g3.app.yop.portal.utils.swagger;

/**
 * title: Spi分析失败信息<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-06-20 17:08
 */
public class CallbackAnalysisFailedInfo {

    private String callbackName;

    private String errorMsg;

    public String getCallbackName() {
        return callbackName;
    }

    public void setCallbackName(String callbackName) {
        this.callbackName = callbackName;
    }

    public CallbackAnalysisFailedInfo withCallbackName(String callbackName) {
        this.callbackName = callbackName;
        return this;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public CallbackAnalysisFailedInfo withErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
        return this;
    }
}
