package com.yeepay.g3.core.yop.doc.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.yeepay.g3.utils.persistence.entity.VersionableEntity;

/**
 * title: 页面负责人实体类<br>
 * description: 页面负责人信息实体<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-26
 */
@JsonIgnoreProperties({"id", "version", "createdDate", "lastModifiedDate"})
public class PageOwnerEntity extends VersionableEntity {
    private static final Long serialVersionUID = -1L;

    /**
     * 页面ID
     */
    private Long pageId;
    
    /**
     * 负责人ID
     */
    private String ownerId;
    
    /**
     * 负责人姓名
     */
    private String ownerName;

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public String getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(String ownerId) {
        this.ownerId = ownerId;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }
}