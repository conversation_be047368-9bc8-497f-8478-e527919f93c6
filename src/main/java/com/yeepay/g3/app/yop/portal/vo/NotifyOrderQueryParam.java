/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.monitor.enums.OrderStatusEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.experimental.Tolerate;

import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/9 11:56 上午
 */
@Builder
@Getter
public class NotifyOrderQueryParam implements Serializable {
    private static final long serialVersionUID = -1L;
    private String notificationId;
    private OrderStatusEnum status;
    private String customerNo;
    private String appId;
    @NonNull
    private Date notifyStartDate;
    @NonNull
    private Date notifyEndDate;
    private String spiName;

    /**
     * 调用的后端系统
     */
    private String backend;

    /**
     * 通知规则
     */
    private String notifyRule;

    /**
     * 通知地址
     */
    private String url;

    /**
     * 最近一次失败错误码
     */
    private String errorCode;

    /**
     * 操作原因
     */
    private String operCause;

    private String guid;

    @Tolerate
    public NotifyOrderQueryParam() {
    }
}
