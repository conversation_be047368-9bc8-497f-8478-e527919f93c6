package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.ErrorCodeVO2;
import com.yeepay.g3.app.yop.portal.vo.NotifyErrorSolutionVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: ErrorCodeQueryService
 * @Description:
 * @date 2018年5月11日 上午10:19:47
 */
public interface ErrorCodeQueryService {

    /**
     * 查询平台公共错误码
     *
     * @return
     */
    List<ErrorCodeVO2> listErrorCodes();

    /**
     * 结果通知的错误码解决方案列表
     */
    List<NotifyErrorSolutionVO> listNotifyErrorSolution();
}
