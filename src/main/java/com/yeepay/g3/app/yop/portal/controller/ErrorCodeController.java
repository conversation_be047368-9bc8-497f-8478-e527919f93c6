package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.ErrorCodeQueryService;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/14 11:41
 */
@Controller
@RequestMapping("/rest/error-code")
public class ErrorCodeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ErrorCodeController.class);

    @Autowired
    private ErrorCodeQueryService errorCodeQueryService;

    @ResponseBody
    @RequestMapping(value = "/commons/error-codes", method = RequestMethod.GET)
    public ResponseMessage listErrorCodes() {
        try {
            return new ResponseMessage("result", errorCodeQueryService.listErrorCodes());
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when query error codes", ex);
            return new ResponseMessage(ex);
        }
    }

}
