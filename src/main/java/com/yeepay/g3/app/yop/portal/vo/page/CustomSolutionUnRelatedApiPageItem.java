/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * title: 自定义解决方案-待关联API分页item<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/19
 */
@Data
public class CustomSolutionUnRelatedApiPageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private String apiId;

    private String apiGroupCode;

    private String apiGroupName;

    private String apiType;

    private String apiTitle;

    private String apiUri;

    private String apiStatus;

    private Date createdDate;

    private Date lastModifiedDate;
}
