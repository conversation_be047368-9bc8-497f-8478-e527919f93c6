package com.yeepay.g3.app.yop.portal.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 17:19
 */
public class ApiServletConfigVO implements Serializable {

    private static final long serialVersionUID = 6318473265309553326L;

    private String endClass;

    private String endMethod;

    private List<Map<String, String>> params = new ArrayList<>();

    private List<Map<String, String>> json = new ArrayList<>();

    public String getEndClass() {
        return endClass;
    }

    public void setEndClass(String endClass) {
        this.endClass = endClass;
    }

    public String getEndMethod() {
        return endMethod;
    }

    public void setEndMethod(String endMethod) {
        this.endMethod = endMethod;
    }

    public List<Map<String, String>> getParams() {
        return params;
    }

    public void setParams(List<Map<String, String>> params) {
        this.params = params;
    }

    public List<Map<String, String>> getJson() {
        return json;
    }

    public void setJson(List<Map<String, String>> json) {
        this.json = json;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
