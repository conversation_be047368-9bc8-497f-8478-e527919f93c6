package com.yeepay.g3.app.yop.portal.utils.page.query;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/5/24 15:40
 */
public abstract class BasePageItemConverter<Item> implements PageItemConverter<Item> {

    @Override
    public List<Item> convert(List<Map<String, Object>> paramsList) {
        List<Item> result = new ArrayList<>(paramsList.size());
        paramsList.forEach(params -> result.add(convert(params)));
        return result;
    }
}
