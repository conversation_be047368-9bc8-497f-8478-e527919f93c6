package com.yeepay.g3.app.yop.portal.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.ArrayList;
import java.util.List;

/**
 * title: api返回参数<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 11:24
 */
public class ApiResponseParamVO extends AbstractApiParamVO {

    private static final long serialVersionUID = 8348314486919783769L;

    private String dtoClassName;

    private List<ApiResponseParamVO> children;

    public String getDtoClassName() {
        return dtoClassName;
    }

    public void setDtoClassName(String dtoClassName) {
        this.dtoClassName = dtoClassName;
    }

    public List<ApiResponseParamVO> getChildren() {
        return children;
    }

    public void setChildren(List<ApiResponseParamVO> children) {
        this.children = children;
    }

    public void addChild(ApiResponseParamVO child) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
    }


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
