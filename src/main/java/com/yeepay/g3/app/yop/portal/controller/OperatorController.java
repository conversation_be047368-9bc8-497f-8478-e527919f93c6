/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import cn.hutool.core.codec.Base64;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.OperatorQueryService;
import com.yeepay.g3.app.yop.portal.utils.HttpUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.OperatorVO;
import com.yeepay.g3.app.yop.portal.vo.page.OperatorPageQueryParam;
import com.yeepay.g3.core.yop.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.yop.sys.dto.CustomerDTO;
import com.yeepay.g3.facade.yop.sys.dto.OperatorAuthDTO;
import com.yeepay.g3.facade.yop.sys.dto.OperatorDTO;
import com.yeepay.g3.facade.yop.sys.enums.IdentityTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.OperatorAuthStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.OperatorStatusEnum;
import com.yeepay.g3.facade.yop.sys.facade.CustomerFacade;
import com.yeepay.g3.facade.yop.sys.facade.OperatorFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/7/22 下午2:46
 */
@Controller
@RequestMapping("/rest/isv/oper")
public class OperatorController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OperatorController.class);

    private OperatorFacade operatorFacade = RemoteServiceFactory.getService(OperatorFacade.class);

    private CustomerFacade customerFacade = RemoteServiceFactory.getService(CustomerFacade.class);

    @Autowired
    private OperatorQueryService operatorQueryService;

    private final ObjectMapper objectMapper = JsonMapper.nonEmptyObjectMapper();

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@Validated OperatorPageQueryParam pageQueryParam,
                                @RequestParam(value = "_pageNo", required = false) Integer _pageNo) {
        pageQueryParam.setPageNo(_pageNo);
        Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
        Long appPageSize = pageSizeMap.get("isv-oper");
        pageQueryParam.setPageSize(appPageSize.intValue());
        return new ResponseMessage("page", operatorQueryService.pageQuery(pageQueryParam));
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody OperatorVO operatorVO) {
        CheckUtils.notNull(operatorVO, "operator");
        CheckUtils.notEmpty(operatorVO.getCustomerNo(), "customerNo");
        CheckUtils.notEmpty(operatorVO.getEmail(), "email");
        CheckUtils.notEmpty(operatorVO.getMobile(), "mobile");

        CustomerDTO customer = customerFacade.find(operatorVO.getCustomerNo());
        if (null == customer) {
            throw new IllegalArgumentException("商户不存在");
        }
        if ("YEEPAY".equals(customer.getProviderCode())) {
            throw new IllegalArgumentException("易宝商户不允许创建操作员");
        }

        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setCustomerNo(operatorVO.getCustomerNo());
        operatorDTO.setStatus(OperatorStatusEnum.VERIFIED);
        List<OperatorAuthDTO> auths = new ArrayList<>();
        operatorDTO.setAuths(auths);
        OperatorAuthDTO emailAuth = new OperatorAuthDTO();
        emailAuth.setIdentityType(IdentityTypeEnum.EMAIL);
        emailAuth.setIdentifier(operatorVO.getEmail());
        emailAuth.setStatus(OperatorAuthStatusEnum.VERIFIED);
        auths.add(emailAuth);
        OperatorAuthDTO mobileAuth = new OperatorAuthDTO();
        mobileAuth.setIdentityType(IdentityTypeEnum.MOBILE);
        mobileAuth.setIdentifier(operatorVO.getMobile());
        mobileAuth.setStatus(OperatorAuthStatusEnum.VERIFIED);
        auths.add(mobileAuth);
        operatorFacade.create(operatorDTO);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam(value = "id") Long id) {
        OperatorVO operatorVO = new OperatorVO();
        OperatorDTO operatorDTO = operatorFacade.find(id);
        operatorVO.setCustomerNo(operatorDTO.getCustomerNo());
        List<OperatorAuthDTO> auths = operatorDTO.getAuths();
        auths.forEach(auth -> {
            switch (auth.getIdentityType()) {
                case EMAIL:
                    operatorVO.setEmail(auth.getIdentifier());
                    break;
                case MOBILE:
                    operatorVO.setMobile(auth.getIdentifier());
                    break;
            }
        });
        return new ResponseMessage("result", operatorVO);
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody @Validated OperatorVO operatorVO) {
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setId(operatorVO.getId());
        operatorDTO.setCustomerNo(operatorVO.getCustomerNo());
        List<OperatorAuthDTO> auths = new ArrayList<>();
        operatorDTO.setAuths(auths);

        OperatorAuthDTO emailAuth = new OperatorAuthDTO();
        emailAuth.setOperatorId(operatorVO.getId());
        emailAuth.setIdentityType(IdentityTypeEnum.EMAIL);
        emailAuth.setIdentifier(operatorVO.getEmail());
        emailAuth.setStatus(OperatorAuthStatusEnum.VERIFIED);
        auths.add(emailAuth);

        OperatorAuthDTO mobileAuth = new OperatorAuthDTO();
        mobileAuth.setOperatorId(operatorVO.getId());
        mobileAuth.setIdentityType(IdentityTypeEnum.MOBILE);
        mobileAuth.setIdentifier(operatorVO.getMobile());
        mobileAuth.setStatus(OperatorAuthStatusEnum.VERIFIED);
        auths.add(mobileAuth);

        operatorFacade.update(operatorDTO);
        return new ResponseMessage();
    }


    @ResponseBody
    @RequestMapping(value = "/frozen", method = RequestMethod.POST)
    public ResponseMessage frozen(@RequestBody OperatorVO operatorVO) {
        CheckUtils.notNull(operatorVO, "operator");
        CheckUtils.notNull(operatorVO.getId(), "id");
        operatorFacade.frozen(operatorVO.getId());
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/unfrozen", method = RequestMethod.POST)
    public ResponseMessage unfrozen(@RequestBody OperatorVO operatorVO) {
        CheckUtils.notNull(operatorVO, "operator");
        CheckUtils.notNull(operatorVO.getId(), "id");
        operatorFacade.unfrozen(operatorVO.getId());
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/unlock", method = RequestMethod.POST)
    public ResponseMessage unlock(@RequestBody OperatorVO operatorVO) {
        CheckUtils.notNull(operatorVO, "operator");
        CheckUtils.notNull(operatorVO.getId(), "id");
        //operatorFacade.frozen(operatorVO.getId());
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestBody OperatorVO operatorVO) {
        CheckUtils.notNull(operatorVO, "operator");
        CheckUtils.notNull(operatorVO.getId(), "id");
        operatorFacade.delete(operatorVO.getId());
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/commons/status", method = RequestMethod.GET)
    public ResponseMessage status() {
        List<CommonsVO> list = new ArrayList<>();
        OperatorStatusEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/reset/pwd", method = RequestMethod.GET)
    public ResponseMessage resetPwd(@RequestParam(value = "operId") Long operId) {
        String developerHost = (String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_DEVELOPER_CENTER_HOST);
        String unlockUrl = developerHost + "/yop-developer-center/login/unlock";
        Map param = new HashMap<>();
        String loginName = getOperLoginName(operId);
        if (StringUtils.isBlank(loginName)) {
            throw new YeepayRuntimeException("重置密码失败，当前账号没有绑定邮箱");
        }
        param.put("loginName", loginName);
        try {
            String response = HttpUtils.getGetResponseMess(unlockUrl, param, getHeaders());
            Map map = objectMapper.readValue(response, Map.class);
            if (!map.get("code").equals("000000")) {
                LOGGER.error("fail to unlock user,id:{},code:{},message:{}", operId, map.get("code"), map.get("message"));
                throw new YeepayRuntimeException("解锁操作员失败");
            }
            operatorFacade.resetPwd(operId);
        } catch (IOException e) {
            throw new YeepayRuntimeException(e);
        }

        return new ResponseMessage();
    }

    private String getOperLoginName(Long id) {
        OperatorDTO operatorDTO = operatorFacade.find(id);
        if (null == operatorDTO) {
            throw new IllegalArgumentException("操作员不存在");
        }
        List<OperatorAuthDTO> auths = operatorDTO.getAuths();
        for (OperatorAuthDTO operatorAuthDTO : auths) {
            if (IdentityTypeEnum.EMAIL.equals(operatorAuthDTO.getIdentityType())) {
                return operatorAuthDTO.getIdentifier();
            }
        }
        return "";
    }

    private Map getHeaders() {
        String basic = (String) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_DEVELOPER_KEY_TOOLS_BASIC);
        Map<Object, Object> headers = new HashMap();
        headers.put("Authorization", "Basic " + Base64.encode(basic.getBytes()));
        return headers;
    }

}
