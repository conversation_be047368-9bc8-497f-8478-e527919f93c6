package com.yeepay.g3.app.yop.portal.shiro.authc;

import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.frame.yop.ca.utils.Encodes;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.*;

import java.util.Map;

/**
 * title: basic auth <br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/6/8 下午2:51
 */
public class BasicAuthToken implements AuthenticationToken {

    private static final long serialVersionUID = 1L;

    private static final Logger LOGGER = LoggerFactory.getLogger(BasicAuthToken.class);

    private String token;

    private String userId;

    private String password;

    public BasicAuthToken() {
        super();
    }

    public BasicAuthToken(String token) {
        this.token = token;
    }

    public BasicAuthToken(String userId, String password) {
        this.userId = userId;
        this.password = password;
    }

    public String getToken() {
        return token;
    }

    public String getUserId() {
        return userId;
    }

    public String getPassword() {
        return password;
    }

    @Override
    public Object getPrincipal() {
        return token;
    }

    @Override
    public Object getCredentials() {
        return token;
    }

    public AuthenticationInfo doGetAuthenticationInfo() throws AuthenticationException {
        String authorization = token;
        try {
            authorization = authorization.substring(6, authorization.length());
            String decodedAuth = new String(Encodes.decodeBase64(authorization));
            String[] split = StringUtils.split(decodedAuth, ":");
            String userId = split[0];
            String password = split[1];
            Map<String, String> gitClientConfig =
                    (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_GIT_SYNC_PRODUCTION_COMMIT_CONFIG);
            if (StringUtils.equals(userId, gitClientConfig.get("username")) && StringUtils.equals(password, gitClientConfig.get("password"))) {
                BasicAuthToken user = new BasicAuthToken(userId, password);
                return new SimpleAuthenticationInfo(user, userId, userId);
            } else {
                throw new UnknownAccountException("认证信息有误");
            }
        } catch (Throwable e) {
            LOGGER.error("basic auth failed, token:" + authorization, e);
            throw e;
        }
    }
}
