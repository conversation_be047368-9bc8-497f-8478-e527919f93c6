package com.yeepay.g3.app.yop.portal.utils;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;

/**
 * title: 配置文件读取 Utils<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/11/30 13:00
 */
public final class YopConfigFileUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(YopConfigFileUtils.class);

    private static final String PATH_SEPARATOR = File.separator;

    private static final String PRO_BASE_PATH = PATH_SEPARATOR + "var" + PATH_SEPARATOR + "yeepay" + PATH_SEPARATOR + "yop" + PATH_SEPARATOR;

    private YopConfigFileUtils() {
        // do nothing
    }

    /**
     * 获取文件流
     *
     * @param name 文件名
     * @return
     */
    public static InputStream getFileInputStream(String name) {
        return getFileInputStream(name, null, null);
    }

    /**
     * 获取文件流
     *
     * @param name 文件名
     * @param type 类型(不传不拼接), 比如: Pub
     * @return
     */
    public static InputStream getFileInputStream(String name, String type) {
        return getFileInputStream(name, type, null);
    }

    /**
     * 获取文件流
     *
     * @param name   文件名
     * @param type   类型(不传不拼接), 比如: Pub
     * @param suffix 文件扩展名(不传拼接.txt), 比如: crt
     * @return
     */
    public static InputStream getFileInputStream(String name, String type, String suffix) {
        String fileName = getFileName(name, type, suffix);
        InputStream is;
        if (ConfigUtils.isProductionMode()) {
            String fullPath = PRO_BASE_PATH + fileName;
            try {
                is = new FileInputStream(new File(fullPath));
            } catch (FileNotFoundException e) {
                LOGGER.error("读取接口配置文件时报错, fullPath:{}", fullPath);
                throw new RuntimeException(e);
            }
        } else {
            String fullPath = "keychain" + PATH_SEPARATOR + fileName;
            is = Thread.currentThread().getContextClassLoader().getResourceAsStream(fullPath);
        }
        return is;
    }

    /**
     * 获取文件名
     *
     * @param name   文件名
     * @param type   类型(不传不拼接), 比如: Pub
     * @param suffix 文件扩展名(不传拼接.txt), 比如: crt
     * @return 文件读取路径, 比如: /var/yeepay/yop/YOP_XXX_PUB.crt
     */
    private static String getFileName(String name, String type, String suffix) {
        String fileName = "YOP_" + name.toUpperCase();
        if (StringUtils.isNotBlank(type)) {
            fileName += "_" + type.toUpperCase();
        }
        if (StringUtils.isNotBlank(suffix)) {
            fileName += "." + suffix.toLowerCase();
        } else {
            fileName += ".txt";
        }
        return fileName;
    }

}
