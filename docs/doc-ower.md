# 页面负责人管理 REST API 文档

## 1. 获取页面负责人列表

- **接口地址**：`GET /rest/doc/page/owners`
- **请求参数**：
  - `pageId`：页面ID（Query参数）
- **响应示例**：
  ```json
  {
    "result": [
      {
        "id": "12345",
        "name": "张三",
        "assignedTime": "2025-07-01T10:00:00"
      },
      {
        "id": "67890",
        "name": "李四",
        "assignedTime": "2025-07-02T09:30:00"
      }
    ]
  }
  ```
- **curl 测试脚本**：
  ```bash
  curl -G 'http://localhost:8080/rest/doc/page/owners' --data-urlencode 'pageId=xxx'
  ```

## 2. 更新页面负责人

- **接口地址**：`POST /rest/doc/page/owners/update`
- **请求参数**：
  - 请求体：
    ```json
    {
      "pageId": "xxx",
      "ownerIds": ["yyy", "zzz"]
    }
    ```
- **响应示例**：
  ```json
  {
    "message": "负责人更新成功"
  }
  ```
- **curl 测试脚本**：
  ```bash
  curl -X POST 'http://localhost:8080/rest/doc/page/owners/update' \
    -H 'Content-Type: application/json' \
    -d '{"pageId": "xxx", "ownerIds": ["yyy", "zzz"]}'
  ```

## 3. 搜索用户（可选负责人）

- **接口地址**：`GET /rest/user/search`
- **请求参数**：
  - `keyword`：搜索关键字（Query参数，支持姓名、工号、邮箱等模糊搜索）
  - `limit`：返回数量限制（Query参数，默认10）
- **响应示例**：
  ```json
  {
    "users": [
      {
        "id": "12345",
        "name": "张三",
        "email": "<EMAIL>",
        "department": "技术部",
        "active": true
      },
      {
        "id": "67890",
        "name": "李四",
        "email": "<EMAIL>",
        "department": "产品部",
        "active": true
      }
    ]
  }
  ```
- **curl 测试脚本**：
  ```bash
  curl -G 'http://localhost:8080/rest/user/search' --data-urlencode 'keyword=张三' --data-urlencode 'limit=10'
  ```

---

### VO对象说明

#### PageOwnerVO
- `id`：负责人ID
- `name`：负责人姓名
- `assignedTime`：分配时间

#### UserVO
- `id`：用户ID
- `name`：用户姓名
- `email`：邮箱
- `department`：部门
- `active`：是否在职

如需补充接口细节或参数说明，请告知。
