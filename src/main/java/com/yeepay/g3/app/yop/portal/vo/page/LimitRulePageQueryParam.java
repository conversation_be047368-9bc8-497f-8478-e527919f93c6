/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.LimitStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.LimitTypeEnum;

import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/5/11 上午10:30
 */
public class LimitRulePageQueryParam extends BasePageQueryParam implements Serializable {

    private static final long serialVersionUID = -1;

    @Size(max = 32)
    private String customerNo;

    @Size(max = 64)
    private String appId;

    @Size(max = 84)
    private String requestPath;

    private LimitStatusEnum status;

    private LimitTypeEnum type;

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getRequestPath() {
        return requestPath;
    }

    public void setRequestPath(String requestPath) {
        this.requestPath = requestPath;
    }

    public LimitStatusEnum getStatus() {
        return status;
    }

    public void setStatus(LimitStatusEnum status) {
        this.status = status;
    }

    public LimitTypeEnum getType() {
        return type;
    }

    public void setType(LimitTypeEnum type) {
        this.type = type;
    }
}
