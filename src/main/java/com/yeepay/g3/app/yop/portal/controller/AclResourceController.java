package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.vo.AclButtonVO;
import com.yeepay.g3.app.yop.portal.vo.AclMenuRefVO;
import com.yeepay.g3.app.yop.portal.vo.OperatorResourceResultVO;
import com.yeepay.g3.core.yop.utils.bean.BeanConvertUtils;
import com.yeepay.g3.facade.yop.perm.dto.AclResourceQueryDTO;
import com.yeepay.g3.facade.yop.perm.dto.OperatorResourceResult;
import com.yeepay.g3.facade.yop.perm.facade.AclPrivilegeMgrFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * title:资源controller <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2016<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/7/23 12:48
 */
@Controller
@RequestMapping("/rest/resource")
public class AclResourceController {

    private AclPrivilegeMgrFacade aclPrivilegeMgrFacade = RemoteServiceFactory.getService(AclPrivilegeMgrFacade.class);

    @ResponseBody
    @RequestMapping(value = "/has-rights", method = RequestMethod.GET)
    public ResponseMessage queryResourceHasRights(@RequestParam(value = "operatorCode", required = false) String operatorCode) {
        if (StringUtils.isEmpty(operatorCode)) {
            operatorCode = ShiroUtils.getOperatorCode();
        }

        OperatorResourceResult resourceResult = aclPrivilegeMgrFacade.findResourceResultByOperatorCode(operatorCode);
        List<AclMenuRefVO> menus = BeanConvertUtils.convertList(resourceResult.getMenus(), AclMenuRefVO.class);
        List<AclButtonVO> buttons = BeanConvertUtils.convertList(resourceResult.getButtons(), AclButtonVO.class);
        OperatorResourceResultVO resourceResultVO = new OperatorResourceResultVO();
        resourceResultVO.setAclButtonVOS(buttons);
        resourceResultVO.setAclMenuRefVOS(menus);

        ResponseMessage responseMessage = new ResponseMessage("result", resourceResultVO);
        return responseMessage;
    }

    @ResponseBody
    @RequestMapping(value = "/menus", method = RequestMethod.GET)
    public ResponseMessage menus(@RequestParam(value = "operatorCode", required = false) String operatorCode) {
        if (StringUtils.isEmpty(operatorCode)) {
            operatorCode = ShiroUtils.getOperatorCode();
        }
        AclResourceQueryDTO params = new AclResourceQueryDTO().setOperatorCode(operatorCode);
        List<AclMenuRefVO> menus = BeanConvertUtils.convertList(aclPrivilegeMgrFacade.findResourceMenus(params), AclMenuRefVO.class);
        ResponseMessage responseMessage = new ResponseMessage("result", menus);
        return responseMessage;
    }


    @ResponseBody
    @RequestMapping(value = "/buttons", method = RequestMethod.GET)
    public ResponseMessage buttons(@RequestParam(value = "operatorCode", required = false) String operatorCode, @RequestParam Long menuId) {
        if (StringUtils.isEmpty(operatorCode)) {
            operatorCode = ShiroUtils.getOperatorCode();
        }
        AclResourceQueryDTO params = new AclResourceQueryDTO().setOperatorCode(operatorCode).setMenuId(menuId);
        List<AclButtonVO> buttons = BeanConvertUtils.convertList(aclPrivilegeMgrFacade.findResourceButtons(params), AclButtonVO.class);

        ResponseMessage responseMessage = new ResponseMessage("result", buttons);
        return responseMessage;
    }


}
