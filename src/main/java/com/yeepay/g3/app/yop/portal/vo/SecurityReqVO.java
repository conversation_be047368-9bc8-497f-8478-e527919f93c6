package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 18:10
 */
 public class SecurityReqVO implements Serializable {

    private static final long serialVersionUID = -1201907760591510217L;

    private String name;

    private List<String> custom;

    private Map<String, List<String>> requirements = new HashMap();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getCustom() {
        return custom;
    }

    public void setCustom(List<String> custom) {
        this.custom = custom;
    }

    public void addCustom(String scope) {
        if (custom == null) {
            custom = new ArrayList<>();
        }
        custom.add(scope);
    }

    @JsonAnyGetter
    public Map<String, List<String>> getRequirements() {
        return this.requirements;
    }

    @JsonAnySetter
    public void setRequirements(String name, List<String> scopes) {
        this.requirements.put(name, scopes);
    }
}
