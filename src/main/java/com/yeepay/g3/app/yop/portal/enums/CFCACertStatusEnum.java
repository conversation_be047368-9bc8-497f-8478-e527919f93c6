/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/3/25 9:31 上午
 */
public enum CFCACertStatusEnum {
    UNDOWNLOAD("3", "UNDOWNLOAD"),
    ACTIVATED("4", "ACTIVATED"),
    FROZEN("5", "FROZEN"),
    REVOKED("6", "REVOKED");

    private static final Map<String, CFCACertStatusEnum> VALUE_MAP;
    private static final Map<String, CFCACertStatusEnum> DESC_MAP;

    static {
        VALUE_MAP = new HashMap<>();
        DESC_MAP = new HashMap<>();
        for (CFCACertStatusEnum cfcaCertStatusEnum : CFCACertStatusEnum.values()) {
            VALUE_MAP.put(cfcaCertStatusEnum.getValue(), cfcaCertStatusEnum);
            DESC_MAP.put(cfcaCertStatusEnum.getDecs(), cfcaCertStatusEnum);
        }
    }

    private String value;
    private String decs;

    CFCACertStatusEnum(String value, String decs) {
        this.value = value;
        this.decs = decs;
    }

    public static CFCACertStatusEnum parse(String value) {
        return VALUE_MAP.get(value);
    }

    public static CFCACertStatusEnum get(String desc) {
        return DESC_MAP.get(desc);
    }

    public String getValue() {
        return value;
    }

    public String getDecs() {
        return decs;
    }
}
