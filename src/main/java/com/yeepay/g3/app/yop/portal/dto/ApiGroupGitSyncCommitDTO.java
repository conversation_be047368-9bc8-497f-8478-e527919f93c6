package com.yeepay.g3.app.yop.portal.dto;

import java.io.Serializable;

/**
 * title: Api分组git同步提交<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-26 14:54
 */
public class ApiGroupGitSyncCommitDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    private String apiGroup;

    private String gitRepository;

    private String gitBranch;

    private String filePath;

    private String commitId;

    private String content;

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    public ApiGroupGitSyncCommitDTO withApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getGitRepository() {
        return gitRepository;
    }

    public void setGitRepository(String gitRepository) {
        this.gitRepository = gitRepository;
    }

    public ApiGroupGitSyncCommitDTO withGitRepository(String gitRepository) {
        this.gitRepository = gitRepository;
        return this;
    }

    public String getGitBranch() {
        return gitBranch;
    }

    public void setGitBranch(String gitBranch) {
        this.gitBranch = gitBranch;
    }

    public ApiGroupGitSyncCommitDTO withGitBranch(String gitBranch) {
        this.gitBranch = gitBranch;
        return this;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public ApiGroupGitSyncCommitDTO withFilePath(String filePath) {
        this.filePath = filePath;
        return this;
    }

    public String getCommitId() {
        return commitId;
    }

    public void setCommitId(String commitId) {
        this.commitId = commitId;
    }

    public ApiGroupGitSyncCommitDTO withCommitId(String commitId) {
        this.commitId = commitId;
        return this;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public ApiGroupGitSyncCommitDTO withContent(String content) {
        this.content = content;
        return this;
    }
}
