/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.ProductAuthzChangesQueryService;
import com.yeepay.g3.app.yop.portal.service.ProductAuthzQueryService;
import com.yeepay.g3.app.yop.portal.service.ProductQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.ProductAuthzBathAuthVO;
import com.yeepay.g3.app.yop.portal.vo.ProductAuthzVO;
import com.yeepay.g3.app.yop.portal.vo.page.ProductAuthzChangesPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.ProductAuthzPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.ProductPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ProductPageQueryParam;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.enums.ProductAuthzOperateTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.ProductAuthzStatusEnum;
import com.yeepay.g3.facade.yop.sys.facade.ProductAuthzFacade;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/6 下午5:37
 */
@Controller
@RequestMapping("/rest/product/authz")
public class ProductAuthzController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductApiController.class);

    private ProductAuthzFacade productAuthzFacade = RemoteServiceFactory.getService(ProductAuthzFacade.class);

    @Autowired
    private ProductQueryService productQueryService;

    @Autowired
    private ProductAuthzQueryService productAuthzQueryService;
    @Autowired
    private ProductAuthzChangesQueryService productAuthzChangesQueryService;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@Validated ProductAuthzPageQueryParam param,
                                @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "_pageSize", required = false) Integer pageSize) {
        try {
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            Date overdueDateStart = null, overdueDate = null;
            Integer overdueTime = param.getOverdueTime();
            if (null != overdueTime) {
                Calendar calendar = Calendar.getInstance();
                final Date now = new Date();
                if (overdueTime > 0) {
                    calendar.setTime(now);
                    calendar.add(Calendar.DATE, overdueTime);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.SECOND, 0);
                    overdueDateStart = calendar.getTime();

                    calendar.setTime(now);
                    calendar.set(Calendar.HOUR_OF_DAY, 23);
                    calendar.set(Calendar.MINUTE, 59);
                    calendar.set(Calendar.SECOND, 59);
                    overdueDate = calendar.getTime();
                } else {
                    calendar.setTime(now);
                    calendar.add(Calendar.DATE, overdueTime);
                    calendar.set(Calendar.HOUR_OF_DAY, 0);
                    calendar.set(Calendar.MINUTE, 0);
                    calendar.set(Calendar.SECOND, 0);
                    overdueDate = calendar.getTime();
                }
            }
            param.setOverdueDateStart(overdueDateStart);
            param.setOverdueDate(overdueDate);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("product-authz");
            param.setPageSize(appPageSize.intValue());
            return new ResponseMessage("page", productAuthzQueryService.pageQuery(param));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list product with param:" + param, e);
            return new ResponseMessage(e);
        }
    }


    @ResponseBody
    @RequestMapping(value = "/change/list", method = RequestMethod.GET)
    public ResponseMessage change(@Validated ProductAuthzChangesPageQueryParam param,
                                  @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                  @RequestParam(value = "_pageSize", required = false) Integer pageSize) {
        try {
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("product-authz-changes");
            param.setPageSize(appPageSize.intValue());
            return new ResponseMessage("page", productAuthzChangesQueryService.pageQuery(param));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list product with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/batch-auth", method = RequestMethod.POST)
    public ResponseMessage batchAuth(@RequestBody @Validated ProductAuthzBathAuthVO productAuthzBathAuthVO) {
        try {
            //过滤掉，非该sp下的产品
            if (ShiroUtils.isSpOperator()) {
                final ProductPageQueryParam param = new ProductPageQueryParam();
                param.setPageSize(Integer.MAX_VALUE);
                final List<ProductPageItem> items = productQueryService.pageQuery(param).getItems();
                if (CollectionUtils.isNotEmpty(items)) {
                    productAuthzBathAuthVO.setProductCodes(
                            productAuthzBathAuthVO.getProductCodes().stream().filter(productCode ->
                                    items.stream().anyMatch(productItem ->
                                            productItem.getCode().equals(productCode))).collect(Collectors.toList())
                    );
                }
            }
            if (CollectionUtils.isNotEmpty(productAuthzBathAuthVO.getProductCodes())) {
                ProductAuthzBatchAuthRequest request = new ProductAuthzBatchAuthRequest(ShiroUtils.getOperatorCode(), productAuthzBathAuthVO.getCause());
                request.setCustomerNo(productAuthzBathAuthVO.getCustomerNo());
                request.setAppId(productAuthzBathAuthVO.getAppId());
                List<ProductAuthzDTO> list = new ArrayList<>();
                productAuthzBathAuthVO.getProductCodes().forEach(productCode -> {
                    ProductAuthzDTO productAuthzDTO = new ProductAuthzDTO();
                    productAuthzDTO.setProductCode(productCode);
                    productAuthzDTO.setEffectiveDate(productAuthzBathAuthVO.getEffectiveDate());
                    productAuthzDTO.setOverdueDate(productAuthzBathAuthVO.getOverdueDate());
                    list.add(productAuthzDTO);
                });
                request.setProductAuthzList(list);
                request.setForUpdate(false);
                return new ResponseMessage("result", productAuthzFacade.batchAuth(request));
            } else {
                throw new YeepayRuntimeException("no available products for authz");
            }

        } catch (Exception e) {
            LOGGER.error("Exception occurred when bath create product authz with param:" + productAuthzBathAuthVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/confirm", method = RequestMethod.POST)
    public ResponseMessage confirm(@RequestBody @Validated ProductAuthzVO productAuthzVO) {
        try {
            ProductAuthzConfirmRequest request = new ProductAuthzConfirmRequest(ShiroUtils.getOperatorCode(), productAuthzVO.getCause());
            request.setId(productAuthzVO.getId());
            productAuthzFacade.confirm(request);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when confirm product authz with param:" + productAuthzVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/unauth", method = RequestMethod.POST)
    public ResponseMessage unauth(@RequestBody @Validated ProductAuthzVO productAuthzVO) {
        try {
            ProductAuthzUnauthRequest request = new ProductAuthzUnauthRequest(ShiroUtils.getOperatorCode(), productAuthzVO.getCause());
            request.setId(productAuthzVO.getId());
            productAuthzFacade.unauth(request);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when unauth product authz with param:" + productAuthzVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/recover", method = RequestMethod.POST)
    public ResponseMessage recover(@RequestBody @Validated ProductAuthzVO productAuthzVO) {
        try {
            ProductAuthzRecoverRequest request = new ProductAuthzRecoverRequest(ShiroUtils.getOperatorCode(), productAuthzVO.getCause());
            request.setId(productAuthzVO.getId());
            productAuthzFacade.recover(request);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when recover product authz with param:" + productAuthzVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/defer", method = RequestMethod.POST)
    public ResponseMessage defer(@RequestBody @Validated ProductAuthzVO productAuthzVO) {
        try {
            ProductAuthzDeferRequest request = new ProductAuthzDeferRequest(ShiroUtils.getOperatorCode(), productAuthzVO.getCause());
            request.setId(productAuthzVO.getId());
            request.setEffectiveDate(productAuthzVO.getEffectiveDate());
            request.setOverdueDate(productAuthzVO.getOverdueDate());
            productAuthzFacade.defer(request);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when recover product authz with param:" + productAuthzVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/reauth", method = RequestMethod.POST)
    public ResponseMessage reauth(@RequestBody @Validated ProductAuthzVO productAuthzVO) {
        try {
            ProductAuthzReauthRequest request = new ProductAuthzReauthRequest(ShiroUtils.getOperatorCode(), productAuthzVO.getCause());
            request.setId(productAuthzVO.getId());
            request.setEffectiveDate(productAuthzVO.getEffectiveDate());
            request.setOverdueDate(productAuthzVO.getOverdueDate());
            productAuthzFacade.reauth(request);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when recover product authz with param:" + productAuthzVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/status", method = RequestMethod.GET)
    public ResponseMessage status() {
        List<CommonsVO> list = new ArrayList<>();
        ProductAuthzStatusEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/oper-type", method = RequestMethod.GET)
    public ResponseMessage operType() {
        List<CommonsVO> list = new ArrayList<>();
        ProductAuthzOperateTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

}
