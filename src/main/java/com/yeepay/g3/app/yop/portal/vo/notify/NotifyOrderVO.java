/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.notify;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yeepay.g3.facade.yop.monitor.enums.OrderStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/9 11:00 上午
 */
@Data
public class NotifyOrderVO implements Serializable {
    private static final long serialVersionUID = -1L;
    private String customerNo;
    private String notifyMerchantNo;
    private String orderId;
    private String notificationId;
    private String notifyRule;
    private String appId;
    private String spiName;
    private String spiTitle;
    private String url;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDate;
    private OrderStatusEnum status;
    private String guid;
    /**
     * 最近失败错误码
     */
    private String latestErrorCode;
    /**
     * 最近失败错误信息
     */
    private String latestErrorMsg;

    /**
     * 最近失败时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestFailDate;
    /**
     * 失败次数
     */
    private int failTimes;
    /**
     * 通知次数
     */
    private int notifyTimes;

    /**
     * 真实通知商户编号
     */
    private String realNotifyMerchantNo;

    /**
     * 真实通知规则
     */
    private String realNotifyRule;
    /**
     * 上一次失败原因
     */
    private String latestErrorName;
}
