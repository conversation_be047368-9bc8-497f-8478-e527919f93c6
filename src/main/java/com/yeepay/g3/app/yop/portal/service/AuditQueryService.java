/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-29 19:52
 */
public interface AuditQueryService {

    PageQueryResult<AuditRecordPageItem> pageQueryRecordList(AuditRecordPageQueryParam param);

    PageQueryResult<AuditRecordPageItem> pageQueryRecordForSp(AuditRecordPageQueryParam param);

    PageQueryResult<AuditRequisitionPageItem> pageQueryRequisitionList(AuditRequisitionPageQueryParam param);

    PageQueryResult<AuditRequisitionPageItem> pageQueryRequisitionForSp(AuditRequisitionPageQueryParam param);
}
