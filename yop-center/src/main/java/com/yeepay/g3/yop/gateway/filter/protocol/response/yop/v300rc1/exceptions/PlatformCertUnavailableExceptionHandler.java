/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.filter.protocol.response.yop.v300rc1.exceptions;

import com.yeepay.g3.yop.frame.exception.protocol.PlatformCertUnavailableException;
import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.gateway.annotations.ExceptionHandler;
import com.yeepay.g3.yop.gateway.filter.protocol.response.yop.v300rc1.AbstractExceptionYopMarshaller;
import com.yeepay.g3.yop.gateway.filter.protocol.response.yop.v300rc1.ExceptionResult;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/26 6:01 下午
 */
@ExceptionHandler(PlatformCertUnavailableException.class)
public class PlatformCertUnavailableExceptionHandler extends AbstractExceptionYopMarshaller {
    @Override
    public ExceptionResult getErrorResult(ServerWebExchange exchange, Response response) {
        return ExceptionResult.newBuilder().code(StatusEnum.ILLEGAL_ARGUMENT)
                .subCode(response.getException().getCode()).subMessage("平台证书不可用").build();
    }
}
