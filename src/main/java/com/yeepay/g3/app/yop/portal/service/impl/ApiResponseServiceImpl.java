/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.exception.PortalExceptionEnum;
import com.yeepay.g3.app.yop.portal.service.ApiResponseService;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.ApiResponseModelVO;
import com.yeepay.g3.facade.yop.sys.checker.ContentType;
import com.yeepay.g3.facade.yop.sys.dto.models.Content;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryService;
import io.swagger.v3.oas.models.media.ObjectSchema;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/15 11:28 AM
 */
@Component
public class ApiResponseServiceImpl implements ApiResponseService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiResponseServiceImpl.class);
    private static final JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();
    @Resource(name = "apiQueryService")
    private QueryService apiQueryService;
    @Resource(name = "apiGroupQueryService")
    private QueryService apiGroupQueryService;
    @Resource(name = "modelQueryService")
    private QueryService modelQueryService;
    @Override
    public ApiResponseModelVO findOutputParams(String apiId) {
        ApiResponseModelVO apiResponseModelVO = new ApiResponseModelVO();
        Map<String, Object> apiResponse = apiQueryService.queryUnique("findResponseByApiId", Collections.singletonMap("apiId", apiId), true);
        PortalExceptionEnum.API_NOT_FOUND.assertIsTrue(MapUtils.isNotEmpty(apiResponse), apiId);

        PortalExceptionEnum.API_CONTENT_TYPE_NONSTANDARD.assertIsTrue(ObjectUtils.isNotEmpty(apiResponse.get("content_type")), apiId);
        String contentType = (String) apiResponse.get("content_type");
        Map<String, String> models = new HashMap<>();
        if (ContentType.APPLICATION_JSON.equals(contentType)) {
            //得到响应参数中主ref的name
            String primaryName = parseRefName(apiId, apiResponse);
            apiResponseModelVO.setPrimaryName(primaryName);
            //查主模型的schema和ref对象
            List<Map<String, Object>> modelAndRefs = getSchemaAndRef(apiId, primaryName);
            String apiGroup = (String) modelAndRefs.get(0).get("api_group");
            models.put(primaryName, (String) modelAndRefs.get(0).get("schema"));
            //封装子ref的name和schema
            for (Map<String, Object> modelAndRef : modelAndRefs) {
                if (ObjectUtils.isNotEmpty(modelAndRef.get("ref_name"))) {
                    String refName = (String) modelAndRef.get("ref_name");
                    Map<String, Object> refModel = getSubRef(refName, apiGroup);
                    models.put(refName, (String) refModel.get("schema"));
                }
            }
            apiResponseModelVO.setModels(models);
        }
        return apiResponseModelVO;
    }

    private Map<String, Object> getSubRef(String refName, String apiGroup) {
        Map<String, Object> temParam = new HashMap<>();
        temParam.put("apiGroup", apiGroup);
        temParam.put("name", refName);
        Map<String, Object> refModel = modelQueryService.queryUnique("findByApiGroupAndName", temParam, true);
        if (MapUtils.isEmpty(refModel)) {
            LOGGER.error("api model does not existed, refName:{0}, apiGroup:{1}", refName, apiGroup);
            PortalExceptionEnum.API_MODEL_NOT_FOUND.newExceptionWithMsg("api模型不存在, refName:{0}, apiGroup:{1}", refName, apiGroup);
        }
        return refModel;
    }

    private List<Map<String, Object>> getSchemaAndRef(String apiId, String refName) {
        //根据apiId查询API分组
        Map<String, Object> groupList = apiGroupQueryService.queryUnique("findGroupByApiId", Collections.singletonMap("apiId", apiId), true);
        PortalExceptionEnum.API_GROUP_NOT_FOUND.assertIsTrue(MapUtils.isNotEmpty(groupList) && ObjectUtils.isNotEmpty(groupList.get("api_group")), apiId);
        String apiGroup = (String) groupList.get("api_group");
        //根据api分组和refName，查主模型和子模型的refName
        Map<String, Object> param = new HashMap<>(2);
        param.put("apiGroup", apiGroup);
        param.put("name", refName);
        List<Map<String, Object>> modelAndRefs = modelQueryService.query("findByApiGroupAndNameWithRef", param);
        PortalExceptionEnum.API_MODEL_NOT_FOUND.assertIsTrue(CollectionUtils.isNotEmpty(modelAndRefs), refName, apiGroup);
        return modelAndRefs;
    }

    private String parseRefName(String apiId, Map<String, Object> apiResponse) {
        PortalExceptionEnum.API_RESPONSE_CONTENT_NONSTANDARD.assertIsTrue(ObjectUtils.isNotEmpty(apiResponse.get("content")), apiId);
        Content content = JSON_MAPPER.fromJson((String) apiResponse.get("content"), Content.class);
        ObjectSchema objectSchema = JSON_MAPPER.fromJson(content.getSchema(), ObjectSchema.class);
        PortalExceptionEnum.API_RESPONSE_SCHEMA_NONSTANDARD.assertIsTrue(ObjectUtils.isNotEmpty(objectSchema) && StringUtils.isNotEmpty(objectSchema.get$ref()) && objectSchema.get$ref().contains("/"), apiId);
        String[] splits = objectSchema.get$ref().split("/");
        return splits[splits.length - 1];
    }
}
