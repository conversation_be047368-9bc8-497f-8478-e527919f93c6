/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.monitor.enums.OrderStatusEnum;
import lombok.Builder;
import lombok.Getter;

import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/22 2:41 下午
 */
@Builder
@Getter
public class NotifyRecordQueryParam implements Serializable {
    private static final long serialVersionUID = -1L;
    private String orderId;
    private Date orderDate;
    private OrderStatusEnum status;
}
