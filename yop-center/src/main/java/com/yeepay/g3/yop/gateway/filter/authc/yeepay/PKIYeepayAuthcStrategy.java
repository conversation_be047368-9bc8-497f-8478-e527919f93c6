/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.filter.authc.yeepay;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqDTO;
import com.yeepay.g3.sdk.yop.http.HttpUtils;
import com.yeepay.g3.yop.frame.cache.CertRefreshableLocalCache;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateStrategyEnum;
import com.yeepay.g3.yop.frame.definition.AppSecretKeys;
import com.yeepay.g3.yop.frame.definition.authenticate.SecretKey;
import com.yeepay.g3.yop.frame.exception.authentication.EncryptionKeyUnavailableException;
import com.yeepay.g3.yop.frame.exception.authentication.IncompatibleSecurityReqException;
import com.yeepay.g3.yop.frame.security.SignAlgEnum;
import com.yeepay.g3.yop.frame.security.signer.Signer;
import com.yeepay.g3.yop.frame.security.signer.SignerFactory;
import com.yeepay.g3.yop.ext.gateway.context.api.impl.ApiContext;
import com.yeepay.g3.yop.ext.gateway.context.app.impl.AppContext;
import com.yeepay.g3.yop.gateway.context.authenticate.impl.PKIContext;
import com.yeepay.g3.yop.ext.gateway.context.encrypt.EncryptContext;
import com.yeepay.g3.yop.ext.gateway.context.param.ParamContext;
import com.yeepay.g3.yop.gateway.filter.protocol.request.decrypt.ParamDecryptor;
import com.yeepay.g3.yop.gateway.filter.protocol.request.decrypt.ParamDecryptorFactory;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.yeepay.g3.yop.frame.CharacterConstants.*;
import static com.yeepay.g3.yop.frame.exception.authentication.IncompatibleSecurityReqException.ENCRYPTION_REQUIRED;
import static com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils.GATEWAY_AUTHC_CONTEXT_ATTR;

/**
 * title: PKIAuthcStrategy<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/11 7:28 下午
 */
@Slf4j
@Component
public class PKIYeepayAuthcStrategy extends AbstractYeepayAuthcStrategy {

    protected static final Splitter SIGNED_HEADER_STRING_SPLITTER = Splitter.on(SEMICOLON);

    protected static final Joiner HEADER_JOINER = Joiner.on(LF);

    @Resource(name = "certRefreshableLocalCache")
    private CertRefreshableLocalCache appSecretKeyCache;

    private SignerFactory signerFactory;

    private ParamDecryptorFactory decryptorFactory;

    @Override
    public List<AuthenticateStrategyEnum> supportAuthenticateStrategyEnums() {
        List<AuthenticateStrategyEnum> strategyEnumList = new ArrayList<>();
        strategyEnumList.add(AuthenticateStrategyEnum.YOP_RSA2048_SHA256);
        strategyEnumList.add(AuthenticateStrategyEnum.YOP_SM2_SM3);
        return strategyEnumList;
    }

    @Override
    public void authenticate(ServerWebExchange exchange) {
        PKIContext pkiContext = ServerWebExchangeUtils.getAuthenticateContext(exchange);
        PKIAuthcChecker pkiAuthcChecker = PKIAuthcCheckerFactory.get(pkiContext.getAuthenticateProtocolVersion());
        // pkiAuthcChecker 不可能为null
        //1.前置校验
        pkiAuthcChecker.preCheck(exchange);
        //2.验签
        verify(exchange);

        String appKey = ServerWebExchangeUtils.getAppId(exchange);
        AppSecretKeys appSecretKeys = appSecretKeyCache.get(appKey);
        decryptRequest(pkiContext,
                ServerWebExchangeUtils.getParamContext(exchange),
                ServerWebExchangeUtils.getAppContext(exchange),
                appSecretKeys);
    }

    @Override
    public String getForceCheckSwitchKey() {
        return "rsa_yop";
    }

    @Override
    protected boolean doCheckAuthenticationOverdue(ServerWebExchange exchange) {
        PKIContext pkiContext = (PKIContext) ServerWebExchangeUtils.getAuthenticateContext(exchange);
        AppContext appContext = ServerWebExchangeUtils.getAppContext(exchange);
        long requestTimestamp;
        try {
            requestTimestamp = parseRequestDate(pkiContext.getTimestamp());
        } catch (Throwable ex) {
            log.warn("unable to parse request date, appKey:" + ServerWebExchangeUtils.getAppId(exchange) + ", date:" + pkiContext.getTimestamp(), ex);
            return false;
        }
        long timeGap = (requestTimestamp + pkiContext.getExpirationInSeconds() * 1000L) - System.currentTimeMillis();
        if (timeGap < 0) {
            log.warn("request is overdue, customerNo:{}，timeGap:{}", appContext.getApp().getCustomerNo(), timeGap);
            return true;
        }
        return false;
    }

    private void verify(ServerWebExchange exchange) {
        String appKey = ServerWebExchangeUtils.getAppId(exchange);
        PKIContext pkiContext = exchange.getRequiredAttribute(GATEWAY_AUTHC_CONTEXT_ATTR);
        String sign = pkiContext.getSignature();
        String[] args = sign.split("\\$");
        String plainText = preparePlainText(exchange, pkiContext);
        AuthenticateStrategyEnum authenticateStrategy = pkiContext.getAuthenticateStrategy();
        SignAlgEnum encryptAlg = SignAlgEnum.SM2;
        if (AuthenticateStrategyEnum.YOP_RSA2048_SHA256.equals(authenticateStrategy)) {
            encryptAlg = SignAlgEnum.RSA2048;
        }
        SecurityReqDTO securityReq = pkiContext.getSecurityReq();
        List<String> authority = securityReq == null ? null : securityReq.getAuthority();
        Signer signer = signerFactory.getSigner(encryptAlg);
        signer.verify(plainText, args[0], appKey, authority);
    }

    private void decryptRequest(PKIContext pkiContext, ParamContext paramContext, AppContext appContext, AppSecretKeys appSecretKeys) {
        SecurityReqDTO securityReq = pkiContext.getSecurityReq();
        boolean needEncrypt = securityReq == null ? false : securityReq.isNeedEncrypt();
        EncryptContext encryptContext = pkiContext.getEncryptContext();
        if (needEncrypt) {
            if (encryptContext == null) {
                Date forceEncryptAppDate = securityReq.getForceEncryptAppDate();
                if (forceEncryptAppDate == null || appContext.getApp().getCreatedDate().after(forceEncryptAppDate)) {
                    throw new IncompatibleSecurityReqException(ENCRYPTION_REQUIRED);
                }
            }
        }
        if (encryptContext == null) {
            return;
        }
        List<SecretKey> encryptKeys = appSecretKeys.getEncryptKeys(encryptContext.getEncryptCertType());
        if (CollectionUtils.isEmpty(encryptKeys)) {
            throw new EncryptionKeyUnavailableException();
        }
        ParamDecryptor paramDecryptor = decryptorFactory.getDecryptor(paramContext.getClass());
        paramDecryptor.decrypt(paramContext, encryptContext, encryptKeys);
    }


    private String preparePlainText(ServerWebExchange exchange, PKIContext pkiContext) {
        HttpServletRequest req = exchange.getRequest();
        //authString
        String authString = new StringBuilder(pkiContext.getAuthenticateProtocolVersion().stringFormat()).append(SLASH)
                .append(pkiContext.getAccessKeyId()).append(SLASH)
                .append(pkiContext.getTimestamp()).append(SLASH)
                .append(pkiContext.getExpirationInSeconds()).toString();
        // Formatting the URL with signing protocol.
        ApiContext apiContext = ServerWebExchangeUtils.getApiContext(exchange);
        String canonicalURI = HttpUtils.getCanonicalURIPath(apiContext.getApiUri());
        // Formatting the query string with signing protocol.
        PKIAuthcChecker pkiAuthcChecker = PKIAuthcCheckerFactory.get(pkiContext.getAuthenticateProtocolVersion());
        String canonicalQueryString = pkiAuthcChecker.getCanonicalQueryString(exchange);
        // Sorted the headers should be signed from the request.
        // Formatting the headers from the request based on signing protocol.
        String canonicalHeader = getCanonicalHeaders(req, Sets.newHashSet(SIGNED_HEADER_STRING_SPLITTER.split(pkiContext.getSignedHeaders())));
        return new StringBuilder(authString).append(LF)
                .append(req.getMethod()).append(LF)
                .append(canonicalURI).append(LF)
                .append(canonicalQueryString).append(LF)
                .append(canonicalHeader).toString();
    }

    private String getCanonicalHeaders(HttpServletRequest req, Set<String> headerNames) {
        List<String> kvs = Lists.newArrayList();
        for (String key : headerNames) {
            String value = req.getHeader(key);
            if (StringUtils.isBlank(value)) {
                continue;
            }
            kvs.add(HttpUtils.normalize(key) + COLON + HttpUtils.normalize(value.trim()));
        }
        Collections.sort(kvs);
        return HEADER_JOINER.join(kvs);
    }

    @Autowired
    public void setSignerFactory(SignerFactory signerFactory) {
        this.signerFactory = signerFactory;
    }

    @Autowired
    public void setDecryptorFactory(ParamDecryptorFactory decryptorFactory) {
        this.decryptorFactory = decryptorFactory;
    }
}
