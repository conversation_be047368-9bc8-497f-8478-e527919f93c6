package com.yeepay.g3.app.yop.portal.controller.support.imports;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * title: Spi导入分析结果<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 17:23
 */
public class SpiImportAnalysisResult implements Serializable {

    private static final long serialVersionUID = -1L;

    private String requestId;

    private List<SpiCreateCheckItem> spisToCreate;

    private List<SpiOverrideCheckItem> spisToOverride;

    private List<String> spisToIgnore;

    private List<ModelCreateCheckItem> modelsToCreate;

    private List<ModelOverrideCheckItem> modelsToOverride;

    private List<String> modelsToIgnore;

    private List<String> unusedModels;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public SpiImportAnalysisResult withRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    public List<SpiCreateCheckItem> getSpisToCreate() {
        return spisToCreate;
    }

    public void setSpisToCreate(List<SpiCreateCheckItem> spisToCreate) {
        this.spisToCreate = spisToCreate;
    }

    public SpiImportAnalysisResult addSpisToCreateItem(SpiCreateCheckItem item) {
        if (this.spisToCreate == null) {
            this.spisToCreate = new ArrayList<>();
        }
        this.spisToCreate.add(item);
        return this;
    }

    public List<SpiOverrideCheckItem> getSpisToOverride() {
        return spisToOverride;
    }

    public void setSpisToOverride(List<SpiOverrideCheckItem> spisToOverride) {
        this.spisToOverride = spisToOverride;
    }

    public SpiImportAnalysisResult addSpisToOverrideItem(SpiOverrideCheckItem item) {
        if (this.spisToOverride == null) {
            this.spisToOverride = new ArrayList<>();
        }
        this.spisToOverride.add(item);
        return this;
    }

    public List<String> getSpisToIgnore() {
        return spisToIgnore;
    }

    public void setSpisToIgnore(List<String> spisToIgnore) {
        this.spisToIgnore = spisToIgnore;
    }

    public SpiImportAnalysisResult withSpisToIgnore(List<String> spisToIgnore) {
        this.spisToIgnore = spisToIgnore;
        return this;
    }

    public SpiImportAnalysisResult addSpisToIgnoreItem(String spi) {
        if (this.spisToIgnore == null) {
            this.spisToIgnore = new ArrayList<>();
        }
        this.spisToIgnore.add(spi);
        return this;
    }

    public List<ModelCreateCheckItem> getModelsToCreate() {
        return modelsToCreate;
    }

    public void setModelsToCreate(List<ModelCreateCheckItem> modelsToCreate) {
        this.modelsToCreate = modelsToCreate;
    }


    public SpiImportAnalysisResult addModelsToCreateItem(ModelCreateCheckItem item) {
        if (this.modelsToCreate == null) {
            this.modelsToCreate = new ArrayList<>();
        }
        this.modelsToCreate.add(item);
        return this;
    }

    public List<ModelOverrideCheckItem> getModelsToOverride() {
        return modelsToOverride;
    }

    public void setModelsToOverride(List<ModelOverrideCheckItem> modelsToOverride) {
        this.modelsToOverride = modelsToOverride;
    }

    public SpiImportAnalysisResult addModelsToOverrideItem(ModelOverrideCheckItem item) {
        if (this.modelsToOverride == null) {
            this.modelsToOverride = new ArrayList<>();
        }
        this.modelsToOverride.add(item);
        return this;
    }

    public List<String> getModelsToIgnore() {
        return modelsToIgnore;
    }

    public void setModelsToIgnore(List<String> modelsToIgnore) {
        this.modelsToIgnore = modelsToIgnore;
    }

    public SpiImportAnalysisResult withModelsToIgnore(List<String> modelsToIgnore) {
        this.modelsToIgnore = modelsToIgnore;
        return this;
    }

    public SpiImportAnalysisResult addModelsToIgnoreItem(String model) {
        if (this.modelsToIgnore == null) {
            this.modelsToIgnore = new ArrayList<>();
        }
        this.modelsToIgnore.add(model);
        return this;
    }

    public List<String> getUnusedModels() {
        return unusedModels;
    }

    public void setUnusedModels(List<String> unusedModels) {
        this.unusedModels = unusedModels;
    }

    public SpiImportAnalysisResult withUnusedModels(List<String> unusedModels) {
        this.unusedModels = unusedModels;
        return this;
    }

}
