/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.IspCreate;
import com.yeepay.g3.facade.yop.perm.enums.IspStatusEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/10/31 20:13
 */
public class IspInfoVO implements Serializable {

    private static final long serialVersionUID = -1L;

    @Size(min = 1, max = 14, groups = {IspCreate.class})
    private String spCode;

    private String spName;

    private IspStatusEnum status;

    private String description;

    private List<String> managerCodes;

    private Date createdDate;

    private Date lastModifiedDate;

    /**
     * 租户code
     */
    private String tenantCode;

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getSpName() {
        return spName;
    }

    public void setSpName(String spName) {
        this.spName = spName;
    }

    public IspStatusEnum getStatus() {
        return status;
    }

    public void setStatus(IspStatusEnum status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<String> getManagerCodes() {
        return managerCodes;
    }

    public void setManagerCodes(List<String> managerCodes) {
        this.managerCodes = managerCodes;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
