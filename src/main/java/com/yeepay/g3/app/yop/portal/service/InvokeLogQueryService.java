package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.InvokeLogRequestParamVO;
import com.yeepay.g3.app.yop.portal.vo.InvokeLogVO;
import com.yeepay.g3.app.yop.portal.vo.page.InvokeLogPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.InvokeLogPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;


/**
 * 新版调用记录查询
 */
public interface InvokeLogQueryService {

    /**
     * 分页查询
     *
     * @param queryParam 查询参数
     * @return 分页查询结果
     */
    PageQueryResult<InvokeLogPageItem> pageQuery(InvokeLogPageQueryParam queryParam);

    /**
     * 详情查询
     *
     * @param queryParam 查询参数
     * @return 详情查询结果
     */
    InvokeLogVO detailQuery(InvokeLogRequestParamVO queryParam);

}
