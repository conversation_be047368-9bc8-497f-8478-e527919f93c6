package com.yeepay.g3.app.yop.portal.service.impl;


import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.yeepay.g3.app.yop.portal.dto.ApiGroupGitSyncContext;
import com.yeepay.g3.app.yop.portal.git.GitClient;
import com.yeepay.g3.app.yop.portal.service.ApiGroupGitSyncService;
import com.yeepay.g3.facade.yop.sys.dto.ApiGroupGitSyncInfoDTO;
import com.yeepay.g3.facade.yop.sys.facade.ApiGroupGitSyncInfoMgrFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-12 18:59
 */
@Component
public class ApiGroupGitSyncServiceImpl implements ApiGroupGitSyncService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiGroupGitSyncService.class);

    private ApiGroupGitSyncInfoMgrFacade gitSyncInfoMgrFacade() {
        return RemoteServiceFactory.getService(ApiGroupGitSyncInfoMgrFacade.class);
    }

    @Autowired
    private GitClient gitClient;

    private ExecutorService executorService;

    @Override
    @CachePut(value = "yp:aggs", key = "#context.apiGroup + ':' + #context.requestId")
    public ApiGroupGitSyncContext saveSyncContext(ApiGroupGitSyncContext context) {
        return context;
    }

    @Override
    @Cacheable(value = "yp:aggs", key = "#apiGroup + ':' + #requestId")
    public ApiGroupGitSyncContext findSyncRequest(String apiGroup, String requestId) {
        return null;
    }

    @Override
    @CacheEvict(value = "yp:aggs", key = "#apiGroup + ':' + #requestId")
    public void deleteSyncRequest(String apiGroup, String requestId) {
        // do nothing
    }


    @Override
    public void refreshAll() {
        List<ApiGroupGitSyncInfoDTO> result = gitSyncInfoMgrFacade().queryAll();
        if (CollectionUtils.isNotEmpty(result)) {
            result.forEach(apiGroupGitSyncInfo -> executorService.submit(new ApiGroupGitSyncTask(apiGroupGitSyncInfo)));
        }
    }

    class ApiGroupGitSyncTask implements Runnable {

        private final ApiGroupGitSyncInfoDTO info;

        ApiGroupGitSyncTask(ApiGroupGitSyncInfoDTO info) {
            this.info = info;
        }

        @Override
        public void run() {
            try {
                String latestCommitId = gitClient.getLatestCommit(info.getGitRepository(), info.getGitBranch(), info.getFilePath());
                if (latestCommitId == null) {
                    LOGGER.error("file not exist, apiGroup:{}", info.getApiGroup());
                } else if (StringUtils.equals(latestCommitId, info.getLatestCommitId())) {
                    LOGGER.info("file not changed, apiGroup:{}", info.getApiGroup());
                } else {
                    gitSyncInfoMgrFacade().updateLatestCommitId(info.getApiGroup(), latestCommitId);
                }
            } catch (Exception ex) {
                LOGGER.error("unexpected exception occurred when sync gitConfig for apiGroup:" + info.getApiGroup(), ex);
            }
        }
    }


    @PostConstruct
    public void init() {
        int processors = Runtime.getRuntime().availableProcessors();
        executorService = new ThreadPoolExecutor(2 * processors,
                2 * processors, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(),
                new ThreadFactoryBuilder().setNameFormat("api-group-git-sync-%d").build());
        ((ThreadPoolExecutor) executorService).allowCoreThreadTimeOut(true);
    }
}
