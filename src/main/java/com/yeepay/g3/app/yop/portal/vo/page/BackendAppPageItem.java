package com.yeepay.g3.app.yop.portal.vo.page;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

public class BackendAppPageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private String spCode;

    private String backendCode;

    private String backendName;

    private String deployMode;

    private String classLoadMode;

    private String rpcMode;

    private String description;

    private Long version;

    private Date createdDate;

    private Date lastModifiedDate;

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getBackendCode() {
        return backendCode;
    }

    public void setBackendCode(String backendCode) {
        this.backendCode = backendCode;
    }

    public String getBackendName() {
        return backendName;
    }

    public void setBackendName(String backendName) {
        this.backendName = backendName;
    }

    public String getDeployMode() {
        return deployMode;
    }

    public void setDeployMode(String deployMode) {
        this.deployMode = deployMode;
    }

    public String getClassLoadMode() {
        return classLoadMode;
    }

    public void setClassLoadMode(String classLoadMode) {
        this.classLoadMode = classLoadMode;
    }

    public String getRpcMode() {
        return rpcMode;
    }

    public void setRpcMode(String rpcMode) {
        this.rpcMode = rpcMode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
