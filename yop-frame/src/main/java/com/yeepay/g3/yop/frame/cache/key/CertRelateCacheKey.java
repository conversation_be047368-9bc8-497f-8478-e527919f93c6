package com.yeepay.g3.yop.frame.cache.key;

import com.yeepay.g3.yop.frame.CharacterConstants;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Objects;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/10/10 上午9:57
 */
public class CertRelateCacheKey {

    private String appId;

    private String certType;

    public CertRelateCacheKey(String appId, String certType) {
        this.appId = appId;
        this.certType = certType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CertRelateCacheKey that = (CertRelateCacheKey) o;
        return Objects.equals(this.getAppId(), that.getAppId()) &&
                Objects.equals(this.getCertType(), that.getCertType());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getAppId() + CharacterConstants.HASH
                + getCertType());
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }
}
