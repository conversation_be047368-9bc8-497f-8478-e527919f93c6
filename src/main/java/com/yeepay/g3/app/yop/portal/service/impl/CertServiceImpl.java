/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.service.CertService;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/6 11:51
 */
@Component
public class CertServiceImpl implements CertService {
    @Resource(name = "certService")
    private QueryService queryService;

    @Override
    public Boolean isExisted(String serialNo) {
        Map<String, Object> param = new HashMap<>();
        param.put("serialNo", String.valueOf(Long.parseLong(serialNo, 16)));
        List list = queryService.query("listBySerialNo", param);
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        return true;
    }

}
