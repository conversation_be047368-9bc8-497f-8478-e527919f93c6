package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.dto.SpiDTO;

import java.io.Serializable;

/**
 * title: Spi导入检查项<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 16:40
 */
public class SpiCreateCheckItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private SpiDTO spi;

    public SpiDTO getSpi() {
        return spi;
    }

    public void setSpi(SpiDTO spi) {
        this.spi = spi;
    }

    public SpiCreateCheckItem withSpi(SpiDTO spi) {
        this.spi = spi;
        return this;
    }

}
