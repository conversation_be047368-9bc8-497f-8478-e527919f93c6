/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.dto.ApiRouteImportContext;
import com.yeepay.g3.app.yop.portal.service.ApiRouteImportCacheService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/27 5:31 PM
 */
@Component
public class ApiRouteImportCacheServiceImpl implements ApiRouteImportCacheService {

    @Override
    @CachePut(value = "yp:api:route", key = "#requestId")
    public ApiRouteImportContext storeContext(String requestId, ApiRouteImportContext context) {
        return context;
    }

    @Override
    @Cacheable(value = "yp:api:route", key = "#requestId")
    public ApiRouteImportContext loadContext(String requestId) {
        return null;
    }

    @Override
    @CacheEvict(value = "yp:api:route", key = "#requestId")
    public void evictContext(String requestId) {

    }
}
