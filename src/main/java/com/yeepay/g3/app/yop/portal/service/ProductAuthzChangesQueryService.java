/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.ProductAuthzChangesPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.ProductAuthzChangesPageItem;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/20 上午11:58
 */
public interface ProductAuthzChangesQueryService {

    PageQueryResult<ProductAuthzChangesPageItem> pageQuery(ProductAuthzChangesPageQueryParam param);

}
