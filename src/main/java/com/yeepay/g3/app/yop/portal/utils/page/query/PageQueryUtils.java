package com.yeepay.g3.app.yop.portal.utils.page.query;

import com.yeepay.g3.app.yop.portal.vo.page.BasePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/16 16:52
 */
public class PageQueryUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(PageQueryUtils.class);

    private static final Integer DEFAULT_PAGE_SIZE = 10;

    public static QueryParam getBaseQueryParam(BasePageQueryParam param) {
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? DEFAULT_PAGE_SIZE : param.getPageSize();
        QueryParam queryParam = new QueryParam();
        queryParam.setStartIndex(pageNo <= 1 ? 1 : (pageNo - 1) * pageSize + 1);
        queryParam.setMaxSize(pageSize);
        return queryParam;
    }

    public static <Item> PageQueryResult<Item> convertResult(QueryResult queryResult, Integer currentPageNo,
                                                             PageItemConverter<Item> converter) {
        PageQueryResult<Item> result = new PageQueryResult<>();
        result.setPageNo(currentPageNo == null ? 1 : currentPageNo);
        result.setTotalPageNum((int) (queryResult.getTotalCount() % queryResult.getMaxFetchSize() == 0 ?
                queryResult.getTotalCount() / queryResult.getMaxFetchSize() : (queryResult.getTotalCount() / queryResult.getMaxFetchSize() + 1)));
        List<Item> items = new ArrayList<>(CollectionUtils.size(queryResult.getData()));
        if (CollectionUtils.isNotEmpty(queryResult.getData())) {
            queryResult.getData().forEach(paramMap -> items.add(converter.convert(paramMap)));
        }
        result.setItems(items);
        return result;
    }

    public static <T> List<T> convertResult(List<T> list, Integer pageNo, Integer pageSize) {
        if (CollectionUtils.isNotEmpty(list)) {
            int count = list.size();
            int start = pageNo * pageSize;
            int end = (pageNo + 1) * pageSize;
            if (start > count) {
                start = count;
            }
            if (end > count) {
                end = count;
            }
            return list.subList(start, end);
        }
        return Collections.EMPTY_LIST;
    }

}
