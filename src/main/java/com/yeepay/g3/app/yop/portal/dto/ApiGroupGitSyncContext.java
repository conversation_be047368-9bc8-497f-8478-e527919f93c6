package com.yeepay.g3.app.yop.portal.dto;

import java.io.Serializable;

/**
 * title: Api分组git同步上下文<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-12 19:09
 */
public class ApiGroupGitSyncContext implements Serializable {

    private static final long serialVersionUID = -1L;

    private String apiGroup;

    private String requestId;

    private String filePath;

    private String currentCommitId;

    private String latestCommitId;

    private String latestFileContent;

    private Long syncInfoVersion;

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    public ApiGroupGitSyncContext withApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public ApiGroupGitSyncContext withRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public ApiGroupGitSyncContext withFilePath(String filePath) {
        this.filePath = filePath;
        return this;
    }

    public String getCurrentCommitId() {
        return currentCommitId;
    }

    public void setCurrentCommitId(String currentCommitId) {
        this.currentCommitId = currentCommitId;
    }

    public ApiGroupGitSyncContext withCurrentCommitId(String currentCommitId) {
        this.currentCommitId = currentCommitId;
        return this;
    }

    public String getLatestCommitId() {
        return latestCommitId;
    }

    public void setLatestCommitId(String latestCommitId) {
        this.latestCommitId = latestCommitId;
    }

    public ApiGroupGitSyncContext withLatestCommitId(String latestCommitId) {
        this.latestCommitId = latestCommitId;
        return this;
    }

    public String getLatestFileContent() {
        return latestFileContent;
    }

    public void setLatestFileContent(String latestFileContent) {
        this.latestFileContent = latestFileContent;
    }

    public ApiGroupGitSyncContext withLatestFileContent(String latestFileContent) {
        this.latestFileContent = latestFileContent;
        return this;
    }

    public Long getSyncInfoVersion() {
        return syncInfoVersion;
    }

    public void setSyncInfoVersion(Long syncInfoVersion) {
        this.syncInfoVersion = syncInfoVersion;
    }

    public ApiGroupGitSyncContext withSyncInfoVersion(Long syncInfoVersion) {
        this.syncInfoVersion = syncInfoVersion;
        return this;
    }
}
