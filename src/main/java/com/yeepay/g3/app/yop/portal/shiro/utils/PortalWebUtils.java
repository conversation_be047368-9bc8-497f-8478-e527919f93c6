/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.utils;

import com.yeepay.g3.utils.common.json.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/10 11:40 上午
 */
@Slf4j
public class PortalWebUtils {
    public static void writeJsonResponse(HttpServletResponse response, int status, Object object) {
        PrintWriter out = null;
        try {
            response.setStatus(status);
            response.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json");
            out = response.getWriter();
            out.println(JSONUtils.toJsonString(object));
        } catch (IOException e) {
            log.error("", e);
        } finally {
            if (out != null) {
                out.flush();
                out.close();
            }
        }
    }

    public static void writeJsonResponse(HttpServletResponse response, HttpStatus status, Object object) throws IOException {
        PrintWriter out = null;
        try {
            response.setStatus(status.value());
            response.setCharacterEncoding("UTF-8");
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json");
            out = response.getWriter();
            out.println(JSONUtils.toJsonString(object));
        } catch (IOException e) {
            throw e;
        } finally {
            if (out != null) {
                out.flush();
                out.close();
            }
        }
    }
}
