package com.yeepay.g3.yop.frame.definition;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yeepay.g3.facade.yop.api.enums.ApiParamDataTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * 参数定义
 *
 * @author：wang.bao
 * @since：2015年3月16日 下午5:09:38
 * @version:
 */
public class JsonParamDefinition extends ApiParamDefinition {

    private ApiParamDataTypeEnum paramDataType;

    private List<JsonParamDefinition> children;

    private Map<String, JsonParamDefinition> childrenNameMap;

    public ApiParamDataTypeEnum getParamDataType() {
        return paramDataType;
    }

    public void setParamDataType(ApiParamDataTypeEnum paramDataType) {
        this.paramDataType = paramDataType;
    }

    public void addChild(JsonParamDefinition child) {
        if (children == null) {
            children = Lists.newArrayList();
            childrenNameMap = Maps.newHashMap();
        }
        children.add(child);
        childrenNameMap.put(child.getParamName(), child);
    }

    public void addChildren(List<JsonParamDefinition> children) {
        for (JsonParamDefinition child : children) {
            addChild(child);
        }
    }

    public JsonParamDefinition getChild(String name) {
        return null == childrenNameMap ? null : childrenNameMap.get(name);
    }

    public JsonParamDefinition getChild() {
        return null == children ? null : children.get(0);
    }

    public List<JsonParamDefinition> getChildren() {
        return children;
    }

}
