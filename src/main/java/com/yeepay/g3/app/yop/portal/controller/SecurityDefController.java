package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.SecurityDefQueryService;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/9 16:17
 */
@Controller
@RequestMapping("/rest/security-def")
public class SecurityDefController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SecurityDefController.class);

    @Autowired
    private SecurityDefQueryService securityDefQueryService;

    @ResponseBody
    @RequestMapping(value = "/commons/list", method = RequestMethod.GET)
    public ResponseMessage list() {
        try {
            return new ResponseMessage("result", securityDefQueryService.list());
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list securityReq.", ex);
            return new ResponseMessage(ex);
        }
    }
}
