/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.dto;

import com.yeepay.g3.app.yop.portal.enums.AclOperStatusEnum;

import java.util.Date;

/**
 * title:用户操作日志DTO <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2016<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/8/1 15:27
 */
public class AclLogDTO extends BaseDTO {

    private static final long serialVersionUID = -1L;

    private Long id;

    private String guid;

    private String operatorCode;

    private String url;

    private String requestIp;

    private String requestMethod;

    private String requestContent;

    private Integer latency;

    private AclOperStatusEnum status;

    private String detail;

    private Date operDatetime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public String getRequestContent() {
        return requestContent;
    }

    public void setRequestContent(String requestContent) {
        this.requestContent = requestContent;
    }

    public AclOperStatusEnum getStatus() {
        return status;
    }

    public void setStatus(AclOperStatusEnum status) {
        this.status = status;
    }

    public Date getOperDatetime() {
        return operDatetime;
    }

    public void setOperDatetime(Date operDatetime) {
        this.operDatetime = operDatetime;
    }

    public String getRequestIp() {
        return requestIp;
    }

    public void setRequestIp(String requestIp) {
        this.requestIp = requestIp;
    }

    public Integer getLatency() {
        return latency;
    }

    public void setLatency(Integer latency) {
        this.latency = latency;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }
}
