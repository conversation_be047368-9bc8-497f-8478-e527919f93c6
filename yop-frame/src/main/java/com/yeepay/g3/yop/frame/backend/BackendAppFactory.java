package com.yeepay.g3.yop.frame.backend;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/9/4 上午10:15
 */
public interface BackendAppFactory {

    /**
     * 根据name寻找BackendApp，如果不存在，就根据endClass初始化一个
     *
     * @param backendAppName 应用名称
     * @param endClass       加载类
     * @return 远程后端应用
     */
    ScalableBackendApp getRemoteBackendApp(final String backendAppName, final String endClass);

    /**
     * 获取内嵌后端应用(所有本地协议的api均属于该应用)
     *
     * @return 内嵌应用
     */
    BackendApp getEmbeddedApp();

}
