package com.yeepay.g3.app.yop.portal.vo.page;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * title: SpiPageItem<br/>
 * description: spi分页查询返回参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:38
 */
@Data
public class SpiPageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private Long version;

    private String name;

    private String status;

    private String title;

    private String apiGroup;

    private String spiType;

    private String description;

    private Date createdDateTime;

    private Date lastModifiedDateTime;

    public Long getId() {
        return id;
    }

    public SpiPageItem setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getVersion() {
        return version;
    }

    public SpiPageItem setVersion(Long version) {
        this.version = version;
        return this;
    }

    public String getName() {
        return name;
    }

    public SpiPageItem setName(String name) {
        this.name = name;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public SpiPageItem setTitle(String title) {
        this.title = title;
        return this;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public SpiPageItem setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getSpiType() {
        return spiType;
    }

    public SpiPageItem setSpiType(String spiType) {
        this.spiType = spiType;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public SpiPageItem setDescription(String description) {
        this.description = description;
        return this;
    }

    public Date getCreatedDateTime() {
        return createdDateTime;
    }

    public SpiPageItem setCreatedDateTime(Date createdDateTime) {
        this.createdDateTime = createdDateTime;
        return this;
    }

    public Date getLastModifiedDateTime() {
        return lastModifiedDateTime;
    }

    public SpiPageItem setLastModifiedDateTime(Date lastModifiedDateTime) {
        this.lastModifiedDateTime = lastModifiedDateTime;
        return this;
    }

    @Override
    public String toString() {
        return "SpiPageItem{" +
                "id=" + id +
                ", version=" + version +
                ", name='" + name + '\'' +
                ", title='" + title + '\'' +
                ", apiGroup='" + apiGroup + '\'' +
                ", spiType='" + spiType + '\'' +
                ", description='" + description + '\'' +
                ", createdDateTime=" + createdDateTime +
                ", lastModifiedDateTime=" + lastModifiedDateTime +
                '}';
    }
}
