package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.dto.*;
import com.yeepay.g3.app.yop.portal.service.DocFaqQueryService;
import com.yeepay.g3.app.yop.portal.service.DocQueryService;
import com.yeepay.g3.app.yop.portal.service.PageOwnerQueryService;
import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.app.yop.portal.vo.DocCategoryItemVO;
import com.yeepay.g3.app.yop.portal.vo.DocPageRefVO;
import com.yeepay.g3.app.yop.portal.vo.DocPageVO;
import com.yeepay.g3.app.yop.portal.vo.PageOwnerVO;
import com.yeepay.g3.app.yop.portal.vo.page.DocFaqPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.DocPageHistoryPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.DocPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.DocPublishPageQueryParam;
import com.yeepay.g3.facade.yop.doc.dto.*;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocPageAddRequest;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocPageDTO;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocPageDragRequest;
import com.yeepay.g3.facade.yop.doc.enums.DocCategoryTypeEnum;
import com.yeepay.g3.facade.yop.doc.enums.OperTypeEnum;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocIncludesEnum;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocTypeEnum;
import com.yeepay.g3.facade.yop.doc.facade.DocMgrFacade;
import com.yeepay.g3.facade.yop.doc.util.DocCategoryUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.janus.facade.doc.dto.DocFaqCreateCmd;
import com.yeepay.janus.facade.doc.dto.DocFaqTopCmd;
import com.yeepay.janus.facade.doc.dto.DocFaqUpdateCmd;
import com.yeepay.janus.facade.doc.facade.DocFaqMgrFacade;
import com.yeepay.janus.facade.doc.facade.PageOwnerFacade;
import com.yeepay.janus.facade.dto.IdBasedCmd;
import com.yeepay.janus.facade.dto.PageOwnerUpdateDTO;
import com.yeepay.janus.facade.dto.data.OperDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

import static com.yeepay.g3.app.yop.portal.exception.PortalExceptionEnum.PERMS_MISSING;
import static com.yeepay.g3.app.yop.portal.utils.PageUtils.getConfigPageSize;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-29 10:11
 */
@Controller
@RequestMapping("/rest/doc")
public class DocController {
    private DocQueryService docQueryService;
    private DocFaqQueryService docFaqQueryService;
    private PageOwnerQueryService pageOwnerQueryService;

    public DocMgrFacade getDocMgrFacade() {
        return RemoteServiceFactory.getService(DocMgrFacade.class);
    }

    public DocFaqMgrFacade getDocFaqMgrFacade() {
        return RemoteServiceFactory.getService(DocFaqMgrFacade.class);
    }

    public PageOwnerFacade getPageOwnerFacade() {
        return RemoteServiceFactory.getService(PageOwnerFacade.class);
    }

    @Autowired
    public void setDocQueryService(DocQueryService docQueryService) {
        this.docQueryService = docQueryService;
    }

    @Autowired
    public void setDocFaqQueryService(DocFaqQueryService docFaqQueryService) {
        this.docFaqQueryService = docFaqQueryService;
    }

    @Autowired
    public void setPageOwnerQueryService(PageOwnerQueryService pageOwnerQueryService) {
        this.pageOwnerQueryService = pageOwnerQueryService;
    }

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage<Object> docList(@Validated DocPageQueryParam param,
                                   @RequestParam(value = "_pageNo", required = false) Integer pageNo
    ) {
        param.setPageNo(pageNo);
        param.setPageSize(getConfigPageSize("doc"));
        return new ResponseMessage<>("page", docQueryService.pageQuery(param));
    }

    @ResponseBody
    @RequestMapping(value = "/check-if-exists", method = RequestMethod.GET)
    public ResponseMessage<Object> docNoCheck(@RequestParam String docNo) {
        return new ResponseMessage<>("result", docQueryService.existsDoc(docNo));
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage<Object> docCreate(@RequestBody DocCreateDTO doc
    ) {
        final ShiroRealm.ShiroUser shiroUser = ShiroUtils.getShiroUser();
        PERMS_MISSING.assertIsTrue(!DocTypeEnum.OPEN.equals(doc.getType()) || !shiroUser.isSpOperator());
        doc.setOperator(shiroUser.getUserId());
        doc.setOperType(OperTypeEnum.CREATE);
        getDocMgrFacade().create(doc);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/settings/detail", method = RequestMethod.GET)
    public ResponseMessage<Object> docSettingView(@RequestParam Long id
    ) {
        return new ResponseMessage<>("result", getDocMgrFacade().findDocById(id,
                new DocIncludesEnum[]{DocIncludesEnum.PRODUCTS, DocIncludesEnum.DOC_CATEGORY}));
    }

    @ResponseBody
    @RequestMapping(value = "/settings/edit", method = RequestMethod.POST)
    public ResponseMessage<Object> docSettingEdit(@RequestBody DocUpdateDTO doc
    ) {
        doc.setOperator(ShiroUtils.getOperatorCode());
        doc.setOperType(OperTypeEnum.UPDATE);
        getDocMgrFacade().update(doc);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/tree", method = RequestMethod.GET)
    public ResponseMessage<Object> docTree(@RequestParam Long id
    ) {
        return new ResponseMessage<>("result", getDocMgrFacade().findDocTree(id));
    }

    @ResponseBody
    @RequestMapping(value = "/tree/refresh", method = RequestMethod.POST)
    public ResponseMessage<Object> docTreeRefresh(@RequestParam Long id
    ) {
        getDocMgrFacade().refreshDocTree(id);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/tree/drag", method = RequestMethod.POST)
    public ResponseMessage<Object> docTreeDrag(@RequestBody DocPageDragRequest dragRequest
    ) {
        getDocMgrFacade().dragDocPage(dragRequest);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/tree/add", method = RequestMethod.POST)
    public ResponseMessage<Object> docTreeAdd(@RequestBody DocPageAddRequest addRequest
    ) {
        getDocMgrFacade().addPageForDoc(addRequest);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/page/delete", method = RequestMethod.POST)
    public ResponseMessage<Object> docPageDelete(@RequestBody DocPageDeleteRequest deleteRequest
    ) {
        deleteRequest.setOperator(ShiroUtils.getOperatorCode());
        getDocMgrFacade().deleteDocPage(deleteRequest);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/page/view", method = RequestMethod.GET)
    public ResponseMessage<Object> docPageView(@RequestParam Long id, @RequestParam String docNo
    ) {
        return new ResponseMessage<>("result", toDocPageVO(getDocMgrFacade().findPageForDoc(id)));
    }

    private DocPageVO toDocPageVO(DocPageDTO page) {
        final DocPageVO pageVO = new DocPageVO();
        BeanUtils.copyProperties(page, pageVO);
        pageVO.setAccessUrl(docQueryService.getPageWithPath(page.getId()).getAccessUrl());
        if (null != page.getRefId()) {
            pageVO.setRefPage(docQueryService.getPageWithPath(page.getRefId()));
        }
        return pageVO;
    }

    @ResponseBody
    @RequestMapping(value = "/page/edit", method = RequestMethod.POST)
    public ResponseMessage<Object> docPageEdit(@RequestBody DocPageUpdateRequest updateRequest
    ) {
        updateRequest.setOperator(ShiroUtils.getOperatorCode());
        getDocMgrFacade().updateDocPage(updateRequest);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/page/recover", method = RequestMethod.POST)
    public ResponseMessage<Object> docPageRecover(@RequestBody DocPageRecoverRequest recoverRequest
    ) {
        final String operatorCode = ShiroUtils.getOperatorCode();
        recoverRequest.setOperator(operatorCode);
        getDocMgrFacade().recoverDocPage(recoverRequest);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/page/change-history", method = RequestMethod.GET)
    public ResponseMessage<Object> docPageHistory(@Validated DocPageHistoryPageQueryParam param,
                                          @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                          @RequestParam(value = "_pageSize", required = false) Integer pageSize
    ) {
        param.setPageNo(pageNo);
        param.setPageSize(getConfigPageSize("doc-page-history"));
        return new ResponseMessage<>("page", docQueryService.historyPageQuery(param));
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage<Object> docDelete(@RequestParam Long id
    ) {
        getDocMgrFacade().delete(id);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "list-for-arrange", method = RequestMethod.GET)
    public ResponseMessage<Object> docListForArrange(@RequestParam(value = "categoryId") Long categoryId
    ) {
        PERMS_MISSING.assertIsTrue(ShiroUtils.isPlatformOperator());
        return new ResponseMessage<>("result", docQueryService.listForArrange(categoryId));
    }

    @ResponseBody
    @RequestMapping(value = "/arrange", method = RequestMethod.POST)
    public ResponseMessage<Object> docArrange(@RequestBody List<SeqDTO> body
    ) {
        PERMS_MISSING.assertIsTrue(ShiroUtils.isPlatformOperator());
        final DocSeqInCategoryUpdateDTO updateParam = new DocSeqInCategoryUpdateDTO();
        updateParam.setOperator(ShiroUtils.getOperatorCode());
        updateParam.setOperType(OperTypeEnum.UPDATE);
        updateParam.setSequences(body);
        getDocMgrFacade().arrangeDocsInCategory(updateParam);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/publish", method = RequestMethod.POST)
    public ResponseMessage<Object> publishProductDoc(DocPublishRequest publishRequest
    ) {
        publishRequest.setOperator(ShiroUtils.getOperatorCode());
        getDocMgrFacade().publishProductDoc(publishRequest);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/check-for-publish", method = RequestMethod.POST)
    public ResponseMessage<Object> checkForPublish(Long docId
    ) {
        return new ResponseMessage<>("result", getDocMgrFacade().checkForPublish(docId));
    }

    @ResponseBody
    @RequestMapping(value = "/recover", method = RequestMethod.POST)
    public ResponseMessage<Object> docRecover(DocRecoverRequest docRecoverRequest
    ) {
        docRecoverRequest.setOperator(ShiroUtils.getOperatorCode());
        getDocMgrFacade().recover(docRecoverRequest);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/publish/list", method = RequestMethod.GET)
    public ResponseMessage<Object> docPublishList(@Validated DocPublishPageQueryParam param,
                                          @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                          @RequestParam(value = "_pageSize", required = false) Integer pageSize
    ) {
        param.setPageNo(pageNo);
        param.setPageSize(getConfigPageSize("doc-publish"));
        return new ResponseMessage<>("page", docQueryService.publishPageQuery(param));
    }

    @ResponseBody
    @RequestMapping(value = "/commons/category/list", method = RequestMethod.GET)
    public ResponseMessage<Object> docCategoryList(@RequestParam(value = "type", defaultValue = "DOC") DocCategoryTypeEnum type,
                                           @RequestParam(value = "scope", required = false) String scope,
                                           @RequestParam(value = "pid", defaultValue = "-1", required = false) Long pid) {
        final List<DocCategoryItemVO> docCategories = docQueryService.listDocCategories(type, pid);
        return new ResponseMessage<>("result", docCategories);
    }

    /**
     * 获取可引用页面列表
     *
     * @param keywords 搜索关键字
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "commons/page/list-for-ref", method = RequestMethod.GET)
    public ResponseMessage<Object> docPageListForRef(String keywords) {
        List<DocPageRefVO> result;
        if (StringUtils.isNotBlank(keywords)) {
            List<String> docNos = (List<String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_REF_DOCS_LIMIT);
            result = docQueryService.pageListForRef(new DocPageRefQueryParam().setDocNos(docNos).setKeywords(keywords));
        } else {
            result = Collections.emptyList();
        }
        return new ResponseMessage<>("result", result);
    }

    @ResponseBody
    @RequestMapping(value = "category/create", method = RequestMethod.POST)
    public ResponseMessage<Object> docCategoryCreate(@RequestBody DocCategoryRequest request) {
        getDocMgrFacade().createCategory(toCategoryDTO(request));
        return new ResponseMessage<>();
    }

    private DocCategoryDTO toCategoryDTO(DocCategoryRequest request) {
        DocCategoryDTO category = new DocCategoryDTO();
        category.setId(request.getId());
        category.setPid(request.getPid());
        if (StringUtils.isBlank(request.getScope())) {
            category.setScope(String.format(DocCategoryUtils.DOC_API_SCOPE_FORMAT, request.getDocNo()));
        } else {
            category.setScope(request.getScope());
        }
        if (null == request.getType()) {
            category.setType(DocCategoryTypeEnum.DOC_API);
        } else {
            category.setType(request.getType());
        }
        category.setCode(request.getCode());
        category.setName(request.getName());
        category.setDesc(request.getDesc());
        return category;
    }

    @ResponseBody
    @RequestMapping(value = "category/update", method = RequestMethod.POST)
    public ResponseMessage<Object> docCategoryUpdate(@RequestBody DocCategoryRequest request) {
        getDocMgrFacade().updateCategory(toCategoryDTO(request));
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "category/delete", method = RequestMethod.POST)
    public ResponseMessage<Object> docCategoryDelete(@RequestParam Long id) {
        getDocMgrFacade().deleteCategory(id);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "/faq/list", method = RequestMethod.GET)
    public ResponseMessage<Object> faqList(@Validated DocFaqPageQueryParam param,
                                   @RequestParam(value = "_pageNo", required = false) Integer pageNo
    ) {
        param.setPageNo(pageNo);
        param.setPageSize(getConfigPageSize("doc-faq"));
        return new ResponseMessage<>("page", docFaqQueryService.faqPageQuery(param));
    }

    @ResponseBody
    @RequestMapping(value = "faq/create", method = RequestMethod.POST)
    public ResponseMessage<Object> docFaqCreate(@RequestBody DocFaqCreateCmd cmd) {
        cmd.setOperDTO(getOperDTO());
        getDocFaqMgrFacade().create(cmd);
        return new ResponseMessage<>();
    }

    private OperDTO getOperDTO() {
        final OperDTO operDTO = new OperDTO();
        operDTO.setOperator(ShiroUtils.getOperatorCode());
        return operDTO;
    }

    @ResponseBody
    @RequestMapping(value = "faq/detail", method = RequestMethod.GET)
    public ResponseMessage<Object> docFaqDetail(@RequestParam Long id) {
        return new ResponseMessage<>("result", docFaqQueryService.faqDetail(id));
    }

    @ResponseBody
    @RequestMapping(value = "faq/acl", method = RequestMethod.GET)
    public ResponseMessage<Object> docFaqAcl(@RequestParam Long id) {
        return new ResponseMessage<>("result", docFaqQueryService.checkAcl(id));
    }

    @ResponseBody
    @RequestMapping(value = "faq/update", method = RequestMethod.POST)
    public ResponseMessage<Object> docFaqUpdate(@RequestBody DocFaqUpdateCmd cmd) {
        cmd.setOperDTO(getOperDTO());
        getDocFaqMgrFacade().update(cmd);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "faq/set-top", method = RequestMethod.POST)
    public ResponseMessage<Object> docFaqSetTop(@RequestBody DocFaqTopCmd cmd) {
        cmd.setOperDTO(getOperDTO());
        getDocFaqMgrFacade().setTop(cmd);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "faq/publish", method = RequestMethod.POST)
    public ResponseMessage<Object> docFaqPublish(@RequestBody IdBasedCmd cmd) {
        cmd.setOperDTO(getOperDTO());
        getDocFaqMgrFacade().publish(cmd);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "faq/delete", method = RequestMethod.POST)
    public ResponseMessage<Object> docFaqDelete(@RequestBody IdBasedCmd cmd) {
        cmd.setOperDTO(getOperDTO());
        getDocFaqMgrFacade().delete(cmd);
        return new ResponseMessage<>();
    }

    @ResponseBody
    @RequestMapping(value = "faq/list-for-relate", method = RequestMethod.GET)
    public ResponseMessage<Object> docFaqRelateList(@RequestParam String keywords) {
        return new ResponseMessage<>("data", docFaqQueryService.listForRelate(keywords));
    }

    /**
     * 获取页面负责人列表
     *
     * @param request 查询请求参数
     * @return 负责人列表
     */
    @ResponseBody
    @RequestMapping(value = "/page/owners", method = RequestMethod.GET)
    public ResponseMessage<Object> getPageOwners(@Validated PageOwnerQueryRequest request) {
        try {
            List<PageOwnerVO> owners = pageOwnerQueryService.getPageOwners(request.getPageId());
            return new ResponseMessage<>("result", owners);
        } catch (Exception e) {
            return new ResponseMessage<>(e);
        }
    }

    /**
     * 更新页面负责人
     *
     * @param request 更新请求参数
     * @return 更新结果
     */
    @ResponseBody
    @RequestMapping(value = "/page/owners/update", method = RequestMethod.POST)
    public ResponseMessage<Object> updatePageOwners(@RequestBody @Validated PageOwnerUpdateRequest request) {
        try {
            // 验证用户权限
            final ShiroRealm.ShiroUser shiroUser = ShiroUtils.getShiroUser();
            if (shiroUser == null) {
                PERMS_MISSING.assertIsTrue(false, "用户未登录");
            }

            // 转换请求对象为DTO
            PageOwnerUpdateDTO updateDTO = new PageOwnerUpdateDTO();
            updateDTO.setPageId(request.getPageId());
            updateDTO.setOwnerIds(request.getOwnerIds());
            updateDTO.setOperDTO(getOperDTO());
            
            // 调用facade服务更新负责人
            getPageOwnerFacade().updatePageOwners(updateDTO);
            
            return new ResponseMessage<>("负责人更新成功");
        } catch (Exception e) {
            return new ResponseMessage<>(e);
        }
    }

}
