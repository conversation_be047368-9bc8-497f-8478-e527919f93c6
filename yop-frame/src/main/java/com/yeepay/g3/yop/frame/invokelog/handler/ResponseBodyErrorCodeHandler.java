/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.frame.invokelog.handler;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.g3.facade.yop.sys.dto.ApiExtendConfigDTO;
import com.yeepay.g3.facade.yop.sys.enums.InvokeLogControlEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.frame.cache.ApiExtendConfigRefreshableLocalCache;
import com.yeepay.g3.yop.frame.context.Context;
import com.yeepay.g3.yop.frame.context.ContextUtils;
import com.yeepay.g3.yop.frame.definition.InvokeLogProto;
import com.yeepay.g3.yop.frame.invokelog.InvokeLogContext;
import com.yeepay.g3.yop.frame.invokelog.chain.InvokeLogHandlerChainExecutionControl;
import com.yeepay.g3.yop.frame.marshaller.YopMarshallerUtils;
import com.yeepay.g3.yop.frame.monitor.enumtype.MonitorOperationEnum;
import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.frame.response.ResponseType;
import com.yeepay.g3.yop.frame.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;


/**
 * title: <br>
 * description: 收集错误码
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/1/16 下午5:00
 */
@Component
public class ResponseBodyErrorCodeHandler extends AbstractInvokeLogHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResponseBodyErrorCodeHandler.class);

    @Autowired
    private ApiExtendConfigRefreshableLocalCache apiExtendConfigRefreshableLocalCache;

    @Override
    protected InvokeLogControlEnum getSupportField() {
        return InvokeLogControlEnum.SUCCESS;
    }

    @Override
    protected void doHandle(InvokeLogContext invokeLogContext, InvokeLogHandlerChainExecutionControl invokeLogHandlerChainExecutionControl) {
        Response response = invokeLogContext.getResponse();
        if (null == response) {
            invokeLogHandlerChainExecutionControl.handleNext();
            return;
        }
        try {
            if (ResponseType.EXCEPTION.equals(response.getResponseType())) {
                invokeLogHandlerChainExecutionControl.handleNext();
                return;
            }
            Context context = invokeLogContext.getContext();
            InvokeLogProto.InvokeLog.Builder builder = invokeLogContext.getBuilder();
            String responseBody = YopMarshallerUtils.marshal(response.getRawValue());
            if (StringUtils.isBlank(responseBody)) {
                LOGGER.info("返回的response数据为空,apiUri:{}", context.getApiContext().getApiUri());
                return;
            }
            if (!StringUtil.isJsonObj(responseBody)) {
                LOGGER.info("返回的response数据不为jsonObj格式,apiUri:{},response:{}", context.getApiContext().getApiUri(), responseBody);
                return;
            }
            String apiId = ContextUtils.getApiId(context);
            builder.setApiId(apiId);
            ApiExtendConfigDTO apiExtendConfigDTO = apiExtendConfigRefreshableLocalCache.get(apiId);
            if (apiExtendConfigDTO != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode node = objectMapper.readTree(responseBody);
                String errorCode = node.path(apiExtendConfigDTO.getErrorCodeLocation()).asText();
                if (!isRequestSuccess(apiExtendConfigDTO, errorCode)) {
                    String message = node.path(apiExtendConfigDTO.getMessageLocation()).asText();
                    builder.setBizErrorCode(errorCode);
                    builder.setBizErrorMessage(message);
                    builder.setBizStatus("FAIL");
                } else {
                    builder.setBizStatus("SUCCESS");
                }
            }
            Map<String, Object> bizInvokeInfo = new HashMap<>();
            bizInvokeInfo.put("bizErrorCode", StringUtils.defaultString(builder.getBizErrorCode()));
            bizInvokeInfo.put("bizErrorMessage", StringUtils.defaultString(builder.getBizErrorMessage()));
            bizInvokeInfo.put("bizStatus", StringUtils.defaultString(builder.getBizStatus()));
            MonitorOperationEnum.BIZ_INVOKE_INFO.log("", context, bizInvokeInfo);
        } catch (Exception e) {
            LOGGER.error("错误码日志处理异常", e);
        }
        invokeLogHandlerChainExecutionControl.handleNext();
    }

    private boolean isRequestSuccess(ApiExtendConfigDTO apiExtendConfigDTO, String errorCode) {
        if (StringUtils.isNotBlank(apiExtendConfigDTO.getRequestSuccessValue())) {
            return StringUtils.equals(errorCode, apiExtendConfigDTO.getRequestSuccessValue());
        } else {
            return errorCode == null;
        }
    }

}
