package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.google.common.collect.Maps;
import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;
import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;
import com.yeepay.g3.facade.yop.sys.dto.SpiDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.*;
import com.yeepay.g3.facade.yop.sys.dto.models.Content;
import com.yeepay.g3.facade.yop.sys.dto.models.Header;
import com.yeepay.g3.facade.yop.sys.dto.models.Parameter;
import com.yeepay.g3.facade.yop.sys.dto.models.RequestBody;
import com.yeepay.g3.facade.yop.sys.dto.spi.SpiBasicDTO;
import com.yeepay.g3.facade.yop.sys.dto.spi.SpiRequestDTO;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import io.swagger.v3.core.util.Json;
import io.swagger.v3.oas.models.media.Schema;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-13 19:04
 */
public class ImportCompareUtils {


    public static boolean isEqualApi(ApiDTO src, ApiDTO target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        return isEqualApiBasic(src.getBasic(), target.getBasic())
                && isEqualApiRequest(src.getRequest(), target.getRequest())
                && isEqualApiResponse(src.getResponse(), target.getResponse())
                && isEqualCollection(src.getCallbacks(), target.getCallbacks())
                && isEqualCollection(src.getDirectedRefModels(), target.getDirectedRefModels())
                && isEqualCollection(src.getSensitiveVariables(), target.getSensitiveVariables());
    }

    private static boolean isEqualApiBasic(ApiBasicDTO src, ApiBasicDTO target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        return StringUtils.equals(src.getName(), target.getName())
                && StringUtils.equals(src.getTitle(), target.getTitle())
                && src.getApiType() == target.getApiType()
                && StringUtils.equals(src.getApiGroup(), target.getApiGroup())
                && StringUtils.equals(src.getDescription(), target.getDescription())
                && isEqualApiSecurity(src.getSecurity(), target.getSecurity())
                && isEqualApiOptions(src.getOptionsRule(), target.getOptionsRule());
    }


    private static boolean isEqualApiSecurity(ApiSecurityReq src, ApiSecurityReq target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        return (BooleanUtils.isTrue(src.getInherited()) == BooleanUtils.isTrue(target.getInherited()))
                && isEqualCollection(src.getSecurityReqs(), target.getSecurityReqs());
    }

    private static boolean isEqualApiOptions(List<ApiOption> src, List<ApiOption> target) {
        if (CollectionUtils.isEmpty(src) && CollectionUtils.isEmpty(target)) {
            return true;
        }
        if (src == null) {
            return false;
        }
        return src.equals(target);
    }

    private static boolean isEqualApiRequest(ApiRequestDTO src, ApiRequestDTO target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        return src.getParameterHandlingType() == target.getParameterHandlingType()
                && StringUtils.equals(src.getPath(), target.getPath())
                && StringUtils.equals(src.getHttpMethod(), target.getHttpMethod())
                && isEqualParameters(src.getParameters(), target.getParameters())
                && isEqualRequestBody(src.getRequestBody(), target.getRequestBody())
                && Boolean.TRUE.equals(src.getEncrypt()) == Boolean.TRUE.equals(target.getEncrypt());
    }

    private static boolean isEqualParameters(List<Parameter> src, List<Parameter> target) {
        if (CollectionUtils.isEmpty(src) && CollectionUtils.isEmpty(target)) {
            return true;
        }
        if (CollectionUtils.isEmpty(src) || CollectionUtils.isEmpty(target)) {
            return false;
        }
        if (src.size() != target.size()) {
            return false;
        }
        Map<String, Parameter> srcParameterMapping = Maps.newHashMapWithExpectedSize(src.size());
        src.forEach(parameter -> srcParameterMapping.put(parameter.getName(), parameter));
        for (Parameter parameter : target) {
            Parameter srcParameter = srcParameterMapping.get(parameter.getName());
            if (srcParameter == null || !isEqualParameter(srcParameter, parameter)) {
                return false;
            }
        }
        return true;
    }

    private static boolean isEqualParameter(Parameter src, Parameter target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        return StringUtils.equals(src.getName(), target.getName())
                && src.getLocation() == target.getLocation()
                && StringUtils.equals(src.getDescription(), target.getDescription())
                && Objects.equals(src.getRequired(), target.getRequired())
                && Objects.equals(src.getDeprecated(), target.getDeprecated())
                && isEqualSchemaJsonStr(src.getSchema(), target.getSchema())
                && isEqualCollection(src.getExamples(), target.getExamples())
                && StringUtils.equals(src.getBackendName(), target.getBackendName())
                && src.getBackendLocation() == target.getBackendLocation();
    }

    private static boolean isEqualApiResponse(ApiResponseDTO src, ApiResponseDTO target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }

        return StringUtils.equals(src.getHttpCode(), target.getHttpCode())
                && StringUtils.equals(src.getContentType(), target.getContentType())
                && isEqualHeaders(src.getHeaders(), target.getHeaders())
                && isEqualContent(src.getContent(), target.getContent())
                && Boolean.TRUE.equals(src.getEncrypt()) == Boolean.TRUE.equals(target.getEncrypt());
    }

    private static boolean isEqualHeaders(List<Header> src, List<Header> target) {
        if (CollectionUtils.isEmpty(src) && CollectionUtils.isEmpty(target)) {
            return true;
        }
        if (CollectionUtils.isEmpty(src) || CollectionUtils.isEmpty(target)) {
            return false;
        }
        if (src.size() != target.size()) {
            return false;
        }
        Map<String, Header> srcHeaderMapping = Maps.newHashMapWithExpectedSize(src.size());
        src.forEach(header -> srcHeaderMapping.put(header.getName(), header));
        for (Header header : target) {
            Header srcHeader = srcHeaderMapping.get(header.getName());
            if (srcHeader == null || !isEqualHeader(srcHeader, header)) {
                return false;
            }
        }
        return true;
    }

    private static boolean isEqualHeader(Header src, Header target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        return StringUtils.equals(src.getName(), target.getName())
                && StringUtils.equals(src.getDescription(), target.getDescription())
                && Objects.equals(src.getDeprecated(), target.getDeprecated())
                && isEqualSchemaJsonStr(src.getSchema(), target.getSchema())
                && isEqualCollection(src.getExamples(), target.getExamples());
    }

    public static boolean isEqualSpi(SpiDTO src, SpiDTO target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        return isEqualSpiBasic(src.getBasic(), target.getBasic())
                && isEqualSpiRequest(src.getRequest(), target.getRequest())
                && isEqualCollection(src.getDirectedRefModels(), target.getDirectedRefModels());
    }

    private static boolean isEqualSpiBasic(SpiBasicDTO src, SpiBasicDTO target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        return StringUtils.equals(src.getName(), target.getName())
                && StringUtils.equals(src.getTitle(), target.getTitle())
                && src.getSpiType() == target.getSpiType()
                && StringUtils.equals(src.getApiGroup(), target.getApiGroup())
                && StringUtils.equals(src.getDescription(), target.getDescription());
    }

    private static boolean isEqualSpiRequest(SpiRequestDTO src, SpiRequestDTO target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        return StringUtils.equals(src.getRequestUrl(), target.getRequestUrl())
                && StringUtils.equals(src.getHttpMethod(), target.getHttpMethod())
                && isEqualRequestBody(src.getRequestBody(), target.getRequestBody());
    }

    private static boolean isEqualRequestBody(RequestBody src, RequestBody target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        return StringUtils.equals(src.getDescription(), target.getDescription())
                && isEqualContents(src.getContents(), target.getContents());
    }

    private static boolean isEqualContents(Map<String, Content> src, Map<String, Content> target) {
        if (MapUtils.isEmpty(src) && MapUtils.isEmpty(target)) {
            return true;
        }
        if (MapUtils.isEmpty(src) || MapUtils.isEmpty(target)) {
            return false;
        }
        if (src.size() != target.size()) {
            return false;
        }
        for (Map.Entry<String, Content> entry : src.entrySet()) {
            String key = entry.getKey();
            Content value = entry.getValue();
            Content targetValue = target.get(key);
            if (targetValue == null || !isEqualContent(value, targetValue)) {
                return false;
            }
        }
        return true;
    }

    private static boolean isEqualContent(Content src, Content target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }

        return isEqualSchemaJsonStr(src.getSchema(), target.getSchema())
                && isEqualCollection(src.getExamples(), target.getExamples());
    }

    public static boolean isEqualModel(ModelDTO src, ModelDTO target) {
        if (src == target) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        return StringUtils.equals(src.getName(), target.getName())
                && StringUtils.equals(src.getApiGroup(), target.getApiGroup())
                && isEqualSchemaJsonStr(src.getSchema(), target.getSchema())
                && StringUtils.equals(src.getDescription(), target.getDescription())
                && isEqualCollection(src.getSensitiveVariables(), target.getSensitiveVariables())
                && isEqualCollection(src.getRefModels(), target.getRefModels());

    }


    private static boolean isEqualSchemaJsonStr(String src, String target) {
        if (StringUtils.equals(src, target)) {
            return true;
        }
        if (src == null || target == null) {
            return false;
        }
        Schema srcJsonNode, targetJsonNode;
        try {
            srcJsonNode = Json.mapper().readValue(src, Schema.class);
        } catch (IOException e) {
            throw new YeepayRuntimeException("illegal schema json str, value:{0}.", src);
        }
        try {
            targetJsonNode = Json.mapper().readValue(target, Schema.class);
        } catch (IOException e) {
            throw new YeepayRuntimeException("illegal schema json str, value:{0}.", target);
        }
        return srcJsonNode.equals(targetJsonNode);
    }

    //null和空集合equal
    private static boolean isEqualCollection(final Collection<?> src, final Collection<?> target) {
        if (CollectionUtils.isEmpty(src) && CollectionUtils.isEmpty(target)) {
            return true;
        }
        if (CollectionUtils.isEmpty(src) || CollectionUtils.isEmpty(target)) {
            return false;
        }
        return CollectionUtils.isEqualCollection(src, target);
    }

}
