package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.boot.mq.MessageProducer;
import com.yeepay.boot.mq.MessageRouteGenerator;
import com.yeepay.g3.app.yop.portal.dto.AclLogDTO;
import com.yeepay.g3.app.yop.portal.enums.AclOperStatusEnum;
import com.yeepay.g3.app.yop.portal.exception.PortalExceptionEnum;
import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.ThreadContextUtil;
import com.yeepay.g3.core.yop.utils.Exceptions;
import com.yeepay.g3.core.yop.utils.NetworkUtils;
import com.yeepay.g3.core.yop.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.web.filter.PathMatchingFilter;
import org.apache.shiro.web.servlet.ShiroHttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;


/**
 * title:日志过滤器 <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2016<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/8/1 11:14
 */
@Component
public class AclLogFilter extends PathMatchingFilter implements CustomShiroFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(AclLogFilter.class);

    @Value("${mq.aclLogPersisTopic}")
    private final String aclLogPersisTopic = "acl_log_persis";
    private static final String EXCEPTION = "org.springframework.boot.autoconfigure.web.DefaultErrorAttributes.ERROR";

    private static final String REQUEST_CONTENT = "requestContent";

    private final JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();

    private MessageProducer messageProducer;
    private MessageRouteGenerator messageRouteGenerator;

    private Date operDate;

    private ThreadLocal<StopWatch> stopWatchThreadLocal = new ThreadLocal<StopWatch>() {
        @Override
        protected StopWatch initialValue() {
            return new StopWatch();
        }
    };

    @Override
    protected boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        operDate = new Date();
        stopWatchThreadLocal.get().reset();
        stopWatchThreadLocal.get().start();

        // 方便 Controller 获取操作员信息
        // 方便频控组件直接获取 _operatorCode
        if (ShiroUtils.getSubject().isAuthenticated()) {
            request.getParameterMap().put("_operatorCode", new String[]{ShiroUtils.getOperatorCode()});
        }

        return super.preHandle(request, response);
    }

    @Override
    protected void postHandle(ServletRequest request, ServletResponse response) throws Exception {
        super.postHandle(request, response);
        StopWatch stopWatch = stopWatchThreadLocal.get();
        stopWatch.stop();
    }

    @Override
    public void afterCompletion(ServletRequest request, ServletResponse response, Exception exception) throws Exception {
        if (SecurityUtils.getSubject().isAuthenticated()) {
            ShiroRealm.ShiroUser user = (ShiroRealm.ShiroUser) SecurityUtils.getSubject().getPrincipal();
            AclLogDTO aclLogDTO = new AclLogDTO();
            aclLogDTO.setOperatorCode(user.getUserId());
            aclLogDTO.setUrl(((HttpServletRequest) request).getRequestURI());
            aclLogDTO.setRequestIp(NetworkUtils.getRemoteHost((HttpServletRequest) request));
            String method = ((HttpServletRequest) request).getMethod();
            aclLogDTO.setRequestMethod(method);
            aclLogDTO.setRequestContent((String) request.getAttribute(REQUEST_CONTENT));
            aclLogDTO.setLatency((int) stopWatchThreadLocal.get().getTime());
            String guid = ThreadContextUtil.getContext().getThreadUID();
            aclLogDTO.setGuid(guid);
            Exception e = (Exception) request.getAttribute(EXCEPTION);
            if (e != null) {
                aclLogDTO.setStatus(AclOperStatusEnum.UNKNOWN);
                String errorMessage = StringUtils.substring(Exceptions.getMessage(e), 0, 500);
                aclLogDTO.setDetail(errorMessage);
            } else {
                int status = ((ShiroHttpServletResponse) response).getStatus();
                switch (status) {
                    case 200:
                        aclLogDTO.setStatus(AclOperStatusEnum.SUCCESS);
                        break;
                    case 401:
                        aclLogDTO.setStatus(AclOperStatusEnum.UNAUTHORIZED);
                        break;
                    case 403:
                        aclLogDTO.setStatus(AclOperStatusEnum.FORBIDDEN);
                        break;
                    case 404:
                        aclLogDTO.setStatus(AclOperStatusEnum.NOT_FOUND);
                        break;
                    default:
                        aclLogDTO.setDetail(String.valueOf(status));
                        aclLogDTO.setStatus(AclOperStatusEnum.UNKNOWN);
                }
            }
            aclLogDTO.setOperDatetime(operDate);
            LOGGER.info("Log operation,operator is {} ,url is {}", aclLogDTO.getOperatorCode(), aclLogDTO.getUrl());

            try {
                boolean sendSuccess = messageProducer.send(messageRouteGenerator.generate(aclLogPersisTopic, null), aclLogDTO);
                if (!sendSuccess) {
                    LOGGER.error("send to rocket mq error");
                    throw PortalExceptionEnum.UNKNOWN_ERROR.newException();
                }
            } catch (Exception e1) {
                LOGGER.error("send to rocket mq error", e1);
                throw PortalExceptionEnum.UNKNOWN_ERROR.newException();
            }
        }
        super.afterCompletion(request, response, exception);
    }

    @Override
    public String shiroName() {
        return "log";
    }

    @Autowired
    public void setMessageProducer(MessageProducer messageProducer) {
        this.messageProducer = messageProducer;
    }

    @Autowired
    public void setMessageRouteGenerator(MessageRouteGenerator messageRouteGenerator) {
        this.messageRouteGenerator = messageRouteGenerator;
    }
}
