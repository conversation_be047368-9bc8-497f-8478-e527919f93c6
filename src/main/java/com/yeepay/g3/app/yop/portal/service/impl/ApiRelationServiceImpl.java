/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.g3.app.yop.portal.exception.PortalExceptionEnum;
import com.yeepay.g3.app.yop.portal.service.ApiQueryService;
import com.yeepay.g3.app.yop.portal.service.ApiRelationService;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.ApiRelationVO;
import com.yeepay.g3.app.yop.portal.vo.SimpleApiVO;
import com.yeepay.g3.app.yop.portal.vo.page.ApiDefinePageItem;
import com.yeepay.g3.facade.yop.sys.dto.ApiRelationDTO;
import com.yeepay.g3.facade.yop.sys.enums.ApiRelationTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.ApiRelationFacade;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/20 2:14 下午
 */
@Component
@Slf4j
public class ApiRelationServiceImpl implements ApiRelationService {

    private final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();
    @Resource(name = "apiRelationQueryService")
    private QueryService queryService;
    @Autowired
    private ApiQueryService apiQueryService;

    private ApiRelationFacade getApiRelationFacade() {
        return RemoteServiceFactory.getService(ApiRelationFacade.class);
    }

    @Override
    public void create(ApiRelationVO apiRelationVO) {
        getApiRelationFacade().create(convert(apiRelationVO));
    }

    @Override
    public void delete(Long id) {
        getApiRelationFacade().delete(id);
    }

    @Override
    public void edit(ApiRelationVO apiRelationVO) {
        getApiRelationFacade().update(convert(apiRelationVO));
    }

    @Override
    public ApiRelationVO detail(Long id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id", id);
        Map map = queryService.queryUnique("findById", param, true);
        ApiRelationVO result = new ApiRelationVO();
        result.setId(id);
        result.setApiId((String) map.get("api_id"));
        result.setRelateType((String) map.get("relate_type"));
        result.setVersion((Long) map.get("version"));
        result.setRelateApi(apiQueryService.findByApiId((String) map.get("relate_id")));
        try {
            result.setRelateConfig(objectMapper.readValue((String) map.get("relate_config"), Map.class));
        } catch (Exception e) {
            throw PortalExceptionEnum.UNKNOWN_ERROR.newException();
        }

        return result;
    }

    @Override
    public List<ApiRelationVO> list(String apiId) {
        Map<String, Object> param = new HashMap<>();
        param.put("apiId", apiId);
        List<Map> list = queryService.query("list", param);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }
        List<ApiRelationVO> result = new ArrayList<>(list.size());
        list.forEach(map -> {
            ApiRelationVO apiRelationVO = new ApiRelationVO();
            apiRelationVO.setApiId((String) map.get("api_id"));
            apiRelationVO.setRelateType((String) map.get("relate_type"));
            apiRelationVO.setId((Long) map.get("id"));
            apiRelationVO.setRelateApi(apiQueryService.findByApiId((String) map.get("relate_id")));
            result.add(apiRelationVO);
        });
        return result;
    }

    @Override
    public List<SimpleApiVO> apis(String apiGroup, String apiId) {
        String[] apiGroups = new String[1];
        apiGroups[0] = apiGroup;
        List<ApiDefinePageItem> apiDefinePageItems = apiQueryService.listApis(apiGroups);
        if (CollectionUtils.isEmpty(apiDefinePageItems)) {
            return Collections.EMPTY_LIST;
        }

        Map<String, Object> relateApiParam = new HashMap<>();
        relateApiParam.put("apiId", apiId);
        relateApiParam.put("relateType", ApiRelationTypeEnum.ORDER_QUERY.name());
        List<Map> relateApi = queryService.query("listRelateId", relateApiParam);
        List<String> relateApiIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relateApi)) {
            relateApi.forEach(map -> {
                relateApiIds.add((String) map.get("relate_id"));
            });
        }
        List<SimpleApiVO> result = new ArrayList<>();
        apiDefinePageItems.forEach(apiDefinePageItem -> {
            if (!relateApiIds.contains(apiDefinePageItem.getApiIdV2())) {
                SimpleApiVO simpleApiVO = new SimpleApiVO();
                simpleApiVO.setApiId(apiDefinePageItem.getApiIdV2());
                simpleApiVO.setApiUri(apiDefinePageItem.getApiUri());
                simpleApiVO.setApiTitle(apiDefinePageItem.getApiTitle());
                // 存在一些脏数据不存在接口名称
                if (StringUtils.isNotEmpty(simpleApiVO.getApiTitle())) {
                    result.add(simpleApiVO);
                }
            }
        });
        return result;
    }

    private ApiRelationDTO convert(ApiRelationVO apiRelationVO) {
        ApiRelationDTO apiRelationDTO = new ApiRelationDTO();
        apiRelationDTO.setApiId(apiRelationVO.getApiId());
        apiRelationDTO.setRelateType(Enum.valueOf(ApiRelationTypeEnum.class, apiRelationVO.getRelateType()));
        apiRelationDTO.setRelateId(apiRelationVO.getRelateApi().getApiId());
        apiRelationDTO.setRelateConfig(apiRelationVO.getRelateConfig());
        apiRelationDTO.setId(apiRelationVO.getId());
        apiRelationDTO.setVersion(apiRelationVO.getVersion());
        return apiRelationDTO;
    }
}
