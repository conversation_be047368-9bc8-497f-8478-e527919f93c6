/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeepay.g3.facade.yop.sys.dto.CertDTO;
import com.yeepay.g3.facade.yop.sys.dto.CertSpec;
import com.yeepay.g3.facade.yop.sys.dto.SymmetricCertSpec;
import com.yeepay.g3.facade.yop.sys.dto.UnSymmetricCertSpec;
import com.yeepay.g3.facade.yop.sys.enums.CertTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.CertUsageEnum;
import com.yeepay.g3.facade.yop.sys.facade.CertFacade;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 19/05/23 10:12
 */
@Component("regTestAppIdEncryptPriKeyLocalCache")
public class RegTestAppIdEncryptPriKeyLocalCache {

    private static final Logger LOGGER = LoggerFactory.getLogger(RegTestAppIdEncryptPriKeyLocalCache.class);

    public static final int CACHE_MAX_SIZE = 500;
    public static final int CACHE_EXPIRE_AFTER_WRITE = 5;

    private CertFacade certFacade = RemoteServiceFactory.getService(CertFacade.class);

    private LoadingCache<String, String> regTestAppIdEncryptPriKeyLocalCache = CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE)
            .expireAfterWrite(CACHE_EXPIRE_AFTER_WRITE, TimeUnit.MINUTES)
            .recordStats()
            .build(getCacheLoader());

    private CacheLoader getCacheLoader() {
        return new CacheLoader<String, String>() {
            @Override
            public String load(String key) throws Exception {
                LOGGER.info("load appid encrypt private key by appid:{}.", key);
                List<CertDTO> certs = certFacade.findByCondition(key, CertTypeEnum.AES256, CertUsageEnum.API_INVOKE_CIPHER);
                CertDTO cert = null;
                for (CertDTO certDTO : certs) {
                    if (certDTO.isActive()) {
                        cert = certDTO;
                        break;
                    }
                }
                if (null == cert) {
                    throw new IllegalArgumentException("回归测试应用" + key + "加密密钥未配置");
                }
                CertSpec certSpec = cert.getCertSpec();
                if (CertTypeEnum.AES256.isSymmetric()) {
                    return ((SymmetricCertSpec) certSpec).getSecretKey();
                } else {
                    return ((UnSymmetricCertSpec) certSpec).getPriKey();
                }
            }
        };
    }

    public String get(String key) {
        try {
            return regTestAppIdEncryptPriKeyLocalCache.get(key);
        } catch (Exception e) {
            LOGGER.error("error when get local cache", e);
            throw new YeepayRuntimeException(e.getMessage());
        }
    }

}
