package com.yeepay.g3.app.yop.portal.vo.page;

import java.util.Date;

/**
 * ApiRouteDeployRecordPageItem
 */

public class ApiRouteDeployRecordQueryParam extends BasePageQueryParam {

  private static final long serialVersionUID = -1L;

  private String opType;

  private Date createdStartDate;

  private Date createdEndDate;

  private String operator;

  private String apiId;

  public String getOpType() {
    return opType;
  }

  public void setOpType(String opType) {
    this.opType = opType;
  }

  public Date getCreatedStartDate() {
    return createdStartDate;
  }

  public void setCreatedStartDate(Date createdStartDate) {
    this.createdStartDate = createdStartDate;
  }

  public Date getCreatedEndDate() {
    return createdEndDate;
  }

  public void setCreatedEndDate(Date createdEndDate) {
    this.createdEndDate = createdEndDate;
  }

  public String getOperator() {
    return operator;
  }

  public void setOperator(String operator) {
    this.operator = operator;
  }

  public String getApiId() {
    return apiId;
  }

  public void setApiId(String apiId) {
    this.apiId = apiId;
  }

  public ApiRouteDeployRecordQueryParam withOpType(String opType) {
    this.opType = opType;
    return this;
  }

  public ApiRouteDeployRecordQueryParam withCreatedStartDate(Date createdStartDate) {
    this.createdStartDate = createdStartDate;
    return this;
  }

  public ApiRouteDeployRecordQueryParam withCreatedEndDate(Date createdEndDate) {
    this.createdEndDate = createdEndDate;
    return this;
  }

  public ApiRouteDeployRecordQueryParam withOperator(String operator) {
    this.operator = operator;
    return this;
  }

  public ApiRouteDeployRecordQueryParam withPageNo(Integer pageNo) {
    super.setPageNo(pageNo);
    return this;
  }

  public ApiRouteDeployRecordQueryParam withPageSize(Integer pageSize) {
    super.setPageSize(pageSize);
    return this;
  }

  public ApiRouteDeployRecordQueryParam withApiId(String apiId) {
    this.apiId = apiId;
    return this;
  }

}
