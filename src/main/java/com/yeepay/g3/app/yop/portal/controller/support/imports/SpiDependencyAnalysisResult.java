package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;
import com.yeepay.g3.facade.yop.sys.dto.SpiDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title: Spi倚赖分析结果<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 15:16
 */
public class SpiDependencyAnalysisResult {

    private List<SpiDependencyInfo> dependencyInfos;

    private Map<String, SpiDTO> spis;

    private Map<String, ModelDTO> models;

    private List<String> unusedModels;

    public List<SpiDependencyInfo> getDependencyInfos() {
        return dependencyInfos;
    }

    public void setDependencyInfos(List<SpiDependencyInfo> dependencyInfos) {
        this.dependencyInfos = dependencyInfos;
    }

    public SpiDependencyAnalysisResult withDependencyInfos(List<SpiDependencyInfo> dependencyInfos) {
        this.dependencyInfos = dependencyInfos;
        return this;
    }

    public SpiDependencyAnalysisResult addDependencyInfo(SpiDependencyInfo dependencyInfo) {
        if (this.dependencyInfos == null) {
            this.dependencyInfos = new ArrayList<>();
        }
        this.dependencyInfos.add(dependencyInfo);
        return this;
    }

    public Map<String, SpiDTO> getSpis() {
        return spis;
    }

    public void setSpis(Map<String, SpiDTO> spis) {
        this.spis = spis;
    }

    public SpiDependencyAnalysisResult withSpis(Map<String, SpiDTO> spis) {
        this.spis = spis;
        return this;
    }

    public Map<String, ModelDTO> getModels() {
        return models;
    }

    public void setModels(Map<String, ModelDTO> models) {
        this.models = models;
    }

    public SpiDependencyAnalysisResult withModels(Map<String, ModelDTO> models) {
        this.models = models;
        return this;
    }

    public List<String> getUnusedModels() {
        return unusedModels;
    }

    public void setUnusedModels(List<String> unusedModels) {
        this.unusedModels = unusedModels;
    }

    public SpiDependencyAnalysisResult withUnusedModels(List<String> unusedModels) {
        this.unusedModels = unusedModels;
        return this;
    }
}
