/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import com.yeepay.g3.app.yop.portal.dto.EndServiceDTO;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.ApiManageVO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/5/26 11:54 上午
 */
public class ClassLoaderServiceTest extends SpringBootTests {
    @Autowired
    private ClassLoaderService classLoaderService;

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();

    @Before
    public void before() {
    }

    @Test
    public void testLoadEndService() {
        String swaggerJson = "{\"swagger\":\"2.0\",\"paths\":{\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/baseType/1\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO baseType(byte b,short s,int i,long l,boolean bo,float f,double d,char c)\",\"operationId\":\"baseType\",\"parameters\":[{\"name\":\"b\",\"in\":\"query\",\"required\":true,\"type\":\"string\",\"format\":\"byte\"},{\"name\":\"s\",\"in\":\"query\",\"required\":true,\"type\":\"integer\",\"format\":\"int32\"},{\"name\":\"i\",\"in\":\"query\",\"required\":true,\"type\":\"integer\",\"format\":\"int32\"},{\"name\":\"l\",\"in\":\"query\",\"required\":true,\"type\":\"integer\",\"format\":\"int64\"},{\"name\":\"bo\",\"in\":\"query\",\"required\":true,\"type\":\"boolean\"},{\"name\":\"f\",\"in\":\"query\",\"required\":true,\"type\":\"number\",\"format\":\"float\"},{\"name\":\"d\",\"in\":\"query\",\"required\":true,\"type\":\"number\",\"format\":\"double\"},{\"name\":\"c\",\"in\":\"query\",\"required\":true,\"type\":\"string\"}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestResDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/generic/2\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestListGenericDTO generic(com.yeepay.g3.facade.yop.sys.dto.TestListGenericDTO dto)\",\"operationId\":\"generic\",\"parameters\":[{\"in\":\"formData\",\"name\":\"dto\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestListGenericDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestListGenericDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/generic/3\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestGenericDTO generic(com.yeepay.g3.facade.yop.sys.dto.TestGenericDTO testGenericDTO)\",\"operationId\":\"generic\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testGenericDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestGenericDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestGenericDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/hessian/4\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO hessian(com.yeepay.g3.facade.yop.sys.dto.TestDTO testDTO,java.lang.String string,java.lang.Byte b,java.lang.Short s,java.lang.Integer i,java.lang.Long l,java.lang.Float f,java.lang.Double d,java.lang.Boolean bo,java.lang.Character c,[Ljava.lang.String; array,java.lang.String constant,java.lang.String internal,java.util.Date date,java.math.BigDecimal bigDecimal,java.time.LocalDate localDate,java.time.LocalTime localTime,java.time.LocalDateTime localDateTime)\",\"operationId\":\"hessian\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"}},{\"name\":\"string\",\"in\":\"query\",\"required\":false,\"type\":\"string\"},{\"name\":\"b\",\"in\":\"query\",\"required\":false,\"type\":\"string\",\"format\":\"byte\"},{\"name\":\"s\",\"in\":\"query\",\"required\":false,\"type\":\"integer\",\"format\":\"int32\"},{\"name\":\"i\",\"in\":\"query\",\"required\":false,\"type\":\"integer\",\"format\":\"int32\"},{\"name\":\"l\",\"in\":\"query\",\"required\":false,\"type\":\"integer\",\"format\":\"int64\"},{\"name\":\"f\",\"in\":\"query\",\"required\":false,\"type\":\"number\",\"format\":\"float\"},{\"name\":\"d\",\"in\":\"query\",\"required\":false,\"type\":\"number\",\"format\":\"double\"},{\"name\":\"bo\",\"in\":\"query\",\"required\":false,\"type\":\"boolean\"},{\"name\":\"c\",\"in\":\"query\",\"required\":false,\"type\":\"string\"},{\"in\":\"formData\",\"name\":\"array\",\"required\":false,\"schema\":{\"type\":\"array\",\"items\":{\"type\":\"string\"}}},{\"name\":\"constant\",\"in\":\"query\",\"required\":false,\"type\":\"string\"},{\"name\":\"internal\",\"in\":\"query\",\"required\":false,\"type\":\"string\"},{\"name\":\"date\",\"in\":\"query\",\"required\":false,\"type\":\"string\",\"format\":\"date-time\"},{\"name\":\"bigDecimal\",\"in\":\"query\",\"required\":false,\"type\":\"number\"},{\"name\":\"localDate\",\"in\":\"query\",\"required\":false,\"type\":\"string\",\"format\":\"date\"},{\"in\":\"formData\",\"name\":\"localTime\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/java.time.LocalTime\"}},{\"name\":\"localDateTime\",\"in\":\"query\",\"required\":false,\"type\":\"string\",\"format\":\"date-time\"}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestResDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/httpGet/5\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO httpGet(com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO testQueryDTO,com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO headerParamDTO)\",\"operationId\":\"httpGet\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testQueryDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\"}},{\"in\":\"formData\",\"name\":\"headerParamDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestResDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/httpJson/6\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO httpJson(com.yeepay.g3.facade.yop.sys.dto.TestDTO testDTO,com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO headerParamDTO)\",\"operationId\":\"httpJson\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"}},{\"in\":\"formData\",\"name\":\"headerParamDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestResDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/httpPost/7\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO httpPost(com.yeepay.g3.facade.yop.sys.dto.TestFormDTO testFormDTO,com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO headerParamDTO)\",\"operationId\":\"httpPost\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testFormDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\"}},{\"in\":\"formData\",\"name\":\"headerParamDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestResDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/notify/8\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"java.lang.String notify(com.yeepay.g3.facade.yop.sys.dto.TestDTO testDTO)\",\"operationId\":\"notify\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"type\":\"string\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/stringResult/9\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"java.lang.String stringResult(java.lang.String string)\",\"operationId\":\"stringResult\",\"parameters\":[{\"name\":\"string\",\"in\":\"query\",\"required\":false,\"type\":\"string\"}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"type\":\"string\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/update/10\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO update(com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO testUploadDTO)\",\"operationId\":\"update\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testUploadDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO\"},\"headers\":{}}}}}},\"definitions\":{\"HeaderParamDTO\":{\"type\":\"object\",\"properties\":{\"appId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"customerNo\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"userId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"productCode\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestDate\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"requestIp\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestSource\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"contentSHA256\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"}}},\"LocalTime\":{\"type\":\"object\",\"properties\":{\"hour\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"minute\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"second\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"nano\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"int\"}}},\"TestDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"},\"testDTO1\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO1\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO1\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"localDate\":{\"type\":\"string\",\"format\":\"date\",\"description\":\"java.time.LocalDate\"},\"localTime\":{\"description\":\"java.time.LocalTime\",\"$ref\":\"#/definitions/java.time.LocalTime\"},\"localDateTime\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.time.LocalDateTime\"}}},\"TestDTO1\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO2\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO2\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO2\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"}}},\"TestDTO2\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"}}},\"TestFormDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\"}}},\"TestQueryDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\"}}},\"TestResDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"},\"testQueryDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\"},\"testFormDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\"},\"headerParamDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"localDate\":{\"type\":\"string\",\"format\":\"date\",\"description\":\"java.time.LocalDate\"},\"localTime\":{\"description\":\"java.time.LocalTime\",\"$ref\":\"#/definitions/java.time.LocalTime\"},\"localDateTime\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.time.LocalDateTime\"}}},\"com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\":{\"type\":\"object\",\"properties\":{\"appId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"customerNo\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"userId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"productCode\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestDate\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"requestIp\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestSource\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"contentSHA256\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"},\"testDTO1\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO1\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO1\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"localDate\":{\"type\":\"string\",\"format\":\"date\",\"description\":\"java.time.LocalDate\"},\"localTime\":{\"description\":\"java.time.LocalTime\",\"$ref\":\"#/definitions/java.time.LocalTime\"},\"localDateTime\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.time.LocalDateTime\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestDTO1\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO2\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO2\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO2\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestDTO2\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"},\"testQueryDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\"},\"testFormDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\"},\"headerParamDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"localDate\":{\"type\":\"string\",\"format\":\"date\",\"description\":\"java.time.LocalDate\"},\"localTime\":{\"description\":\"java.time.LocalTime\",\"$ref\":\"#/definitions/java.time.LocalTime\"},\"localDateTime\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.time.LocalDateTime\"}}},\"java.time.LocalTime\":{\"type\":\"object\",\"properties\":{\"hour\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"minute\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"second\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"nano\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"int\"}}},\"TestListGenericDTO\":{\"type\":\"object\",\"properties\":{\"list\":{\"type\":\"array\",\"description\":\"java.util.List\",\"items\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"}}}},\"com.yeepay.g3.facade.yop.sys.dto.TestListGenericDTO\":{\"type\":\"object\",\"properties\":{\"list\":{\"type\":\"array\",\"description\":\"java.util.List\",\"items\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"}}}},\"TestGenericDTO\":{\"type\":\"object\"},\"com.yeepay.g3.facade.yop.sys.dto.TestGenericDTO\":{\"type\":\"object\"},\"TestUploadDTO\":{\"type\":\"object\",\"properties\":{\"file\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileOriginalFileName\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileFileSize\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileMd5\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileBucket\":{\"type\":\"string\",\"description\":\"java.lang.String\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO\":{\"type\":\"object\",\"properties\":{\"file\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileOriginalFileName\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileFileSize\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileMd5\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileBucket\":{\"type\":\"string\",\"description\":\"java.lang.String\"}}}}}";
        EndServiceDTO result = classLoaderService.loadEndService(swaggerJson);
        Assert.assertFalse(Objects.isNull(result));
    }

    @Test
    public void testCreateModel() {
        String swaggerJson = "{\"swagger\":\"2.0\",\"paths\":{\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/baseType/1\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO baseType(byte b,short s,int i,long l,boolean bo,float f,double d,char c)\",\"operationId\":\"baseType\",\"parameters\":[{\"name\":\"b\",\"in\":\"query\",\"required\":true,\"type\":\"string\",\"format\":\"byte\"},{\"name\":\"s\",\"in\":\"query\",\"required\":true,\"type\":\"integer\",\"format\":\"int32\"},{\"name\":\"i\",\"in\":\"query\",\"required\":true,\"type\":\"integer\",\"format\":\"int32\"},{\"name\":\"l\",\"in\":\"query\",\"required\":true,\"type\":\"integer\",\"format\":\"int64\"},{\"name\":\"bo\",\"in\":\"query\",\"required\":true,\"type\":\"boolean\"},{\"name\":\"f\",\"in\":\"query\",\"required\":true,\"type\":\"number\",\"format\":\"float\"},{\"name\":\"d\",\"in\":\"query\",\"required\":true,\"type\":\"number\",\"format\":\"double\"},{\"name\":\"c\",\"in\":\"query\",\"required\":true,\"type\":\"string\"}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestResDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/generic/2\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestListGenericDTO generic(com.yeepay.g3.facade.yop.sys.dto.TestListGenericDTO dto)\",\"operationId\":\"generic\",\"parameters\":[{\"in\":\"formData\",\"name\":\"dto\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestListGenericDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestListGenericDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/generic/3\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestGenericDTO generic(com.yeepay.g3.facade.yop.sys.dto.TestGenericDTO testGenericDTO)\",\"operationId\":\"generic\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testGenericDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestGenericDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestGenericDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/hessian/4\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO hessian(com.yeepay.g3.facade.yop.sys.dto.TestDTO testDTO,java.lang.String string,java.lang.Byte b,java.lang.Short s,java.lang.Integer i,java.lang.Long l,java.lang.Float f,java.lang.Double d,java.lang.Boolean bo,java.lang.Character c,[Ljava.lang.String; array,java.lang.String constant,java.lang.String internal,java.util.Date date,java.math.BigDecimal bigDecimal,java.time.LocalDate localDate,java.time.LocalTime localTime,java.time.LocalDateTime localDateTime)\",\"operationId\":\"hessian\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"}},{\"name\":\"string\",\"in\":\"query\",\"required\":false,\"type\":\"string\"},{\"name\":\"b\",\"in\":\"query\",\"required\":false,\"type\":\"string\",\"format\":\"byte\"},{\"name\":\"s\",\"in\":\"query\",\"required\":false,\"type\":\"integer\",\"format\":\"int32\"},{\"name\":\"i\",\"in\":\"query\",\"required\":false,\"type\":\"integer\",\"format\":\"int32\"},{\"name\":\"l\",\"in\":\"query\",\"required\":false,\"type\":\"integer\",\"format\":\"int64\"},{\"name\":\"f\",\"in\":\"query\",\"required\":false,\"type\":\"number\",\"format\":\"float\"},{\"name\":\"d\",\"in\":\"query\",\"required\":false,\"type\":\"number\",\"format\":\"double\"},{\"name\":\"bo\",\"in\":\"query\",\"required\":false,\"type\":\"boolean\"},{\"name\":\"c\",\"in\":\"query\",\"required\":false,\"type\":\"string\"},{\"in\":\"formData\",\"name\":\"array\",\"required\":false,\"schema\":{\"type\":\"array\",\"items\":{\"type\":\"string\"}}},{\"name\":\"constant\",\"in\":\"query\",\"required\":false,\"type\":\"string\"},{\"name\":\"internal\",\"in\":\"query\",\"required\":false,\"type\":\"string\"},{\"name\":\"date\",\"in\":\"query\",\"required\":false,\"type\":\"string\",\"format\":\"date-time\"},{\"name\":\"bigDecimal\",\"in\":\"query\",\"required\":false,\"type\":\"number\"},{\"name\":\"localDate\",\"in\":\"query\",\"required\":false,\"type\":\"string\",\"format\":\"date\"},{\"in\":\"formData\",\"name\":\"localTime\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/java.time.LocalTime\"}},{\"name\":\"localDateTime\",\"in\":\"query\",\"required\":false,\"type\":\"string\",\"format\":\"date-time\"}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestResDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/httpGet/5\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO httpGet(com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO testQueryDTO,com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO headerParamDTO)\",\"operationId\":\"httpGet\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testQueryDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\"}},{\"in\":\"formData\",\"name\":\"headerParamDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestResDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/httpJson/6\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO httpJson(com.yeepay.g3.facade.yop.sys.dto.TestDTO testDTO,com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO headerParamDTO)\",\"operationId\":\"httpJson\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"}},{\"in\":\"formData\",\"name\":\"headerParamDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestResDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/httpPost/7\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO httpPost(com.yeepay.g3.facade.yop.sys.dto.TestFormDTO testFormDTO,com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO headerParamDTO)\",\"operationId\":\"httpPost\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testFormDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\"}},{\"in\":\"formData\",\"name\":\"headerParamDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestResDTO\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/notify/8\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"java.lang.String notify(com.yeepay.g3.facade.yop.sys.dto.TestDTO testDTO)\",\"operationId\":\"notify\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"type\":\"string\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/stringResult/9\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"java.lang.String stringResult(java.lang.String string)\",\"operationId\":\"stringResult\",\"parameters\":[{\"name\":\"string\",\"in\":\"query\",\"required\":false,\"type\":\"string\"}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"type\":\"string\"},\"headers\":{}}}}},\"/com.yeepay.g3.facade.yop.sys.facade.TestFacade/update/10\":{\"post\":{\"tags\":[\"TestFacade\"],\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO update(com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO testUploadDTO)\",\"operationId\":\"update\",\"parameters\":[{\"in\":\"formData\",\"name\":\"testUploadDTO\",\"required\":false,\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO\"}}],\"responses\":{\"200\":{\"description\":\"\",\"schema\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO\"},\"headers\":{}}}}}},\"definitions\":{\"HeaderParamDTO\":{\"type\":\"object\",\"properties\":{\"appId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"customerNo\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"userId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"productCode\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestDate\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"requestIp\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestSource\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"contentSHA256\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"}}},\"LocalTime\":{\"type\":\"object\",\"properties\":{\"hour\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"minute\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"second\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"nano\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"int\"}}},\"TestDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"},\"testDTO1\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO1\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO1\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"localDate\":{\"type\":\"string\",\"format\":\"date\",\"description\":\"java.time.LocalDate\"},\"localTime\":{\"description\":\"java.time.LocalTime\",\"$ref\":\"#/definitions/java.time.LocalTime\"},\"localDateTime\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.time.LocalDateTime\"}}},\"TestDTO1\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO2\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO2\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO2\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"}}},\"TestDTO2\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"}}},\"TestFormDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\"}}},\"TestQueryDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\"}}},\"TestResDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"},\"testQueryDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\"},\"testFormDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\"},\"headerParamDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"localDate\":{\"type\":\"string\",\"format\":\"date\",\"description\":\"java.time.LocalDate\"},\"localTime\":{\"description\":\"java.time.LocalTime\",\"$ref\":\"#/definitions/java.time.LocalTime\"},\"localDateTime\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.time.LocalDateTime\"}}},\"com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\":{\"type\":\"object\",\"properties\":{\"appId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"customerNo\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"userId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"productCode\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestId\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestDate\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"requestIp\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"requestSource\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"contentSHA256\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"},\"testDTO1\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO1\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO1\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"localDate\":{\"type\":\"string\",\"format\":\"date\",\"description\":\"java.time.LocalDate\"},\"localTime\":{\"description\":\"java.time.LocalTime\",\"$ref\":\"#/definitions/java.time.LocalTime\"},\"localDateTime\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.time.LocalDateTime\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestDTO1\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO2\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO2\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO2\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestDTO2\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestResDTO\":{\"type\":\"object\",\"properties\":{\"string\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"b\":{\"type\":\"string\",\"format\":\"byte\",\"description\":\"java.lang.Byte\"},\"s\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Short\"},\"i\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"java.lang.Integer\"},\"l\":{\"type\":\"integer\",\"format\":\"int64\",\"description\":\"java.lang.Long\"},\"f\":{\"type\":\"number\",\"format\":\"float\",\"description\":\"java.lang.Float\"},\"d\":{\"type\":\"number\",\"format\":\"double\",\"description\":\"java.lang.Double\"},\"bo\":{\"type\":\"boolean\",\"description\":\"java.lang.Boolean\"},\"c\":{\"type\":\"string\",\"description\":\"java.lang.Character\"},\"array\":{\"type\":\"array\",\"description\":\"[Ljava.lang.String;\",\"items\":{\"type\":\"string\"}},\"constant\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"internal\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"testDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"},\"testQueryDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestQueryDTO\"},\"testFormDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestFormDTO\"},\"headerParamDTO\":{\"description\":\"com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\",\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.HeaderParamDTO\"},\"date\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.util.Date\"},\"bigDecimal\":{\"type\":\"number\",\"description\":\"java.math.BigDecimal\"},\"localDate\":{\"type\":\"string\",\"format\":\"date\",\"description\":\"java.time.LocalDate\"},\"localTime\":{\"description\":\"java.time.LocalTime\",\"$ref\":\"#/definitions/java.time.LocalTime\"},\"localDateTime\":{\"type\":\"string\",\"format\":\"date-time\",\"description\":\"java.time.LocalDateTime\"}}},\"java.time.LocalTime\":{\"type\":\"object\",\"properties\":{\"hour\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"minute\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"second\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"byte\"},\"nano\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"int\"}}},\"TestListGenericDTO\":{\"type\":\"object\",\"properties\":{\"list\":{\"type\":\"array\",\"description\":\"java.util.List\",\"items\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"}}}},\"com.yeepay.g3.facade.yop.sys.dto.TestListGenericDTO\":{\"type\":\"object\",\"properties\":{\"list\":{\"type\":\"array\",\"description\":\"java.util.List\",\"items\":{\"$ref\":\"#/definitions/com.yeepay.g3.facade.yop.sys.dto.TestDTO\"}}}},\"TestGenericDTO\":{\"type\":\"object\"},\"com.yeepay.g3.facade.yop.sys.dto.TestGenericDTO\":{\"type\":\"object\"},\"TestUploadDTO\":{\"type\":\"object\",\"properties\":{\"file\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileOriginalFileName\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileFileSize\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileMd5\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileBucket\":{\"type\":\"string\",\"description\":\"java.lang.String\"}}},\"com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO\":{\"type\":\"object\",\"properties\":{\"file\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileOriginalFileName\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileFileSize\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileMd5\":{\"type\":\"string\",\"description\":\"java.lang.String\"},\"fileBucket\":{\"type\":\"string\",\"description\":\"java.lang.String\"}}}}}";
        EndServiceDTO endServiceDTO = classLoaderService.loadEndService(swaggerJson);
        String serviceSwaggerJson = "";
        String apiManageVOJson = "{\"createdByExitedInterface\":true,\"appName\":\"yop-sys-hessian\",\"className\":\"com.yeepay.g3.facade.yop.sys.facade.TestFacade\",\"method\":\"update(com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO testUploadDTO)\",\"basic\":{\"name\":\"tt\",\"apiType\":\"COMMON\",\"apiGroup\":\"test\",\"title\":\"tt\",\"classic\":\"\",\"description\":\"\",\"options\":{\"IDEMPOTENT\":false}},\"request\":{\"httpMethod\":\"POST\",\"path\":\"/rest/v1.0/test/tt\",\"requestBody\":{\"description\":\"\",\"contents\":{\"application/json\":{\"schema\":\"{\\\"type\\\":\\\"object\\\",\\\"properties\\\":{\\\"testDTO\\\":{\\\"exclusiveMaximum\\\":true,\\\"type\\\":\\\"object\\\",\\\"exclusiveMinimum\\\":true,\\\"$ref\\\":\\\"#/components/schemas/com.yeepay.g3.facade.yop.sys.dto.TestUploadDTO\\\"},\\\"fileOriginalFileName\\\":{\\\"type\\\":\\\"string\\\",\\\"description\\\":\\\"java.lang.String\\\"},\\\"fileFileSize\\\":{\\\"type\\\":\\\"string\\\",\\\"description\\\":\\\"java.lang.String\\\"},\\\"fileMd5\\\":{\\\"type\\\":\\\"string\\\",\\\"description\\\":\\\"java.lang.String\\\"},\\\"fileBucket\\\":{\\\"type\\\":\\\"string\\\",\\\"description\\\":\\\"java.lang.String\\\"}}} \",\"examples\":[]}}},\"encrypt\":false},\"response\":{\"httpCode\":200,\"contentType\":\"application/json\",\"encrypt\":false,\"content\":{\"schema\":\"{\\\"type\\\":\\\"object\\\",\\\"properties\\\":{\\\"testDTO\\\":{\\\"exclusiveMaximum\\\":true,\\\"type\\\":\\\"object\\\",\\\"exclusiveMinimum\\\":true,\\\"$ref\\\":\\\"#/components/schemas/com.yeepay.g3.facade.yop.sys.dto.TestDTO\\\"},\\\"fileOriginalFileName\\\":{\\\"type\\\":\\\"string\\\",\\\"description\\\":\\\"java.lang.String\\\"},\\\"fileFileSize\\\":{\\\"type\\\":\\\"string\\\",\\\"description\\\":\\\"java.lang.String\\\"},\\\"fileMd5\\\":{\\\"type\\\":\\\"string\\\",\\\"description\\\":\\\"java.lang.String\\\"},\\\"fileBucket\\\":{\\\"type\\\":\\\"string\\\",\\\"description\\\":\\\"java.lang.String\\\"}}}\",\"examples\":[]}},\"callbacks\":[],\"sensitiveVariables\":[]}";
        ApiManageVO apiManageVO = JSON_MAPPER.fromJson(apiManageVOJson, ApiManageVO.class);
        classLoaderService.buildModel(apiManageVO);
        System.out.println();
    }
}
