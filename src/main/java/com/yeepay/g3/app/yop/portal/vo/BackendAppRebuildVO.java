/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.BackendAppCommon;
import com.yeepay.g3.app.yop.portal.validation.group.BackendAppRebuild;
import com.yeepay.g3.facade.yop.sys.dto.ClassLoaderRebuildRequest;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/9/25 上午11:18
 */
public class BackendAppRebuildVO extends BaseVO {

    private static final long serialVersionUID = -1;

    @NotEmpty(message = "{backendAppRebuildVO.backendCode}", groups = BackendAppCommon.class)
    private String backendCode;

    @NotNull(message = "{backendAppRebuildVO.rebuildType}", groups = BackendAppRebuild.class)
    private ClassLoaderRebuildRequest.RebuildTypeEnum rebuildType;

    @NotEmpty(message = "{backendAppRebuildVO.reason}", groups = BackendAppRebuild.class)
    private String reason;

    public String getBackendCode() {
        return backendCode;
    }

    public void setBackendCode(String backendCode) {
        this.backendCode = backendCode;
    }

    public ClassLoaderRebuildRequest.RebuildTypeEnum getRebuildType() {
        return rebuildType;
    }

    public void setRebuildType(ClassLoaderRebuildRequest.RebuildTypeEnum rebuildType) {
        this.rebuildType = rebuildType;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

}
