package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;

/**
 * title: 老版api<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-24 18:46
 */
@Component
public class OldApiUriBasedAuthorizationFilter  extends CacheAbstractAuthorizationFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(OldApiUriBasedAuthorizationFilter.class);

    // 根据apiUri获得spCode
    @Override
    protected String[] getSpCodes(String[] params) {
        String[] spCodes;
        if (params == null || params.length < 1) {
            spCodes = null;
        } else {
            spCodes = new String[params.length];
            try {
                for (int i = 0; i < params.length; i++) {
                    spCodes[i] = oldApiUriLocalCache.get(params[i]);
                }
            } catch (ExecutionException e) {
                LOGGER.info("get spCode of apiUri wrong, params are :{},exception is :{}", params, e);
            }
            LOGGER.debug("spCodes by apiUri:{}", spCodes);
        }
        return spCodes;
    }

    @Override
    public String shiroName() {
        return "old_api_uri_based";
    }
}
