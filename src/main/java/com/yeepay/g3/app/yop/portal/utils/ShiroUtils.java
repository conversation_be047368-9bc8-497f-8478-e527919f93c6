package com.yeepay.g3.app.yop.portal.utils;

import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;

/**
 * title: <PERSON><PERSON><br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/6/12 下午2:44
 */
public final class ShiroUtils {

    public static Subject getSubject() {
        return SecurityUtils.getSubject();
    }

    public static ShiroRealm.ShiroUser getShiroUser() {
        return (ShiroRealm.ShiroUser) getSubject().getPrincipal();
    }

    public static OperatorTypeEnum getOperatorType() {
        return getShiroUser().getUserType();
    }

    public static boolean isPlatformOperator() {
        return getShiroUser().isPlatformOperator();
    }

    public static boolean isSpOperator() {
        return getShiroUser().isSpOperator();
    }

    public static String getOperatorCode() {
        return getShiroUser().getUserId();
    }

}
