/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.ProductApiPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ProductApiPageQueryParam;

import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/5 下午3:56
 */
public interface ProductApiQueryService {

    PageQueryResult<ProductApiPageItem> pageQuery(ProductApiPageQueryParam param);

    List<Map> listAllByProductCode(String productCode);

}
