2025-07-16 19:46:33,298 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:33,302 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:33,387 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:33,388 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:33,427 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:33,427 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:33,553 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:33,554 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:33,554 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:33,559 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:33,560 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:34,324 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-16 19:46:34,324 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:39,931 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:39,935 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:40,008 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:40,010 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:40,072 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:40,073 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:40,175 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:40,176 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:40,176 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:40,181 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:40,183 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:40,713 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-16 19:48:40,715 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:10,349 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:10,354 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:10,433 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:10,434 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:10,487 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:10,488 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:10,596 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:10,596 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:10,597 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:10,605 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:10,607 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:11,387 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-16 19:56:11,390 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:14,878 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:14,882 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:14,978 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:14,979 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:15,027 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:15,028 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:15,133 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:15,133 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:15,134 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:15,139 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:15,140 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:17,963 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-17 11:33:17,965 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:29,761 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:29,765 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:29,941 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:29,942 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:29,987 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:29,988 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:30,100 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:30,100 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:30,100 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:30,106 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:30,106 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:30,503 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-17 13:07:30,504 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:19,961 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:19,966 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:20,065 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:20,066 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:20,129 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:20,130 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:20,306 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:20,306 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:20,307 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:20,321 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:20,322 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:20,859 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-17 15:04:20,861 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:43,368 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:43,372 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:43,442 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:43,443 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:43,506 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:43,507 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:43,604 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:43,604 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:43,604 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:43,610 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:43,610 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:44,197 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-17 15:07:44,199 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:44,830 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.1] is created, dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:44,841 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:44,897 [DEBUG] [main        ] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.rpc;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class Protocol$Adaptive implements org.apache.dubbo.rpc.Protocol {
public org.apache.dubbo.rpc.Invoker refer(java.lang.Class arg0, org.apache.dubbo.common.URL arg1) throws org.apache.dubbo.rpc.RpcException {
if (arg1 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg1;
String extName = ( url.getProtocol() == null ? "dubbo" : url.getProtocol() );
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.Protocol) name from url (" + url.toString() + ") use keys([protocol])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.Protocol.class);
org.apache.dubbo.rpc.Protocol extension = (org.apache.dubbo.rpc.Protocol)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.Protocol.class).getExtension(extName);
return extension.refer(arg0, arg1);
}
public org.apache.dubbo.rpc.Exporter export(org.apache.dubbo.rpc.Invoker arg0) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = ( url.getProtocol() == null ? "dubbo" : url.getProtocol() );
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.Protocol) name from url (" + url.toString() + ") use keys([protocol])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.Protocol.class);
org.apache.dubbo.rpc.Protocol extension = (org.apache.dubbo.rpc.Protocol)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.Protocol.class).getExtension(extName);
return extension.export(arg0);
}
public java.util.List getServers()  {
throw new UnsupportedOperationException("The method public default java.util.List org.apache.dubbo.rpc.Protocol.getServers() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
public void destroy()  {
throw new UnsupportedOperationException("The method public abstract void org.apache.dubbo.rpc.Protocol.destroy() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
public int getDefaultPort()  {
throw new UnsupportedOperationException("The method public abstract int org.apache.dubbo.rpc.Protocol.getDefaultPort() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:45,158 [DEBUG] [main        ] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.rpc;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class ProxyFactory$Adaptive implements org.apache.dubbo.rpc.ProxyFactory {
public java.lang.Object getProxy(org.apache.dubbo.rpc.Invoker arg0) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getProxy(arg0);
}
public java.lang.Object getProxy(org.apache.dubbo.rpc.Invoker arg0, boolean arg1) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getProxy(arg0, arg1);
}
public org.apache.dubbo.rpc.Invoker getInvoker(java.lang.Object arg0, java.lang.Class arg1, org.apache.dubbo.common.URL arg2) throws org.apache.dubbo.rpc.RpcException {
if (arg2 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg2;
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getInvoker(arg0, arg1, arg2);
}
}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:45,220 [INFO ] [main        ] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is starting., dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:45,222 [INFO ] [main        ] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown) is starting., dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:45,234 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ConfigCenterConfig with prefix [dubbo.config-center], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:45,247 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:50,322 [ERROR] [main        ] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] start failed: java.lang.IllegalStateException: No application config found or it's not a valid config! Please add <dubbo:application name="..." /> to your spring config., dubbo version: 3.0.7, current host: *************
java.lang.IllegalStateException: No application config found or it's not a valid config! Please add <dubbo:application name="..." /> to your spring config.
	at org.apache.dubbo.config.utils.ConfigValidationUtils.validateApplicationConfig(ConfigValidationUtils.java:468)
	at org.apache.dubbo.config.utils.DefaultConfigValidator.validate(DefaultConfigValidator.java:47)
	at org.apache.dubbo.config.context.AbstractConfigManager.checkDefaultAndValidateConfigs(AbstractConfigManager.java:579)
	at org.apache.dubbo.config.context.ConfigManager.checkConfigs(ConfigManager.java:281)
	at org.apache.dubbo.config.context.ConfigManager.loadConfigs(ConfigManager.java:261)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.loadApplicationConfigs(DefaultApplicationDeployer.java:217)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.initialize(DefaultApplicationDeployer.java:187)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:141)
	at org.apache.dubbo.config.ReferenceConfig.get(ReferenceConfig.java:215)
	at com.yeepay.g3.utils.rmi.soa.SoaSupportUtils.getSoaService(SoaSupportUtils.java:307)
	at com.yeepay.g3.utils.rmi.DefaultServiceRepository.getService(DefaultServiceRepository.java:279)
	at com.yeepay.g3.utils.rmi.RemoteServiceFactory.getService(RemoteServiceFactory.java:77)
	at com.yeepay.g3.app.yop.portal.cache.ApiSwaggerLocalCache.<clinit>(ApiSwaggerLocalCache.java:37)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:204)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1312)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1214)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:226)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:893)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.yeepay.g3.app.yop.portal.WebApplication.main(WebApplication.java:70)
2025-07-17 15:08:50,360 [INFO ] [main        ] ConfigurableMetadataServiceExporter -  [DUBBO] Metadata Service Port hasn't been set will use default protocol defined in protocols., dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:50,367 [INFO ] [main        ] ConfigurableMetadataServiceExporter -  [DUBBO] Using dubbo protocol to export metadata service on port -1, dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:50,368 [INFO ] [main        ] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] is starting., dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:50,368 [ERROR] [main        ] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown) found failed module: Dubbo Module[1.1.1], dubbo version: 3.0.7, current host: *************
java.lang.IllegalStateException: No application config found or it's not a valid config! Please add <dubbo:application name="..." /> to your spring config.
	at org.apache.dubbo.config.utils.ConfigValidationUtils.validateApplicationConfig(ConfigValidationUtils.java:468)
	at org.apache.dubbo.config.utils.DefaultConfigValidator.validate(DefaultConfigValidator.java:47)
	at org.apache.dubbo.config.context.AbstractConfigManager.checkDefaultAndValidateConfigs(AbstractConfigManager.java:579)
	at org.apache.dubbo.config.context.ConfigManager.checkConfigs(ConfigManager.java:281)
	at org.apache.dubbo.config.context.ConfigManager.loadConfigs(ConfigManager.java:261)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.loadApplicationConfigs(DefaultApplicationDeployer.java:217)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.initialize(DefaultApplicationDeployer.java:187)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:141)
	at org.apache.dubbo.config.ReferenceConfig.get(ReferenceConfig.java:215)
	at com.yeepay.g3.utils.rmi.soa.SoaSupportUtils.getSoaService(SoaSupportUtils.java:307)
	at com.yeepay.g3.utils.rmi.DefaultServiceRepository.getService(DefaultServiceRepository.java:279)
	at com.yeepay.g3.utils.rmi.RemoteServiceFactory.getService(RemoteServiceFactory.java:77)
	at com.yeepay.g3.app.yop.portal.cache.ApiSwaggerLocalCache.<clinit>(ApiSwaggerLocalCache.java:37)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:204)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1312)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1214)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:226)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:893)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.yeepay.g3.app.yop.portal.WebApplication.main(WebApplication.java:70)
2025-07-17 15:08:50,372 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ConfigCenterConfig with prefix [dubbo.config-center], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:50,373 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {hostname=bogon, protocol=dubbo}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:50,400 [ERROR] [main        ] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] start failed: java.lang.IllegalStateException: No application config found or it's not a valid config! Please add <dubbo:application name="..." /> to your spring config., dubbo version: 3.0.7, current host: *************
java.lang.IllegalStateException: No application config found or it's not a valid config! Please add <dubbo:application name="..." /> to your spring config.
	at org.apache.dubbo.config.utils.ConfigValidationUtils.validateApplicationConfig(ConfigValidationUtils.java:468)
	at org.apache.dubbo.config.utils.DefaultConfigValidator.validate(DefaultConfigValidator.java:47)
	at org.apache.dubbo.config.context.AbstractConfigManager.checkDefaultAndValidateConfigs(AbstractConfigManager.java:579)
	at org.apache.dubbo.config.context.ConfigManager.checkConfigs(ConfigManager.java:281)
	at org.apache.dubbo.config.context.ConfigManager.loadConfigs(ConfigManager.java:261)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.loadApplicationConfigs(DefaultApplicationDeployer.java:217)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.initialize(DefaultApplicationDeployer.java:187)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:141)
	at org.apache.dubbo.config.ServiceConfig.export(ServiceConfig.java:217)
	at org.apache.dubbo.config.metadata.ConfigurableMetadataServiceExporter.export(ConfigurableMetadataServiceExporter.java:63)
	at org.apache.dubbo.config.metadata.ExporterDeployListener.onModuleStarted(ExporterDeployListener.java:70)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.exportMetadataService(DefaultApplicationDeployer.java:923)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.prepareApplicationInstance(DefaultApplicationDeployer.java:613)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.checkState(DefaultApplicationDeployer.java:824)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.notifyModuleChanged(DefaultApplicationDeployer.java:812)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.onModuleFailed(DefaultModuleDeployer.java:267)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:174)
	at org.apache.dubbo.config.ReferenceConfig.get(ReferenceConfig.java:215)
	at com.yeepay.g3.utils.rmi.soa.SoaSupportUtils.getSoaService(SoaSupportUtils.java:307)
	at com.yeepay.g3.utils.rmi.DefaultServiceRepository.getService(DefaultServiceRepository.java:279)
	at com.yeepay.g3.utils.rmi.RemoteServiceFactory.getService(RemoteServiceFactory.java:77)
	at com.yeepay.g3.app.yop.portal.cache.ApiSwaggerLocalCache.<clinit>(ApiSwaggerLocalCache.java:37)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:204)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1312)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1214)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:226)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:893)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.yeepay.g3.app.yop.portal.WebApplication.main(WebApplication.java:70)
2025-07-17 15:08:50,402 [ERROR] [main        ] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown) found failed module: Dubbo Module[1.1.0], dubbo version: 3.0.7, current host: *************
java.lang.IllegalStateException: No application config found or it's not a valid config! Please add <dubbo:application name="..." /> to your spring config.
	at org.apache.dubbo.config.utils.ConfigValidationUtils.validateApplicationConfig(ConfigValidationUtils.java:468)
	at org.apache.dubbo.config.utils.DefaultConfigValidator.validate(DefaultConfigValidator.java:47)
	at org.apache.dubbo.config.context.AbstractConfigManager.checkDefaultAndValidateConfigs(AbstractConfigManager.java:579)
	at org.apache.dubbo.config.context.ConfigManager.checkConfigs(ConfigManager.java:281)
	at org.apache.dubbo.config.context.ConfigManager.loadConfigs(ConfigManager.java:261)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.loadApplicationConfigs(DefaultApplicationDeployer.java:217)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.initialize(DefaultApplicationDeployer.java:187)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:141)
	at org.apache.dubbo.config.ServiceConfig.export(ServiceConfig.java:217)
	at org.apache.dubbo.config.metadata.ConfigurableMetadataServiceExporter.export(ConfigurableMetadataServiceExporter.java:63)
	at org.apache.dubbo.config.metadata.ExporterDeployListener.onModuleStarted(ExporterDeployListener.java:70)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.exportMetadataService(DefaultApplicationDeployer.java:923)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.prepareApplicationInstance(DefaultApplicationDeployer.java:613)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.checkState(DefaultApplicationDeployer.java:824)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.notifyModuleChanged(DefaultApplicationDeployer.java:812)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.onModuleFailed(DefaultModuleDeployer.java:267)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:174)
	at org.apache.dubbo.config.ReferenceConfig.get(ReferenceConfig.java:215)
	at com.yeepay.g3.utils.rmi.soa.SoaSupportUtils.getSoaService(SoaSupportUtils.java:307)
	at com.yeepay.g3.utils.rmi.DefaultServiceRepository.getService(DefaultServiceRepository.java:279)
	at com.yeepay.g3.utils.rmi.RemoteServiceFactory.getService(RemoteServiceFactory.java:77)
	at com.yeepay.g3.app.yop.portal.cache.ApiSwaggerLocalCache.<clinit>(ApiSwaggerLocalCache.java:37)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:204)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1312)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1214)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:226)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:893)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.yeepay.g3.app.yop.portal.WebApplication.main(WebApplication.java:70)
2025-07-17 15:08:50,402 [ERROR] [main        ] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown) an exception occurred when handle starting event, dubbo version: 3.0.7, current host: *************
java.lang.IllegalStateException: No application config found or it's not a valid config! Please add <dubbo:application name="..." /> to your spring config.
	at org.apache.dubbo.config.utils.ConfigValidationUtils.validateApplicationConfig(ConfigValidationUtils.java:468)
	at org.apache.dubbo.config.utils.DefaultConfigValidator.validate(DefaultConfigValidator.java:47)
	at org.apache.dubbo.config.context.AbstractConfigManager.checkDefaultAndValidateConfigs(AbstractConfigManager.java:579)
	at org.apache.dubbo.config.context.ConfigManager.checkConfigs(ConfigManager.java:281)
	at org.apache.dubbo.config.context.ConfigManager.loadConfigs(ConfigManager.java:261)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.loadApplicationConfigs(DefaultApplicationDeployer.java:217)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.initialize(DefaultApplicationDeployer.java:187)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:141)
	at org.apache.dubbo.config.ServiceConfig.export(ServiceConfig.java:217)
	at org.apache.dubbo.config.metadata.ConfigurableMetadataServiceExporter.export(ConfigurableMetadataServiceExporter.java:63)
	at org.apache.dubbo.config.metadata.ExporterDeployListener.onModuleStarted(ExporterDeployListener.java:70)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.exportMetadataService(DefaultApplicationDeployer.java:923)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.prepareApplicationInstance(DefaultApplicationDeployer.java:613)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.checkState(DefaultApplicationDeployer.java:824)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.notifyModuleChanged(DefaultApplicationDeployer.java:812)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.onModuleFailed(DefaultModuleDeployer.java:267)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:174)
	at org.apache.dubbo.config.ReferenceConfig.get(ReferenceConfig.java:215)
	at com.yeepay.g3.utils.rmi.soa.SoaSupportUtils.getSoaService(SoaSupportUtils.java:307)
	at com.yeepay.g3.utils.rmi.DefaultServiceRepository.getService(DefaultServiceRepository.java:279)
	at com.yeepay.g3.utils.rmi.RemoteServiceFactory.getService(RemoteServiceFactory.java:77)
	at com.yeepay.g3.app.yop.portal.cache.ApiSwaggerLocalCache.<clinit>(ApiSwaggerLocalCache.java:37)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:204)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1312)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1214)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:226)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:893)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.yeepay.g3.app.yop.portal.WebApplication.main(WebApplication.java:70)
2025-07-17 15:08:50,405 [INFO ] [main        ] ServiceInstanceMetadataUtils -  [DUBBO] Start registering instance address to registry., dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:50,414 [ERROR] [main        ] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown) found failed module: Dubbo Module[1.1.0], dubbo version: 3.0.7, current host: *************
java.lang.IllegalStateException: No application config found or it's not a valid config! Please add <dubbo:application name="..." /> to your spring config.
	at org.apache.dubbo.config.utils.ConfigValidationUtils.validateApplicationConfig(ConfigValidationUtils.java:468)
	at org.apache.dubbo.config.utils.DefaultConfigValidator.validate(DefaultConfigValidator.java:47)
	at org.apache.dubbo.config.context.AbstractConfigManager.checkDefaultAndValidateConfigs(AbstractConfigManager.java:579)
	at org.apache.dubbo.config.context.ConfigManager.checkConfigs(ConfigManager.java:281)
	at org.apache.dubbo.config.context.ConfigManager.loadConfigs(ConfigManager.java:261)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.loadApplicationConfigs(DefaultApplicationDeployer.java:217)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.initialize(DefaultApplicationDeployer.java:187)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:141)
	at org.apache.dubbo.config.ServiceConfig.export(ServiceConfig.java:217)
	at org.apache.dubbo.config.metadata.ConfigurableMetadataServiceExporter.export(ConfigurableMetadataServiceExporter.java:63)
	at org.apache.dubbo.config.metadata.ExporterDeployListener.onModuleStarted(ExporterDeployListener.java:70)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.exportMetadataService(DefaultApplicationDeployer.java:923)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.prepareApplicationInstance(DefaultApplicationDeployer.java:613)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.checkState(DefaultApplicationDeployer.java:824)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.notifyModuleChanged(DefaultApplicationDeployer.java:812)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.onModuleFailed(DefaultModuleDeployer.java:267)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:174)
	at org.apache.dubbo.config.ReferenceConfig.get(ReferenceConfig.java:215)
	at com.yeepay.g3.utils.rmi.soa.SoaSupportUtils.getSoaService(SoaSupportUtils.java:307)
	at com.yeepay.g3.utils.rmi.DefaultServiceRepository.getService(DefaultServiceRepository.java:279)
	at com.yeepay.g3.utils.rmi.RemoteServiceFactory.getService(RemoteServiceFactory.java:77)
	at com.yeepay.g3.app.yop.portal.cache.ApiSwaggerLocalCache.<clinit>(ApiSwaggerLocalCache.java:37)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:204)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1312)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1214)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:226)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:893)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.yeepay.g3.app.yop.portal.WebApplication.main(WebApplication.java:70)
2025-07-17 15:08:50,627 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:55,642 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Ignore duplicated config: <dubbo:application protocol="dubbo" />, dubbo version: 3.0.7, current host: *************
2025-07-17 15:08:55,663 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:09:00,736 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:09:05,809 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:09:22,659 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:09:27,699 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:09:32,760 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:09:37,829 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:09:42,868 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:09:47,977 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:09:53,111 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:09:58,130 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,601 [INFO ] [DubboShutdownHook] DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,608 [INFO ] [DubboShutdownHook] FrameworkModel -  [DUBBO] Reset global default application from Dubbo Application[1.1](unknown) to null, dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,610 [INFO ] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown) is stopping., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,612 [INFO ] [DubboShutdownHook] RegistryManager -  [DUBBO] Close all registries [], dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,625 [DEBUG] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown)'s all ServiceDiscoveries have been destroyed., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,635 [INFO ] [DubboShutdownHook] DubboProtocol -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,636 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,637 [ERROR] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown) found failed module: Dubbo Module[1.1.0], dubbo version: 3.0.7, current host: *************
java.lang.IllegalStateException: No application config found or it's not a valid config! Please add <dubbo:application name="..." /> to your spring config.
	at org.apache.dubbo.config.utils.ConfigValidationUtils.validateApplicationConfig(ConfigValidationUtils.java:468)
	at org.apache.dubbo.config.utils.DefaultConfigValidator.validate(DefaultConfigValidator.java:47)
	at org.apache.dubbo.config.context.AbstractConfigManager.checkDefaultAndValidateConfigs(AbstractConfigManager.java:579)
	at org.apache.dubbo.config.context.ConfigManager.checkConfigs(ConfigManager.java:281)
	at org.apache.dubbo.config.context.ConfigManager.loadConfigs(ConfigManager.java:261)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.loadApplicationConfigs(DefaultApplicationDeployer.java:217)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.initialize(DefaultApplicationDeployer.java:187)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:141)
	at org.apache.dubbo.config.ServiceConfig.export(ServiceConfig.java:217)
	at org.apache.dubbo.config.metadata.ConfigurableMetadataServiceExporter.export(ConfigurableMetadataServiceExporter.java:63)
	at org.apache.dubbo.config.metadata.ExporterDeployListener.onModuleStarted(ExporterDeployListener.java:70)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.exportMetadataService(DefaultApplicationDeployer.java:923)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.prepareApplicationInstance(DefaultApplicationDeployer.java:613)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.checkState(DefaultApplicationDeployer.java:824)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.notifyModuleChanged(DefaultApplicationDeployer.java:812)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.onModuleFailed(DefaultModuleDeployer.java:267)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:174)
	at org.apache.dubbo.config.ReferenceConfig.get(ReferenceConfig.java:215)
	at com.yeepay.g3.utils.rmi.soa.SoaSupportUtils.getSoaService(SoaSupportUtils.java:307)
	at com.yeepay.g3.utils.rmi.DefaultServiceRepository.getService(DefaultServiceRepository.java:279)
	at com.yeepay.g3.utils.rmi.RemoteServiceFactory.getService(RemoteServiceFactory.java:77)
	at com.yeepay.g3.app.yop.portal.cache.ApiSwaggerLocalCache.<clinit>(ApiSwaggerLocalCache.java:37)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:204)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1312)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1214)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:226)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:893)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.yeepay.g3.app.yop.portal.WebApplication.main(WebApplication.java:70)
2025-07-17 15:10:02,642 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,642 [ERROR] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown) found failed module: Dubbo Module[1.1.0], dubbo version: 3.0.7, current host: *************
java.lang.IllegalStateException: No application config found or it's not a valid config! Please add <dubbo:application name="..." /> to your spring config.
	at org.apache.dubbo.config.utils.ConfigValidationUtils.validateApplicationConfig(ConfigValidationUtils.java:468)
	at org.apache.dubbo.config.utils.DefaultConfigValidator.validate(DefaultConfigValidator.java:47)
	at org.apache.dubbo.config.context.AbstractConfigManager.checkDefaultAndValidateConfigs(AbstractConfigManager.java:579)
	at org.apache.dubbo.config.context.ConfigManager.checkConfigs(ConfigManager.java:281)
	at org.apache.dubbo.config.context.ConfigManager.loadConfigs(ConfigManager.java:261)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.loadApplicationConfigs(DefaultApplicationDeployer.java:217)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.initialize(DefaultApplicationDeployer.java:187)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:141)
	at org.apache.dubbo.config.ServiceConfig.export(ServiceConfig.java:217)
	at org.apache.dubbo.config.metadata.ConfigurableMetadataServiceExporter.export(ConfigurableMetadataServiceExporter.java:63)
	at org.apache.dubbo.config.metadata.ExporterDeployListener.onModuleStarted(ExporterDeployListener.java:70)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.exportMetadataService(DefaultApplicationDeployer.java:923)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.prepareApplicationInstance(DefaultApplicationDeployer.java:613)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.checkState(DefaultApplicationDeployer.java:824)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.notifyModuleChanged(DefaultApplicationDeployer.java:812)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.onModuleFailed(DefaultModuleDeployer.java:267)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:174)
	at org.apache.dubbo.config.ReferenceConfig.get(ReferenceConfig.java:215)
	at com.yeepay.g3.utils.rmi.soa.SoaSupportUtils.getSoaService(SoaSupportUtils.java:307)
	at com.yeepay.g3.utils.rmi.DefaultServiceRepository.getService(DefaultServiceRepository.java:279)
	at com.yeepay.g3.utils.rmi.RemoteServiceFactory.getService(RemoteServiceFactory.java:77)
	at com.yeepay.g3.app.yop.portal.cache.ApiSwaggerLocalCache.<clinit>(ApiSwaggerLocalCache.java:37)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62)
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:204)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:87)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1312)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1214)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:557)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:517)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:323)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:226)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:321)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:893)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.yeepay.g3.app.yop.portal.WebApplication.main(WebApplication.java:70)
2025-07-17 15:10:02,644 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,645 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,646 [INFO ] [DubboShutdownHook] DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,646 [INFO ] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown) has stopped., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,649 [INFO ] [DubboShutdownHook] FrameworkModel -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,650 [INFO ] [DubboShutdownHook] FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,650 [INFO ] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,650 [INFO ] [DubboShutdownHook] RegistryManager -  [DUBBO] Close all registries [], dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,650 [DEBUG] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION)'s all ServiceDiscoveries have been destroyed., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,651 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,651 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,651 [INFO ] [DubboShutdownHook] DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,651 [INFO ] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,653 [INFO ] [DubboShutdownHook] FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,654 [INFO ] [DubboShutdownHook] FrameworkModel -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,658 [INFO ] [DubboShutdownHook] GlobalResourcesRepository -  [DUBBO] Destroying global resources ..., dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,659 [INFO ] [DubboShutdownHook] GlobalResourcesRepository -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.0.7, current host: *************
2025-07-17 15:10:02,660 [INFO ] [DubboShutdownHook] FrameworkExecutorRepository -  [DUBBO] destroying framework executor repository .., dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,195 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,199 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,266 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,267 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,306 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,307 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,423 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,423 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,423 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,429 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,429 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,854 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:04:23,855 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:50,576 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:50,579 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:50,639 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:50,640 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:50,675 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:50,675 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:50,772 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:50,772 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:50,772 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:50,777 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:50,778 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:51,199 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:07:51,199 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:19,024 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:19,028 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:19,102 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:19,103 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:19,145 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:19,145 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:19,269 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:19,270 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:19,270 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:19,274 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:19,276 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:20,067 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:08:20,067 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:16,826 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:16,830 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:16,899 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:16,900 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:16,933 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:16,934 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:17,039 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:17,039 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:17,039 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:17,044 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:17,045 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:17,451 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:09:17,451 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:06,720 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:06,724 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:06,791 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:06,792 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:06,826 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:06,826 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:06,926 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:06,926 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:06,926 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:06,930 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:06,931 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:07,355 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:07,355 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:44,643 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:44,647 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:44,716 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:44,717 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:44,751 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:44,752 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:44,854 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:44,854 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:44,854 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:44,858 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:44,860 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:45,279 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:45,280 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:54,799 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:54,807 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:55,069 [DEBUG] [main        ] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.remoting.http;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class HttpBinder$Adaptive implements org.apache.dubbo.remoting.http.HttpBinder {
public org.apache.dubbo.remoting.http.HttpServer bind(org.apache.dubbo.common.URL arg0, org.apache.dubbo.remoting.http.HttpHandler arg1)  {
if (arg0 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg0;
String extName = url.getParameter("server", "jetty");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.remoting.http.HttpBinder) name from url (" + url.toString() + ") use keys([server])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.remoting.http.HttpBinder.class);
org.apache.dubbo.remoting.http.HttpBinder extension = (org.apache.dubbo.remoting.http.HttpBinder)scopeModel.getExtensionLoader(org.apache.dubbo.remoting.http.HttpBinder.class).getExtension(extName);
return extension.bind(arg0, arg1);
}
}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:55,390 [DEBUG] [main        ] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.rpc;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class ProxyFactory$Adaptive implements org.apache.dubbo.rpc.ProxyFactory {
public org.apache.dubbo.rpc.Invoker getInvoker(java.lang.Object arg0, java.lang.Class arg1, org.apache.dubbo.common.URL arg2) throws org.apache.dubbo.rpc.RpcException {
if (arg2 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg2;
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getInvoker(arg0, arg1, arg2);
}
public java.lang.Object getProxy(org.apache.dubbo.rpc.Invoker arg0, boolean arg1) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getProxy(arg0, arg1);
}
public java.lang.Object getProxy(org.apache.dubbo.rpc.Invoker arg0) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getProxy(arg0);
}
}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:55,490 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {service-discovery.migration=FORCE_INTERFACE, logger=log4j, register-mode=all, name=yop-portal, qos-enable=false, metadata-type=remote}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:55,909 [INFO ] [Curator-ConnectionStateManager-0] CuratorZookeeperClient$CuratorConnectionStateListener -  [DUBBO] Curator zookeeper client instance initiated successfully, session id is 1982ff13a17615a, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:55,910 [INFO ] [main        ] AbstractZookeeperTransporter -  [DUBBO] No valid zookeeper client found from cache, therefore create a new client for url. zookeeperx://zk.bass.3g:2181/org.apache.dubbo.registry.RegistryService?application=yop-portal&dubbo=3.0.7&file.cache=false&interface=org.apache.dubbo.registry.RegistryService, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:55,946 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Subscribe: config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-commoncfg&version=*, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:55,986 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Notify urls for subscribe url config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-commoncfg&version=*, url size: 2, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:55,998 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Subscribe: config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-yop-portal&version=*, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:56,018 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Notify urls for subscribe url config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-yop-portal&version=*, url size: 1, dubbo version: 3.0.7, current host: *************
2025-07-28 20:11:56,814 [DEBUG] [main        ] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.rpc;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class Protocol$Adaptive implements org.apache.dubbo.rpc.Protocol {
public java.util.List getServers()  {
throw new UnsupportedOperationException("The method public default java.util.List org.apache.dubbo.rpc.Protocol.getServers() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
public org.apache.dubbo.rpc.Invoker refer(java.lang.Class arg0, org.apache.dubbo.common.URL arg1) throws org.apache.dubbo.rpc.RpcException {
if (arg1 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg1;
String extName = ( url.getProtocol() == null ? "dubbo" : url.getProtocol() );
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.Protocol) name from url (" + url.toString() + ") use keys([protocol])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.Protocol.class);
org.apache.dubbo.rpc.Protocol extension = (org.apache.dubbo.rpc.Protocol)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.Protocol.class).getExtension(extName);
return extension.refer(arg0, arg1);
}
public org.apache.dubbo.rpc.Exporter export(org.apache.dubbo.rpc.Invoker arg0) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = ( url.getProtocol() == null ? "dubbo" : url.getProtocol() );
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.Protocol) name from url (" + url.toString() + ") use keys([protocol])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.Protocol.class);
org.apache.dubbo.rpc.Protocol extension = (org.apache.dubbo.rpc.Protocol)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.Protocol.class).getExtension(extName);
return extension.export(arg0);
}
public void destroy()  {
throw new UnsupportedOperationException("The method public abstract void org.apache.dubbo.rpc.Protocol.destroy() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
public int getDefaultPort()  {
throw new UnsupportedOperationException("The method public abstract int org.apache.dubbo.rpc.Protocol.getDefaultPort() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:15:11,159 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-28 20:15:11,163 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:15:11,246 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:15:11,247 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:00,644 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:00,648 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:00,717 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:00,718 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:00,753 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:00,754 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:00,863 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:00,864 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:00,864 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:00,868 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:00,869 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:01,326 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:01,327 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:15,273 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:15,282 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:15,525 [DEBUG] [main        ] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.remoting.http;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class HttpBinder$Adaptive implements org.apache.dubbo.remoting.http.HttpBinder {
public org.apache.dubbo.remoting.http.HttpServer bind(org.apache.dubbo.common.URL arg0, org.apache.dubbo.remoting.http.HttpHandler arg1)  {
if (arg0 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg0;
String extName = url.getParameter("server", "jetty");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.remoting.http.HttpBinder) name from url (" + url.toString() + ") use keys([server])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.remoting.http.HttpBinder.class);
org.apache.dubbo.remoting.http.HttpBinder extension = (org.apache.dubbo.remoting.http.HttpBinder)scopeModel.getExtensionLoader(org.apache.dubbo.remoting.http.HttpBinder.class).getExtension(extName);
return extension.bind(arg0, arg1);
}
}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:15,900 [DEBUG] [main        ] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.rpc;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class ProxyFactory$Adaptive implements org.apache.dubbo.rpc.ProxyFactory {
public java.lang.Object getProxy(org.apache.dubbo.rpc.Invoker arg0) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getProxy(arg0);
}
public java.lang.Object getProxy(org.apache.dubbo.rpc.Invoker arg0, boolean arg1) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getProxy(arg0, arg1);
}
public org.apache.dubbo.rpc.Invoker getInvoker(java.lang.Object arg0, java.lang.Class arg1, org.apache.dubbo.common.URL arg2) throws org.apache.dubbo.rpc.RpcException {
if (arg2 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg2;
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getInvoker(arg0, arg1, arg2);
}
}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:15,991 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {service-discovery.migration=FORCE_INTERFACE, logger=log4j, register-mode=all, name=yop-portal, qos-enable=false, metadata-type=remote}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:16,429 [INFO ] [Curator-ConnectionStateManager-0] CuratorZookeeperClient$CuratorConnectionStateListener -  [DUBBO] Curator zookeeper client instance initiated successfully, session id is 3982ff0baa25c9b, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:16,430 [INFO ] [main        ] AbstractZookeeperTransporter -  [DUBBO] No valid zookeeper client found from cache, therefore create a new client for url. zookeeperx://zk.bass.3g:2181/org.apache.dubbo.registry.RegistryService?application=yop-portal&dubbo=3.0.7&file.cache=false&interface=org.apache.dubbo.registry.RegistryService, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:16,460 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Subscribe: config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-commoncfg&version=*, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:16,498 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Notify urls for subscribe url config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-commoncfg&version=*, url size: 2, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:16,511 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Subscribe: config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-yop-portal&version=*, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:16,533 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Notify urls for subscribe url config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-yop-portal&version=*, url size: 1, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:17,356 [DEBUG] [main        ] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.rpc;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class Protocol$Adaptive implements org.apache.dubbo.rpc.Protocol {
public org.apache.dubbo.rpc.Exporter export(org.apache.dubbo.rpc.Invoker arg0) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = ( url.getProtocol() == null ? "dubbo" : url.getProtocol() );
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.Protocol) name from url (" + url.toString() + ") use keys([protocol])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.Protocol.class);
org.apache.dubbo.rpc.Protocol extension = (org.apache.dubbo.rpc.Protocol)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.Protocol.class).getExtension(extName);
return extension.export(arg0);
}
public org.apache.dubbo.rpc.Invoker refer(java.lang.Class arg0, org.apache.dubbo.common.URL arg1) throws org.apache.dubbo.rpc.RpcException {
if (arg1 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg1;
String extName = ( url.getProtocol() == null ? "dubbo" : url.getProtocol() );
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.Protocol) name from url (" + url.toString() + ") use keys([protocol])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.Protocol.class);
org.apache.dubbo.rpc.Protocol extension = (org.apache.dubbo.rpc.Protocol)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.Protocol.class).getExtension(extName);
return extension.refer(arg0, arg1);
}
public java.util.List getServers()  {
throw new UnsupportedOperationException("The method public default java.util.List org.apache.dubbo.rpc.Protocol.getServers() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
public void destroy()  {
throw new UnsupportedOperationException("The method public abstract void org.apache.dubbo.rpc.Protocol.destroy() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
public int getDefaultPort()  {
throw new UnsupportedOperationException("The method public abstract int org.apache.dubbo.rpc.Protocol.getDefaultPort() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,338 [DEBUG] [http-nio-8088-exec-1] _Log4jLoggerFactory$Log4jLogger - Couldn't find template in cache for "error.ftlh"("zh_CN", UTF-8, parsed); will try to load it.
2025-07-28 20:16:25,348 [DEBUG] [http-nio-8088-exec-1] _Log4jLoggerFactory$Log4jLogger - TemplateLoader.findTemplateSource("error_zh_CN.ftlh"): Not found
2025-07-28 20:16:25,351 [DEBUG] [http-nio-8088-exec-1] _Log4jLoggerFactory$Log4jLogger - TemplateLoader.findTemplateSource("error_zh.ftlh"): Not found
2025-07-28 20:16:25,352 [DEBUG] [http-nio-8088-exec-1] _Log4jLoggerFactory$Log4jLogger - TemplateLoader.findTemplateSource("error.ftlh"): Not found
2025-07-28 20:16:25,385 [INFO ] [DelayRecoverThread] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is starting., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,388 [INFO ] [DelayRecoverThread] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown) is starting., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,414 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing RegistryConfig with prefix [dubbo.registry], extracted props: {address=zookeeperx://zk.bass.3g:2181?file.cache=false, check=false, file=/Users/<USER>/dubbo-cache/dubbo-registry-yop-portal.cache, session=30000}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,474 [INFO ] [DelayRecoverThread] DefaultApplicationDeployer -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : zookeeperx] does not support as the config center, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,476 [INFO ] [DelayRecoverThread] DefaultApplicationDeployer -  [DUBBO] The registry[<dubbo:registry file="/Users/<USER>/dubbo-cache/dubbo-registry-yop-portal.cache" address="zookeeperx://zk.bass.3g:2181?file.cache=false" protocol="zookeeperx" check="false" port="2181" session="30000" />] will be not used as the config center, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,493 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ConfigCenterConfig with prefix [dubbo.config-center], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,510 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing MonitorConfig with prefix [dubbo.monitor], extracted props: {protocol=soamonitor, address=soamonitor://soamonitor}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,527 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=jsonx] with prefix [dubbo.protocols.jsonx], extracted props: {server=servletx, contextpath=yop-portal/soa/json, id=jsonx, name=jsonx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,539 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=rest] with prefix [dubbo.protocols.rest], extracted props: {contextpath=yop-portal/soa/rest, server=servletx, id=rest, name=rest}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,541 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=restx] with prefix [dubbo.protocols.restx], extracted props: {server=servletx, contextpath=yop-portal/soa/restx, id=restx, name=restx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,547 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=health] with prefix [dubbo.protocols.health], extracted props: {server=servletx, id=health, name=health}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,550 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=hessianx] with prefix [dubbo.protocols.hessianx], extracted props: {server=servletx, contextpath=yop-portal/soa/hessian, id=hessianx, name=hessianx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,552 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=httpx] with prefix [dubbo.protocols.httpx], extracted props: {server=servletx, contextpath=yop-portal/soa/http, id=httpx, name=httpx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,555 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=dubbo] with prefix [dubbo.protocols.dubbo], extracted props: {contextpath=yop-portal/soa/dubbo, id=dubbo, name=dubbo}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,567 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing MetadataReportConfig with prefix [dubbo.metadata-report], extracted props: {address=zookeeper://metazk.bass.3g:2181, report-metadata=true}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,573 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig[name=yop-portal] with prefix [dubbo.application], extracted props: {service-discovery.migration=FORCE_INTERFACE, logger=log4j, register-mode=all, name=yop-portal, qos-enable=false, hostname=bogon, protocol=dubbo, metadata-type=remote}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,577 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing MonitorConfig with prefix [dubbo.monitor], extracted props: {protocol=soamonitor, address=soamonitor://soamonitor}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,586 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=httpx] with prefix [dubbo.protocols.httpx], extracted props: {server=servletx, contextpath=yop-portal/soa/http, id=httpx, name=httpx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,596 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=rest] with prefix [dubbo.protocols.rest], extracted props: {contextpath=yop-portal/soa/rest, server=servletx, name=rest, id=rest}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,609 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=restx] with prefix [dubbo.protocols.restx], extracted props: {server=servletx, contextpath=yop-portal/soa/restx, name=restx, id=restx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,627 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=health] with prefix [dubbo.protocols.health], extracted props: {server=servletx, name=health, id=health}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,631 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=dubbo] with prefix [dubbo.protocols.dubbo], extracted props: {contextpath=yop-portal/soa/dubbo, name=dubbo, id=dubbo}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,633 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=jsonx] with prefix [dubbo.protocols.jsonx], extracted props: {server=servletx, contextpath=yop-portal/soa/json, id=jsonx, name=jsonx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,635 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=hessianx] with prefix [dubbo.protocols.hessianx], extracted props: {server=servletx, contextpath=yop-portal/soa/hessian, id=hessianx, name=hessianx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,642 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing RegistryConfig with prefix [dubbo.registry], extracted props: {address=zookeeperx://zk.bass.3g:2181?file.cache=false, check=false, file=/Users/<USER>/dubbo-cache/dubbo-registry-yop-portal.cache, session=30000, parameters=[{file.cache:false}], port=2181, protocol=zookeeperx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,653 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing MetadataReportConfig with prefix [dubbo.metadata-report], extracted props: {address=zookeeper://metazk.bass.3g:2181, report-metadata=true, protocol=zookeeper, port=2181}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,663 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing SslConfig with prefix [dubbo.ssl], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,695 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProviderConfig with prefix [dubbo.provider], extracted props: {filter=-exception,-context,-monitor,-generic,contextx,exceptionx,genericx,flowControl,access, loadbalance=randomx, retries=0, cluster=failfast}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,724 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ConsumerConfig with prefix [dubbo.consumer], extracted props: {loadbalance=randomx, retries=0, filter=-consumercontext,-monitor,consumercontextx,circuitBreaker, cluster=dynamic, check=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,745 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ModuleConfig with prefix [dubbo.module], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,752 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ModuleConfig with prefix [dubbo.module], extracted props: {background=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,759 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProviderConfig with prefix [dubbo.provider], extracted props: {filter=-exception,-context,-monitor,-generic,contextx,exceptionx,genericx,flowControl,access, loadbalance=randomx, retries=0, cluster=failfast, deprecated=false, dynamic=true}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,764 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ConsumerConfig with prefix [dubbo.consumer], extracted props: {loadbalance=randomx, retries=0, filter=-consumercontext,-monitor,consumercontextx,circuitBreaker, cluster=dynamic, check=false, sticky=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,778 [INFO ] [DelayRecoverThread] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] has been initialized!, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,782 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProviderConfig with prefix [dubbo.provider], extracted props: {filter=-exception,-context,-monitor,-generic,contextx,exceptionx,genericx,flowControl,access, loadbalance=randomx, retries=0, cluster=failfast}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,787 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ConsumerConfig with prefix [dubbo.consumer], extracted props: {loadbalance=randomx, retries=0, filter=-consumercontext,-monitor,consumercontextx,circuitBreaker, cluster=dynamic, check=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,791 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ModuleConfig with prefix [dubbo.module], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,794 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ModuleConfig with prefix [dubbo.module], extracted props: {background=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,796 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProviderConfig with prefix [dubbo.provider], extracted props: {filter=-exception,-context,-monitor,-generic,contextx,exceptionx,genericx,flowControl,access, loadbalance=randomx, retries=0, cluster=failfast, deprecated=false, dynamic=true}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,800 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ConsumerConfig with prefix [dubbo.consumer], extracted props: {loadbalance=randomx, retries=0, filter=-consumercontext,-monitor,consumercontextx,circuitBreaker, cluster=dynamic, check=false, sticky=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,802 [INFO ] [DelayRecoverThread] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has been initialized!, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:25,831 [DEBUG] [DelayRecoverThread] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.metadata.report;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class MetadataReportFactory$Adaptive implements org.apache.dubbo.metadata.report.MetadataReportFactory {
public org.apache.dubbo.metadata.report.MetadataReport getMetadataReport(org.apache.dubbo.common.URL arg0)  {
if (arg0 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg0;
String extName = ( url.getProtocol() == null ? "redis" : url.getProtocol() );
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.metadata.report.MetadataReportFactory) name from url (" + url.toString() + ") use keys([protocol])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.metadata.report.MetadataReportFactory.class);
org.apache.dubbo.metadata.report.MetadataReportFactory extension = (org.apache.dubbo.metadata.report.MetadataReportFactory)scopeModel.getExtensionLoader(org.apache.dubbo.metadata.report.MetadataReportFactory.class).getExtension(extName);
return extension.getMetadataReport(arg0);
}
public void destroy()  {
throw new UnsupportedOperationException("The method public default void org.apache.dubbo.metadata.report.MetadataReportFactory.destroy() of interface org.apache.dubbo.metadata.report.MetadataReportFactory is not adaptive method!");
}
}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,194 [ERROR] [DelayRecoverThread] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] start failed: java.lang.IllegalStateException: zookeeper not connected, dubbo version: 3.0.7, current host: *************
java.lang.IllegalStateException: zookeeper not connected
	at org.apache.dubbo.remoting.zookeeper.curator.CuratorZookeeperClient.<init>(CuratorZookeeperClient.java:89)
	at org.apache.dubbo.remoting.zookeeper.curator.CuratorZookeeperTransporter.createZookeeperClient(CuratorZookeeperTransporter.java:26)
	at org.apache.dubbo.remoting.zookeeper.AbstractZookeeperTransporter.connect(AbstractZookeeperTransporter.java:69)
	at org.apache.dubbo.metadata.store.zookeeper.ZookeeperMetadataReport.<init>(ZookeeperMetadataReport.java:77)
	at org.apache.dubbo.metadata.store.zookeeper.ZookeeperMetadataReportFactory.createMetadataReport(ZookeeperMetadataReportFactory.java:47)
	at org.apache.dubbo.metadata.report.support.AbstractMetadataReportFactory.getMetadataReport(AbstractMetadataReportFactory.java:67)
	at org.apache.dubbo.metadata.report.MetadataReportFactory$Adaptive.getMetadataReport(MetadataReportFactory$Adaptive.java)
	at org.apache.dubbo.metadata.report.MetadataReportInstance.init(MetadataReportInstance.java:96)
	at org.apache.dubbo.metadata.report.MetadataReportInstance.init(MetadataReportInstance.java:78)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.startMetadataCenter(DefaultApplicationDeployer.java:289)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.initialize(DefaultApplicationDeployer.java:192)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:141)
	at com.yeepay.g3.utils.soa.exporter.ServicePackageExporter.export(ServicePackageExporter.java:273)
	at com.yeepay.g3.utils.soa.trigger.SoaServiceRecoverTrigger.onStart(SoaServiceRecoverTrigger.java:73)
	at com.yeepay.g3.utils.soa.trigger.ServerDetector$1.run(ServerDetector.java:60)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalStateException: zookeeper not connected
	at org.apache.dubbo.remoting.zookeeper.curator.CuratorZookeeperClient.<init>(CuratorZookeeperClient.java:85)
	... 15 more
2025-07-28 20:16:56,222 [INFO ] [DelayRecoverThread] ServiceInstanceMetadataUtils -  [DUBBO] Start registering instance address to registry., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,240 [ERROR] [DelayRecoverThread] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](yop-portal) found failed module: Dubbo Module[1.1.1], dubbo version: 3.0.7, current host: *************
java.lang.IllegalStateException: zookeeper not connected
	at org.apache.dubbo.remoting.zookeeper.curator.CuratorZookeeperClient.<init>(CuratorZookeeperClient.java:89)
	at org.apache.dubbo.remoting.zookeeper.curator.CuratorZookeeperTransporter.createZookeeperClient(CuratorZookeeperTransporter.java:26)
	at org.apache.dubbo.remoting.zookeeper.AbstractZookeeperTransporter.connect(AbstractZookeeperTransporter.java:69)
	at org.apache.dubbo.metadata.store.zookeeper.ZookeeperMetadataReport.<init>(ZookeeperMetadataReport.java:77)
	at org.apache.dubbo.metadata.store.zookeeper.ZookeeperMetadataReportFactory.createMetadataReport(ZookeeperMetadataReportFactory.java:47)
	at org.apache.dubbo.metadata.report.support.AbstractMetadataReportFactory.getMetadataReport(AbstractMetadataReportFactory.java:67)
	at org.apache.dubbo.metadata.report.MetadataReportFactory$Adaptive.getMetadataReport(MetadataReportFactory$Adaptive.java)
	at org.apache.dubbo.metadata.report.MetadataReportInstance.init(MetadataReportInstance.java:96)
	at org.apache.dubbo.metadata.report.MetadataReportInstance.init(MetadataReportInstance.java:78)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.startMetadataCenter(DefaultApplicationDeployer.java:289)
	at org.apache.dubbo.config.deploy.DefaultApplicationDeployer.initialize(DefaultApplicationDeployer.java:192)
	at org.apache.dubbo.config.deploy.DefaultModuleDeployer.start(DefaultModuleDeployer.java:141)
	at com.yeepay.g3.utils.soa.exporter.ServicePackageExporter.export(ServicePackageExporter.java:273)
	at com.yeepay.g3.utils.soa.trigger.SoaServiceRecoverTrigger.onStart(SoaServiceRecoverTrigger.java:73)
	at com.yeepay.g3.utils.soa.trigger.ServerDetector$1.run(ServerDetector.java:60)
	at java.lang.Thread.run(Thread.java:750)
Caused by: java.lang.IllegalStateException: zookeeper not connected
	at org.apache.dubbo.remoting.zookeeper.curator.CuratorZookeeperClient.<init>(CuratorZookeeperClient.java:85)
	... 15 more
2025-07-28 20:16:56,243 [INFO ] [DubboShutdownHook] DubboShutdownHook -  [DUBBO] Run shutdown hook now., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,255 [INFO ] [DubboShutdownHook] FrameworkModel -  [DUBBO] Reset global default application from Dubbo Application[1.1](yop-portal) to null, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,257 [INFO ] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](yop-portal) is stopping., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,260 [INFO ] [DubboShutdownHook] RegistryManager -  [DUBBO] Close all registries [zookeeperx://zk.bass.3g:2181/org.apache.dubbo.registry.RegistryService?application=yop-portal&dubbo=3.0.7&file.cache=false&interface=org.apache.dubbo.registry.RegistryService], dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,264 [INFO ] [DubboShutdownHook] AbstractRegistry -  [DUBBO] Destroy registry:zookeeperx://zk.bass.3g:2181/org.apache.dubbo.registry.RegistryService?application=yop-portal&dubbo=3.0.7&file.cache=false&interface=org.apache.dubbo.registry.RegistryService, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,273 [INFO ] [DubboShutdownHook] AbstractRegistry -  [DUBBO] Unsubscribe: config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-yop-portal&version=*, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,279 [INFO ] [DubboShutdownHook] AbstractRegistry -  [DUBBO] Destroy unsubscribe url config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-yop-portal&version=*, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,279 [INFO ] [DubboShutdownHook] AbstractRegistry -  [DUBBO] Unsubscribe: config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-commoncfg&version=*, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,280 [INFO ] [DubboShutdownHook] AbstractRegistry -  [DUBBO] Destroy unsubscribe url config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-commoncfg&version=*, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,286 [DEBUG] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](yop-portal)'s all ServiceDiscoveries have been destroyed., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,292 [INFO ] [DubboShutdownHook] DubboProtocol -  [DUBBO] Destroying protocol [DubboProtocol] ..., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,295 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is stopping., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,304 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has stopped., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,307 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] is stopping., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,307 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] has stopped., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,309 [INFO ] [DubboShutdownHook] DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,309 [INFO ] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](yop-portal) has stopped., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,328 [INFO ] [DubboShutdownHook] FrameworkModel -  [DUBBO] Destroying default framework model: Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,328 [INFO ] [DubboShutdownHook] FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroying ..., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,328 [INFO ] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) is stopping., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,329 [INFO ] [DubboShutdownHook] RegistryManager -  [DUBBO] Close all registries [], dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,329 [DEBUG] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION)'s all ServiceDiscoveries have been destroyed., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,330 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] is stopping., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,330 [INFO ] [DubboShutdownHook] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.0.0] has stopped., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,331 [INFO ] [DubboShutdownHook] DefaultExecutorRepository -  [DUBBO] destroying application executor repository .., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,331 [INFO ] [DubboShutdownHook] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.0](DUBBO_INTERNAL_APPLICATION) has stopped., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,338 [INFO ] [DubboShutdownHook] FrameworkModel -  [DUBBO] Dubbo Framework[1] is destroyed, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,338 [INFO ] [DubboShutdownHook] FrameworkModel -  [DUBBO] Reset global default framework from Dubbo Framework[1] to null, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,343 [INFO ] [DubboShutdownHook] GlobalResourcesRepository -  [DUBBO] Destroying global resources ..., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,348 [INFO ] [DubboShutdownHook] GlobalResourcesRepository -  [DUBBO] Dubbo is completely destroyed, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,353 [INFO ] [DubboShutdownHook] FrameworkExecutorRepository -  [DUBBO] destroying framework executor repository .., dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,394 [INFO ] [SpringContextShutdownHook] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[2], dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,396 [INFO ] [SpringContextShutdownHook] FrameworkModel -  [DUBBO] Dubbo Framework[2] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,441 [INFO ] [SpringContextShutdownHook] ApplicationModel -  [DUBBO] Dubbo Application[2.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,442 [INFO ] [SpringContextShutdownHook] ModuleModel -  [DUBBO] Dubbo Module[2.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,470 [INFO ] [SpringContextShutdownHook] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,472 [INFO ] [SpringContextShutdownHook] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,523 [INFO ] [SpringContextShutdownHook] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[2.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,528 [INFO ] [SpringContextShutdownHook] ApplicationModel -  [DUBBO] Dubbo Application[2.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,528 [INFO ] [SpringContextShutdownHook] ModuleModel -  [DUBBO] Dubbo Module[2.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,535 [INFO ] [SpringContextShutdownHook] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,537 [INFO ] [SpringContextShutdownHook] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,543 [INFO ] [SpringContextShutdownHook] ModuleModel -  [DUBBO] Dubbo Module[2.1.1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:16:56,550 [INFO ] [SpringContextShutdownHook] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:38,660 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default framework from null to Dubbo Framework[1], dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:38,664 [INFO ] [main        ] FrameworkModel -  [DUBBO] Dubbo Framework[1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:38,745 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.0](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:38,745 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.0.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:38,789 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:38,789 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:38,893 [INFO ] [main        ] FrameworkModel -  [DUBBO] Reset global default application from null to Dubbo Application[1.1](unknown), dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:38,894 [INFO ] [main        ] ApplicationModel -  [DUBBO] Dubbo Application[1.1](unknown) is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:38,895 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.0] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:38,898 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:38,899 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:39,314 [WARN ] [main        ] ConfigUtils -  [DUBBO] No /apps/product/commoncfg/dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:39,315 [WARN ] [main        ] ConfigUtils -  [DUBBO] No dubbo.properties found on the class path., dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:48,545 [INFO ] [main        ] ModuleModel -  [DUBBO] Dubbo Module[1.1.1] is created, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:48,554 [INFO ] [main        ] AbstractConfigManager -  [DUBBO] Config settings: {dubbo.config.mode=STRICT, dubbo.config.ignore-duplicated-interface=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:48,797 [DEBUG] [main        ] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.remoting.http;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class HttpBinder$Adaptive implements org.apache.dubbo.remoting.http.HttpBinder {
public org.apache.dubbo.remoting.http.HttpServer bind(org.apache.dubbo.common.URL arg0, org.apache.dubbo.remoting.http.HttpHandler arg1)  {
if (arg0 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg0;
String extName = url.getParameter("server", "jetty");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.remoting.http.HttpBinder) name from url (" + url.toString() + ") use keys([server])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.remoting.http.HttpBinder.class);
org.apache.dubbo.remoting.http.HttpBinder extension = (org.apache.dubbo.remoting.http.HttpBinder)scopeModel.getExtensionLoader(org.apache.dubbo.remoting.http.HttpBinder.class).getExtension(extName);
return extension.bind(arg0, arg1);
}
}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:49,033 [DEBUG] [main        ] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.rpc;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class ProxyFactory$Adaptive implements org.apache.dubbo.rpc.ProxyFactory {
public java.lang.Object getProxy(org.apache.dubbo.rpc.Invoker arg0) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getProxy(arg0);
}
public java.lang.Object getProxy(org.apache.dubbo.rpc.Invoker arg0, boolean arg1) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getProxy(arg0, arg1);
}
public org.apache.dubbo.rpc.Invoker getInvoker(java.lang.Object arg0, java.lang.Class arg1, org.apache.dubbo.common.URL arg2) throws org.apache.dubbo.rpc.RpcException {
if (arg2 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg2;
String extName = url.getParameter("proxy", "javassist");
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.ProxyFactory) name from url (" + url.toString() + ") use keys([proxy])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.ProxyFactory.class);
org.apache.dubbo.rpc.ProxyFactory extension = (org.apache.dubbo.rpc.ProxyFactory)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.ProxyFactory.class).getExtension(extName);
return extension.getInvoker(arg0, arg1, arg2);
}
}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:49,145 [DEBUG] [main        ] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig with prefix [dubbo.application], extracted props: {service-discovery.migration=FORCE_INTERFACE, logger=log4j, register-mode=all, name=yop-portal, qos-enable=false, metadata-type=remote}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:49,551 [INFO ] [Curator-ConnectionStateManager-0] CuratorZookeeperClient$CuratorConnectionStateListener -  [DUBBO] Curator zookeeper client instance initiated successfully, session id is 2982ff0cef95b3f, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:49,552 [INFO ] [main        ] AbstractZookeeperTransporter -  [DUBBO] No valid zookeeper client found from cache, therefore create a new client for url. zookeeperx://zk.bass.3g:2181/org.apache.dubbo.registry.RegistryService?application=yop-portal&dubbo=3.0.7&file.cache=false&interface=org.apache.dubbo.registry.RegistryService, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:49,581 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Subscribe: config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-commoncfg&version=*, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:49,627 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Notify urls for subscribe url config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-commoncfg&version=*, url size: 2, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:49,639 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Subscribe: config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-yop-portal&version=*, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:49,656 [INFO ] [main        ] AbstractRegistry -  [DUBBO] Notify urls for subscribe url config://*************?category=configurators&check=false&classifier=*&enabled=*&group=*&interface=config-yop-portal&version=*, url size: 1, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:50,461 [DEBUG] [main        ] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.rpc;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class Protocol$Adaptive implements org.apache.dubbo.rpc.Protocol {
public org.apache.dubbo.rpc.Exporter export(org.apache.dubbo.rpc.Invoker arg0) throws org.apache.dubbo.rpc.RpcException {
if (arg0 == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument == null");
if (arg0.getUrl() == null) throw new IllegalArgumentException("org.apache.dubbo.rpc.Invoker argument getUrl() == null");
org.apache.dubbo.common.URL url = arg0.getUrl();
String extName = ( url.getProtocol() == null ? "dubbo" : url.getProtocol() );
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.Protocol) name from url (" + url.toString() + ") use keys([protocol])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.Protocol.class);
org.apache.dubbo.rpc.Protocol extension = (org.apache.dubbo.rpc.Protocol)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.Protocol.class).getExtension(extName);
return extension.export(arg0);
}
public org.apache.dubbo.rpc.Invoker refer(java.lang.Class arg0, org.apache.dubbo.common.URL arg1) throws org.apache.dubbo.rpc.RpcException {
if (arg1 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg1;
String extName = ( url.getProtocol() == null ? "dubbo" : url.getProtocol() );
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.rpc.Protocol) name from url (" + url.toString() + ") use keys([protocol])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.rpc.Protocol.class);
org.apache.dubbo.rpc.Protocol extension = (org.apache.dubbo.rpc.Protocol)scopeModel.getExtensionLoader(org.apache.dubbo.rpc.Protocol.class).getExtension(extName);
return extension.refer(arg0, arg1);
}
public java.util.List getServers()  {
throw new UnsupportedOperationException("The method public default java.util.List org.apache.dubbo.rpc.Protocol.getServers() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
public void destroy()  {
throw new UnsupportedOperationException("The method public abstract void org.apache.dubbo.rpc.Protocol.destroy() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
public int getDefaultPort()  {
throw new UnsupportedOperationException("The method public abstract int org.apache.dubbo.rpc.Protocol.getDefaultPort() of interface org.apache.dubbo.rpc.Protocol is not adaptive method!");
}
}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,493 [DEBUG] [http-nio-8088-exec-1] _Log4jLoggerFactory$Log4jLogger - Couldn't find template in cache for "error.ftlh"("zh_CN", UTF-8, parsed); will try to load it.
2025-07-28 20:17:58,499 [DEBUG] [http-nio-8088-exec-1] _Log4jLoggerFactory$Log4jLogger - TemplateLoader.findTemplateSource("error_zh_CN.ftlh"): Not found
2025-07-28 20:17:58,503 [DEBUG] [http-nio-8088-exec-1] _Log4jLoggerFactory$Log4jLogger - TemplateLoader.findTemplateSource("error_zh.ftlh"): Not found
2025-07-28 20:17:58,504 [DEBUG] [http-nio-8088-exec-1] _Log4jLoggerFactory$Log4jLogger - TemplateLoader.findTemplateSource("error.ftlh"): Not found
2025-07-28 20:17:58,548 [INFO ] [DelayRecoverThread] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] is starting., dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,551 [INFO ] [DelayRecoverThread] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](unknown) is starting., dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,619 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing RegistryConfig with prefix [dubbo.registry], extracted props: {address=zookeeperx://zk.bass.3g:2181?file.cache=false, check=false, file=/Users/<USER>/dubbo-cache/dubbo-registry-yop-portal.cache, session=30000}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,720 [INFO ] [DelayRecoverThread] DefaultApplicationDeployer -  [DUBBO] No value is configured in the registry, the DynamicConfigurationFactory extension[name : zookeeperx] does not support as the config center, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,722 [INFO ] [DelayRecoverThread] DefaultApplicationDeployer -  [DUBBO] The registry[<dubbo:registry file="/Users/<USER>/dubbo-cache/dubbo-registry-yop-portal.cache" address="zookeeperx://zk.bass.3g:2181?file.cache=false" protocol="zookeeperx" check="false" port="2181" session="30000" />] will be not used as the config center, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,734 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ConfigCenterConfig with prefix [dubbo.config-center], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,748 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing MonitorConfig with prefix [dubbo.monitor], extracted props: {protocol=soamonitor, address=soamonitor://soamonitor}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,763 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=jsonx] with prefix [dubbo.protocols.jsonx], extracted props: {server=servletx, contextpath=yop-portal/soa/json, id=jsonx, name=jsonx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,778 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=rest] with prefix [dubbo.protocols.rest], extracted props: {contextpath=yop-portal/soa/rest, server=servletx, id=rest, name=rest}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,783 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=restx] with prefix [dubbo.protocols.restx], extracted props: {server=servletx, contextpath=yop-portal/soa/restx, id=restx, name=restx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,793 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=health] with prefix [dubbo.protocols.health], extracted props: {server=servletx, id=health, name=health}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,798 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=hessianx] with prefix [dubbo.protocols.hessianx], extracted props: {server=servletx, contextpath=yop-portal/soa/hessian, id=hessianx, name=hessianx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,801 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=httpx] with prefix [dubbo.protocols.httpx], extracted props: {server=servletx, contextpath=yop-portal/soa/http, id=httpx, name=httpx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,806 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=dubbo] with prefix [dubbo.protocols.dubbo], extracted props: {contextpath=yop-portal/soa/dubbo, id=dubbo, name=dubbo}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,816 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing MetadataReportConfig with prefix [dubbo.metadata-report], extracted props: {address=zookeeper://metazk.bass.3g:2181, report-metadata=true}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,823 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ApplicationConfig[name=yop-portal] with prefix [dubbo.application], extracted props: {service-discovery.migration=FORCE_INTERFACE, logger=log4j, register-mode=all, name=yop-portal, qos-enable=false, hostname=bogon, protocol=dubbo, metadata-type=remote}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,827 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing MonitorConfig with prefix [dubbo.monitor], extracted props: {protocol=soamonitor, address=soamonitor://soamonitor}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,829 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=httpx] with prefix [dubbo.protocols.httpx], extracted props: {server=servletx, contextpath=yop-portal/soa/http, id=httpx, name=httpx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,831 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=rest] with prefix [dubbo.protocols.rest], extracted props: {contextpath=yop-portal/soa/rest, server=servletx, name=rest, id=rest}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,834 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=restx] with prefix [dubbo.protocols.restx], extracted props: {server=servletx, contextpath=yop-portal/soa/restx, name=restx, id=restx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,844 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=health] with prefix [dubbo.protocols.health], extracted props: {server=servletx, name=health, id=health}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,856 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=dubbo] with prefix [dubbo.protocols.dubbo], extracted props: {contextpath=yop-portal/soa/dubbo, name=dubbo, id=dubbo}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,873 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=jsonx] with prefix [dubbo.protocols.jsonx], extracted props: {server=servletx, contextpath=yop-portal/soa/json, id=jsonx, name=jsonx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,879 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProtocolConfig[id=hessianx] with prefix [dubbo.protocols.hessianx], extracted props: {server=servletx, contextpath=yop-portal/soa/hessian, id=hessianx, name=hessianx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,896 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing RegistryConfig with prefix [dubbo.registry], extracted props: {address=zookeeperx://zk.bass.3g:2181?file.cache=false, check=false, file=/Users/<USER>/dubbo-cache/dubbo-registry-yop-portal.cache, session=30000, parameters=[{file.cache:false}], port=2181, protocol=zookeeperx}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,913 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing MetadataReportConfig with prefix [dubbo.metadata-report], extracted props: {address=zookeeper://metazk.bass.3g:2181, report-metadata=true, protocol=zookeeper, port=2181}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,932 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing SslConfig with prefix [dubbo.ssl], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,963 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProviderConfig with prefix [dubbo.provider], extracted props: {filter=-exception,-context,-monitor,-generic,contextx,exceptionx,genericx,flowControl,access, loadbalance=randomx, retries=0, cluster=failfast}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:58,983 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ConsumerConfig with prefix [dubbo.consumer], extracted props: {loadbalance=randomx, retries=0, filter=-consumercontext,-monitor,consumercontextx,circuitBreaker, cluster=dynamic, check=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,001 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ModuleConfig with prefix [dubbo.module], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,011 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ModuleConfig with prefix [dubbo.module], extracted props: {background=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,015 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProviderConfig with prefix [dubbo.provider], extracted props: {filter=-exception,-context,-monitor,-generic,contextx,exceptionx,genericx,flowControl,access, loadbalance=randomx, retries=0, cluster=failfast, deprecated=false, dynamic=true}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,022 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ConsumerConfig with prefix [dubbo.consumer], extracted props: {loadbalance=randomx, retries=0, filter=-consumercontext,-monitor,consumercontextx,circuitBreaker, cluster=dynamic, check=false, sticky=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,034 [INFO ] [DelayRecoverThread] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] has been initialized!, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,037 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProviderConfig with prefix [dubbo.provider], extracted props: {filter=-exception,-context,-monitor,-generic,contextx,exceptionx,genericx,flowControl,access, loadbalance=randomx, retries=0, cluster=failfast}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,040 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ConsumerConfig with prefix [dubbo.consumer], extracted props: {loadbalance=randomx, retries=0, filter=-consumercontext,-monitor,consumercontextx,circuitBreaker, cluster=dynamic, check=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,042 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ModuleConfig with prefix [dubbo.module], extracted props: {}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,045 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ModuleConfig with prefix [dubbo.module], extracted props: {background=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,047 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ProviderConfig with prefix [dubbo.provider], extracted props: {filter=-exception,-context,-monitor,-generic,contextx,exceptionx,genericx,flowControl,access, loadbalance=randomx, retries=0, cluster=failfast, deprecated=false, dynamic=true}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,051 [DEBUG] [DelayRecoverThread] AbstractConfig -  [DUBBO] Refreshing ConsumerConfig with prefix [dubbo.consumer], extracted props: {loadbalance=randomx, retries=0, filter=-consumercontext,-monitor,consumercontextx,circuitBreaker, cluster=dynamic, check=false, sticky=false}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,055 [INFO ] [DelayRecoverThread] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has been initialized!, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,083 [DEBUG] [DelayRecoverThread] AdaptiveClassCodeGenerator -  [DUBBO] package org.apache.dubbo.metadata.report;
import org.apache.dubbo.rpc.model.ScopeModel;
import org.apache.dubbo.rpc.model.ScopeModelUtil;
public class MetadataReportFactory$Adaptive implements org.apache.dubbo.metadata.report.MetadataReportFactory {
public org.apache.dubbo.metadata.report.MetadataReport getMetadataReport(org.apache.dubbo.common.URL arg0)  {
if (arg0 == null) throw new IllegalArgumentException("url == null");
org.apache.dubbo.common.URL url = arg0;
String extName = ( url.getProtocol() == null ? "redis" : url.getProtocol() );
if(extName == null) throw new IllegalStateException("Failed to get extension (org.apache.dubbo.metadata.report.MetadataReportFactory) name from url (" + url.toString() + ") use keys([protocol])");
ScopeModel scopeModel = ScopeModelUtil.getOrDefault(url.getScopeModel(), org.apache.dubbo.metadata.report.MetadataReportFactory.class);
org.apache.dubbo.metadata.report.MetadataReportFactory extension = (org.apache.dubbo.metadata.report.MetadataReportFactory)scopeModel.getExtensionLoader(org.apache.dubbo.metadata.report.MetadataReportFactory.class).getExtension(extName);
return extension.getMetadataReport(arg0);
}
public void destroy()  {
throw new UnsupportedOperationException("The method public default void org.apache.dubbo.metadata.report.MetadataReportFactory.destroy() of interface org.apache.dubbo.metadata.report.MetadataReportFactory is not adaptive method!");
}
}, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,181 [INFO ] [Curator-ConnectionStateManager-0] CuratorZookeeperClient$CuratorConnectionStateListener -  [DUBBO] Curator zookeeper client instance initiated successfully, session id is 1001245a1490000, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,182 [INFO ] [DelayRecoverThread] AbstractZookeeperTransporter -  [DUBBO] No valid zookeeper client found from cache, therefore create a new client for url. zookeeper://metazk.bass.3g:2181/org.apache.dubbo.metadata.report.MetadataReport?application=yop-portal&port=2181&protocol=zookeeper&report-metadata=true, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,184 [INFO ] [DelayRecoverThread] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](yop-portal) has been initialized!, dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,184 [INFO ] [DelayRecoverThread] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] is starting., dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,185 [INFO ] [DelayRecoverThread] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.0] has started., dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,187 [INFO ] [DelayRecoverThread] DefaultModuleDeployer -  [DUBBO] Dubbo Module[1.1.1] has started., dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,195 [INFO ] [DelayRecoverThread] ServiceInstanceMetadataUtils -  [DUBBO] Start registering instance address to registry., dubbo version: 3.0.7, current host: *************
2025-07-28 20:17:59,203 [INFO ] [DelayRecoverThread] DefaultApplicationDeployer -  [DUBBO] Dubbo Application[1.1](yop-portal) is ready., dubbo version: 3.0.7, current host: *************
