/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.perm.dto.AclAuditRecordStageDTO;
import com.yeepay.g3.facade.yop.perm.dto.AclAuditStageDTO;
import com.yeepay.g3.facade.yop.perm.enums.AuditStatusEnum;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-12-01 11:52
 */
public class AuditRecordDetailVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private String code;

    private Date createdDate;

    private String originator;

    private AuditStatusEnum status;

    private String resourceName;

    private String cause;

    private List<String> nextRoles;

    private List<String> nextOperators;

    private List<AclAuditRecordStageDTO> auditRecordStage;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getOriginator() {
        return originator;
    }

    public void setOriginator(String originator) {
        this.originator = originator;
    }

    public AuditStatusEnum getStatus() {
        return status;
    }

    public void setStatus(AuditStatusEnum status) {
        this.status = status;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getCause() {
        return cause;
    }

    public void setCause(String cause) {
        this.cause = cause;
    }

    public List<String> getNextRoles() {
        return nextRoles;
    }

    public void setNextRoles(List<String> nextRoles) {
        this.nextRoles = nextRoles;
    }

    public List<String> getNextOperators() {
        return nextOperators;
    }

    public void setNextOperators(List<String> nextOperators) {
        this.nextOperators = nextOperators;
    }

    public List<AclAuditRecordStageDTO> getAuditRecordStage() {
        return auditRecordStage;
    }

    public void setAuditRecordStage(List<AclAuditRecordStageDTO> auditRecordStage) {
        this.auditRecordStage = auditRecordStage;
    }
}
