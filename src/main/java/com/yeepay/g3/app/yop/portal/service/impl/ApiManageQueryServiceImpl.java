package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ApiManageQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.facade.yop.sys.enums.RequestSuccessTypeEnum;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * title: SpiQueryServiceImpl<br/>
 * description: spi查询实现<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:46
 */
@Component
public class ApiManageQueryServiceImpl implements ApiManageQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiManageQueryServiceImpl.class);

    @Resource(name = "apiManageQueryService")
    private QueryService queryService;

    @Resource(name = "apiRouteQueryService")
    private QueryService apiRouteQueryService;

    private PageItemConverter<ApiManagePageItem> pageItemConverter = new ApiManageItemConverter();
    private PageItemConverter<ErrorCodeExtendInfoItem> errorCodeExtendInfoConverter = new ErrorCodeExtendInfoConverter();

    private PageItemConverter<ApiPublishRecordPageItem> publishRecordPageItemConverter = new ApiPublishRecordItemConverter();

    @Override
    public PageQueryResult<ApiManagePageItem> pageList(ApiManagePageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        QueryResult queryResult = queryService.query("pageList", queryParam);
        PageQueryResult<ApiManagePageItem> pageQueryResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        addRouteDeployStatus(pageQueryResult.getItems());
        return pageQueryResult;
    }

    @Override
    public PageQueryResult<ApiManagePageItem> pageListForSp(ApiManagePageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> map = getBizParams(param);
        queryParam.setParams(map);
        QueryResult queryResult = queryService.query("pageListForSp", queryParam);
        PageQueryResult<ApiManagePageItem> pageQueryResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        addRouteDeployStatus(pageQueryResult.getItems());
        return pageQueryResult;
    }

    @Override
    public Map<String, Object> simpleApiDetail(String apiId, String apiUri) {
        if (StringUtils.isBlank(apiId) && StringUtils.isBlank(apiUri)) {
            throw new YeepayRuntimeException("at least one query key should specified");
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("apiId", apiId);
        paramMap.put("apiUri", apiUri);
        List<Map> list = queryService.query("apiDetail", paramMap);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public PageQueryResult<ApiPublishRecordPageItem> publishRecordList(ApiPublishRecordPageQueryParam param) {
        List queryResult = queryService.query("listPublishRecord", getPublishRecordBizParams(param));
        return convertResult(queryResult, param, publishRecordPageItemConverter);
    }

    @SuppressWarnings("unchecked")
    private <Item> PageQueryResult<Item> convertResult(List<Map> queryResult, BasePageQueryParam param, PageItemConverter<Item> itemConverter) {
        PageQueryResult<Item> result = new PageQueryResult<>();
        List<Item> items = new ArrayList<>(CollectionUtils.size(queryResult));
        if (CollectionUtils.isNotEmpty(queryResult)) {
            queryResult.forEach(map -> items.add((Item) itemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public PageQueryResult<ApiPublishRecordPageItem> publishRecordListForSp(ApiPublishRecordPageQueryParam param) {
        List queryResult = queryService.query("listPublishRecordForSp", getPublishRecordBizParams(param));
        return convertResult(queryResult, param, publishRecordPageItemConverter);
    }

    @Override
    public List<ApiManagePageItem> subRefList(String apiId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("apiId", apiId);
        List<Map> queryResult = queryService.query("subRefList", params);
        List<ApiManagePageItem> res = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(queryResult)) {
            queryResult.forEach(item -> {
                res.add(pageItemConverter.convert(item));
            });
        }
        addRouteDeployStatus(res);
        return res;
    }

    @Override
    public Object errorCodeLocationDetail(String apiId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("apiId", apiId);
        List<Map<String, Object>> queryResult = queryService.query("getErrorCodeLocation", params);
        if (CollectionUtils.isNotEmpty(queryResult)) {
            return errorCodeExtendInfoConverter.convert(queryResult.get(0));
        }
        return new HashMap<>();
    }

    @Override
    public List queryNoConfigErrorCodeLocationApi(String apiGroup) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("apiGroup", apiGroup);
        List<Map<String, Object>> queryResult = queryService.query("queryNoConfigErrorCodeLocationApi", params);
        return queryResult;
    }

    private void addRouteDeployStatus(List<ApiManagePageItem> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }
        List<String> apiIds = items.stream()
                .map(ApiManagePageItem::getApiId)
                .collect(Collectors.toList());
        List<Map<String, Object>> result = apiRouteQueryService.query("queryDeployStatus", Collections.singletonMap("apiIds", apiIds));
        Map<String, String> statusMap = new HashMap<>();
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        result.forEach(map -> {
            String apiId = (String) map.get("api_id");
            boolean hasDeploy = 1 == ((Number) map.get("has_deploy")).intValue();
            boolean hasUpdate = 1 == ((Number) map.get("has_update")).intValue();
            String status = hasDeploy ? (hasUpdate ? "UNPUBLISHED" : "PUBLISHED") : "NEVER_PUBLISHED";
            statusMap.put(apiId, status);
        });
        items.forEach(item -> item.setRouteStatus(StringUtils.defaultIfEmpty(statusMap.get(item.getApiId()), "NEVER_PUBLISHED")));
    }

    private Map<String, Object> getBizParams(ApiManagePageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("path", param.getPath());
        bizParams.put("method", param.getMethod());
        bizParams.put("name", param.getName());
        bizParams.put("title", param.getTitle());
        bizParams.put("apiGroup", param.getApiGroup());
        bizParams.put("status", param.getStatus());
        if (CollectionUtils.isNotEmpty(param.getApiGroupCodes())) {
            bizParams.put("apiGroupCodes", param.getApiGroupCodes());
        }
        bizParams.put("routeStatus", param.getRouteStatus());
        bizParams.put("apiType", param.getApiType());
        return bizParams;
    }

    private Map<String, Object> getPublishRecordBizParams(ApiPublishRecordPageQueryParam param) {
        Map<String, Object> result = Maps.newHashMap();
        if (param.getApiGroup() != null) {
            result.put("apiGroup", param.getApiGroup());
        }
        if (CollectionUtils.isNotEmpty(param.getApiGroupCodes())) {
            result.put("apiGroupCodes", param.getApiGroupCodes());
        }
        if (param.getPath() != null) {
            result.put("path", param.getPath());
        }
        if (param.getMethod() != null) {
            result.put("method", param.getMethod());
        }
        if (param.getOpType() != null) {
            result.put("opType", param.getOpType());
        }
        if (param.getCreatedStartDate() != null) {
            result.put("createdStartDate", param.getCreatedStartDate());
        }
        if (param.getCreatedEndDate() != null) {
            result.put("createdEndDate", param.getCreatedEndDate());
        }
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        result.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        result.put("_maxSize", pageSize);
        return result;
    }

    class ApiManageItemConverter extends BasePageItemConverter<ApiManagePageItem> {

        @Override
        public ApiManagePageItem convert(Map<String, Object> params) {
            ApiManagePageItem item = new ApiManagePageItem();
            item.setApiId((String) params.get("api_id"));
            item.setVersion((Long) params.get("version"));
            item.setName((String) params.get("name"));
            item.setPath((String) params.get("path"));
            item.setHttpMethod((String) params.get("http_method"));
            item.setContentType((String) params.get("content_type"));
            item.setTitle((String) params.get("title"));
            item.setApiGroup((String) params.get("api_group"));
            item.setApiGroupName((String) params.get("api_group_name"));
            item.setApiType((String) params.get("api_type"));
            item.setDescription((String) params.get("description"));
            String status = (String) params.get("status");
            status = StringUtils.replace(status, "DOC_", "");
            item.setStatus(status);
            item.setCreatedDateTime((Date) params.get("created_datetime"));
            item.setLastModifiedDateTime((Date) params.get("last_modified_datetime"));
            return item;
        }
    }

    class ApiPublishRecordItemConverter extends BasePageItemConverter<ApiPublishRecordPageItem> {

        @Override
        public ApiPublishRecordPageItem convert(Map<String, Object> params) {
            return new ApiPublishRecordPageItem()
                    .withId((Long) params.get("id"))
                    .withApiId((String) params.get("api_id"))
                    .withApiGroup((String) params.get("api_group"))
                    .withApiGroupName((String) params.get("api_group_name"))
                    .withRequestPath((String) params.get("request_path"))
                    .withRequestMethod((String) params.get("request_method"))
                    .withOpType((String) params.get("op_type"))
                    .withOperator((String) params.get("operator"))
                    .withCause((String) params.get("cause"))
                    .withCreatedDate((Date) params.get("created_datetime"));
        }
    }

    class ErrorCodeExtendInfoConverter extends BasePageItemConverter<ErrorCodeExtendInfoItem> {

        @Override
        public ErrorCodeExtendInfoItem convert(Map<String, Object> params) {
            return ErrorCodeExtendInfoItem.builder()
                    .requestSuccessValue((String) params.get("requestSuccessValue"))
                    .requestSuccessType(StringUtils.isBlank((String) params.get("requestSuccessValue")) ? RequestSuccessTypeEnum.NO_BACK_CODE.name() : RequestSuccessTypeEnum.BACK_CODE.name())
                    .messageLocation((String) params.get("messageLocation"))
                    .errorCodeLocation((String) params.get("errorCodeLocation"))
                    .nonce((Long) params.get("nonce"))
                    .build();
        }
    }
}
