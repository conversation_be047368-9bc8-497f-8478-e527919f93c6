/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.ext.gateway.nbyh.context.authenticate;

import com.google.common.base.Charsets;
import com.yeepay.g3.encryptor.yop.certloader.CertLoader;
import com.yeepay.g3.encryptor.yop.dto.EncryptorCertDTO;
import com.yeepay.g3.yop.ext.gateway.context.authenticate.AuthenticateContext;
import com.yeepay.g3.yop.ext.gateway.context.authenticate.AuthenticateContextParser;
import com.yeepay.g3.yop.ext.gateway.context.protocol.ProtocolMakerEnum;
import com.yeepay.g3.yop.ext.gateway.nbyh.context.authenticate.iml.NbyhAuthenticateContext;
import com.yeepay.g3.yop.ext.gateway.nbyh.utils.NbyhGmUtil;
import com.yeepay.g3.yop.ext.gateway.nbyh.utils.NbyhUtils;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Base64;
import java.util.Map;

import static com.yeepay.g3.yop.ext.gateway.nbyh.utils.NbyhUtils.YOP_KEY_ALIAS;
import static com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_BODY_ATTR;

/**
 * title: 宁波银行认证上下文解析器<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/4
 */
@Component
public class NbyhAuthenticateContextParser implements AuthenticateContextParser {

    @Autowired
    private CertLoader<EncryptorCertDTO, EncryptorCertDTO> certLoader;

    @Override
    public AuthenticateContext parse(ServerWebExchange exchange) {
        final Map<String, String> requestBodyMap = exchange.getAttribute(GATEWAY_REQUEST_BODY_ATTR);
        String customerNo = requestBodyMap.get(NbyhUtils.COMMON_FIELD_IOBP_CHANNELID);
        String scrtSign = requestBodyMap.get(NbyhUtils.COMMON_FIELD_SCRTSGN);
        String scrtData = requestBodyMap.get(NbyhUtils.COMMON_FIELD_SCRTDATA);
        String scrtKey = requestBodyMap.get(NbyhUtils.COMMON_FIELD_SCRTKEY);
        try {
            String random = new String(NbyhGmUtil.sm2Decrypt(Base64.getDecoder().decode(scrtKey),
                    certLoader.getYopKey(YOP_KEY_ALIAS).getPrivateKey()), Charsets.UTF_8);

            String decryptData = new String(NbyhGmUtil.sm4DecryptECB(random.getBytes(Charsets.UTF_8),
                    Base64.getDecoder().decode(scrtData), NbyhUtils.ENCRYPT_ALG), Charsets.UTF_8);

            byte[] decryptSign = NbyhGmUtil.sm4DecryptECB(random.getBytes(Charsets.UTF_8), Base64.getDecoder().decode(scrtSign), NbyhUtils.ENCRYPT_ALG);

            requestBodyMap.put(NbyhUtils.COMMON_FIELD_DECRYPTED_DATA, decryptData);
            requestBodyMap.put(NbyhUtils.COMMON_FIELD_DECRYPTED_KEY, random);
            requestBodyMap.put(NbyhUtils.COMMON_FIELD_DECRYPTED_SIGN, Base64.getEncoder().encodeToString(decryptSign));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        NbyhAuthenticateContext authenticateContext = new NbyhAuthenticateContext(ServerWebExchangeUtils.getApiUri(exchange),
                requestBodyMap, customerNo);
        authenticateContext.initialize("", "");
        return authenticateContext;
    }

    @Override
    public ProtocolMakerEnum getProtocolMaker() {
        return ProtocolMakerEnum.NBYH;
    }
}
