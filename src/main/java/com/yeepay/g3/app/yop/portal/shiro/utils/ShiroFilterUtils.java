package com.yeepay.g3.app.yop.portal.shiro.utils;

import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Map;

/**
 * title: <br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017/5/25 下午11:16
 */
public final class ShiroFilterUtils {

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonDefaultMapper();

    public ShiroFilterUtils() {
        // do nothing
    }

    /**
     * 是否是Ajax请求
     *
     * @param request 请求
     * @return 是否是Ajax请求
     */
    public static boolean isAjax(ServletRequest request) {
        return "XMLHttpRequest".equalsIgnoreCase(((HttpServletRequest) request).getHeader("X-Requested-With"));
    }

    /**
     * response 输出JSON
     *
     * @param response
     * @param resultMap
     * @throws IOException
     */
    public static void out(ServletResponse response, Map<String, String> resultMap) {

        PrintWriter out = null;
        try {
            response.setCharacterEncoding("UTF-8");
            out = response.getWriter();
            out.println(JSON_MAPPER.toJson(resultMap));
        } catch (Exception e) {
            // do nothing
        } finally {
            if (null != out) {
                out.flush();
                out.close();
            }
        }
    }

}
