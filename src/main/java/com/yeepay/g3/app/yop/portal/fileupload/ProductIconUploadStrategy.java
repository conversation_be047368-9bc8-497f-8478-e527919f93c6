/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.fileupload;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/15 14:14
 */
@Component
public class ProductIconUploadStrategy implements FileUploadStrategy {
    @Override
    public String resolveFileName(String bizCode, String fileId, String extName) {
        return "docs/products/" + convertToKebabCase(bizCode) + "/icon" + extName;
    }

    private String convertToKebabCase(String input) {
        // 将下划线和大写字母转换为中划线加小写字母
        return input.replaceAll("_", "-").toLowerCase();
    }

    @Override
    public String name() {
        return "product_icon";
    }

    @PostConstruct
    public void register() {
        FileUploadFactory.register(this);
    }
}
