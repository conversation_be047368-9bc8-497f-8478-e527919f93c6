/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.google.common.collect.Lists;
import com.yeepay.g3.facade.yop.api.dto.ApiParamDefineDTO;
import com.yeepay.g3.facade.yop.api.dto.ApiReturnParamDefineDTO;
import com.yeepay.g3.facade.yop.api.enums.*;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/9/1 8:58 下午
 */
@Data
public class ApiVO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 服务名
     */
    private String apiTitle;

    /**
     * 服务URI
     */
    private String apiUri;

    /**
     * 分组
     */
    private String apiGroup;

    /**
     * 版本
     */
    private String apiVersion;

    /**
     * 签名算法
     */
    private String signAlg;

    /**
     * 请求方法
     */
    private HttpMethodType[] httpMethod;

    /**
     * API类型
     */
    private ApiTypeEnum apiType = ApiTypeEnum.WEB;

    /**
     * 状态
     */
    private ApiStatusEnum status = ApiStatusEnum.ACTIVE;

    /**
     * API适配类型
     */
    private ApiAdapterType apiAdapterType = ApiAdapterType.TRANSFORM;

    /**
     * URI形式定义端点详情，以灵活支持后端多类型的服务，endXXX参数为冗余信息，便于管理或查询过滤
     */
    private String backendUri;

    /**
     * 端点协议
     */
    private EndProtocolEnum endProtocol = EndProtocolEnum.HESSIAN;

    /**
     * 端点服务类名
     */
    private String endClass;

    /**
     * 端点服务方法名
     */
    private String endMethod;

    /**
     * 端点服务URL
     */
    private String endServiceUrl;

    /**
     * 服务分组
     */
    private List<String> serviceGroups = Lists.newArrayList();

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 最后更新时间
     */
    private Date lastModifyTime;

    private String description;

    /**
     * 请求派发hessian（也即此api所属hessian，由老版yop-center向新版nginx迁移时使用），需在soa注册中心可以找到
     */
    private String backendApp;

    /**
     * 标签
     */
    private List<String> tags;

    private Long version;

    /**
     * 请求参数列表，为了兼容原因，此处paramList仅指原有form类型参数
     */
    private List<ApiParamDefineDTO> paramList;

    /**
     * 请求参数列表，json类型参数
     */
    private List<ApiParamDefineDTO> jsonParamList;

    /**
     * 回参列表
     */
    private List<ApiReturnParamDefineDTO> returnParamList;

    /**
     * 单个 API 的安全需求
     */
    private List<SecurityReqDTO> apiSecurity;

    /**
     * 业务订单号参数
     */
    private String bizOrderFormParam;

    private String bizOrderJsonParam;

}
