package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.dto.SpiDTO;

import java.io.Serializable;
import java.util.List;

/**
 * title: Spi导入项<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 17:53
 */
public class SpiImportItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private SpiDTO spi;

    private List<String> modelsToCreate;

    private List<String> modelsToOverride;

    public SpiDTO getSpi() {
        return spi;
    }

    public void setSpi(SpiDTO spi) {
        this.spi = spi;
    }

    public SpiImportItem withSpi(SpiDTO spi) {
        this.spi = spi;
        return this;
    }

    public List<String> getModelsToCreate() {
        return modelsToCreate;
    }

    public void setModelsToCreate(List<String> modelsToCreate) {
        this.modelsToCreate = modelsToCreate;
    }

    public SpiImportItem withModelsToCreate(List<String> modelsToCreate) {
        this.modelsToCreate = modelsToCreate;
        return this;
    }

    public List<String> getModelsToOverride() {
        return modelsToOverride;
    }

    public void setModelsToOverride(List<String> modelsToOverride) {
        this.modelsToOverride = modelsToOverride;
    }

    public SpiImportItem withModelsToOverride(List<String> modelsToOverride) {
        this.modelsToOverride = modelsToOverride;
        return this;
    }
}
