package com.yeepay.g3.yop.gateway.filter.protocol.response.yop.v300rc1;

import com.yeepay.g3.yop.frame.CharacterConstants;
import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.frame.response.ResponseType;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;
import com.yeepay.g3.yop.gateway.annotations.ResponseTypeAdaptive;
import com.yeepay.g3.yop.gateway.annotations.SDKVersionAdaptive;
import com.yeepay.g3.yop.ext.gateway.context.SDKVersion;
import com.yeepay.g3.yop.gateway.filter.protocol.response.yop.v300rc1.exceptions.StatusEnum;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/1/10 下午3:32
 */
@ResponseTypeAdaptive(ResponseType.EXCEPTION)
@SDKVersionAdaptive(min = SDKVersion.V300RC1)
public abstract class AbstractExceptionYopMarshaller extends AbstractYopMarshallerV3 {

    private static final String PLATFORM_PREFIX = "#platform_";

    private static final String PLATFORM_40044_CODE_PREFIX = "isp.code";

    private static final String PLATFORM_40044_SCENE_PREFIX = "isp.scene";

    private static final String PLATFORM_40044_SCENE_FILE_PREFIX = "isp.scene.filestore";

    @Override
    protected int getHttpStatus() {
        return HttpStatus.SC_INTERNAL_SERVER_ERROR;
    }

    @Override
    protected Object getUnMarshalResult(ServerWebExchange exchange, Response response) {
        ExceptionResult result = getErrorResult(exchange, response);
        result.setRequestId(ServerWebExchangeUtils.getRequestId(exchange));
        SDKVersion sdkVersion = ServerWebExchangeUtils.getSDKVersion(exchange);
        if (sdkVersion == SDKVersion.V331 || sdkVersion == SDKVersion.V430) {
            String subCode = result.getSubCode();
            String docPath = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_MBR_ERROR_CODE_DOC_PATH, String.class);
            String subPath = subCode;
            if (StringUtils.equals(result.getCode(), StatusEnum.BUSINESS_ERROR.getCode())) {
                if (StringUtils.startsWith(subCode, PLATFORM_40044_CODE_PREFIX)) {
                    subPath = PLATFORM_40044_CODE_PREFIX;
                }
                if (StringUtils.startsWith(subCode, PLATFORM_40044_SCENE_PREFIX) && !StringUtils.startsWith(subCode, PLATFORM_40044_SCENE_FILE_PREFIX)) {
                    subPath = PLATFORM_40044_SCENE_PREFIX;
                }
            }
            subPath = StringUtils.replace(subPath, CharacterConstants.DOT, CharacterConstants.UNDER_LINE);
            result.setDocUrl(docPath + PLATFORM_PREFIX + subPath);
        }
        return result;
    }

    /**
     * 获取错误信息
     *
     * @param exchange
     * @param response
     * @return
     */
    public abstract ExceptionResult getErrorResult(ServerWebExchange exchange, Response response);
}
