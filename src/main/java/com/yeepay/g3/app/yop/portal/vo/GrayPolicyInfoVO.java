/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.app.yop.portal.vo;

import java.io.Serializable;

/**
 * title: <br>
 * description: GrayPolicyInfoVO<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-06-26 16:44
 */
public class GrayPolicyInfoVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private String policyDesc;

    private int weight = 10000;

    private Boolean enabled;

    public Long getId() {
        return id;
    }

    public GrayPolicyInfoVO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getPolicyDesc() {
        return policyDesc;
    }

    public GrayPolicyInfoVO setPolicyDesc(String policyDesc) {
        this.policyDesc = policyDesc;
        return this;
    }

    public int getWeight() {
        return weight;
    }

    public GrayPolicyInfoVO setWeight(int weight) {
        this.weight = weight;
        return this;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public GrayPolicyInfoVO setEnabled(Boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    @Override
    public String toString() {
        return "GrayPolicyInfoVO{" +
                "id=" + id +
                ", policyDesc='" + policyDesc + '\'' +
                ", weight=" + weight +
                ", enabled=" + enabled +
                '}';
    }
}