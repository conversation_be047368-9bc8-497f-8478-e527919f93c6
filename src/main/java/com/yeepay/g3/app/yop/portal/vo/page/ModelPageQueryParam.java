package com.yeepay.g3.app.yop.portal.vo.page;

import java.util.List;

/**
 * title: ModelPageQueryParam<br/>
 * description: 模型分页查询参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:33
 */
public class ModelPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private String name;

    private String apiGroup;

    private String description;

    private List<String> apiGroupCodes;

    public String getName() {
        return name;
    }

    public ModelPageQueryParam setName(String name) {
        this.name = name;
        return this;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public ModelPageQueryParam setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public ModelPageQueryParam setDescription(String description) {
        this.description = description;
        return this;
    }

    public List<String> getApiGroupCodes() {
        return apiGroupCodes;
    }

    public ModelPageQueryParam setApiGroupCodes(List<String> apiGroupCodes) {
        this.apiGroupCodes = apiGroupCodes;
        return this;
    }

    @Override
    public String toString() {
        return "ModelPageQueryParam{" +
                "name='" + name + '\'' +
                ", apiGroup='" + apiGroup + '\'' +
                ", description='" + description + '\'' +
                ", apiGroupCodes=" + apiGroupCodes +
                '}';
    }
}
