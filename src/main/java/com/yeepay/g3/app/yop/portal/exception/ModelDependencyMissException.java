package com.yeepay.g3.app.yop.portal.exception;

import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;

/**
 * title: Model倚赖缺失异常<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 16:16
 */
public class ModelDependencyMissException extends YeepayRuntimeException {

    private static final long serialVersionUID = -1L;

    private String modelName;

    public ModelDependencyMissException(String modelName) {
        super("model dependency miss, modelName:{0}.", modelName);
        this.modelName = modelName;
    }

    public String getModelName() {
        return modelName;
    }
}
