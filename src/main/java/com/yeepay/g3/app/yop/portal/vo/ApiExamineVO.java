/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/5/17 5:04 下午
 */
@Setter
@Getter
public class ApiExamineVO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * apiId
     */
    private String apiId;

    /**
     * api名称
     */
    private String apiName;

    /**
     * api标题
     */
    private String apiTitle;

    /**
     * 方法
     */
    private String httpMethod;

    /**
     * 接口请求路径
     */
    private String apiUri;

    /**
     * 接口描述
     */
    private String apiDescription;

    /**
     * 是否已关联回调
     */
    private boolean relateCallback;

    /**
     * api是否已关联错误码
     */
    private boolean relateErrorCode;

    /**
     * 分组是否已关联错误码
     */
    private boolean groupRelateErrorCode;
}
