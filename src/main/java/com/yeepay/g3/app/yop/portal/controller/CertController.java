/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.enums.CertInputTypeEnum;
import com.yeepay.g3.app.yop.portal.exception.YopPortalException;
import com.yeepay.g3.app.yop.portal.service.CertChangeLogQueryService;
import com.yeepay.g3.app.yop.portal.service.CertQueryService;
import com.yeepay.g3.app.yop.portal.service.CustomerService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.validation.group.CertEdit;
import com.yeepay.g3.app.yop.portal.vo.CertVO;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.page.CertChangeLogPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.CertPageParam;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.enums.*;
import com.yeepay.g3.facade.yop.sys.facade.AppFacade;
import com.yeepay.g3.facade.yop.sys.facade.CertFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/13 下午8:00
 */
@Controller
@RequestMapping("/rest/isv/cert")
public class CertController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CertController.class);

    private static final String PATTERN = "yyyy-MM-dd";

    private FastDateFormat dateFormat = FastDateFormat.getInstance(PATTERN);

    @Autowired
    private CertQueryService certQueryService;

    @Autowired
    private CertChangeLogQueryService certChangeLogQueryService;

    private CertFacade certFacade = RemoteServiceFactory.getService(CertFacade.class);

    @Autowired
    private CustomerService customerService;

    private AppFacade appFacade = RemoteServiceFactory.getService(AppFacade.class);

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@RequestParam(value = "customerNo", required = false) String customerNo,
                                @RequestParam(value = "appId", required = false) String appId,
                                @RequestParam(value = "appName", required = false) String appName,
                                @RequestParam(value = "type", required = false) CertTypeEnum type,
                                @RequestParam(value = "status", required = false) CertStatusEnum status,
                                @RequestParam(value = "createdStartDate", required = false) String createdStartDate,
                                @RequestParam(value = "createdEndDate", required = false) String createdEndDate,
                                @RequestParam(value = "_pageNo", required = false, defaultValue = "1") Integer _pageNo) {
        CertPageParam param = new CertPageParam();
        try {
            param.setAppId(appId);
            param.setCustomerNo(customerNo);
            param.setAppName(appName);
            param.setType(type);
            param.setStatus(status);

            if (StringUtils.isNotEmpty(createdStartDate)) {
                param.setCreatedStartDate(dateFormat.parse(createdStartDate));
            }

            if (StringUtils.isNotEmpty(createdEndDate)) {
                param.setCreatedEndDate(dateFormat.parse(createdEndDate));
            }

            param.setPageNo(_pageNo);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("cert");
            param.setPageSize(appPageSize.intValue());
            return new ResponseMessage("page", certQueryService.pageQuery(param));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list cert with param:" + param, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/change-record", method = RequestMethod.GET)
    public ResponseMessage changeLogList(@RequestParam(value = "certId", required = false) String certId,
                                         @RequestParam(value = "appId", required = false) String appId,
                                         @RequestParam(value = "operator", required = false) String operator,
                                         @RequestParam(value = "operatedStartDate", required = false) String operatedStartDate,
                                         @RequestParam(value = "operatedEndDate", required = false) String operatedEndDate,
                                         @RequestParam(value = "operatedType", required = false) CertOperateTypeEnum type,
                                         @RequestParam(value = "_pageNo", required = false, defaultValue = "1") Integer _pageNo) {
        CertChangeLogPageQueryParam param = new CertChangeLogPageQueryParam();
        try {
            param.setCertId(certId);
            param.setAppId(appId);
            param.setOperator(operator);
            param.setType(type);
            if (StringUtils.isNotEmpty(operatedStartDate)) {
                param.setOperatedStartDate(dateFormat.parse(operatedStartDate));
            }

            if (StringUtils.isNotEmpty(operatedEndDate)) {
                param.setOperatedEndDate(dateFormat.parse(operatedEndDate));
            }
            param.setPageNo(_pageNo);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("cert-change-log");
            param.setPageSize(appPageSize.intValue());
            return new ResponseMessage("page", certChangeLogQueryService.pageQuery(param));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list cert change record with param:" + param, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestParam(value = "appId") String appId,
                                  @RequestParam(value = "inputType", defaultValue = "TEXT") CertInputTypeEnum inputType,
                                  @RequestParam(value = "key", required = false) String key,
                                  @RequestParam(value = "type") CertTypeEnum type,
                                  @RequestParam(value = "usage") CertUsageEnum usage,
                                  @RequestParam(value = "alias", required = false) String alias,
                                  @RequestParam(value = "certFile", required = false) MultipartFile certFile,
                                  @RequestParam(value = "needNotify", required = false) boolean needNotify) {
        try {
            CertCreateRequest request = null;
            switch (inputType) {
                case FILE:
                    if (null == certFile || certFile.isEmpty()) {
                        throw new YopPortalException("密钥不能为空");
                    }
                    CertFileCreateRequest fileRequest = new CertFileCreateRequest();
                    fileRequest.setCertFile(certFile.getBytes());
                    CertFileTypeEnum certFileType = CertFileTypeEnum.CER;
                    String extension = FilenameUtils.getExtension(certFile.getOriginalFilename());
                    if ("p7b".equalsIgnoreCase(extension)) {
                        certFileType = CertFileTypeEnum.P7B;
                    }
                    fileRequest.setCertFileType(certFileType);
                    request = fileRequest;
                    break;
                case TEXT:
                    if (!type.isSymmetric() && StringUtils.isEmpty(key)) {
                        throw new YopPortalException("密钥不能为空");
                    }
                    CertTextCreateRequest textRequest = new CertTextCreateRequest();
                    textRequest.setCertText(key);
                    request = textRequest;
                    break;
            }
            request.setUsage(usage);
            request.setAppId(appId);
            request.setAlias(alias);
            request.setType(type);
            OperateRequest operateRequest = new OperateRequest(ShiroUtils.getOperatorCode(), "通过运营后台操作");
            request.setOperateRequest(operateRequest);
            AppDTO appDTO = appFacade.find(appId);
            if (needNotify) {
                request.setNotifyMails(customerService.getAdminEmail(appDTO.getCustomerNo()));
            }
            certFacade.create(request);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when create cert", e);
            return new ResponseMessage(e);
        }
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/exists", method = RequestMethod.GET)
    public ResponseMessage exists(@RequestParam(value = "appId") String appId,
                                  @RequestParam(value = "type") CertTypeEnum type,
                                  @RequestParam(value = "usage") CertUsageEnum usage) {
        try {
            List<CertDTO> certs = certFacade.findByCondition(appId, type, usage);
            return new ResponseMessage("result", CollectionUtils.isNotEmpty(certs));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when exists cert", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/disable", method = RequestMethod.POST)
    public ResponseMessage disable(@RequestBody @Validated({CertEdit.class}) CertVO vo) {
        try {
            CertDisableRequest request = new CertDisableRequest();
            request.setId(vo.getId());
            OperateRequest operateRequest = new OperateRequest(ShiroUtils.getOperatorCode(), vo.getCause());
            request.setOperateRequest(operateRequest);
            certFacade.disable(request);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when disable cert[" + vo + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/value", method = RequestMethod.GET)
    public ResponseMessage enable(@RequestParam(value = "id") Long id) {
        try {
            CertDTO certDTO = certFacade.find(id);
            String value;
            if (certDTO.getType().isSymmetric()) {
                SymmetricCertSpec certSpec = (SymmetricCertSpec) certDTO.getCertSpec();
                value = certSpec.getSecretKey();
            } else {
                UnSymmetricCertSpec certSpec = (UnSymmetricCertSpec) certDTO.getCertSpec();
                value = certSpec.getPubKey();
            }
            return new ResponseMessage("value", value);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when enable cert[" + id + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/enable", method = RequestMethod.POST)
    public ResponseMessage enable(@RequestBody @Validated({CertEdit.class}) CertVO vo) {
        try {
            CertEnableRequest request = new CertEnableRequest();
            request.setId(vo.getId());
            request.setForce(true);
            OperateRequest operateRequest = new OperateRequest(ShiroUtils.getOperatorCode(), vo.getCause());
            request.setOperateRequest(operateRequest);
            certFacade.enable(request);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when enable cert[" + vo + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/status", method = RequestMethod.GET)
    public ResponseMessage certStatuses() {
        List<CommonsVO> list = new ArrayList<>();
        CertStatusEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/type", method = RequestMethod.GET)
    public ResponseMessage certTypes(@RequestParam(value = "usage", required = false) CertUsageEnum usage) {
        List<CommonsVO> list = new ArrayList<>();
        if (null != usage) {
            switch (usage) {
                case BOTH:
                case API_INVOKE:
                case SPI_NOTIFY:
                    list.add(new CommonsVO(CertTypeEnum.AES128.getValue(), CertTypeEnum.AES128.getDisplayName()));
                    list.add(new CommonsVO(CertTypeEnum.RSA2048.getValue(), CertTypeEnum.RSA2048.getDisplayName()));
                    break;
                case API_INVOKE_CIPHER:
                    list.add(new CommonsVO(CertTypeEnum.AES256.getValue(), CertTypeEnum.AES256.getDisplayName()));
                    break;
            }
        } else {
            CertTypeEnum.getValueMap().forEach((value, certTypeEnum) -> list.add(new CommonsVO(value, value)));
        }
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/usage", method = RequestMethod.GET)
    public ResponseMessage certUsages() {
        List<CommonsVO> list = new ArrayList<>();
        CertUsageEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/operate-type", method = RequestMethod.GET)
    public ResponseMessage operateTypeTypes() {
        List<CommonsVO> list = new ArrayList<>();
        CertOperateTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/yop-platform-public-key", method = RequestMethod.GET)
    public ResponseMessage yopPlatformPublicKey() {
        Map<String, String> certs = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PUBLIC_KEY);
        List<Map<String, String>> list = new ArrayList<>(certs.size());
        certs.forEach((type, value) -> {
            Map<String, String> map = new HashMap<>();
            map.put("type", type);
            map.put("value", value);
            list.add(map);
        });
        return new ResponseMessage("result", list);
    }

}

