package com.yeepay.g3.app.yop.portal.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.SecurityDefQueryService;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.SecurityDefVO;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/9 17:29
 */
@Component
public class SecurityDefQueryServiceImpl implements SecurityDefQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SecurityReqQueryServiceImpl.class);

    private final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();

    @Override
    public List<SecurityDefVO> list() throws IOException {
        Map<String, String> securityDefConfigMap = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_SECURITY_DEF_CONFIG);
        if (MapUtils.isEmpty(securityDefConfigMap)) {
            return Collections.emptyList();
        }
        return convert(securityDefConfigMap);
    }

    private List<SecurityDefVO> convert(Map<String, String> securityDefConfigMap) {
        List<SecurityDefVO> securityDefList = new ArrayList<>(securityDefConfigMap.size());
        securityDefConfigMap.forEach((k, v) -> {
            try {
                JsonNode securityDefDetail = objectMapper.readTree(v);
                SecurityDefVO securityDef = new SecurityDefVO();
                securityDef.setName(k);
                JsonNode desc = securityDefDetail.get("desc");
                if (null != desc) {
                    securityDef.setDesc(desc.asText());
                }

                ObjectNode scopes = (ObjectNode) securityDefDetail.get("scopes");
                if (null != scopes) {
                    List<String> scopeList = new ArrayList<>();
                    scopes.fieldNames().forEachRemaining(scope -> scopeList.add(scope));
                    securityDef.setScopes(scopeList);
                }

                ArrayNode extensions = (ArrayNode) securityDefDetail.get("extensions");
                if (null != extensions) {
                    Map<String, Map<String, Object>> extensionMap = Maps.newHashMapWithExpectedSize(extensions.size());
                    extensions.forEach(extension -> {
                        ObjectNode extensionObj = (ObjectNode) extension;
                        Map<String, Object> extensionPropsMap = Maps.newHashMapWithExpectedSize(extension.size());
                        extensionObj.fields().forEachRemaining(stringJsonNodeEntry -> {
                            if (!"name".equals(stringJsonNodeEntry.getKey()) && !"type".equals(stringJsonNodeEntry.getKey())) {
                                extensionPropsMap.put(stringJsonNodeEntry.getKey(), stringJsonNodeEntry.getValue());
                            }
                        });
                        extensionMap.put(extensionObj.get("name").asText(), extensionPropsMap);
                    });
                    securityDef.setExtensions(extensionMap);
                }
                securityDefList.add(securityDef);
            } catch (Exception e) {
                LOGGER.error("error when parse YOP_SECURITY_DEF_CONFIG", e);
            }
        });
        return securityDefList;
    }

    private List<SecurityDefVO> convert(List queryResult) throws IOException {
        List<SecurityDefVO> securityDefs = new ArrayList<>(queryResult.size());
        for (Object result : queryResult) {
            Map resultMap = (Map) result;
            SecurityDefVO securityDef = new SecurityDefVO();
            securityDef.setName((String) resultMap.get("CODE"));
            securityDef.setDesc((String) resultMap.get("DESCRIPTION"));
            String detail = (String) resultMap.get("DETAIL");
            if (StringUtils.isNotEmpty(detail)) {
                Map<String, Object> detailMap = objectMapper.readValue(detail, new TypeReference<Map<String, Object>>() {
                });
                if (detailMap != null && detailMap.size() > 1) {
                    Map<String, String> scopes = (Map<String, String>) detailMap.get("scopes");
                    List<Map<String, Object>> extensions = (List<Map<String, Object>>) detailMap.get("extensions");
                    if (scopes != null) {
                        securityDef.setScopes(new ArrayList<>(scopes.keySet()));
                    }
                    if (CollectionUtils.isNotEmpty(extensions)) {
                        Map<String, Map<String, Object>> extensionMap = Maps.newHashMapWithExpectedSize(extensions.size());
                        extensions.forEach(extension ->
                                extensionMap.put((String) extension.get("code"), extension));
                        securityDef.setExtensions(extensionMap);
                    }
                }
            }
            securityDefs.add(securityDef);
        }
        return securityDefs;
    }
}
