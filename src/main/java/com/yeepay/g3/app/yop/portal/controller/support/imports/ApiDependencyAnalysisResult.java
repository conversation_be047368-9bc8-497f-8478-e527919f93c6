package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;
import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;
import com.yeepay.g3.facade.yop.sys.dto.SpiDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title: Api倚赖分析结果<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 15:16
 */
public class ApiDependencyAnalysisResult {

    private List<ApiDependencyInfo> dependencyInfos;

    private Map<ApiRequestKey, ApiDTO> apis;

    private Map<String, SpiDTO> spis;

    private Map<String, ModelDTO> models;

    private List<String> unusedSpis;

    private List<String> unusedModels;

    public List<ApiDependencyInfo> getDependencyInfos() {
        return dependencyInfos;
    }

    public void setDependencyInfos(List<ApiDependencyInfo> dependencyInfos) {
        this.dependencyInfos = dependencyInfos;
    }

    public ApiDependencyAnalysisResult withDependencyInfos(List<ApiDependencyInfo> dependencyInfos) {
        this.dependencyInfos = dependencyInfos;
        return this;
    }

    public ApiDependencyAnalysisResult addDependencyInfo(ApiDependencyInfo dependencyInfo) {
        if (this.dependencyInfos == null) {
            this.dependencyInfos = new ArrayList<>();
        }
        this.dependencyInfos.add(dependencyInfo);
        return this;
    }

    public Map<ApiRequestKey, ApiDTO> getApis() {
        return apis;
    }

    public void setApis(Map<ApiRequestKey, ApiDTO> apis) {
        this.apis = apis;
    }

    public ApiDependencyAnalysisResult withApis(Map<ApiRequestKey, ApiDTO> apis) {
        this.apis = apis;
        return this;
    }

    public Map<String, SpiDTO> getSpis() {
        return spis;
    }

    public void setSpis(Map<String, SpiDTO> spis) {
        this.spis = spis;
    }

    public ApiDependencyAnalysisResult withSpis(Map<String, SpiDTO> spis) {
        this.spis = spis;
        return this;
    }

    public Map<String, ModelDTO> getModels() {
        return models;
    }

    public void setModels(Map<String, ModelDTO> models) {
        this.models = models;
    }

    public ApiDependencyAnalysisResult withModels(Map<String, ModelDTO> models) {
        this.models = models;
        return this;
    }

    public List<String> getUnusedSpis() {
        return unusedSpis;
    }

    public void setUnusedSpis(List<String> unusedSpis) {
        this.unusedSpis = unusedSpis;
    }

    public ApiDependencyAnalysisResult withUnusedSpis(List<String> unusedSpis) {
        this.unusedSpis = unusedSpis;
        return this;
    }

    public List<String> getUnusedModels() {
        return unusedModels;
    }

    public void setUnusedModels(List<String> unusedModels) {
        this.unusedModels = unusedModels;
    }

    public ApiDependencyAnalysisResult withUnusedModels(List<String> unusedModels) {
        this.unusedModels = unusedModels;
        return this;
    }
}
