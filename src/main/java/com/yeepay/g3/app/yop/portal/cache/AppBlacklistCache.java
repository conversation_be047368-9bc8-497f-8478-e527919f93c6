/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.cache;

import com.alibaba.druid.util.StringUtils;
import com.yeepay.g3.app.yop.portal.cache.key.AppBlacklistCacheKey;
import com.yeepay.g3.yop.frame.cache.AbstractRefreshableLocalCache;
import com.yeepay.g3.yop.frame.cache.YopCacheLoader;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * title: AppBlacklistCache<br>
 * description: 应用的黑名单缓存<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/7/5 11:47 上午
 */
@Component
public class AppBlacklistCache extends AbstractRefreshableLocalCache<AppBlacklistCacheKey, String> {
    @Autowired
    private BlacklistConfigCache blacklistConfigCache;

    @Override
    protected int getMaxSize() {
        return 2000;
    }

    @Override
    protected int getRefreshDuration() {
        return 60;
    }

    @Override
    protected YopCacheLoader<AppBlacklistCacheKey, String> getCacheLoader() {
        return new YopCacheLoader<AppBlacklistCacheKey, String>() {
            @Override
            protected String doLoad(AppBlacklistCacheKey cacheKey) throws Exception {
                Map<String, Map<String, String>> config = blacklistConfigCache.get(BlacklistConfigCache.CACHE_KEY);
                Map<String, String> urlConfig = config.get(cacheKey.getAppId());
                if (MapUtils.isEmpty(urlConfig)) {
                    urlConfig = config.get("*");
                } else {
                    urlConfig.putAll(config.get("*"));
                }

                if (MapUtils.isNotEmpty(urlConfig)) {
                    for (Map.Entry<String, String> entry : urlConfig.entrySet()) {
                        if (urlMatch(cacheKey.getUrl(), entry.getKey())) {
                            return entry.getValue();
                        }
                    }
                    return "";
                }
                return null;
            }

            @Override
            protected String getCacheName() {
                return "app_blacklist_cache";
            }
        };
    }

    private boolean urlMatch(String url, String urlExp) {
        if (StringUtils.equals("*", urlExp)) {
            return true;
        }
        Pattern pattern = Pattern.compile(urlExp);
        Matcher matcher = pattern.matcher(url);
        return matcher.matches();
    }
}
