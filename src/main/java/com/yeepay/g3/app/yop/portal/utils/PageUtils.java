/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.utils;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.Collections;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/10/25 17:43
 */
public class PageUtils {

    public static PageQueryResult getEmptyPage(Integer pageNo) {
        PageQueryResult emptyResult = new PageQueryResult();
        emptyResult.setPageNo(pageNo);
        emptyResult.setItems(Collections.emptyList());
        emptyResult.setTotalPageNum(0);
        return emptyResult;
    }

    /**
     * 获取各个业务分页大小配置
     *
     * @param bizKey 业务编码
     * @return 参数值
     */
    public static int getConfigPageSize(String bizKey) {
        Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE, Map.class);
        Long appPageSize = pageSizeMap.get(bizKey);
        return appPageSize.intValue();
    }

}
