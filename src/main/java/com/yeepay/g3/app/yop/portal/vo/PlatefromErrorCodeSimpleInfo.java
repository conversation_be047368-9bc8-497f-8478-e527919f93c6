package com.yeepay.g3.app.yop.portal.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午3:42
 */
public class PlatefromErrorCodeSimpleInfo implements Serializable {

    private static final long serialVersionUID = -1L;

    private String code;

    private String msg;

    private List<PlatefromErrorCodeSimpleInfo> subCodes;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<PlatefromErrorCodeSimpleInfo> getSubCodes() {
        return subCodes;
    }

    public void setSubCodes(List<PlatefromErrorCodeSimpleInfo> subCodes) {
        this.subCodes = subCodes;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
