/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.regression.execute.engine;


import com.yeepay.g3.app.yop.portal.vo.RegressionTestRequestVO;
import com.yeepay.g3.sdk.yop.client.YopRequest;
import com.yeepay.g3.sdk.yop.client.YopResponse;
import com.yeepay.g3.sdk.yop.http.Headers;
import org.springframework.stereotype.Component;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/19 下午8:49
 */
@Component
public class Oauth2ExecuteEngine extends AbstractExecuteEngine {

    @Override
    protected YopResponse doExecute(RegressionTestRequestVO vo) throws Exception {
        YopRequest request = new YopRequest();
        vo.getFormParam().asMap().forEach((key, value) -> {
            value.forEach(value0 -> {
                request.addParam((String) key, value0);
            });
        });
        request.addHeader(Headers.AUTHORIZATION, "Bearer " + vo.getAccessToken());
        request.getAppSdkConfig().setServerRoot(vo.getServerRoot());
        return proxyToSdk(vo.getResourcePath(), vo.getSupportMethods(), request, false);
    }

    @Override
    public String getSecurity() {
        return "YOP-OAUTH2";
    }

    @Override
    public boolean supportJson() {
        return false;
    }
}
