package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;

import java.io.Serializable;

/**
 * title: Api导入失败信息<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 18:42
 */
public class ApiImportFailedInfo implements Serializable {

    private static final long serialVersionUID = -1L;

    private ApiRequestKey key;

    private String reason;

    public ApiRequestKey getKey() {
        return key;
    }

    public void setKey(ApiRequestKey key) {
        this.key = key;
    }

    public ApiImportFailedInfo withKey(ApiRequestKey key) {
        this.key = key;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public ApiImportFailedInfo withReason(String reason) {
        this.reason = reason;
        return this;
    }
}
