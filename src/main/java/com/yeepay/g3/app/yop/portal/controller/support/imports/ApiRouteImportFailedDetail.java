/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;
import lombok.Data;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/7 9:19 PM
 */
@Data
public class ApiRouteImportFailedDetail implements Serializable {

    private static final long serialVersionUID = -1L;

    private ApiRequestKey apiRequestKey;

    private String reason;
}
