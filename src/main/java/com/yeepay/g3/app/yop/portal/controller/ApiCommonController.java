package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.enums.ApiVersionEnum;
import com.yeepay.g3.app.yop.portal.service.ApiRequestService;
import com.yeepay.g3.app.yop.portal.service.UnifyApiService;
import com.yeepay.g3.app.yop.portal.vo.ApiParamDataFormatVO;
import com.yeepay.g3.app.yop.portal.vo.ApiQueryParamVO;
import com.yeepay.g3.app.yop.portal.vo.EnumCommonVO;
import com.yeepay.g3.facade.yop.api.enums.ApiParamDataFormatEnum;
import com.yeepay.g3.facade.yop.api.enums.ApiParamDataTypeEnum;
import com.yeepay.g3.facade.yop.api.enums.ApiStatusEnum;
import com.yeepay.g3.facade.yop.api.enums.ApiTypeEnum;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiParam;
import com.yeepay.g3.facade.yop.sys.enums.DeployEnvEnum;
import com.yeepay.g3.facade.yop.sys.enums.DeployOPTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.ApiMgrFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/14 14:37
 */
@Controller
@RequestMapping("/rest/api/commons")
@Slf4j
public class ApiCommonController {

    private static final ApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(ApiMgrFacade.class);

    @Autowired
    private ApiRequestService apiRequestService;

    @Autowired
    private UnifyApiService unifyApiService;

    @ResponseBody
    @RequestMapping(value = "/status/list", method = RequestMethod.GET)
    public ResponseMessage queryApiStatus(@RequestParam(required = false, defaultValue = "V1") String apiVersion) {
        List<EnumCommonVO> result = new ArrayList<>();
        if (ApiVersionEnum.V1.name().equalsIgnoreCase(apiVersion)) {
            for (ApiStatusEnum status : ApiStatusEnum.values()) {
                result.add(new EnumCommonVO().withValue(status.name()).withDesc(status.getDesc()));
            }
        } else {
            for (com.yeepay.g3.facade.yop.sys.enums.ApiStatusEnum status : com.yeepay.g3.facade.yop.sys.enums.ApiStatusEnum.values()) {
                result.add(new EnumCommonVO().withValue(status.name()).withDesc(status.getDisplayName()));
            }
        }
        return new ResponseMessage("statusList", result);
    }


    @ResponseBody
    @RequestMapping(value = "/param-data-type/list", method = RequestMethod.GET)
    public ResponseMessage listParamType() {
        List<EnumCommonVO> result = new ArrayList<>();
        for (ApiParamDataTypeEnum dataTypeEnum : ApiParamDataTypeEnum.values()) {
            EnumCommonVO dataType = new EnumCommonVO()
                    .withValue(dataTypeEnum.getValue())
                    .withDesc(dataTypeEnum.getDisplayName());
            for (ApiParamDataFormatEnum dateFormatEnum : ApiParamDataFormatEnum.getByDataType(dataTypeEnum)) {
                dataType.addFormat(new ApiParamDataFormatVO()
                        .withValue(dateFormatEnum.getValue())
                        .withDesc(dateFormatEnum.getFormat())
                        .withDefaulted(dateFormatEnum.isTypeDefault()));
            }
            result.add(dataType);
        }
        return new ResponseMessage("result", result);
    }

    @ResponseBody
    @RequestMapping(value = "/type/list")
    public ResponseMessage listApiType(@RequestParam(required = false, defaultValue = "V1") String apiVersion) {
        List<EnumCommonVO> result = new ArrayList<>();
        if (ApiVersionEnum.V1.name().equalsIgnoreCase(apiVersion)) {
            for (ApiTypeEnum apiTypeEnum : ApiTypeEnum.values()) {
                result.add(new EnumCommonVO().withValue(apiTypeEnum.getValue()).withDesc(apiTypeEnum.getDisplayName()));
            }
        } else {
            for (com.yeepay.g3.facade.yop.sys.enums.ApiTypeEnum apiTypeEnum : com.yeepay.g3.facade.yop.sys.enums.ApiTypeEnum.values()) {
                result.add(new EnumCommonVO().withValue(apiTypeEnum.getValue()).withDesc(apiTypeEnum.getDisplayName()));
            }
        }
        return new ResponseMessage("result", result);
    }

    @ResponseBody
    @RequestMapping(value = "/deploy-env")
    public ResponseMessage listDeployEnv() {
        List<EnumCommonVO> result = new ArrayList<>();
        for (DeployEnvEnum deployEnvEnum : DeployEnvEnum.values()) {
            result.add(new EnumCommonVO().withValue(deployEnvEnum.getValue()).withDesc(deployEnvEnum.getDisplayName()));
        }
        return new ResponseMessage("result", result);
    }

    @ResponseBody
    @RequestMapping(value = "/deploy-op-types")
    public ResponseMessage listDeployOpTypes() {
        List<EnumCommonVO> result = new ArrayList<>();
        for (DeployOPTypeEnum opType : DeployOPTypeEnum.values()) {
            result.add(new EnumCommonVO().withValue(opType.getValue()).withDesc(opType.getDisplayName()));
        }
        return new ResponseMessage("result", result);
    }

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage<?> listApi(@RequestParam(value = "keyword") String keyword) {
        ApiQueryParamVO param = new ApiQueryParamVO();
        param.setKeyword(keyword);
        return new ResponseMessage<>("result", unifyApiService.findApis(param));
    }

    @ResponseBody
    @RequestMapping(value = "/request-param/list", method = RequestMethod.GET)
    public ResponseMessage<?> listApiParam(@RequestParam(value = "apiUri") String apiUri) {
        String apiId = apiRequestService.findApiIdByPath(apiUri);
        List<ApiParam> requestParams = Collections.emptyList();
        if (null != apiId) {
            requestParams = apiMgrFacade.findRequestParams(apiId, 1);
        } else {
            log.warn("api not found, apiUri:{}", apiUri);
        }
        return new ResponseMessage<>("result", null != requestParams ? requestParams : Collections.emptyList());
    }



}
