package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yeepay.g3.app.yop.portal.cache.SpLocalCache;
import com.yeepay.g3.app.yop.portal.dto.DocPageRefQueryParam;
import com.yeepay.g3.app.yop.portal.exception.YopPortalException;
import com.yeepay.g3.app.yop.portal.service.DocQueryService;
import com.yeepay.g3.app.yop.portal.utils.Constants;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocProductDTO;
import com.yeepay.g3.facade.yop.doc.enums.DocCategoryTypeEnum;
import com.yeepay.g3.facade.yop.doc.enums.v2.*;
import com.yeepay.g3.facade.yop.doc.util.DocUtils;
import com.yeepay.g3.facade.yop.perm.dto.IspInfoDTO;
import com.yeepay.g3.facade.yop.sys.dto.ProductDTO;
import com.yeepay.g3.facade.yop.sys.dto.validate.ProductQueryReqDTO;
import com.yeepay.g3.facade.yop.sys.facade.ProductQueryFacade;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.yeepay.g3.app.yop.portal.utils.Constants.ALL_SP_INFO_KEY;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-29 10:43
 */
@Slf4j
@Component
public class DocQueryServiceImpl implements DocQueryService {

    private ProductQueryFacade productQueryFacade = RemoteServiceFactory.getService(ProductQueryFacade.class);

    @Resource(name = "docQueryService")
    private QueryService queryService;

    @Autowired
    private SpLocalCache spLocalCache;

    private final PageItemConverter<DocPageItem> pageItemConverter = new DocPageItemConverter();

    private final PageItemConverter<DocHistoryPageItem> historyPageItemConverter = new DocHistoryPageItemConverter();

    private final PageItemConverter<DocPublishPageItem> publishPageItemConverter = new DocPublishPageItemConverter();

    @Override
    public PageQueryResult<DocPageItem> pageQuery(DocPageQueryParam param) {
        PageQueryResult<DocPageItem> result = new PageQueryResult<>();
        result.setPageNo(param.getPageNo());
        Map<String, Object> bizParams = getBizParams(param);
        if (null == bizParams) {
            return result;
        }
        List<Map> list = queryService.query("pageList", bizParams);
        Map<String, String> accessConfig = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_MBR_DOC_ACCESS_CONFIG);
        List<DocPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            for (Map map : list) {
                final DocPageItem pageItem = pageItemConverter.convert(map);
                if (!StringUtils.isAnyBlank(pageItem.getDocNo(), pageItem.getDocVersion())) {
                    //查看、预览
                    // 产品：https//{host}/docs/products/{docNo}/{version}/index.html
                    // 平台：https//{host}/docs/platform
                    // 普通：https//{host}/docs/open/{docNo}
                    String urlPrefix = accessConfig.get(Constants.YOP_MBR_DOC_ACCESS_MBR_PREFIX_CFG_KEY),
                            urlPreviewPrefix = accessConfig.get(Constants.YOP_MBR_DOC_ACCESS_PREVIEW_PREFIX_CFG_KEY),
                            urlSuffix = null;
                    switch (pageItem.getType()) {
                        case PRODUCT:
                            urlSuffix = String.format(Constants.YOP_MBR_DOC_PRODUCTS_INDEX_SUFFIX_FORMAT, pageItem.getDocNo());
                            break;
                        case OPEN:
                            urlSuffix = String.format(Constants.YOP_MBR_DOC_OPEN_INDEX_SUFFIX_FORMAT, pageItem.getDocNo());
                            break;
                        case SOLUTION:
                            urlSuffix = String.format(Constants.YOP_MBR_DOC_SOLUTION_INDEX_SUFFIX_FORMAT, pageItem.getDocNo());
                            break;
                        default:
                            urlSuffix = Constants.YOP_MBR_DOC_PLATFORM_INDEX_SUFFIX_FORMAT;
                    }

                    pageItem.setDocUrl(urlPrefix + urlSuffix);
                    pageItem.setDocPreviewUrl(urlPreviewPrefix + urlSuffix + "?loginUser=" + ShiroUtils.getOperatorCode());
                    pageItem.setOwner(true);
                }
                items.add(pageItem);
            }
        }
        fillProductInfoForDocs(items);
        result.setItems(items);
        return result;
    }

    private void fillProductInfoForDocs(List<DocPageItem> docs) {
        if (CollectionUtils.isEmpty(docs)) return;
        Set<String> productCodes = new LinkedHashSet<>();
        docs.forEach(p -> productCodes.addAll(p.getProductCodes()));
        List<ProductDTO> foundProducts = null;
        if (CollectionUtils.isNotEmpty(productCodes)) {
            final ProductQueryReqDTO queryReq = new ProductQueryReqDTO();
            queryReq.setProductCodes(new ArrayList<>(productCodes));
            foundProducts = productQueryFacade.queryProducts(queryReq);
        }
        if (CollectionUtils.isNotEmpty(foundProducts)) {
            Map<String, String> productMap = Maps.newHashMapWithExpectedSize(foundProducts.size());
            foundProducts.forEach(p -> productMap.put(p.getCode(), p.getName()));
            for (DocPageItem doc : docs) {
                final List<String> docProductCodes = doc.getProductCodes();
                if (CollectionUtils.isNotEmpty(docProductCodes)) {
                    doc.setProducts(docProductCodes.stream().map(p -> {
                        final DocProductDTO docProduct = new DocProductDTO();
                        docProduct.setDocNo(doc.getDocNo());
                        docProduct.setProductCode(p);
                        docProduct.setProductName(productMap.get(p));
                        return docProduct;
                    }).collect(Collectors.toList()));
                } else {
                    doc.setProducts(Collections.emptyList());
                }
            }
        }
    }

    @Override
    public PageQueryResult<DocHistoryPageItem> historyPageQuery(DocPageHistoryPageQueryParam param) {
        Map<String, Object> bizParams = getHistoryBizParams(param);
        PageQueryResult<DocHistoryPageItem> result = new PageQueryResult<>();
        result.setPageNo(param.getPageNo());
        if (ShiroUtils.isSpOperator()) {
            final List<String> ownDocs = getSpDocNos();
            if (CollectionUtils.isEmpty(ownDocs)) {
                return result;
            } else {
                bizParams.put("docNos", ownDocs);
            }
        }

        List<Map> list = queryService.query("historyPageList", bizParams);
        List<DocHistoryPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(historyPageItemConverter.convert(map)));
        }
        result.setItems(items);
        return result;
    }

    @Override
    public List<DocArrangeVo> listForArrange(Long categoryId) {
        Map<String, Object> bizParams = Collections.singletonMap("docCategoryId", categoryId);
        final List<Map> data = queryService.query("listForArrange", bizParams);
        if (CollectionUtils.isNotEmpty(data)) {
            List<DocArrangeVo> result = new ArrayList<>(data.size());
            data.forEach(item -> {
                final DocArrangeVo doc = new DocArrangeVo();
                doc.setId((Long) item.get("id"));
                doc.setDocNo((String) item.get("doc_no"));
                doc.setTitle((String) item.get("title"));
                doc.setSeq((int) item.get("seq"));
                doc.setStatus(DocStatusEnum.valueOf((String) item.get("status")));
                result.add(doc);
            });
            return result;
        }
        return Collections.emptyList();
    }

    @Override
    public PageQueryResult<DocPublishPageItem> publishPageQuery(DocPublishPageQueryParam param) {
        Map<String, Object> bizParams = getPublishBizParams(param);
        PageQueryResult<DocPublishPageItem> result = new PageQueryResult<>();
        result.setPageNo(param.getPageNo());
        if (ShiroUtils.isSpOperator()) {
            final List<String> ownDocs = getSpDocNos();
            if (CollectionUtils.isEmpty(ownDocs)) {
                return result;
            } else {
                bizParams.put("docNos", ownDocs);
            }
        }

        List<Map> list = queryService.query("publishPageList", bizParams);
        List<DocPublishPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(publishPageItemConverter.convert(map)));
        }
        result.setItems(items);
        return result;
    }

    private Map<String, Object> getBizParams(DocPageQueryParam param) {
        List<String> docNos = param.getDocNos();
        if (!ShiroUtils.isPlatformOperator()) {
            final List<String> spDocNos = getSpDocNos();
            if (CollectionUtils.isEmpty(spDocNos)) {
                return null;
            }
            if (CollectionUtils.isNotEmpty(docNos)) {
                docNos = new ArrayList<>(CollectionUtils.intersection(docNos, spDocNos));
            } else {
                docNos = spDocNos;
            }
        }

        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("docNos", docNos);
        bizParams.put("productCode", param.getProductCode());
        bizParams.put("title", param.getTitle());
        bizParams.put("docNo", param.getDocNo());
        bizParams.put("visible", param.getVisible());
        bizParams.put("status", param.getStatus());
        bizParams.put("spCode", param.getSpCode());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    private Map<String, Object> getHistoryBizParams(DocPageHistoryPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("docNo", param.getDocNo());
        bizParams.put("title", param.getTitle());
        bizParams.put("operator", param.getOperator());
        bizParams.put("createdDateStart", param.getOperDateBegin());
        bizParams.put("createdDateEnd", param.getOperDateEnd());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    @Override
    public List<String> getSpDocNos() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("spCodes", new ArrayList<>(ShiroUtils.getShiroUser().getSpScopes()));
        final List<Map> result = queryService.query("findDocsBySpCodes", map);
        if (CollectionUtils.isNotEmpty(result)) {
            Set<String> docNos = Sets.newHashSetWithExpectedSize(result.size());
            for (Map resultMap : result) {
                String tmpDocNo = (String) resultMap.get("doc_no");
                if (StringUtils.isNotBlank(tmpDocNo)) {
                    docNos.add(tmpDocNo);
                }
            }
            return new ArrayList<>(docNos);
        }
        return Collections.emptyList();
    }

    @Override
    public String findDocByPublishId(Long docPublishId) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("id", docPublishId);
        final Map map = queryService.queryUnique("publishPageOneById", bizParams, false);
        if (MapUtils.isNotEmpty(map)) {
            return (String) map.get("doc_no");
        }
        return null;
    }

    @Override
    public List<DocPageRefVO> pageListForRef(DocPageRefQueryParam queryParam) {
        Map<String, Object> bizParams = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(queryParam.getDocNos())) {
            bizParams.put("docNos", queryParam.getDocNos());
        }
        bizParams.put("keywords", queryParam.getKeywords());
        final List queryResult = queryService.query("pageListForRef", bizParams);
        if (CollectionUtils.isNotEmpty(queryResult)) {
            String mbrDocUrlPrefix = (String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_MBR_DOC_URL_PREFIX);
            List<DocPageRefVO> result = new ArrayList<>(queryResult.size());
            queryResult.forEach(pageItem -> {
                Map pageMap = (Map) pageItem;
                result.add(toPageRefVo(mbrDocUrlPrefix, pageMap));
            });
            return result;
        }
        return Collections.emptyList();
    }

    private DocPageRefVO toPageRefVo(String mbrDocUrlPrefix, Map pageMap) {
        Long id = (Long) pageMap.get("id");
        String docNo  = (String) pageMap.get("doc_no");
        String title  = (String) pageMap.get("title");
        String pagePath  = (String) pageMap.get("page_path");
        String docTitle = pageMap.get("doc_title") == null ? null : (String) pageMap.get("doc_title");
        return new DocPageRefVO(id, title, DocUtils.pageLocation(docNo, pagePath), getAccessUrl(mbrDocUrlPrefix, docNo, pagePath), docTitle);
    }

    private String getAccessUrl(String mbrDocUrlPrefix, String docNo, String pagePath) {
        String accessPrefix = mbrDocUrlPrefix, accessPath = DocUtils.pageAccessPath(docNo, pagePath);
        if (mbrDocUrlPrefix.endsWith("/") && accessPath.startsWith("/")) {
            accessPath = accessPath.replaceFirst("/+", "");
        }
        return accessPrefix + accessPath;
    }

    @Override
    public DocPageRefVO getPageWithPath(Long pageId) {
        if (null != pageId) {
            Map<String, Object> bizParams = Maps.newHashMap();
            bizParams.put("pageId", pageId);
            final Map map = queryService.queryUnique("getPageWithPath", bizParams, false);
            if (MapUtils.isNotEmpty(map)) {
                String mbrDocUrlPrefix = (String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_MBR_DOC_URL_PREFIX);
                return toPageRefVo(mbrDocUrlPrefix, map);
            }
        }
        throw new YopPortalException("page not found, pageId:{0}", pageId);
    }

    @Override
    public List<DocCategoryItemVO> listDocCategories(DocCategoryTypeEnum type, Long pid) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("type", type);
        bizParams.put("pid", pid);
        List<Map> list = queryService.query("listDocCategories", bizParams);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(category -> {
                final DocCategoryItemVO item = new DocCategoryItemVO();
                item.setId((Long) category.get("id"));
                item.setPid((Long) category.get("pid"));
                item.setCode((String) category.get("code"));
                item.setName((String) category.get("name"));
                return item;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public boolean existsDoc(String docNo) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("docNo", docNo);
        List<Map> list = queryService.query("checkIfExists", bizParams);
        return CollectionUtils.isNotEmpty(list);
    }

    @Override
    public List<String> listSolutionDocs(List<String> solutionCodes) {
        Map<String, Object> map = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(solutionCodes)) {
            map.put("docNos", solutionCodes);
        }
        final List<Map> result = queryService.query("listSolutionDocs", map);
        if (CollectionUtils.isNotEmpty(result)) {
            Set<String> docNos = Sets.newHashSetWithExpectedSize(result.size());
            for (Map resultMap : result) {
                String tmpDocNo = (String) resultMap.get("doc_no");
                if (StringUtils.isNotBlank(tmpDocNo)) {
                    docNos.add(tmpDocNo);
                }
            }
            return new ArrayList<>(docNos);
        }
        return Collections.emptyList();
    }

    @Override
    public DocCategoryVO findCategoryByParentCode(DocCategoryQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("code", param.getCode());
        bizParams.put("parentCodes", param.getParentCodes());
        bizParams.put("type", param.getType());
        Map map = queryService.queryUnique("findCategoryByParentCode", bizParams, false);
        if (MapUtils.isNotEmpty(map)) {
            DocCategoryVO vo = new DocCategoryVO();
            vo.setCode((String) map.get("code"));
            vo.setId((Long) map.get("id"));
            vo.setName((String) map.get("name"));
            vo.setPid((Long) map.get("pid"));
            return vo;
        }
        return null;
    }

    private Map<String, Object> getPublishBizParams(DocPublishPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("docNo", param.getDocNo());
        bizParams.put("createdDateStart", param.getCreatedDateStart());
        bizParams.put("createdDateEnd", param.getCreatedDateEnd());
        bizParams.put("operator", param.getOperator());
        bizParams.put("operType", param.getOperType());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class DocPageItemConverter extends BasePageItemConverter<DocPageItem> {

        @Override
        public DocPageItem convert(Map<String, Object> params) {
            DocPageItem item = new DocPageItem();
            item.setId((Long) params.get("id"));
            item.setDocNo((String) params.get("doc_no"));
            item.setTitle((String) params.get("title"));
            item.setApiChanged(false);
            item.setType(DocTypeEnum.valueOf((String) params.get("type")));
            item.setStatus(DocStatusEnum.valueOf((String) params.get("status")));
            item.setVisible(DocVisableEnum.valueOf((String) params.get("visible")));
            item.setDocVersion((String) params.get("doc_version"));
            item.setVersion((Long) params.get("version"));
            item.setDisplay(1 == ((Integer) params.get("display")));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            item.setOwner(true);
            String productCodes = (String) params.get("product_codes");
            item.setProductCodes(StringUtils.isNotBlank(productCodes)
                    ? Arrays.asList(productCodes.split(",")) : Collections.emptyList());
            String spCodes = (String) params.get("sp_codes");
            item.setSpInfos(toSpInfo(spCodes));
            return item;
        }
    }

    private List<IspInfoDTO> toSpInfo(String spCodes) {
        if (StringUtils.isNotBlank(spCodes)) {
            String[] spCodeArr = spCodes.split(",");
            List<IspInfoDTO> result = new ArrayList<>(spCodeArr.length);
            Map<String, IspInfoDTO> spMap = spLocalCache.get(ALL_SP_INFO_KEY);
            for (String spCode : spCodeArr) {
                if (spMap.containsKey(spCode)) {
                    result.add(spMap.get(spCode));
                } else {
                    log.warn("no sp info found, spCode:{}", spCode);
                }
            }
            return result;
        }
        return Collections.emptyList();
    }

    class DocHistoryPageItemConverter extends BasePageItemConverter<DocHistoryPageItem> {

        @Override
        public DocHistoryPageItem convert(Map<String, Object> params) {
            DocHistoryPageItem item = new DocHistoryPageItem();
            item.setId((Long) params.get("id"));
            item.setDocNo((String) params.get("doc_no"));
            item.setTitle((String) params.get("title"));
            item.setPageNo((String) params.get("page_no"));
            item.setPageId((Long) params.get("page_id"));
            item.setPagePid((Long) params.get("page_pid"));
            item.setOperator((String) params.get("operator"));
            item.setVersion((Long) params.get("version"));
            item.setOperType(DocPageOperTypeEnum.valueOf((String) params.get("oper_type")));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setDocTitle((String) params.get("doc_title"));
            return item;
        }
    }

    class DocPublishPageItemConverter extends BasePageItemConverter<DocPublishPageItem> {

        @Override
        public DocPublishPageItem convert(Map<String, Object> params) {
            DocPublishPageItem item = new DocPublishPageItem();
            item.setId((Long) params.get("id"));
            item.setDocNo((String) params.get("doc_no"));
            item.setDocTitle((String) params.get("doc_title"));
            item.setOperator((String) params.get("operator"));
            item.setDocVersion((String) params.get("doc_version"));
            item.setOperType(DocPublishOperTypeEnum.valueOf((String) params.get("oper_type")));
            item.setCreatedDate((Date) params.get("created_datetime"));
            return item;
        }
    }
}
