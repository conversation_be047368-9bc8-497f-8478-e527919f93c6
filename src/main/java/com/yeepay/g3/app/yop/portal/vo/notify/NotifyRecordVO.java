/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.notify;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yeepay.g3.facade.yop.monitor.enums.OrderStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/9 11:13 上午
 */
@Data
public class NotifyRecordVO implements Serializable {
    private static final long serialVersionUID = -1L;
    private String id;
    private String notificationId;
    private String url;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendDate;
    private OrderStatusEnum status;
    /**
     * 所走的后端系统
     */
    @ApiModelProperty("调用系统")
    private String backend;
    /**
     * 上次失败时间
     */
    @ApiModelProperty("上次失败时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestFailDate;
    /**
     * 上次失败信息
     */
    @ApiModelProperty("上次失败信息")
    private String latestFailMsg;
    private String errorCode;
    private String errorMsg;
    /**
     * 通知内容
     */
    private String content;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDate;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作原因
     */
    private String operCause;
    /**
     * 操作来源
     */
    private String operSource;
    /**
     * 上一次失败原因
     */
    private String latestErrorName;
}
