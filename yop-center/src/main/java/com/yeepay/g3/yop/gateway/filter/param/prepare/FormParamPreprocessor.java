/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.filter.param.prepare;

import com.yeepay.g3.yop.frame.definition.ApiDefinition;
import com.yeepay.g3.yop.frame.definition.ApiParamDefinition;
import com.yeepay.g3.yop.frame.xsd.SystemParamNames;
import com.yeepay.g3.yop.ext.gateway.context.param.FormContext;
import com.yeepay.g3.yop.gateway.filter.param.internalvariable.InternalVariableGetter;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * title: FormParamPreprocessor<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/12 4:36 下午
 */
@Component
public class FormParamPreprocessor implements ParamPreprocessor {
    private InternalVariableGetter internalVariableGetter;

    @Override
    public String getContentType() {
        return MediaType.APPLICATION_FORM_URLENCODED_VALUE;
    }

    @Override
    public void preprocess(ServerWebExchange exchange) {
        removeRedundantParams(exchange);
        bindInternalVariable(exchange);
    }

    private void removeRedundantParams(ServerWebExchange exchange) {
        // TODO 等所有依赖透传特性的API整改完毕后，不再移除多余参数，改为绑定时按实际的参数配置进行参数绑定
        ApiDefinition apiDefinition = ServerWebExchangeUtils.getApiDefinition(exchange);
        Set<String> legalParamNames = apiDefinition.getFormParamDefinitionsDetail().getLegalRequestParamNameSet();
        FormContext formContext = ServerWebExchangeUtils.getParamContext(exchange);
        Iterator<Map.Entry<String, String[]>> it = formContext.getParamContainer().entrySet().iterator();

        while (it.hasNext()) {
            String paramName = it.next().getKey();
            if (!SystemParamNames.contains(paramName) && !legalParamNames.contains(paramName)) {
                it.remove();
            }
        }
    }

    private void bindInternalVariable(ServerWebExchange exchange) {
        FormContext formContext = ServerWebExchangeUtils.getParamContext(exchange);
        Map<String, String[]> params = formContext.getParamContainer();
        List<ApiParamDefinition> paramDefinitions = ServerWebExchangeUtils.getApiDefinition(exchange).getFormParamDefinitionsDetail().getParamDefinitions();
        for (ApiParamDefinition paramDefinition : paramDefinitions) {
            String name = paramDefinition.getParamName();
            // 内部参数
            if (Boolean.TRUE.equals(paramDefinition.getInternal())) {
                params.remove(name);
            }

            // 默认值
            String dv = paramDefinition.getDefaultValue();
            if (StringUtils.isBlank(dv) || params.get(name) != null) {
                continue;
            }
            params.put(name, new String[]{internalVariableGetter.get(exchange, dv)});
        }
    }

    @Autowired
    public void setInternalVariableGetter(InternalVariableGetter internalVariableGetter) {
        this.internalVariableGetter = internalVariableGetter;
    }
}
