package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.sys.dto.models.RequestBody;

import java.io.Serializable;

/**
 * title: SpiVO<br/>
 * description: SpiVO<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午7:32
 */
public class SpiVO implements Serializable {

    private static final long serialVersionUID = 6318473265309553326L;

    private Long id;

    private Long version;

    private SpiBasicVO basic;

    private SpiRequestVO request;

    public Long getId() {
        return id;
    }

    public SpiVO setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getVersion() {
        return version;
    }

    public SpiVO setVersion(Long version) {
        this.version = version;
        return this;
    }

    public SpiBasicVO getBasic() {
        return basic;
    }

    public SpiVO setBasic(SpiBasicVO basic) {
        this.basic = basic;
        return this;
    }

    public SpiRequestVO getRequest() {
        return request;
    }

    public SpiVO setRequest(SpiRequestVO request) {
        this.request = request;
        return this;
    }

    @Override
    public String toString() {
        return "SpiVO{" +
                "id=" + id +
                ", version=" + version +
                ", basic=" + basic +
                ", request=" + request +
                '}';
    }

    public static class SpiBasicVO implements Serializable {

        private static final long serialVersionUID = -1L;

        private String apiGroup;

        private String name;

        private String title;

        private String spiType;

        private String description;

        public String getApiGroup() {
            return apiGroup;
        }

        public SpiBasicVO setApiGroup(String apiGroup) {
            this.apiGroup = apiGroup;
            return this;
        }

        public String getName() {
            return name;
        }

        public SpiBasicVO setName(String name) {
            this.name = name;
            return this;
        }

        public String getTitle() {
            return title;
        }

        public SpiBasicVO setTitle(String title) {
            this.title = title;
            return this;
        }

        public String getSpiType() {
            return spiType;
        }

        public SpiBasicVO setSpiType(String spiType) {
            this.spiType = spiType;
            return this;
        }

        public String getDescription() {
            return description;
        }

        public SpiBasicVO setDescription(String description) {
            this.description = description;
            return this;
        }

        @Override
        public String toString() {
            return "SpiBasicVO{" +
                    "apiGroup='" + apiGroup + '\'' +
                    ", name='" + name + '\'' +
                    ", title='" + title + '\'' +
                    ", spiType='" + spiType + '\'' +
                    ", description='" + description + '\'' +
                    '}';
        }
    }

    public static class SpiRequestVO implements Serializable {

        private static final long serialVersionUID = -1L;

        private String requestUrl;

        private String httpMethod;

        private RequestBody requestBody;

        public String getRequestUrl() {
            return requestUrl;
        }

        public SpiRequestVO setRequestUrl(String requestUrl) {
            this.requestUrl = requestUrl;
            return this;
        }

        public String getHttpMethod() {
            return httpMethod;
        }

        public SpiRequestVO setHttpMethod(String httpMethod) {
            this.httpMethod = httpMethod;
            return this;
        }

        public RequestBody getRequestBody() {
            return requestBody;
        }

        public SpiRequestVO setRequestBody(RequestBody requestBody) {
            this.requestBody = requestBody;
            return this;
        }

        @Override
        public String toString() {
            return "SpiRequestVO{" +
                    "requestUrl='" + requestUrl + '\'' +
                    ", httpMethod='" + httpMethod + '\'' +
                    ", requestBody=" + requestBody +
                    '}';
        }
    }

    public static class SpiListVO implements Serializable {

        private static final long serialVersionUID = -1L;

        private String name;

        private String title;

        public String getName() {
            return name;
        }

        public SpiListVO setName(String name) {
            this.name = name;
            return this;
        }

        public String getTitle() {
            return title;
        }

        public SpiListVO setTitle(String title) {
            this.title = title;
            return this;
        }

        @Override
        public String toString() {
            return "SpiListVO{" +
                    "name='" + name + '\'' +
                    ", title='" + title + '\'' +
                    '}';
        }
    }

}
