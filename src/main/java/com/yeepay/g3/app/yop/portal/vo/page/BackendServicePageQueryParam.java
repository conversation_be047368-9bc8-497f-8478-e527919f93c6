package com.yeepay.g3.app.yop.portal.vo.page;

import java.util.List;

/**
 * title: BackendServicePageQueryParam<br/>
 * description: 后端服务分页查询参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:33
 */
public class BackendServicePageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private String name;

    private String type;

    private String spCode;

    private List<String> spCodes;

    public String getName() {
        return name;
    }

    public BackendServicePageQueryParam setName(String name) {
        this.name = name;
        return this;
    }

    public String getType() {
        return type;
    }

    public BackendServicePageQueryParam setType(String type) {
        this.type = type;
        return this;
    }

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public List<String> getSpCodes() {
        return spCodes;
    }

    public void setSpCodes(List<String> spCodes) {
        this.spCodes = spCodes;
    }

    @Override
    public String toString() {
        return "BackendServicePageQueryParam{" +
                "name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", spCode='" + spCode + '\'' +
                '}';
    }
}
