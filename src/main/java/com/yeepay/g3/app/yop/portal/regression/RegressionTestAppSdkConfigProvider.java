package com.yeepay.g3.app.yop.portal.regression;

import com.yeepay.g3.app.yop.portal.utils.YopPubKeyUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.sdk.yop.client.AbstractClient;
import com.yeepay.g3.sdk.yop.client.router.GateWayRouter;
import com.yeepay.g3.sdk.yop.config.*;
import com.yeepay.g3.sdk.yop.config.support.BackUpAppSdkConfigManager;
import com.yeepay.g3.sdk.yop.encrypt.CertTypeEnum;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Map;

@Component
public class RegressionTestAppSdkConfigProvider implements AppSdkConfigProvider, InitializingBean {

    @Override
    public AppSdkConfig getConfig(String s) {
        return null;
    }

    @Override
    public AppSdkConfig getDefaultConfig() {
        AppSdkConfig appSdkConfig = BackUpAppSdkConfigManager.getBackUpConfig();
        Map<String, String> sdkConfig = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_REGRESSION_TEST_SDK_CONFIG);
        Map<String, String> yopPublicKey = YopPubKeyUtils.getYopPublicKey();
        appSdkConfig.setServerRoot(sdkConfig.get("proServerRoot"));
        try {
            CertConfig[] certConfigs = new CertConfig[1];
            CertConfig certConfig = new CertConfig();
            certConfig.setStoreType(CertStoreType.STRING);
            certConfig.setCertType(CertTypeEnum.RSA2048);
            certConfig.setValue(yopPublicKey.get(CertTypeEnum.RSA2048.getValue()));
            certConfigs[0] = certConfig;
            appSdkConfig.setDefaultYopPublicKey(YopPubKeyUtils.getInstance());
            appSdkConfig.storeYopPublicKey(certConfigs);
        } catch (Throwable ex) {
            throw new YeepayRuntimeException("yop public key not set", ex);
        }
        return appSdkConfig;
    }

    @Override
    public AppSdkConfig getConfigWithDefault(String s) {
        return null;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        AppSdkConfigProviderRegistry.registerCustomProvider(this);
        Class.forName(AbstractClient.class.getName());
        GateWayRouter gateWayRouter = (s, yopRequest) -> yopRequest.getAppSdkConfig().getServerRoot();
        Field field = AbstractClient.class.getDeclaredField("GATE_WAY_ROUTER");
        field.setAccessible(true);
        Field modifiersField = Field.class.getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        //去掉final修饰符
        modifiersField.setInt(field, field.getModifiers() & ~Modifier.FINAL);
        field.set("GATE_WAY_ROUTER", gateWayRouter);
    }
}
