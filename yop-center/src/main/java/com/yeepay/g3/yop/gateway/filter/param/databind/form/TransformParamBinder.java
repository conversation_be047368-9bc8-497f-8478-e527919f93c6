package com.yeepay.g3.yop.gateway.filter.param.databind.form;

import com.yeepay.g3.yop.gateway.backend.RmiBackend;
import com.yeepay.g3.yop.ext.gateway.backend.handler.ApiHandler;
import com.yeepay.g3.yop.gateway.filter.param.databind.AbstractParamBinder;
import com.yeepay.g3.yop.gateway.filter.param.databind.MethodInvokeDataBinder;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import org.springframework.stereotype.Component;

/**
 * title: 本地参数绑定，迁移自TransformApiHandler<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/12/17 下午3:26
 */
@Component
public class TransformParamBinder extends AbstractParamBinder {

    @Override
    protected Object[] bindInvokeParams(ServerWebExchange exchange) {
        ApiHandler handler = ServerWebExchangeUtils.getApiContext(exchange).getApiHandler();
        RmiBackend rmiBackend = (RmiBackend) handler.getApiBackend();
        return MethodInvokeDataBinder.bind(exchange, rmiBackend.getEndMethod(), handler.getApiDefinition().getFormParamDefinitionsDetail().getEndParamMapping());
    }
}
