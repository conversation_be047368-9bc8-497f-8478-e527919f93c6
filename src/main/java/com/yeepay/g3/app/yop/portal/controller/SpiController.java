package com.yeepay.g3.app.yop.portal.controller;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yeepay.g3.app.yop.portal.controller.support.imports.*;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.dto.SpiImportContext;
import com.yeepay.g3.app.yop.portal.dto.spi.SpiApiVO;
import com.yeepay.g3.app.yop.portal.dto.spi.SpiSubscribeVO;
import com.yeepay.g3.app.yop.portal.service.ApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.service.SpiImportCacheService;
import com.yeepay.g3.app.yop.portal.service.SpiQueryService;
import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.app.yop.portal.utils.IdGenerator;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.swagger.SwaggerAnalysisResult;
import com.yeepay.g3.app.yop.portal.utils.swagger.SwaggerAnalyzer;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.SpiVO;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.core.yop.utils.Exceptions;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;
import com.yeepay.g3.facade.yop.sys.dto.imports.ImportFailedInfo;
import com.yeepay.g3.facade.yop.sys.dto.spi.SpiBasicDTO;
import com.yeepay.g3.facade.yop.sys.dto.spi.SpiRequestDTO;
import com.yeepay.g3.facade.yop.sys.enums.*;
import com.yeepay.g3.facade.yop.sys.facade.SpiMgrFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * title: SpiController<br/>
 * description: spi管理<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午3:30
 */
@Controller
@RequestMapping("/rest/spi")
public class SpiController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SpiController.class);

    private SpiMgrFacade spiMgrFacade = RemoteServiceFactory.getService(SpiMgrFacade.class);

    @Autowired
    private SpiQueryService spiQueryService;

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    @Autowired
    private SpiImportCacheService importCacheService;

    /**
     * 订阅
     *
     * @param spiSubscribeVO
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/subscribe", method = RequestMethod.POST)
    public ResponseMessage subscribe(@RequestBody SpiSubscribeVO spiSubscribeVO) {
        try {
            CheckUtils.notNull(spiSubscribeVO, "spiSubscribeVO");
            CheckUtils.notEmpty(spiSubscribeVO.getDestinations(), "destinations");
            SpiSubscribeRequest request = new SpiSubscribeRequest();
            request.setSpiName(spiSubscribeVO.getSpiName());
            List<DestinationDTO> destinationDTOS = spiSubscribeVO.getDestinations().stream()
                    .map(destinationVO -> {
                        DestinationDTO destinationDTO = new DestinationDTO();
                        destinationDTO.setCustomerNo(destinationVO.getCustomerNo());
                        destinationDTO.setAppId(destinationVO.getAppId());
                        destinationDTO.setUrl(destinationVO.getUrl());
                        return destinationDTO;
                    })
                    .collect(Collectors.toList());
            request.setDestinations(destinationDTOS);
            spiMgrFacade.subscribe(request);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when subscribe spi with param: " + spiSubscribeVO, ex);
            return new ResponseMessage(ex);
        }
    }

    /**
     * 取消订阅
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/unsubscribe", method = RequestMethod.POST)
    public ResponseMessage unsubscribe(@RequestBody SpiSubscribeVO spiSubscribeVO) {
        try {
            CheckUtils.notNull(spiSubscribeVO, "spiSubscribeVO");
            CheckUtils.notEmpty(spiSubscribeVO.getDestinations(), "destinations");
            SpiUnSubscribeRequest request = new SpiUnSubscribeRequest();
            request.setSpiName(spiSubscribeVO.getSpiName());
            List<DestinationDTO> destinationDTOS = spiSubscribeVO.getDestinations().stream()
                    .map(destinationVO -> {
                        DestinationDTO destinationDTO = new DestinationDTO();
                        destinationDTO.setCustomerNo(destinationVO.getCustomerNo());
                        destinationDTO.setAppId(destinationVO.getAppId());
                        destinationDTO.setUrl(destinationVO.getUrl());
                        return destinationDTO;
                    })
                    .collect(Collectors.toList());
            request.setDestinations(destinationDTOS);
            spiMgrFacade.unsubscribe(request);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when unsubscribe spi with param: " + spiSubscribeVO, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(SpiPageQueryParam param) {
        try {
            if (ShiroUtils.isPlatformOperator()) {
                PageQueryResult<SpiPageItem> pageQueryResult = spiQueryService.pageList(param);
                return new ResponseMessage("page", pageQueryResult);
            } else {
                List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
                if (apiGroupCodes.size() == 0) {
                    return new ResponseMessage("page", new PageQueryResult());
                }
                param.setApiGroupCodes(apiGroupCodes);
                return new ResponseMessage("page", spiQueryService.pageListForSp(param));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list spi with param: " + param, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam(value = "id", required = true) Long id) {
        try {
            CheckUtils.notNull(id, "id");
            SpiVO spiVO = converSpiVO(spiMgrFacade.find(id));
            return new ResponseMessage("result", spiVO);
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when query spi detail with id: " + id, ex);
            return new ResponseMessage(ex);
        }
    }

    private SpiVO converSpiVO(SpiDTO spiDTO) {
        SpiVO spiVO = new SpiVO();
        if (spiDTO == null) {
            return spiVO;
        }
        SpiVO.SpiBasicVO spiBasicVO = new SpiVO.SpiBasicVO();
        spiBasicVO.setApiGroup(spiDTO.getBasic().getApiGroup());
        spiBasicVO.setName(spiDTO.getBasic().getName());
        spiBasicVO.setTitle(spiDTO.getBasic().getTitle());
        spiBasicVO.setSpiType(spiDTO.getBasic().getSpiType().getValue());
        spiBasicVO.setDescription(spiDTO.getBasic().getDescription());
        spiVO.setBasic(spiBasicVO);
        SpiVO.SpiRequestVO spiRequestVO = new SpiVO.SpiRequestVO();
        spiRequestVO.setRequestUrl(spiDTO.getRequest().getRequestUrl());
        spiRequestVO.setHttpMethod(spiDTO.getRequest().getHttpMethod());
        spiRequestVO.setRequestBody(spiDTO.getRequest().getRequestBody());
        spiVO.setRequest(spiRequestVO);
        return spiVO;
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody SpiVO spiVO) {
        SpiDTO spiDTO = new SpiDTO();
        try {
            CheckUtils.notNull(spiVO, "spiVO");
            CheckUtils.notNull(spiVO.getBasic(), "basic");
            CheckUtils.notEmpty(spiVO.getBasic().getApiGroup(), "apiGroup");
            CheckUtils.notNull(spiVO.getRequest(), "request");
            converCreateSpiDTO(spiVO, spiDTO);
            SpiCreateRequest spiCreateRequest = new SpiCreateRequest();
            spiCreateRequest.withSpi(spiDTO).withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(null));
            spiMgrFacade.create(spiCreateRequest);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when create spi with param: " + spiDTO, ex);
            return new ResponseMessage(ex);
        }
    }

    private void converCreateSpiDTO(SpiVO spiVO, SpiDTO spiDTO) {
        SpiBasicDTO spiBasicDTO = new SpiBasicDTO();
        spiBasicDTO.withName(spiVO.getBasic().getName()).withTitle(spiVO.getBasic().getTitle()).
                withSpiType(SpiTypeEnum.valueOf(spiVO.getBasic().getSpiType())).
                withApiGroup(spiVO.getBasic().getApiGroup()).
                withDescription(spiVO.getBasic().getDescription());
        spiDTO.withBasic(spiBasicDTO);
        SpiRequestDTO spiRequestDTO = new SpiRequestDTO();
        spiRequestDTO.withRequestUrl(spiVO.getRequest().getRequestUrl()).
                withHttpMethod(spiVO.getRequest().getHttpMethod()).
                withRequestBody(spiVO.getRequest().getRequestBody());
        spiDTO.withRequest(spiRequestDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody SpiVO spiVO) {
        SpiDTO spiDTO = new SpiDTO();
        try {
            CheckUtils.notNull(spiVO.getId(), "id");
            CheckUtils.notNull(spiVO.getVersion(), "version");
            CheckUtils.notNull(spiVO, "spiVO");
            CheckUtils.notNull(spiVO.getBasic(), "basic");
            CheckUtils.notEmpty(spiVO.getBasic().getApiGroup(), "apiGroup");
            CheckUtils.notNull(spiVO.getRequest(), "request");
            converUpdateSpiDTO(spiVO, spiDTO);
            SpiUpdateRequest spiUpdateRequest = new SpiUpdateRequest();
            spiUpdateRequest.withSpi(spiDTO).withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(null));
            spiMgrFacade.update(spiUpdateRequest);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when update spi with param: " + spiDTO, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/enable", method = RequestMethod.POST)
    public ResponseMessage enable(String spiName) {
        CheckUtils.notEmpty(spiName, "spiName");
        SpiEnableRequest spiEnableRequest = new SpiEnableRequest();
        OperationInfo operationInfo = new OperationInfo();
        operationInfo.setOperator(ShiroUtils.getOperatorCode());
        spiEnableRequest.setOperationInfo(operationInfo);
        spiEnableRequest.setSpiName(spiName);
        spiMgrFacade.enable(spiEnableRequest);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/disable", method = RequestMethod.POST)
    public ResponseMessage disable(String spiName) {
        CheckUtils.notEmpty(spiName, "spiName");
        SpiDisableRequest spiDisableRequest = new SpiDisableRequest();
        OperationInfo operationInfo = new OperationInfo();
        operationInfo.setOperator(ShiroUtils.getOperatorCode());
        spiDisableRequest.setOperationInfo(operationInfo);
        spiDisableRequest.setSpiName(spiName);
        spiMgrFacade.disable(spiDisableRequest);
        return new ResponseMessage();
    }

    /**
     * 查询已关联的api
     *
     * @param spiApiQueryParam
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/api/list", method = RequestMethod.GET)
    public ResponseMessage listApi(SpiApiQueryParam spiApiQueryParam,
                                   @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                   @RequestParam(value = "_pageSize", required = false) Integer pageSize) {
        spiApiQueryParam.setPageNo(pageNo);
        spiApiQueryParam.setPageSize(pageSize);
        return new ResponseMessage("data", spiQueryService.listApiContacted(spiApiQueryParam));

    }

    /**
     * 关联api
     *
     * @param spiApiVO
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/api/contact", method = RequestMethod.POST)
    public ResponseMessage contactApi(@RequestBody SpiApiVO spiApiVO) {
        SpiBatchContactApiRequest spiBatchContactApiRequest = new SpiBatchContactApiRequest();
        spiBatchContactApiRequest.setSpiName(spiApiVO.getSpiName());
        spiBatchContactApiRequest.setApiIds(spiApiVO.getApiIds());
        spiMgrFacade.contactApis(spiBatchContactApiRequest);
        return new ResponseMessage();
    }

    /**
     * 取消api关联
     *
     * @param spiApiVO
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/api/uncontact", method = RequestMethod.POST)
    public ResponseMessage unContactApi(@RequestBody SpiApiVO spiApiVO) {
        SpiBatchUnContactApiRequest spiBatchUnContactApiRequest = new SpiBatchUnContactApiRequest();
        spiBatchUnContactApiRequest.setSpiName(spiApiVO.getSpiName());
        spiBatchUnContactApiRequest.setApiIds(spiApiVO.getApiIds());
        spiMgrFacade.unContactApis(spiBatchUnContactApiRequest);
        return new ResponseMessage();
    }

    private void converUpdateSpiDTO(SpiVO spiVO, SpiDTO spiDTO) {
        spiDTO.setId(spiVO.getId());
        spiDTO.setVersion(spiVO.getVersion());
        SpiBasicDTO spiBasicDTO = new SpiBasicDTO();
        spiBasicDTO.withTitle(spiVO.getBasic().getTitle()).withDescription(spiVO.getBasic().getDescription());
        spiBasicDTO.withApiGroup(spiVO.getBasic().getApiGroup());
        spiDTO.withBasic(spiBasicDTO);
        SpiRequestDTO spiRequestDTO = new SpiRequestDTO();
        spiRequestDTO.withRequestUrl(spiVO.getRequest().getRequestUrl()).
                withHttpMethod(spiVO.getRequest().getHttpMethod()).
                withRequestBody(spiVO.getRequest().getRequestBody());
        spiDTO.withRequest(spiRequestDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam(value = "id", required = true) Long id, @RequestParam(value = "cause") String cause) {
        try {
            CheckUtils.notNull(id, "id");
            SpiDeleteRequest spiDeleteRequest = new SpiDeleteRequest();
            spiDeleteRequest.withSpiId(id).withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(cause));
            spiMgrFacade.delete(spiDeleteRequest);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when delete spi with id: " + id, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/simple-list", method = RequestMethod.GET)
    public ResponseMessage simpleList(@RequestParam(value = "apiGroup", required = false) String apiGroup,
                                      @RequestParam(value = "spiNames", required = false) List<String> spiNames) {
        try {
            if (StringUtils.isNotEmpty(apiGroup)) {
                return new ResponseMessage("result", spiQueryService.simpleList(apiGroup));
            }
            if (CollectionUtils.isNotEmpty(spiNames)) {
                return new ResponseMessage("result", spiQueryService.simpleList(spiNames));
            }
            return new ResponseMessage("result", Collections.emptyList());
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list spi with apiGroup: " + apiGroup, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/change-record", method = RequestMethod.GET)
    public ResponseMessage list(SpiChangeRecordPageQueryParam param) {
        try {
            if (ShiroUtils.isPlatformOperator()) {
                PageQueryResult<SpiChangeRecordPageItem> pageQueryResult = spiQueryService.changeRecordPageList(param);
                return new ResponseMessage("page", pageQueryResult);
            } else {
                List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
                if (apiGroupCodes.size() == 0) {
                    return new ResponseMessage("page", new PageQueryResult());
                }
                param.setApiGroupCodes(apiGroupCodes);
                return new ResponseMessage("page", spiQueryService.changeRecordPageListForSp(param));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list spi change record with param: " + param, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/change-record/detail", method = RequestMethod.GET)
    public ResponseMessage changeRecordDetail(@RequestParam(value = "id", required = true) Long id) {
        try {
            CheckUtils.notNull(id, "id");
            return new ResponseMessage("result", spiMgrFacade.findChangeRecord(id));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when query spi change record detail with id: " + id, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/change-record/rollback", method = RequestMethod.POST)
    public ResponseMessage rollback(@RequestParam(value = "id", required = true) Long id, @RequestParam(value = "cause") String cause) {
        try {
            CheckUtils.notNull(id, "id");
            SpiRollbackRequest spiRollbackRequest = new SpiRollbackRequest();
            spiRollbackRequest.withChangeRecordId(id).withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(cause));
            spiMgrFacade.rollback(spiRollbackRequest);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when spi change record rollback with id: " + id, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/op-type", method = RequestMethod.GET)
    public ResponseMessage opTypes() {
        List<CommonsVO> list = new ArrayList<>(SpiOperationTypeEnum.values().length);
        SpiOperationTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/import/analysis", method = RequestMethod.POST)
    public ResponseMessage importAnalysis(@RequestParam("apiGroup") String apiGroup,
                                          @RequestParam("data") MultipartFile data,
                                          @RequestParam("dataFormat") SwaggerDataFormatEnum dataFormat) {
        try {
            CheckUtils.notEmpty(apiGroup, "apiGroup");
            CheckUtils.notNull(data.getInputStream(), "data");
            SwaggerAnalysisResult swaggerAnalysisResult = SwaggerAnalyzer.analysis(data.getInputStream(), dataFormat);
            if (swaggerAnalysisResult.getApiGroup() != null && !StringUtils.equals(swaggerAnalysisResult.getApiGroup(), apiGroup)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "swagger中的api分组与提交的api分组不一致");
            }
            List<ImportAnalysisFailedInfo> failedInfos = ImportsUtils.getFailedInfos(swaggerAnalysisResult);
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "swagger解析失败")
                        .put("detail", failedInfos);
            }
            if (MapUtils.isEmpty(swaggerAnalysisResult.getCallbacks())) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "没有有效的spi");
            }
            checkPermission(apiGroup);
            failedInfos = ImportsUtils.checkAnalysisResult(swaggerAnalysisResult);
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "导入数据合法性校验失败")
                        .put("detail", failedInfos);
            }
            SpiDependencyAnalysisResult dependencyAnalysisResult = new SpiDependencyAnalysisResult();
            failedInfos = ImportsUtils.spiDependencyAnalysis(new SpiDependencyAnalysisRequest(swaggerAnalysisResult.getCallbacks(),
                    swaggerAnalysisResult.getModels()), dependencyAnalysisResult);
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "依赖分析失败")
                        .put("detail", failedInfos);
            }
            SpiImportCheckResult checkResult = spiMgrFacade.checkImport(new SpiImportCheckRequest()
                    .withApiGroup(apiGroup)
                    .withSpis(new ArrayList<>(swaggerAnalysisResult.getCallbacks().keySet()))
                    .withModels(MapUtils.isEmpty(dependencyAnalysisResult.getModels()) ? null : new ArrayList<>(dependencyAnalysisResult.getModels().keySet())));
            if (MapUtils.isNotEmpty(checkResult.getFailedSpis())) {
                failedInfos = new ArrayList<>(checkResult.getFailedSpis().size());
                for (Map.Entry<String, String> entry : checkResult.getFailedSpis().entrySet()) {
                    failedInfos.add(new ImportAnalysisFailedInfo().withItem(ImportItemEnum.SPI).withValue(entry.getKey())
                            .withReason(entry.getValue()));
                }
            }
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "导入数据与现有数据存在冲突")
                        .put("detail", failedInfos);
            }
            String requestId = IdGenerator.generate();
            SpiImportContext context = new SpiImportContext()
                    .withCallbackApiRelations(swaggerAnalysisResult.getCallbackApiRelations())
                    .withModels(dependencyAnalysisResult.getModels()).withApiGroup(apiGroup);
            SpiImportAnalysisResult result = new SpiImportAnalysisResult()
                    .withRequestId(requestId);
            doAnalysis(dependencyAnalysisResult, checkResult, context, result);
            importCacheService.storeContext(requestId, context);
            return new ResponseMessage("result", result);
        } catch (Throwable ex) {
            LOGGER.error("spi import data analysis failed.", ex);
            return new ResponseMessage(ex);
        }
    }

    private void checkPermission(String apiGroup) {
        if (ShiroUtils.isPlatformOperator()) {
            return;
        }
        ShiroRealm.ShiroUser user = ShiroUtils.getShiroUser();
        if (CollectionUtils.isNotEmpty(user.getApiGroupScopes()) && user.getApiGroupScopes().contains(apiGroup)) {
            return;
        }
        throw new YeepayRuntimeException("no permission for apiGroup:{0}.", apiGroup);
    }

    private void doAnalysis(SpiDependencyAnalysisResult dependencyAnalysisResult, SpiImportCheckResult checkResult, SpiImportContext context, SpiImportAnalysisResult result) {
        result.withUnusedModels(dependencyAnalysisResult.getUnusedModels());
        if (CollectionUtils.isNotEmpty(checkResult.getModelsToCreate())) {
            checkResult.getModelsToCreate().forEach(modelName -> {
                ModelDTO model = context.getModels().get(modelName);
                result.addModelsToCreateItem(new ModelCreateCheckItem().withModel(model));
            });
        }
        if (MapUtils.isNotEmpty(checkResult.getModelsToOverride())) {
            checkResult.getModelsToOverride().forEach((modelName, srcModel) -> {
                ModelDTO model = context.getModels().get(modelName);
                model.setId(srcModel.getId());
                model.setVersion(srcModel.getVersion());
                if (ImportCompareUtils.isEqualModel(srcModel, model)) {
                    result.addModelsToIgnoreItem(modelName);
                    context.getModels().remove(modelName);
                } else {
                    result.addModelsToOverrideItem(new ModelOverrideCheckItem().withModel(model).withSrcModel(srcModel));
                }
            });
        }
        dependencyAnalysisResult.getDependencyInfos().forEach(dependencyInfo -> {
            List<String> modelsToCreate = null;
            List<String> modelsToOverride = null;
            if (CollectionUtils.isNotEmpty(dependencyInfo.getRefModels())) {
                modelsToCreate = new ArrayList<>();
                modelsToOverride = new ArrayList<>();
                for (String refModel : dependencyInfo.getRefModels()) {
                    if (checkResult.getModelsToOverride() != null && checkResult.getModelsToOverride().containsKey(refModel)) {
                        // 有变更的model才覆盖
                        if (CollectionUtils.isEmpty(result.getModelsToIgnore()) || !result.getModelsToIgnore().contains(refModel)) {
                            modelsToOverride.add(refModel);
                        }
                    } else {
                        modelsToCreate.add(refModel);
                    }
                }
            }
            String spiName = dependencyInfo.getSpiName();
            SpiDTO spi = dependencyAnalysisResult.getSpis().get(spiName);
            SpiDTO srcSpi = checkResult.getSpisToOverride() == null ? null : checkResult.getSpisToOverride().get(spiName);
            if (srcSpi == null) {
                result.addSpisToCreateItem(new SpiCreateCheckItem().withSpi(spi));
                context.addSpisToCreateItem(new SpiImportItem().withSpi(spi)
                        .withModelsToCreate(modelsToCreate)
                        .withModelsToOverride(modelsToOverride));
            } else {
                // 模型没有变动，并且spi本身也没有变动的才忽略
                if (CollectionUtils.isEmpty(modelsToCreate) && CollectionUtils.isEmpty(modelsToOverride) && ImportCompareUtils.isEqualSpi(srcSpi, spi)) {
                    result.addSpisToIgnoreItem(spiName);
                } else {
                    spi.setId(srcSpi.getId());
                    spi.setVersion(srcSpi.getVersion());
                    result.addSpisToOverrideItem(new SpiOverrideCheckItem().withSpi(spi).withSrcSpi(srcSpi));
                    context.addSpisToOverrideItem(new SpiImportItem().withSpi(spi).withModelsToCreate(modelsToCreate)
                            .withModelsToOverride(modelsToOverride));
                }
            }
        });
    }

    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage doImport(@RequestBody SpisImportRequest request) {
        Throwable exception = null;
        try {
            CheckUtils.notNull(request, "request");
            CheckUtils.notEmpty(request.getRequestId(), "request.requestId");
            SpiImportContext context = importCacheService.loadContext(request.getRequestId());
            if (context == null) {
                throw new YeepayRuntimeException("ImportContext for requestId[{0}] not exist.", request.getRequestId());
            }
            checkPermission(context.getApiGroup());
            if (CollectionUtils.isEmpty(context.getSpisToCreate()) && CollectionUtils.isEmpty(request.getSpisToOverride())) {
                throw new YeepayRuntimeException("no content to import");
            }
            Set<String> successImportModels = Sets.newHashSet();
            Map<String, ImportFailedInfo> failedImportModels = Maps.newHashMap();

            List<String> modelsToOverride = request.getModelsToOverride();
            if (CollectionUtils.isNotEmpty(modelsToOverride)) {
                request.setModelsToOverride(modelsToOverride.stream()
                        .map(modelName -> context.getApiGroup() + "." + modelName)
                        .collect(Collectors.toList()));
            }

            SpisImportResult result = new SpisImportResult();
            result.setTotal(CollectionUtils.size(context.getSpisToCreate()) + CollectionUtils.size(request.getSpisToOverride()));
            if (CollectionUtils.isNotEmpty(context.getSpisToCreate())) {
                context.getSpisToCreate().forEach(item -> doHandleImportItem(item, ImportOperationEnum.CREATE,
                        request, context, successImportModels, failedImportModels, result));
            }
            if (CollectionUtils.isNotEmpty(context.getSpisToOverride()) && CollectionUtils.isNotEmpty(request.getSpisToOverride())) {
                List<SpiImportItem> targetSpisToOverride = context.getSpisToOverride().stream()
                        .filter(spiImportItem -> request.getSpisToOverride().contains(spiImportItem.getSpi().getBasic().getName()))
                        .collect(Collectors.toList());
                targetSpisToOverride.forEach(item -> doHandleImportItem(item, ImportOperationEnum.OVERRIDE, request, context, successImportModels, failedImportModels, result));
            }
            return new ResponseMessage().put("result", result);
        } catch (Throwable ex) {
            LOGGER.error("spi import failed.", ex);
            exception = ex;
            return new ResponseMessage(ex);
        } finally {
            if (exception == null) {
                importCacheService.evictContext(request.getRequestId());
            }
        }
    }

    private void doHandleImportItem(SpiImportItem item,
                                    ImportOperationEnum operation,
                                    SpisImportRequest request,
                                    SpiImportContext context,
                                    Set<String> successImportModels,
                                    Map<String, ImportFailedInfo> failedImportModels,
                                    SpisImportResult result) {
        String spiName = item.getSpi().getBasic().getName();
        SpiImportRequest spiImportRequest = new SpiImportRequest()
                .withSpi(item.getSpi())
                .withImportOperation(operation)
                .withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(null));
        //1. 查看请求中是否包含已经失败的模型
        ImportFailedInfo existFailedInfo = ImportsUtils.findFailedImportItem(item.getModelsToCreate(), item.getModelsToOverride(), failedImportModels);
        if (existFailedInfo != null) {
            result.addFailedDetail(new SpiImportFailedInfo().withSpiName(spiName).withReason(existFailedInfo.toString()));
            return;
        }

        // 取出来spi跟api的关联关系，用来导入
        Map<String, List<ApiRequestKey>> callbackApiRelations = context.getCallbackApiRelations();

        //2. 过滤用户确认不覆盖的模型和已经导入成功的模型
        spiImportRequest.withModels(ImportsUtils.filterImportItems(item.getModelsToCreate(),
                item.getModelsToOverride(),
                request.getModelsToOverride(),
                successImportModels,
                context.getModels()));

        // 兼容以前导出的文件，没有api-spi的关联关系就不设置
        if (MapUtils.isNotEmpty(callbackApiRelations)) {
            spiImportRequest.withApiRelations(callbackApiRelations.get(spiName));
        }

        //3. 执行导入
        SpiImportResult importResult = spiMgrFacade.importSpi(spiImportRequest);
        if (StringUtils.equalsIgnoreCase(importResult.getStatus(), "success")) {
            //3. 导入成功记录成功结果，同时缓存导入成功的models
            result.increaseSuccess();
            if (MapUtils.isNotEmpty(spiImportRequest.getModels())) {
                saveSuccessImportModels(successImportModels, spiImportRequest);
            }
        } else {
            //4. 导入失败记录记录失败结果，同时缓存导入失败信息
            result.addFailedDetail(new SpiImportFailedInfo().withSpiName(spiName)
                    .withReason(importResult.getFailedInfo().toString()));
            ImportFailedInfo importFailedInfo = importResult.getFailedInfo();
            if (importFailedInfo.getItem() == ImportItemEnum.MODEL) {
                failedImportModels.put(importFailedInfo.getValue(), importFailedInfo);
            }
        }
    }

    private void saveSuccessImportModels(Set<String> successImportModels, SpiImportRequest spiImportRequest) {
        spiImportRequest.getModels().values().forEach(models -> models.forEach(model -> successImportModels.add(model.getName())));
    }

    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public ResponseEntity<byte[]> export(@RequestParam("apiGroup") String apiGroup,
                                         @RequestParam("spi") String[] spis,
                                         @RequestParam("dataFormat") SwaggerDataFormatEnum datFormat) {
        try {
            CheckUtils.notEmpty(spis, "spi");
            SpiExportResult result = spiMgrFacade.exportSpi(new SpiExportRequest().withApiGroup(apiGroup).withDataFormat(datFormat)
                    .withSpis(Lists.newArrayList(spis)));
            String fileName = "swagger." + datFormat.getValue().toLowerCase();
            byte[] data = result.getData().getBytes(StandardCharsets.UTF_8);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            return new ResponseEntity<>(data, headers, HttpStatus.OK);
        } catch (Exception ex) {
            LOGGER.error("export spi failed.", ex);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_HTML);
            return new ResponseEntity<>(Exceptions.getRootCause(ex).getMessage().getBytes(StandardCharsets.UTF_8), headers,
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


}
