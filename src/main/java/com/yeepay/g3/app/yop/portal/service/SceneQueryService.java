/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.SceneParamVO;
import com.yeepay.g3.app.yop.portal.vo.SceneVO;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/7 上午10:40
 */
public interface SceneQueryService {

    List<SceneVO> list(SceneParamVO sceneParamVO);
}
