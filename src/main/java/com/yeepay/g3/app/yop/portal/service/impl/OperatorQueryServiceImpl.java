/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.enums.DataMaskEnum;
import com.yeepay.g3.app.yop.portal.service.OperatorQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.page.OperatorPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.OperatorPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.sys.enums.OperatorAuthStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.OperatorStatusEnum;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/7/22 下午3:35
 */
@Component
public class OperatorQueryServiceImpl implements OperatorQueryService {

    @Resource(name = "operatorQueryService")
    private QueryService queryService;

    private final PageItemConverter<OperatorPageItem> pageItemConverter = new OperatorPageItemConverter();

    @Override
    public PageQueryResult<OperatorPageItem> pageQuery(OperatorPageQueryParam param) {
        Map<String, Object> bizParams = getBizParams(param);
        List<Map> list = queryService.query("pageList", bizParams);
        PageQueryResult<OperatorPageItem> result = new PageQueryResult<>();
        List<OperatorPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    private Map<String, Object> getBizParams(OperatorPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("customerNo", param.getCustomerNo());
        bizParams.put("email", param.getEmail());
        bizParams.put("mobile", param.getMobile());
        bizParams.put("status", param.getStatus());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class OperatorPageItemConverter extends BasePageItemConverter<OperatorPageItem> {

        @Override
        public OperatorPageItem convert(Map<String, Object> params) {
            OperatorPageItem item = new OperatorPageItem();
            item.setId((Long) params.get("id"));
            item.setCustomerNo((String) params.get("customer_no"));
            item.setCustomerName((String) params.get("customer_name"));
            item.setEmail(DataMaskEnum.EMAIL.sensitive((String) params.get("email")));
            item.setEmailStatus(OperatorAuthStatusEnum.parse((String) params.get("email_status")));
            item.setMobile(DataMaskEnum.MOBILE.sensitive((String) params.get("mobile")));
            item.setMobileStatus(OperatorAuthStatusEnum.parse((String) params.get("mobile_status")));
            item.setStatus(OperatorStatusEnum.parse((String) params.get("status")));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }
}
