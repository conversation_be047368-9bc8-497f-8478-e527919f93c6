package com.yeepay.g3.app.yop.portal.controller;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.*;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.facade.yop.api.dto.ApiDefineDTO;
import com.yeepay.g3.facade.yop.api.facade.OldApiMgrFacade;
import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqChangeDTO;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;
import com.yeepay.g3.facade.yop.sys.enums.SecurityReqTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.ApiRequestFacade;
import com.yeepay.g3.facade.yop.sys.facade.SecurityReqMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.SecurityReqQueryFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/16 11:06
 */
@Controller
@RequestMapping("/rest/api")
public class ApiController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiController.class);

    private OldApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(OldApiMgrFacade.class);

    private com.yeepay.g3.facade.yop.sys.facade.ApiMgrFacade apiMgrFacadeNew = RemoteServiceFactory.getService(com.yeepay.g3.facade.yop.sys.facade.ApiMgrFacade.class);

    private SecurityReqMgrFacade securityReqMgrFacade = RemoteServiceFactory.getService(SecurityReqMgrFacade.class);

    private SecurityReqQueryFacade securityReqQueryFacade = RemoteServiceFactory.getService(SecurityReqQueryFacade.class);

    private ApiRequestFacade apiRequestFacade = RemoteServiceFactory.getService(ApiRequestFacade.class);

    @Autowired
    private UnifyApiService unifyApiService;

    private static final PageQueryResult<ApiDefinePageItem> EMPTY_RESULT = new PageQueryResult<ApiDefinePageItem>() {
        {
            setPageNo(0);
            setItems(new ArrayList<>());
            setTotalPageNum(0);
        }
    };

    @Autowired
    private ApiQueryService apiQueryService;

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    @Autowired
    private SpiQueryService spiQueryService;

    @Autowired
    private SoaAppService soaAppService;

    @Autowired
    private ApiRequestService apiRequestService;

    @ResponseBody
    @RequestMapping(value = "/path/exists", method = RequestMethod.GET)
    public ResponseMessage exists(@RequestParam(value = "path") String path) {
        try {
            path = URLDecoder.decode(path, "utf-8");
            String apiId = apiRequestService.findApiIdByPath(path);
            return new ResponseMessage("result", StringUtils.isNotEmpty(apiId));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when exists path[" + path + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/sandbox/deploy", method = RequestMethod.POST)
    public ResponseMessage sandbox(@RequestBody ServiceEnvDataVO serviceEnvDataVO) {
        boolean result = soaAppService.supportSandbox(serviceEnvDataVO.getData());
        Map<String, Object> data = new HashMap<>();
        data.put("result", result);
        return new ResponseMessage(data);
    }

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@RequestParam(value = "apiTitle", required = false) String apiTitle,
                                @RequestParam(value = "apiUri", required = false) String apiUri,
                                @RequestParam(value = "apiType", required = false) String apiType,
                                @RequestParam(value = "apiGroupCode", required = false) String apiGroupCode,
                                @RequestParam(value = "status", required = false) String status,
//                                @RequestParam(value = "securityReq", required = false) String securityReq,
                                @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "pageSize", required = false) Integer pageSize,
                                HttpServletRequest request) {
        ApiDefinePageQueryParam.Builder builder = ApiDefinePageQueryParam.Builder.anApiDefinePageQueryParam()
                .withApiTitle(apiTitle)
                .withApiUri(apiUri)
                .withApiType(apiType)
                .withApiGroupCode(apiGroupCode)
                .withStatus(status)
//                .withSecurityReq(securityReq)
                .withPageNo(pageNo)
                .withPageSize(pageSize);
        try {
            if (ShiroUtils.isPlatformOperator()) {
                return new ResponseMessage("page", apiQueryService.pageQuery(builder.build()));
            } else {
                List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
                if (apiGroupCodes.size() == 0) {
                    return new ResponseMessage("page", EMPTY_RESULT);
                }
                return new ResponseMessage("page", apiQueryService.pageQueryForSp(builder.withApiGroupCodes(apiGroupCodes).build(), ShiroUtils.getOperatorCode()));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when query api list with param:" + builder.build(), ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/exists", method = RequestMethod.GET)
    public ResponseMessage exists(@RequestParam(value = "httpMethod", required = false) String httpMethod,
                                  @RequestParam(value = "path") String path) {
        try {
            boolean exists;
            if (StringUtils.isEmpty(httpMethod)) {
                ApiDefineDTO api = apiMgrFacade.findApiByUri(path);
                exists = api != null;
            } else {
                ApiDTO api = apiMgrFacadeNew.findByRequestKey(new ApiRequestKey().withHttpMethod(httpMethod).withPath(path));
                exists = api != null;
            }
            return new ResponseMessage("result", exists);
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when query api exists with httpMethod: " + httpMethod + ", path:" + path, ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "/security-req", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage findSecurityReq(@RequestParam(value = "apiGroupCode") String apiGroupCode,
                                           @RequestParam(value = "apiUri", required = false) String apiUri) {
        CheckUtils.notEmpty(apiGroupCode, "apiGroupCode");
        checkUserPerm(apiGroupCode);

        Map<String, SecurityReqChangeVo> result = Maps.newHashMapWithExpectedSize(2);

        List<String> paramList = StringUtils.isNotBlank(apiUri) ?
                Lists.newArrayList(apiUri, apiGroupCode) : Lists.newArrayList(apiGroupCode);
        Map<String, List<SecurityReqDTO>> securityReqMap = securityReqQueryFacade.findByValues(paramList);
        SecurityReqChangeVo apiSecurity = null, apiGroupSecurity = null;
        if (MapUtils.isNotEmpty(securityReqMap)) {
            apiSecurity = toSecurityReqVo(securityReqMap.get(apiUri));
            apiGroupSecurity = toSecurityReqVo(securityReqMap.get(apiGroupCode));
        }
        result.put("apiSecurity", apiSecurity);
        result.put("apiGroupSecurity", apiGroupSecurity);
        return new ResponseMessage("result", result);
    }

    private SecurityReqChangeVo toSecurityReqVo(List<SecurityReqDTO> securityReqs) {
        if (CollectionUtils.isNotEmpty(securityReqs)) {
            SecurityReqChangeVo securityReq = new SecurityReqChangeVo();
            long securityReqVersion = 0L;
            List<NewSecurityReqVO> securities = new ArrayList<>(securityReqs.size());
            for (SecurityReqDTO securityReqDTO : securityReqs) {
                NewSecurityReqVO securityReqVo = new NewSecurityReqVO();
                securityReqVo.setName(securityReqDTO.getName());
                securityReqVo.setScopes(securityReqDTO.getScopes());
                securityReqVo.setExtensions(securityReqDTO.getExtensions());
                securities.add(securityReqVo);
                if (null != securityReqDTO.getVersion() && securityReqDTO.getVersion() > securityReqVersion) {
                    securityReqVersion = securityReqDTO.getVersion();
                }
            }
            securityReq.setSecurityReqVersion(securityReqVersion);
            securityReq.setSecurities(securities);
            return securityReq;
        }
        return null;
    }

    @RequestMapping(value = "/security-req/update", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage updateApiSecurityReq(@RequestBody SecurityReqChangeVo apiSecurityReq) {
        CheckUtils.notNull(apiSecurityReq, "apiSecurityReq");
        CheckUtils.notNull(apiSecurityReq.getApiUri(), "apiSecurityReq.apiUri");
//        CheckUtils.notNull(apiSecurityReq.getSecurityReqVersion(), "apiSecurityReq.securityReqVersion");

        if (null == apiSecurityReq.getSecurityReqVersion()) {
            apiSecurityReq.setSecurityReqVersion(0L);
        }
        ApiDefineDTO api = apiMgrFacade.findApiByUri(apiSecurityReq.getApiUri());
        CheckUtils.notNull(api, "该api不存在！");
        checkUserPerm(api.getApiGroup());

        SecurityReqChangeDTO securityReqChange = new SecurityReqChangeDTO();
        securityReqChange.setType(SecurityReqTypeEnum.API);
        securityReqChange.setValue(apiSecurityReq.getApiUri());
        if (CollectionUtils.isNotEmpty(apiSecurityReq.getSecurities())) {
            securityReqChange.setData(apiSecurityReq.getSecurities().stream().map(vo -> {
                SecurityReqDTO dto = new SecurityReqDTO();
                dto.setName(vo.getName());
                dto.setScopes(vo.getScopes());
                dto.setExtensions(vo.getExtensions());
                dto.setVersion(apiSecurityReq.getSecurityReqVersion());
                return dto;
            }).collect(Collectors.toList()));
        }
        securityReqMgrFacade.update(securityReqChange);
        return new ResponseMessage("result", true);
    }

    private void checkUserPerm(String apiGroup) {
        List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
        if (ShiroUtils.isSpOperator() && (CollectionUtils.isEmpty(apiGroupCodes) || apiGroupCodes.stream().noneMatch(item -> item.equals(apiGroup)))) {
            throw new IllegalArgumentException("无权限操作该分组，" + apiGroup);
        }
    }

    /**
     * 根据apiUri获取spi列表
     *
     * @param apiUri
     * @return
     */
    @RequestMapping(value = "old/callbacks", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage findOldApiCallbacks(@RequestParam String apiUri) {
        try {
            String apiId = apiRequestFacade.findApiIdByPathForOldApi(apiUri);
            List<SpiVO.SpiListVO> result = spiQueryService.apiCallbacks(apiId);
            return new ResponseMessage("result", result);
        } catch (Exception e) {
            LOGGER.error("error to get oldApiCallbacks, ex:", e);
            return new ResponseMessage(e);
        }
    }

    /**
     * 添加spi关联
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "old/callback/add", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage addOldApiCallback(@RequestBody @Validated ApiCallbacksParamVO param) {
        try {
            String apiId = apiRequestFacade.findApiIdByPathForOldApi(param.getApiUri());
            apiMgrFacadeNew.batchCreateCallback(apiId, param.getCallbacks());
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("error to add oldApiCallback, ex:", e);
            return new ResponseMessage(e);
        }
    }

    /**
     * 删除spi关联
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "old/callback/delete", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage deleteOldApiCallback(@RequestBody @Validated ApiCallbacksParamVO param) {
        try {
            String apiId = apiRequestFacade.findApiIdByPathForOldApi(param.getApiUri());
            apiMgrFacadeNew.batchDeleteCallback(apiId, param.getCallbacks());
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("error to delete oldApiCallback, ex:", e);
            return new ResponseMessage(e);
        }
    }

    /**
     * 通用关联api筛选页(api状态过滤，由前端控制)
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "list-for-join", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage listForJoin(UnifyApiPageQueryParam param) {
        if (null == param.getJoinCode()) {
            param.setJoinCode(UnifyApiPageQueryParam.JoinCodeEnum.ALL);
        }
        if (param.getJoinCode() != UnifyApiPageQueryParam.JoinCodeEnum.ALL) {
            CheckUtils.notNull(param.getJoinCode(), "joinCode");
            CheckUtils.notNull(param.getJoinValue(), "joinValue");
        }
        PageQueryResult<UnifyApiPageItem> apis = unifyApiService.findApisPage(param);
        return new ResponseMessage("page", apis);
    }

    @GetMapping("/related-spis")
    @ResponseBody
    public ResponseMessage<List<TypeVO>> listSpi(@RequestParam String apiId) {
        return new ResponseMessage("result", apiQueryService.listCallback(apiId));
    }

}
