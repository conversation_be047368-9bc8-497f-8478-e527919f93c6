/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * title: 基于 RoleCode的鉴权拦截器<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 19/1/7 20:28
 */
@Component
public class RoleBasedAuthorizationFilter extends CacheAbstractAuthorizationFilter {

    public static final String PLATFORM_SP_CODE = "PLATFORM";

    @Override
    protected String[] getSpCodes(String[] params) {
        String[] spCodes = new String[params.length];
        for (int i = 0; i < params.length; i++) {
            String[] paramStrs = StringUtils.split(params[i], ":");
            if (paramStrs.length == 1) {
                spCodes[i] = PLATFORM_SP_CODE;
            } else {
                spCodes[i] = paramStrs[0];
            }
        }
        return spCodes;
    }

    @Override
    public String shiroName() {
        return "role_based";
    }
}
