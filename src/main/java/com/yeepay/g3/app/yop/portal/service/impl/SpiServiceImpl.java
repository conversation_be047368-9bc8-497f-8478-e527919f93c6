/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.service.SpiService;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.query.QueryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/9 4:06 下午
 */
@Component
public class SpiServiceImpl implements SpiService {
    @Resource(name = "spiQueryService")
    private QueryService spiQueryService;

    @Override
    public String findSpiTitle(String spiName) {
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("spiName", spiName);
        List<Map> queryResult = spiQueryService.query("findSpiTitle", queryParam);
        if (CollectionUtils.isEmpty(queryResult)) {
            return "";
        }
        return (String) queryResult.get(0).get("title");
    }
}
