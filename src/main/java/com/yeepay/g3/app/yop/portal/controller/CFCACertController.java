/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.CfcaCertService;
import com.yeepay.g3.app.yop.portal.vo.CfcaCertParamVO;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/4 18:31
 */
@Controller
@RequestMapping("/rest/isv/cfca-cert")
@Api(tags = "cfca证书管理")
@Profile("cfca")
public class CFCACertController {
    private static final Logger LOGGER = LoggerFactory.getLogger(CFCACertController.class);

    @Autowired
    private CfcaCertService cfcaCertService;

    @ApiOperation("吊销")
    @ResponseBody
    @PostMapping(value = "/revoke")
    public ResponseMessage revoke(@RequestParam String serialNo) {
        LOGGER.info("吊销证书的serialNo: {}", serialNo);
        cfcaCertService.revoke(serialNo);
        return new ResponseMessage();
    }

    @ApiOperation("列表")
    @ResponseBody
    @GetMapping(value = "/list")
    public ResponseMessage list(@RequestParam(value = "customerNo", required = false) String customerNo,
                                @RequestParam(value = "serialNo", required = false) String serialNo,
                                @RequestParam(value = "status", required = false) String status,
                                @RequestParam(value = "used", required = false) Boolean used) {
        CfcaCertParamVO param = new CfcaCertParamVO();
        param.setCustomerNo(customerNo);
        param.setSerialNo(serialNo);
        param.setStatus(status);
        param.setUsed(used);
        return new ResponseMessage("result", cfcaCertService.list(param));
    }
}
