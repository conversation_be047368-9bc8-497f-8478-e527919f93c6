package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * HttpPropertyConfigVO
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.v3.generators.java.SpringCodegen", date = "2020-11-03T07:28:20.629Z[GMT]")


public class HttpPropertyConfigVO implements OneOfApiRouteVOProperties, Serializable {

    private static final long serialVersionUID = -1;

    @JsonProperty("path")
    private String path = null;

    @JsonProperty("httpMethod")
    private String httpMethod = null;

    @JsonProperty("contentType")
    private String contentType = null;

    @JsonProperty("connectTimeout")
    private Integer connectTimeout = null;

    @JsonProperty("readTimeout")
    private Integer readTimeout = null;

    public HttpPropertyConfigVO path(String path) {
        this.path = path;
        return this;
    }

    /**
     * path
     *
     * @return path
     **/
    @Schema(required = true, description = "path")
    @NotNull

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public HttpPropertyConfigVO httpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
        return this;
    }

    /**
     * HTTP请求方法
     *
     * @return httpMethod
     **/
    @Schema(required = true, description = "HTTP请求方法")
    @NotNull

    public String getHttpMethod() {
        return httpMethod;
    }

    public void setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
    }

    public HttpPropertyConfigVO contentType(String contentType) {
        this.contentType = contentType;
        return this;
    }

    /**
     * ContentType
     *
     * @return contentType
     **/
    @Schema(required = true, description = "ContentType")
    @NotNull

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public HttpPropertyConfigVO connectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
        return this;
    }

    /**
     * 连接超时
     *
     * @return connectTimeout
     **/
    @Schema(description = "连接超时")

    public Integer getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    public HttpPropertyConfigVO readTimeout(Integer readTimeout) {
        this.readTimeout = readTimeout;
        return this;
    }

    /**
     * 读取超时
     *
     * @return readTimeout
     **/
    @Schema(description = "读取超时")

    public Integer getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(Integer readTimeout) {
        this.readTimeout = readTimeout;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HttpPropertyConfigVO httpPropertyConfigVO = (HttpPropertyConfigVO) o;
        return Objects.equals(this.path, httpPropertyConfigVO.path) &&
                Objects.equals(this.httpMethod, httpPropertyConfigVO.httpMethod) &&
                Objects.equals(this.contentType, httpPropertyConfigVO.contentType) &&
                Objects.equals(this.connectTimeout, httpPropertyConfigVO.connectTimeout) &&
                Objects.equals(this.readTimeout, httpPropertyConfigVO.readTimeout);
    }

    @Override
    public int hashCode() {
        return Objects.hash(path, httpMethod, contentType, connectTimeout, readTimeout);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class HttpPropertyConfigVO {\n");

        sb.append("    path: ").append(toIndentedString(path)).append("\n");
        sb.append("    httpMethod: ").append(toIndentedString(httpMethod)).append("\n");
        sb.append("    contentType: ").append(toIndentedString(contentType)).append("\n");
        sb.append("    connectTimeout: ").append(toIndentedString(connectTimeout)).append("\n");
        sb.append("    readTimeout: ").append(toIndentedString(readTimeout)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
