package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ApiErrcodeService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 10:57
 */
@Component
public class ApiErrcodeServiceImpl implements ApiErrcodeService {

    private final PageItemConverter<ErrcodeApiPageItem> errcodeApiPageConverter = new ErrcodeApiPageItemConverter();
    private final PageItemConverter<ApiErrcodePageItem> apiErrcodePageConverter = new ApiErrcodePageItemConverter();

    @Resource(name = "apiErrcodeQueryService")
    private QueryService queryService;

    @Resource(name = "apiRequestQueryService")
    private QueryService apiRequestQueryService;

    @Override
    public List<String> checkRelatedApis(Long errcodeId) {
        Map<String, Object> bizParams = Collections.singletonMap("errcodeId", errcodeId);
        List<Map<String, Object>> list = queryService.query("checkRelatedApis", bizParams);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(item -> (String) item.get("path")).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public PageQueryResult<ErrcodeApiPageItem> findRelatedApisPage(ErrcodeApiPageQueryParam pageQueryParam) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(pageQueryParam);
        queryParam.setParams(Collections.singletonMap("errcodeId", pageQueryParam.getErrcodeId()));
        QueryResult queryResult = apiRequestQueryService.query("findRelatedApisPage", queryParam);
        final PageQueryResult<ErrcodeApiPageItem> result = PageQueryUtils.convertResult(queryResult, pageQueryParam.getPageNo(), errcodeApiPageConverter);
        return result;
    }

    @Override
    public PageQueryResult<ApiErrcodePageItem> findRelatedErrcodePage(ApiErrcodePageQueryParam pageQueryParam) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(pageQueryParam);
        final Map<String, String> params = Maps.newHashMap();
        params.put("apiId", pageQueryParam.getApiId());
        params.put("subErrorCode", pageQueryParam.getSubErrorCode());
        queryParam.setParams(params);
        QueryResult queryResult = queryService.query("findRelatedErrcodePage", queryParam);
        final PageQueryResult<ApiErrcodePageItem> result = PageQueryUtils.convertResult(queryResult, pageQueryParam.getPageNo(), apiErrcodePageConverter);
        return result;
    }

    @Override
    public PageQueryResult<ApiErrcodePageItem> findUnRelatedErrcodePage(ApiErrcodePageQueryParam pageQueryParam) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(pageQueryParam);
        queryParam.setParams(getBizParams(pageQueryParam));
        QueryResult queryResult = queryService.query("findUnRelatedErrcodePage", queryParam);
        final PageQueryResult<ApiErrcodePageItem> result = PageQueryUtils.convertResult(queryResult, pageQueryParam.getPageNo(), apiErrcodePageConverter);
        return result;
    }

    private Map<String, Object> getBizParams(ApiErrcodePageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiId", param.getApiId());
        bizParams.put("apiGroupCode", param.getApiGroupCode());
        bizParams.put("subErrorCode", param.getSubErrorCode());
        return bizParams;
    }

    class ErrcodeApiPageItemConverter extends BasePageItemConverter<ErrcodeApiPageItem> {

        @Override
        public ErrcodeApiPageItem convert(Map<String, Object> params) {
            ErrcodeApiPageItem item = new ErrcodeApiPageItem();
            item.setId((Long) params.get("id"));
            item.setApiId((String) params.get("api_id"));
            item.setApiUri((String) params.get("path"));
            final String httpMethod = (String) params.get("http_method");
            final boolean oldApi = "OLD_API".equals(httpMethod);
            item.setOldApi(oldApi);
            if (!oldApi) {
                item.setHttpMethod(httpMethod);
            }
            return item;
        }
    }


    class ApiErrcodePageItemConverter extends BasePageItemConverter<ApiErrcodePageItem> {

        @Override
        public ApiErrcodePageItem convert(Map<String, Object> params) {
            ApiErrcodePageItem item = new ApiErrcodePageItem();
            item.setId((Long) params.get("id"));
            item.setApiId((String) params.get("api_id"));
            item.setErrcodeId((Long) params.get("errcode_id"));
            item.setApiGroupCode((String) params.get("api_group_code"));
            item.setErrorCode((String) params.get("error_code"));
            item.setSubErrorCode((String) params.get("sub_error_code"));
            item.setSubErrorMsg((String) params.get("sub_error_msg"));
            return item;
        }
    }

}
