package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.app.yop.portal.vo.ApiManageVO;

import java.io.Serializable;

/**
 * title: Api创建检查项<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-19 11:22
 */
public class ApiCreateCheckItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private ApiManageVO api;

    public ApiManageVO getApi() {
        return api;
    }

    public void setApi(ApiManageVO api) {
        this.api = api;
    }

    public ApiCreateCheckItem withApi(ApiManageVO api) {
        this.api = api;
        return this;
    }
}
