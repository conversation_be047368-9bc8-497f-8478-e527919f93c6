# YOP Portal 系统

## 项目概述

YOP Portal 是易宝支付（YeePay）开放平台的门户系统，为第三方开发者和合作伙伴提供API访问、文档管理和集成服务的统一界面。该系统作为易宝支付开放平台的核心组件，负责管理API资源、权限控制、开发者账户以及API文档等功能。

## 主要功能

- **用户认证与授权**：基于Apache Shiro和OAuth2实现的多种认证方式
- **API资源管理**：管理开放平台的API资源和权限
- **开发者门户**：为第三方开发者提供API文档、SDK下载和测试工具
- **应用管理**：管理第三方应用的注册、审核和授权
- **监控与统计**：API调用监控和统计分析

## 技术栈

- **后端框架**：Spring Boot
- **安全框架**：Apache Shiro
- **RPC框架**：Dubbo
- **缓存**：Redis + EhCache
- **消息队列**：RabbitMQ
- **数据库**：MySQL + DB2
- **文档**：Swagger

## 系统架构

系统采用分层架构设计，主要包括：

- 表现层（Controller）
- 业务逻辑层（Service）
- 数据访问层（DAO）
- 外部集成层（Facade）

详细的系统架构请参考 [系统架构文档](system-architecture.md)。

## 组件结构

系统由多个核心组件组成，包括认证组件、资源管理组件、文档管理组件等。详细的组件结构请参考 [组件图文档](component-diagram.md)。

## 数据流

系统的主要数据流包括用户认证流程、API资源访问流程、开发者注册流程等。详细的数据流请参考 [数据流图文档](data-flow-diagram.md)。

## 部署结构

系统支持多环境部署，包括开发环境、测试环境、预生产环境和生产环境。详细的部署结构请参考 [部署图文档](deployment-diagram.md)。

## 快速开始

### 环境要求

- JDK 1.8+
- Maven 3.0+
- MySQL 5.7+
- Redis 5.0+
- RabbitMQ 3.8+

### 构建与运行

```bash
# 克隆项目
<NAME_EMAIL>:yop/yop-portal.git

# 进入项目目录
cd yop-portal

# 编译打包
mvn clean package

# 运行应用
java -jar target/yop-portal.jar
```

## 文档

- [系统架构](system-architecture.md)
- [组件图](component-diagram.md)
- [数据流图](data-flow-diagram.md)
- [部署图](deployment-diagram.md)
