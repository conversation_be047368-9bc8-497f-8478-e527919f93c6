/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.exception;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.core.yop.utils.Exceptions;
import com.yeepay.g3.facade.yop.doc.exceptions.PageOwnerException;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authc.AuthenticationException;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.SQLException;
import java.util.Set;
import java.util.concurrent.TimeoutException;

/**
 * <p>Title: 微信异常统一处理 Handler</p>
 * <p>Description: 描述</p>
 * <p>Copyright: Copyright (c)2011</p>
 * <p>Company: 易宝支付(YeePay)</p>
 *
 * <AUTHOR>
 * @version 0.1, 14-8-18 15:58
 */
@Slf4j
@ControllerAdvice
public class YopPortalExceptionHandler {

    @ExceptionHandler(AuthenticationException.class)
    @ResponseBody
    public ResponseMessage handleTwoFactorAuthException(AuthenticationException e) {
        log.info("登录异常", e);
        return new ResponseMessage(ResponseMessage.Status.ERROR, e.getMessage());
    }

    @ExceptionHandler(YopPortalException.class)
    @ResponseBody
    public ResponseMessage handleYopPortalException(Exception e) {
        log.warn("内部系统异常", e);
        return new ResponseMessage(ResponseMessage.Status.ERROR, "内部系统异常:" + e.getMessage());
    }

    @ExceptionHandler(MultipartException.class)
    @ResponseBody
    public ResponseMessage handleMultipartException(MultipartException ex) {
        log.warn("上传文件异常", ex);
        return new ResponseMessage(ResponseMessage.Status.ERROR, Exceptions.getRootCause(ex).getMessage());
    }

    // ========== 页面负责人管理相关异常处理 ==========

    /**
     * 处理页面负责人管理异常
     */
    @ExceptionHandler(PageOwnerException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handlePageOwnerException(PageOwnerException ex) {
        log.warn("页面负责人管理异常", ex);
        return new ResponseMessage(ResponseMessage.Status.ERROR, ex.getMessage());
    }

    /**
     * 处理数据库操作异常
     */
    @ExceptionHandler(DataAccessException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseMessage handleDataAccessException(DataAccessException ex) {
        log.error("数据库操作异常", ex);
        
        // 检查是否是并发更新冲突
        if (ex.getCause() instanceof SQLException) {
            SQLException sqlEx = (SQLException) ex.getCause();
            // MySQL 乐观锁冲突通常返回 affected rows = 0
            if (sqlEx.getMessage().contains("Deadlock") || sqlEx.getMessage().contains("Lock wait timeout")) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "数据已被其他用户修改，请刷新后重试");
            }
        }
        
        return new ResponseMessage(ResponseMessage.Status.ERROR, "数据库操作失败，请稍后重试");
    }

    /**
     * 处理重复键异常
     */
    @ExceptionHandler(DuplicateKeyException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.CONFLICT)
    public ResponseMessage handleDuplicateKeyException(DuplicateKeyException ex) {
        log.warn("数据重复异常", ex);
        return new ResponseMessage(ResponseMessage.Status.ERROR, "数据已存在，请检查后重试");
    }

    /**
     * 处理数据完整性违反异常
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handleDataIntegrityViolationException(DataIntegrityViolationException ex) {
        log.warn("数据完整性违反异常", ex);
        
        // 检查是否是外键约束违反
        if (ex.getMessage().contains("foreign key constraint")) {
            return new ResponseMessage(ResponseMessage.Status.ERROR, "关联数据不存在，请检查输入参数");
        }
        
        return new ResponseMessage(ResponseMessage.Status.ERROR, "数据完整性验证失败");
    }

    /**
     * 处理参数验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        log.warn("参数验证异常", ex);
        
        StringBuilder errorMsg = new StringBuilder("参数验证失败: ");
        for (FieldError fieldError : ex.getBindingResult().getFieldErrors()) {
            errorMsg.append(fieldError.getField()).append(" ").append(fieldError.getDefaultMessage()).append("; ");
        }
        
        return new ResponseMessage(ResponseMessage.Status.ERROR, errorMsg.toString());
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handleBindException(BindException ex) {
        log.warn("参数绑定异常", ex);
        
        StringBuilder errorMsg = new StringBuilder("参数绑定失败: ");
        for (FieldError fieldError : ex.getBindingResult().getFieldErrors()) {
            errorMsg.append(fieldError.getField()).append(" ").append(fieldError.getDefaultMessage()).append("; ");
        }
        
        return new ResponseMessage(ResponseMessage.Status.ERROR, errorMsg.toString());
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handleConstraintViolationException(ConstraintViolationException ex) {
        log.warn("约束违反异常", ex);
        
        StringBuilder errorMsg = new StringBuilder("参数验证失败: ");
        Set<ConstraintViolation<?>> violations = ex.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            errorMsg.append(violation.getPropertyPath()).append(" ").append(violation.getMessage()).append("; ");
        }
        
        return new ResponseMessage(ResponseMessage.Status.ERROR, errorMsg.toString());
    }

    /**
     * 处理超时异常
     */
    @ExceptionHandler(TimeoutException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.REQUEST_TIMEOUT)
    public ResponseMessage handleTimeoutException(TimeoutException ex) {
        log.warn("操作超时异常", ex);
        return new ResponseMessage(ResponseMessage.Status.ERROR, "操作超时，请稍后重试");
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage handleIllegalArgumentException(IllegalArgumentException ex) {
        log.warn("非法参数异常", ex);
        return new ResponseMessage(ResponseMessage.Status.ERROR, "参数错误: " + ex.getMessage());
    }

    /**
     * 处理非法状态异常
     */
    @ExceptionHandler(IllegalStateException.class)
    @ResponseBody
    @ResponseStatus(HttpStatus.CONFLICT)
    public ResponseMessage handleIllegalStateException(IllegalStateException ex) {
        log.warn("非法状态异常", ex);
        return new ResponseMessage(ResponseMessage.Status.ERROR, "操作状态异常: " + ex.getMessage());
    }

}
