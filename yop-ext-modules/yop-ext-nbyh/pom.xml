<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.yeepay.g3.yop</groupId>
        <artifactId>yop-center-parent</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>yop-ext-nbyh</artifactId>
    <version>${yop-ext-nbyh.version}</version>
    <name>YOP :: EXT :: NBYH</name>

    <dependencies>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-ext-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-ext-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>
    </dependencies>

</project>