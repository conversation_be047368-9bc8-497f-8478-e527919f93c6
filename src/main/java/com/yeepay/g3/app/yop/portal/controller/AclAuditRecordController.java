/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.AuditQueryService;
import com.yeepay.g3.app.yop.portal.utils.HttpUtils;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.AuditRecordDetailVO;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.page.AuditRecordPageQueryParam;
import com.yeepay.g3.core.yop.utils.bean.BeanConvertUtils;
import com.yeepay.g3.facade.yop.perm.dto.AclAuditRecordReqDTO;
import com.yeepay.g3.facade.yop.perm.dto.AclAuditRecordStageDTO;
import com.yeepay.g3.facade.yop.perm.enums.AuditOperateEnum;
import com.yeepay.g3.facade.yop.perm.enums.AuditStatusEnum;
import com.yeepay.g3.facade.yop.perm.facade.AclAuditFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * title:审核设置 <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-25 15:11
 */
@Controller
@RequestMapping("/rest/acl/audit/record")
public class AclAuditRecordController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AclAuditRecordController.class);

    private AclAuditFacade aclAuditFacade = RemoteServiceFactory.getService(AclAuditFacade.class);

    @Autowired
    private AuditQueryService auditQueryService;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@Validated AuditRecordPageQueryParam param,
                                @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "_pageSize", required = false) Integer pageSize) {
        try {
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            param.setOperator(ShiroUtils.getOperatorCode());
            if (ShiroUtils.isSpOperator()) {
                param.setOperator(ShiroUtils.getOperatorCode());
                return new ResponseMessage("page", auditQueryService.pageQueryRecordForSp(param));
            } else {
                return new ResponseMessage("page", auditQueryService.pageQueryRecordList(param));
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list auditRecord with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/handle", method = RequestMethod.POST)
    public ResponseMessage handle(@RequestParam("code") String code) {
        try {
            AclAuditRecordStageDTO stageDTO = new AclAuditRecordStageDTO();
            stageDTO.setRecordCode(code);
            stageDTO.setOperate(AuditOperateEnum.HANDLE);
            stageDTO.setOperator(ShiroUtils.getOperatorCode());
            aclAuditFacade.updateRecord(stageDTO);
            AuditRecordDetailVO detailVO = BeanConvertUtils.convert(aclAuditFacade.findRecordWithStageByCode(code), AuditRecordDetailVO.class);
            return new ResponseMessage("result", detailVO);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when handle auditRecord with code:" + code, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam("code") String code) {
        try {
            AuditRecordDetailVO detailVO = BeanConvertUtils.convert(aclAuditFacade.findRecordWithStageByCode(code), AuditRecordDetailVO.class);
            return new ResponseMessage("result", detailVO);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when detail auditRecord with code:" + code, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public ResponseMessage cancel(@RequestParam("code") String code) {
        try {
            AclAuditRecordStageDTO stageDTO = new AclAuditRecordStageDTO();
            stageDTO.setRecordCode(code);
            stageDTO.setOperate(AuditOperateEnum.CANCEL);
            stageDTO.setOperator(ShiroUtils.getOperatorCode());
            aclAuditFacade.updateRecord(stageDTO);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when cancel auditRecord with code:" + code, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/accept", method = RequestMethod.POST)
    public ResponseMessage accept(@RequestParam("code") String code,
                                  @RequestParam(value = "cause", required = false) String cause,
                                  HttpServletRequest request) {
        try {
            AclAuditRecordReqDTO reqDTO = aclAuditFacade.findRecordReqByCode(code);
            if (reqDTO.getEndStage()) {
                String baseUrl = (String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_BASE_URL);
                String url = baseUrl + reqDTO.getResourceUrl();
                HttpUtils.post(request, reqDTO.getContent(), url, aclAuditFacade.findRecordByCode(code), ShiroUtils.getOperatorCode());
            }
            AclAuditRecordStageDTO stageDTO = new AclAuditRecordStageDTO();
            stageDTO.setRecordCode(code);
            stageDTO.setOperate(AuditOperateEnum.ACCEPT);
            stageDTO.setCause(cause);
            stageDTO.setOperator(ShiroUtils.getOperatorCode());
            aclAuditFacade.updateRecord(stageDTO);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when accept auditRecord with code:" + code, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/refuse", method = RequestMethod.POST)
    public ResponseMessage refuse(@RequestParam("code") String code,
                                  @RequestParam("cause") String cause) {
        try {
            AclAuditRecordStageDTO stageDTO = new AclAuditRecordStageDTO();
            stageDTO.setRecordCode(code);
            stageDTO.setOperate(AuditOperateEnum.REFUSE);
            stageDTO.setCause(cause);
            stageDTO.setOperator(ShiroUtils.getOperatorCode());
            aclAuditFacade.updateRecord(stageDTO);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when refuse auditRecord with code:" + code, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/content", method = RequestMethod.GET)
    public ResponseMessage getContent(@RequestParam("code") String code) {
        try {
            return new ResponseMessage("result", aclAuditFacade.findRecordByCode(code).getContent());
        } catch (Exception e) {
            LOGGER.error("Exception occurred when get auditRecordContent with code:" + code, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/status", method = RequestMethod.GET)
    public ResponseMessage status() {
        List<CommonsVO> list = new ArrayList<>();
        AuditStatusEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/operate", method = RequestMethod.GET)
    public ResponseMessage operate() {
        List<CommonsVO> list = new ArrayList<>();
        AuditOperateEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }
}
