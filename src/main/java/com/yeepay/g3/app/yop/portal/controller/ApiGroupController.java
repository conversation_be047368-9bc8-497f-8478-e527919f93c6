/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.yeepay.g3.app.yop.portal.dto.ApiSimpleInfoDTO;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.ApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.service.ApiQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.utils.security.SecurityUtils;
import com.yeepay.g3.app.yop.portal.validation.group.ApiGroupCommon;
import com.yeepay.g3.app.yop.portal.validation.group.ApiGroupCreate;
import com.yeepay.g3.app.yop.portal.validation.group.ApiGroupEdit;
import com.yeepay.g3.app.yop.portal.vo.ApiGroupVO;
import com.yeepay.g3.app.yop.portal.vo.SecurityReqChangeVo;
import com.yeepay.g3.app.yop.portal.vo.page.ApiGroupPageQueryParam;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.facade.yop.sys.dto.ApiGroupDTO;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqChangeDTO;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqDTO;
import com.yeepay.g3.facade.yop.sys.enums.SecurityReqTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.ApiGroupFacade;
import com.yeepay.g3.facade.yop.sys.facade.SecurityReqMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.SecurityReqQueryFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR> @version 1.0.0
 * @since 18/3/9 14:10
 */
@Controller
@RequestMapping("/rest/api-group")
public class ApiGroupController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiGroupController.class);

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    @Autowired
    private ApiQueryService apiQueryService;

    private ApiGroupFacade apiGroupFacade = RemoteServiceFactory.getService(ApiGroupFacade.class);

    private ObjectMapper objectMapper;

    private SecurityReqMgrFacade securityReqMgrFacade = RemoteServiceFactory.getService(SecurityReqMgrFacade.class);

    private SecurityReqQueryFacade securityReqQueryFacade = RemoteServiceFactory.getService(SecurityReqQueryFacade.class);

    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";

    @RequestMapping(value = "/commons/api-group-codes", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage apiGroupCodes() {
        try {
            List<ApiGroupVO> apiGroupList;
            if (OperatorTypeEnum.PLATFORM == ShiroUtils.getOperatorType()) {
                apiGroupList = apiGroupQueryService.list();
            } else {
                apiGroupList = apiGroupQueryService.listForSp(ShiroUtils.getShiroUser().getSpScopes());
            }
            return new ResponseMessage("result", apiGroupList);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list api group codes.", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@RequestParam(value = "apiGroupCode", required = false) String apiGroupCode,
                                @RequestParam(value = "spCode", required = false) String spCode,
                                @RequestParam(value = "createdStartDate", required = false) String createdStartDate,
                                @RequestParam(value = "createdEndDate", required = false) String createdEndDate,
                                @RequestParam(value = "_pageNo", required = false) Integer _pageNo,
                                @RequestParam(value = "_pageSize", required = false) Integer _pageSize) {
        ApiGroupPageQueryParam param = new ApiGroupPageQueryParam();
        try {
            param.setApiGroupCode(apiGroupCode);
            param.setSpCode(spCode);
            param.setPageNo(_pageNo);
            param.setPageSize(_pageSize);
            SimpleDateFormat sdf = new SimpleDateFormat(PATTERN);
            if (StringUtils.isNotBlank(createdStartDate)) {
                param.setCreatedStartDate(sdf.parse(createdStartDate));
            }
            if (StringUtils.isNotBlank(createdEndDate)) {
                param.setCreatedEndDate(sdf.parse(createdEndDate));
            }
            if (ShiroUtils.isPlatformOperator()) {
                return new ResponseMessage("page", apiGroupQueryService.pageList(param));
            } else {
                return new ResponseMessage("page", apiGroupQueryService.pageListForSp(param, ShiroUtils.getShiroUser().getSpScopes()));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list api groups with param:" + param, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody @Validated({ApiGroupCommon.class, ApiGroupCreate.class}) ApiGroupVO apiGroupVO) {
        ApiGroupDTO dto = conver(apiGroupVO);
        apiGroupFacade.add(dto);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/check-exists", method = RequestMethod.GET)
    public ResponseMessage create(@RequestParam("apiGroupCode") String apiGroupCode) {
        ApiGroupDTO apiGroupDTO = apiGroupFacade.findByApiGroupCode(apiGroupCode);
        return new ResponseMessage("result", apiGroupDTO != null);
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody @Validated({ApiGroupCommon.class, ApiGroupEdit.class}) ApiGroupVO apiGroupVO) {
        ApiGroupDTO dto = conver(apiGroupVO);
        apiGroupFacade.update(dto);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam("apiGroupCode") String apiGroupCode) {
        try {
            ApiGroupDTO apiGroupDTO = apiGroupFacade.findByApiGroupCode(apiGroupCode);
            return new ResponseMessage("result", conver(apiGroupDTO));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when get detail of apiGroup[" + apiGroupCode + "]", ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam("apiGroupCode") String apiGroupCode) {
        try {
            apiGroupFacade.delete(apiGroupCode);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when delete apiGroupCode[" + apiGroupCode + "]", ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/api-list", method = RequestMethod.GET)
    public ResponseMessage queryApiForApiGroup(@RequestParam("apiGroupCode") String apiGroupCode) {
        List<ApiSimpleInfoDTO> apiList = apiQueryService.queryApiForByGroupCode(apiGroupCode);
        return new ResponseMessage("result", apiList);
    }

    @RequestMapping(value = "/security-req/update", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage updateApiSecurityReq(@RequestBody SecurityReqChangeVo apiSecurityReq) {
        CheckUtils.notNull(apiSecurityReq, "apiSecurityReq");

        if (null == apiSecurityReq.getSecurityReqVersion()) {
            apiSecurityReq.setSecurityReqVersion(0L);
        }
        checkUserPerm(apiSecurityReq.getApiGroupCode());

        SecurityReqChangeDTO securityReqChange = new SecurityReqChangeDTO();
        securityReqChange.setType(SecurityReqTypeEnum.API_GROUP);
        securityReqChange.setValue(apiSecurityReq.getApiGroupCode());
        if (CollectionUtils.isNotEmpty(apiSecurityReq.getSecurities())) {
            securityReqChange.setData(apiSecurityReq.getSecurities().stream().map(vo -> {
                SecurityReqDTO dto = new SecurityReqDTO();
                dto.setName(vo.getName());
                dto.setScopes(vo.getScopes());
                dto.setExtensions(vo.getExtensions());
                dto.setVersion(apiSecurityReq.getSecurityReqVersion());
                return dto;
            }).collect(Collectors.toList()));
        }
        securityReqMgrFacade.update(securityReqChange);
        return new ResponseMessage("result", true);
    }

    private void checkUserPerm(String apiGroup) {
        List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
        if (ShiroUtils.isSpOperator() && (CollectionUtils.isEmpty(apiGroupCodes) || apiGroupCodes.stream().noneMatch(item -> item.equals(apiGroup)))) {
            throw new IllegalArgumentException("无权限操作该分组，" + apiGroup);
        }
    }

    @RequestMapping(value = "/security-req", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage findSecurityReq(@RequestParam(value = "apiGroupCode") String apiGroupCode) {
        CheckUtils.notEmpty(apiGroupCode, "apiGroupCode");
        checkUserPerm(apiGroupCode);

        List<String> paramList = Lists.newArrayList(apiGroupCode);
        Map<String, List<SecurityReqDTO>> securityReqMap = securityReqQueryFacade.findByValues(paramList);
        SecurityReqChangeVo apiGroupSecurity = null;
        if (MapUtils.isNotEmpty(securityReqMap)) {
            apiGroupSecurity = SecurityUtils.toSecurityReqVo(securityReqMap.get(apiGroupCode));
        }
        return new ResponseMessage("result", apiGroupSecurity);
    }

    private ApiGroupDTO conver(ApiGroupVO apiGroupVO) {
        ApiGroupDTO dto = new ApiGroupDTO();
        dto.setApiGroupCode(apiGroupVO.getApiGroupCode());
        dto.setApiGroupName(apiGroupVO.getApiGroupName());
        dto.setSpCode(apiGroupVO.getSpCode());
        dto.setDescription(apiGroupVO.getDescription());
        dto.setVersion(apiGroupVO.getVersion());
        return dto;
    }

    private ApiGroupVO conver(ApiGroupDTO dto) {
        ApiGroupVO vo = new ApiGroupVO();
        vo.setApiGroupCode(dto.getApiGroupCode());
        vo.setApiGroupName(dto.getApiGroupName());
        vo.setSpCode(dto.getSpCode());
        vo.setDescription(dto.getDescription());
        vo.setVersion(dto.getVersion());
        return vo;
    }

    @PostConstruct
    public void init() {
        objectMapper = JsonMapper.nonEmptyMapper().getMapper();
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        objectMapper.disableDefaultTyping();
    }

}
