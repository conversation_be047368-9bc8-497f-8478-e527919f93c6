package com.yeepay.g3.app.yop.portal.dto;

import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 14:10
 */
public class ErrcodeApiBatchAddDTO extends BaseDTO {

    private static final long serialVersionUID = -1L;

    @NotNull
    private Long errcodeId;

    @NotEmpty
    private List<String> apiIds;

    public Long getErrcodeId() {
        return errcodeId;
    }

    public void setErrcodeId(Long errcodeId) {
        this.errcodeId = errcodeId;
    }

    public List<String> getApiIds() {
        return apiIds;
    }

    public void setApiIds(List<String> apiIds) {
        this.apiIds = apiIds;
    }
}
