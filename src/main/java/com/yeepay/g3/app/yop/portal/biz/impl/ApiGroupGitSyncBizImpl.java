package com.yeepay.g3.app.yop.portal.biz.impl;

import com.yeepay.g3.app.yop.portal.biz.ApiGroupGitSyncBiz;
import com.yeepay.g3.app.yop.portal.controller.support.sync.ApiGroupGitSyncDiffDTO;
import com.yeepay.g3.app.yop.portal.dto.ApiGroupGitSyncCommitDTO;
import com.yeepay.g3.app.yop.portal.dto.ApiGroupGitSyncContext;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.git.GitClient;
import com.yeepay.g3.app.yop.portal.git.GitFile;
import com.yeepay.g3.app.yop.portal.service.ApiGroupGitSyncService;
import com.yeepay.g3.app.yop.portal.utils.CephUtil;
import com.yeepay.g3.app.yop.portal.utils.Constants;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.yop.sys.dto.ApiGroupGitSyncInfoDTO;
import com.yeepay.g3.facade.yop.sys.facade.ApiGroupGitSyncInfoMgrFacade;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import okhttp3.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.http.Body;
import retrofit2.http.POST;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static com.yeepay.g3.app.yop.portal.utils.Constants.*;

/**
 * title: Api分组git同步biz<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-26 11:14
 */
@Component
public class ApiGroupGitSyncBizImpl implements ApiGroupGitSyncBiz {

    private static final String GIT_SYNC_CEPH_FOLDER = "gitSync";

    private ApiGroupGitSyncInfoMgrFacade apiGroupGitSyncInfoMgrFacade() {
        return RemoteServiceFactory.getService(ApiGroupGitSyncInfoMgrFacade.class);
    }

    @Autowired
    private ApiGroupGitSyncService syncService;

    @Autowired
    private GitClient gitClient;

    private GitSyncClient gitSyncClient;

    @Override
    public ApiGroupGitSyncDiffDTO diff(ApiGroupGitSyncInfoDTO syncInfo, String currentCommitId, String latestCommitId) {
        ApiGroupGitSyncDiffDTO diff;
        String syncMode = (String) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_GIT_SYNC_MODE);
        if (StringUtils.equals(syncMode, YOP_GIT_SYNC_ACTIVE_MODE)) {
            diff = showDiffFromGit(syncInfo, currentCommitId, latestCommitId);
        } else {
            try {
                diff = showDiffFromCeph(syncInfo, currentCommitId, latestCommitId);
            } catch (IOException ex) {
                throw new YeepayRuntimeException("unexpected exception occurred when show diff from ceph", ex);
            }
        }
        return diff;
    }

    private ApiGroupGitSyncDiffDTO showDiffFromCeph(ApiGroupGitSyncInfoDTO syncInfo, String currentCommitId, String latestCommitId) throws IOException {
        String requestId = getSyncRequestId();
        ApiGroupGitSyncDiffDTO result = new ApiGroupGitSyncDiffDTO()
                .withApiGroup(syncInfo.getApiGroup())
                .withRequestId(requestId);
        Map<String, String> cephConfig = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CEPH_CONFIG);
        CephUtil.CephToken token = new CephUtil.CephToken(cephConfig.get(Constants.CEPH_YCS_URL), cephConfig.get(Constants.CEPH_TOKEN));
        if (StringUtils.isNotEmpty(currentCommitId)) {
            InputStream currentIn = CephUtil.get(token, cephConfig.get(Constants.CEPH_BUCKET_NAME), getGitSyncCephFilePath(syncInfo.getApiGroup(), currentCommitId));
            if (currentIn == null) {
                throw new YeepayRuntimeException("current file not exist, apiGroup:{0}, commitId:{1}.", syncInfo.getApiGroup(), currentCommitId);
            }
            result.withCurrentContent(IOUtils.toString(currentIn, DEFAULT_CHAR_SET));
        }
        InputStream latestIn = CephUtil.get(token, cephConfig.get(CEPH_BUCKET_NAME), getGitSyncCephFilePath(syncInfo.getApiGroup(), latestCommitId));
        if (latestIn == null) {
            throw new YeepayRuntimeException("latest file not exist, apiGroup:{0}, commitId:{1}.", syncInfo.getApiGroup(), currentCommitId);
        }
        result.withLatestContent(IOUtils.toString(latestIn, DEFAULT_CHAR_SET));
        ApiGroupGitSyncContext context = new ApiGroupGitSyncContext()
                .withApiGroup(syncInfo.getApiGroup())
                .withRequestId(requestId)
                .withCurrentCommitId(currentCommitId)
                .withLatestCommitId(latestCommitId)
                .withLatestFileContent(result.getLatestContent())
                .withSyncInfoVersion(syncInfo.getVersion());
        this.syncService.saveSyncContext(context);
        return result;
    }

    private String getGitSyncCephFilePath(String apiGroup, String commitId) {
        return GIT_SYNC_CEPH_FOLDER + File.separator + apiGroup + File.separator + commitId;
    }

    private ApiGroupGitSyncDiffDTO showDiffFromGit(ApiGroupGitSyncInfoDTO syncInfo, String currentCommitId, String latestCommitId) {
        GitFile currentFile = null;
        if (StringUtils.isNotEmpty(currentCommitId)) {
            currentFile = gitClient.getFile(syncInfo.getGitRepository(), syncInfo.getFilePath(), currentCommitId);
            if (currentFile == null) {
                throw new YeepayRuntimeException("currentCommitId not exist");
            }
        }
        GitFile latestFile = gitClient.getFile(syncInfo.getGitRepository(), syncInfo.getFilePath(), latestCommitId);
        if (latestFile == null) {
            throw new YeepayRuntimeException("latestCommitId not exist.");
        }
        String requestId = getSyncRequestId();
        ApiGroupGitSyncContext context = new ApiGroupGitSyncContext()
                .withApiGroup(syncInfo.getApiGroup())
                .withRequestId(requestId)
                .withCurrentCommitId(currentCommitId)
                .withLatestCommitId(latestCommitId)
                .withLatestFileContent(latestFile.getContent())
                .withSyncInfoVersion(syncInfo.getVersion());
        this.syncService.saveSyncContext(context);
        return new ApiGroupGitSyncDiffDTO()
                .withApiGroup(syncInfo.getApiGroup())
                .withCurrentContent(currentFile == null ? null : currentFile.getContent())
                .withLatestContent(latestFile.getContent())
                .withRequestId(requestId);
    }

    private String getSyncRequestId() {
        return UUID.randomUUID().toString();
    }

    @Override
    public void commitToProduction(ApiGroupGitSyncCommitDTO commit) {
        Call<ResponseMessage> callback = gitSyncClient.commit(commit);
        try {
            ResponseMessage responseMessage = callback.execute().body();
            if (responseMessage == null) {
                throw new YeepayRuntimeException("no response returned");
            }
            if (!StringUtils.equalsIgnoreCase(responseMessage.getStatus(), "success")) {
                throw new YeepayRuntimeException("commit failed, msg:{0}", responseMessage.getMessage());
            }
        } catch (IOException ex) {
            throw new YeepayRuntimeException("Unexpected exception occurred when commit to production", ex);
        }
    }

    @Override
    public void handleCommit(ApiGroupGitSyncCommitDTO commit) {
        ApiGroupGitSyncInfoDTO info = apiGroupGitSyncInfoMgrFacade().find(commit.getApiGroup());
        if (info == null) {
            throw new YeepayRuntimeException("GitSyncInfo not exist, apiGroup:{0}.", commit.getApiGroup());
        }
        if (BooleanUtils.isNotTrue(info.getSyncOpen())) {
            throw new YeepayRuntimeException("GitSync is closed, apiGroup:{0}.", commit.getApiGroup());
        }
        info.setGitRepository(commit.getGitRepository());
        info.setGitBranch(commit.getGitBranch());
        info.setFilePath(commit.getFilePath());
        info.setLatestCommitId(commit.getCommitId());
        apiGroupGitSyncInfoMgrFacade().update(info);
        uploadFileToCeph(commit);
    }

    private void uploadFileToCeph(ApiGroupGitSyncCommitDTO commit) {
        Map<String, String> cephConfig = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CEPH_CONFIG);
        CephUtil.CephToken token = new CephUtil.CephToken(cephConfig.get(Constants.CEPH_YCS_URL), cephConfig.get(Constants.CEPH_TOKEN));
        byte[] contentBytes = commit.getContent().getBytes(DEFAULT_CHAR_SET);
        CephUtil.upload(token, cephConfig.get(CEPH_BUCKET_NAME), getGitSyncCephFilePath(commit.getApiGroup(), commit.getCommitId()),
                contentBytes.length, new ByteArrayInputStream(contentBytes));
    }


    @Override
    public ApiGroupGitSyncContext findSyncRequest(String apiGroup, String requestId) {
        return syncService.findSyncRequest(apiGroup, requestId);
    }

    @Override
    public void deleteSyncRequest(String apiGroup, String requestId) {
        syncService.deleteSyncRequest(apiGroup, requestId);
    }

    @Override
    public void refreshAll() {
        syncService.refreshAll();
    }


    interface GitSyncClient {

        /**
         * 提交
         *
         * @param commit commit
         * @return call
         */
        @POST("rest/api-group/git-sync/handle-commit")
        Call<ResponseMessage> commit(@Body ApiGroupGitSyncCommitDTO commit);
    }

    @PostConstruct
    public void init() {
        Map<String, String> gitClientConfig = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_GIT_SYNC_PRODUCTION_COMMIT_CONFIG);
        Retrofit retrofit = new Retrofit.Builder().baseUrl(gitClientConfig.get("baseUrl"))
                .addConverterFactory(JacksonConverterFactory.create(JsonMapper.nonNullMapper().getMapper()))
                .client(new OkHttpClient()
                        .newBuilder()
                        .addInterceptor(new BasicAuthInterceptor(gitClientConfig.get("username"), gitClientConfig.get("password")))
                        .connectTimeout(10, TimeUnit.SECONDS)
                        .build())
                .build();
        gitSyncClient = retrofit.create(GitSyncClient.class);
    }

    class BasicAuthInterceptor implements Interceptor {

        private final String credentials;

        public BasicAuthInterceptor(String username, String password) {
            this.credentials = Credentials.basic(username, password);
        }

        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            request = request.newBuilder().addHeader("Authorization", credentials).build();
            return chain.proceed(request);
        }
    }

}
