package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;
import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;
import com.yeepay.g3.facade.yop.sys.dto.SpiDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;

import java.io.Serializable;
import java.util.Map;

/**
 * title: Api倚赖分析请求<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 15:26
 */
public class ApiDependencyAnalysisRequest implements Serializable {

    private static final long serialVersionUID = -1L;

    private Map<ApiRequestKey, ApiDTO> apis;

    private Map<String, SpiDTO> spis;

    private Map<String, ModelDTO> models;

    public ApiDependencyAnalysisRequest(Map<ApiRequestKey, ApiDTO> apis, Map<String, SpiDTO> spis, Map<String, ModelDTO> models) {
        this.apis = apis;
        this.spis = spis;
        this.models = models;
    }

    public Map<ApiRequestKey, ApiDTO> getApis() {
        return apis;
    }

    public Map<String, SpiDTO> getSpis() {
        return spis;
    }

    public Map<String, ModelDTO> getModels() {
        return models;
    }
}
