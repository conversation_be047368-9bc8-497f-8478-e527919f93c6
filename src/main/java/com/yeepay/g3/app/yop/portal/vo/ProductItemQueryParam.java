package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.sys.enums.ProductStatusEnum;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-29 12:44
 */
public class ProductItemQueryParam implements Serializable {

    private static final long serialVersionUID = -1L;

    private List<String> includeProducts;
    private List<String> excludeProducts;
    private ProductStatusEnum status;

    public List<String> getIncludeProducts() {
        return includeProducts;
    }

    public void setIncludeProducts(List<String> includeProducts) {
        this.includeProducts = includeProducts;
    }

    public List<String> getExcludeProducts() {
        return excludeProducts;
    }

    public void setExcludeProducts(List<String> excludeProducts) {
        this.excludeProducts = excludeProducts;
    }

    public ProductStatusEnum getStatus() {
        return status;
    }

    public void setStatus(ProductStatusEnum status) {
        this.status = status;
    }
}
