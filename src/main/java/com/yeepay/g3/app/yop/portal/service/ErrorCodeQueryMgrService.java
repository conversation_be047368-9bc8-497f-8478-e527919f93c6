package com.yeepay.g3.app.yop.portal.service;


import com.yeepay.g3.app.yop.portal.vo.page.ErrorCodeItem;
import com.yeepay.g3.app.yop.portal.vo.page.ErrorCodePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午3:51
 */
public interface ErrorCodeQueryMgrService {

    PageQueryResult<ErrorCodeItem> queryErrorCodes(ErrorCodePageQueryParam errorCodePageQueryParam);
}
