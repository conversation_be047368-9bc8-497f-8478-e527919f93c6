package com.yeepay.g3.app.yop.portal.vo;

import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: 测试用例执行历史VO<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/6/18 下午4:14
 */
public class RegTestExecHisVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private Long version;

    private String status;

    private String operator;

    private String environment;

    private Date startExecuteTime;

    private Date finishExecuteTime;

    private Date createdDateTime;

    private Date lastModifiedDateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getEnvironment() {
        return environment;
    }

    public void setEnvironment(String environment) {
        this.environment = environment;
    }

    public Date getStartExecuteTime() {
        return startExecuteTime;
    }

    public void setStartExecuteTime(Date startExecuteTime) {
        this.startExecuteTime = startExecuteTime;
    }

    public Date getFinishExecuteTime() {
        return finishExecuteTime;
    }

    public void setFinishExecuteTime(Date finishExecuteTime) {
        this.finishExecuteTime = finishExecuteTime;
    }

    public Date getCreatedDateTime() {
        return createdDateTime;
    }

    public void setCreatedDateTime(Date createdDateTime) {
        this.createdDateTime = createdDateTime;
    }

    public Date getLastModifiedDateTime() {
        return lastModifiedDateTime;
    }

    public void setLastModifiedDateTime(Date lastModifiedDateTime) {
        this.lastModifiedDateTime = lastModifiedDateTime;
    }

    @Override
    public String toString() {
        return "RegTestExecHisVO{" +
                "id=" + id +
                ", version=" + version +
                ", status='" + status + '\'' +
                ", operator='" + operator + '\'' +
                ", environment='" + environment + '\'' +
                ", startExecuteTime=" + startExecuteTime +
                ", finishExecuteTime=" + finishExecuteTime +
                ", createdDateTime=" + createdDateTime +
                ", lastModifiedDateTime=" + lastModifiedDateTime +
                '}';
    }
}
