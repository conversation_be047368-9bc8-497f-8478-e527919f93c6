/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.yeepay.g3.facade.yop.perm.dto.BaseDTO;

import java.util.List;

/**
 * title:审核的各步骤所需人员 <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-18 13:20
 */
public class AclAuditStageVO extends BaseDTO {

    private static final long serialVersionUID = -1L;

    /**
     * 审核步骤
     */
    private Integer stage;

    /**
     * 审核办理角色
     */
    private List<String> roles;

    /**
     * 审核办理人员
     */
    private List<String> operators;

    public int getStage() {
        return stage;
    }

    public void setStage(int stage) {
        this.stage = stage;
    }

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public List<String> getOperators() {
        return operators;
    }

    public void setOperators(List<String> operators) {
        this.operators = operators;
    }
}
