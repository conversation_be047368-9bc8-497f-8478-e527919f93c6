/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.SpiSubscribeQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.SpiSubscribePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.SpiSubscribePageQueryParam;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/7 17:54
 */
@Component
public class SpiSubscribeQueryServiceImpl implements SpiSubscribeQueryService {
    @Resource(name = "spiSubscribeQueryService")
    private QueryService queryService;
    private PageItemConverter<SpiSubscribePageItem> pageItemConverter = new SpiSubscribeItemConverter();

    @Override
    public PageQueryResult<SpiSubscribePageItem> pageList(SpiSubscribePageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        QueryResult queryResult = queryService.query("pageList", queryParam);
        PageQueryResult<SpiSubscribePageItem> pageQueryResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        return pageQueryResult;
    }

    @Override
    public boolean exist(String appId, String spiName) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("appId", appId);
        params.put("spiName", spiName);
        return CollectionUtils.isNotEmpty(queryService.query("exist", params));
    }

    private Map<String, Object> getBizParams(SpiSubscribePageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("spiName", param.getSpiName());
        bizParams.put("customerNo", param.getCustomerNo());
        bizParams.put("appId", param.getAppId());
        return bizParams;
    }

    class SpiSubscribeItemConverter extends BasePageItemConverter<SpiSubscribePageItem> {

        @Override
        public SpiSubscribePageItem convert(Map<String, Object> params) {
            SpiSubscribePageItem item = new SpiSubscribePageItem();
            item.setId((Long) params.get("id"));
            item.setCustomerNo((String) params.get("customer_no"));
            item.setAppId((String) params.get("app_id"));
            item.setSpiName((String) params.get("spi_name"));
            item.setUrl((String) params.get("url"));
            return item;
        }
    }
}
