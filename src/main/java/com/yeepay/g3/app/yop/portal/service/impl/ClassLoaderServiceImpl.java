/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.dto.EndServiceDTO;
import com.yeepay.g3.app.yop.portal.exception.PortalExceptionEnum;
import com.yeepay.g3.app.yop.portal.exception.YopPortalException;
import com.yeepay.g3.app.yop.portal.service.ClassLoaderService;
import com.yeepay.g3.app.yop.portal.service.ModelQueryService;
import com.yeepay.g3.app.yop.portal.utils.YopPubKeyUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.ApiManageVO;
import com.yeepay.g3.app.yop.portal.vo.ServiceModelVO;
import com.yeepay.g3.facade.yop.sys.checker.ContentType;
import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;
import com.yeepay.g3.sdk.yop.client.YopClient;
import com.yeepay.g3.sdk.yop.client.YopConstants;
import com.yeepay.g3.sdk.yop.client.YopRequest;
import com.yeepay.g3.sdk.yop.client.YopResponse;
import com.yeepay.g3.sdk.yop.config.AppSdkConfig;
import com.yeepay.g3.sdk.yop.utils.JsonUtils;
import io.swagger.models.Model;
import io.swagger.models.RefModel;
import io.swagger.models.Swagger;
import io.swagger.models.parameters.BodyParameter;
import io.swagger.models.parameters.FormParameter;
import io.swagger.models.parameters.QueryParameter;
import io.swagger.models.properties.ArrayProperty;
import io.swagger.models.properties.Property;
import io.swagger.models.properties.RefProperty;
import io.swagger.parser.SwaggerParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/3/24 11:54 上午
 */
@Component
@Slf4j
public class ClassLoaderServiceImpl implements ClassLoaderService {
    private ModelQueryService modelQueryService;
    private static final String DEFAULT_DESCRIPTION = "请修改我";

    @Override
    public List<String> loadMethods(String className) {
        try {
            Map<String, String> config = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_SDK_CONFIG);
            YopRequest request = new YopRequest(config.get("appId"), config.get("secretKey"));
            AppSdkConfig appSdkConfig = request.getAppSdkConfig();
            appSdkConfig.setServerRoot(config.get("serverRoot"));
            appSdkConfig.setDefaultYopPublicKey(YopPubKeyUtils.getInstance());
            request.setSignAlg(YopConstants.ALG_SHA1);
            request.setRequestSource("yop-portal");
            request.addParam("className", className);
            YopResponse response = YopClient.get("/rest/v1.0/system/loader/methods", request);
            if (!response.isSuccess()) {
                throw new YopPortalException(response.getError().getSubMessage());
            }
            List<Map<String, String>> result = (List<Map<String, String>>) response.getResult();
            if (CollectionUtils.isNotEmpty(result)) {
                return result.stream()
                        .map(map -> StringUtils.substringAfter(map.get("longSignature"), " "))
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            throw new YopPortalException(e);
        }
        return Collections.emptyList();
    }

    @Override
    public EndServiceDTO loadEndService(String serviceSwaggerJson) {
        PortalExceptionEnum.SERVICE_NOT_FOUND.assertIsTrue(StringUtils.isNotEmpty(serviceSwaggerJson));
        Swagger swagger = new SwaggerParser().parse(serviceSwaggerJson);
        Map<String, EndServiceDTO.MethodModel> methodModelMap = new HashMap<>();
        EndServiceDTO endServiceDTO = new EndServiceDTO();
        if (!MapUtils.isEmpty(swagger.getPaths())) {
            swagger.getPaths().forEach((k, v) -> {
                EndServiceDTO.MethodModel methodModel = new EndServiceDTO.MethodModel();
                methodModel.setSimpleMethod(handleApiMethod(StringUtils.substringAfter(v.getPost().getDescription(), " ")));
                methodModel.setRequestModel(buildRequestModel(v, fixDefinitions(swagger.getDefinitions())));
                methodModel.setResponseModel(buildResponseModel(v, fixDefinitions(swagger.getDefinitions())));
                methodModelMap.put(methodModel.getSimpleMethod(), methodModel);
            });
            endServiceDTO.setMethods(methodModelMap);
        }
        return endServiceDTO;
    }

    private Map<String, Model> fixDefinitions(Map<String, Model> rawDefinitions) {
        Map<String, Model> result = new HashMap<>();
        if (MapUtils.isNotEmpty(rawDefinitions)) {
            rawDefinitions.forEach((k, v) -> {
                String name = k;
                if (StringUtils.contains(k, ".")) {
                    name = StringUtils.substringAfterLast(k, ".");
                }
                result.put(name, v);
            });
        }
        return result;
    }

    @Override
    public List<ModelDTO> buildModel(ApiManageVO apiManageVO) {
        // 创建json请求model
        ServiceModelVO requestModel = apiManageVO.getRequest().getRequestModel();
        String apiGroup = apiManageVO.getBasic().getApiGroup();
        List<ModelDTO> result = new ArrayList<>();
        List<String> existedModelNames = new ArrayList<>();
        if (!Objects.isNull(requestModel)) {
            try {
                List<ModelDTO> requestModels = createModel(requestModel, apiGroup);
                if (CollectionUtils.isNotEmpty(requestModels)) {
                    requestModels.forEach(modelDTO -> {
                        if (!existedModelNames.contains(modelDTO.getName())) {
                            result.add(modelDTO);
                            existedModelNames.add(modelDTO.getName());
                        }
                    });
                }
            } catch (Exception e) {
                log.error("", e);
                throw PortalExceptionEnum.UNKNOWN_ERROR.newException(e);
            }
        }

        // 创建响应结果model
        try {
            if (Objects.isNull(apiManageVO.getResponse().getContent())) {
                return result;
            }
            String responseSchema = apiManageVO.getResponse().getContent().getSchema();
            if (StringUtils.isEmpty(responseSchema)) {
                return result;
            }

            List<ModelDTO> responseModels = createModel(apiManageVO.getResponse().getResponseModel(), apiGroup);
            if (CollectionUtils.isNotEmpty(responseModels)) {
                responseModels.forEach(modelDTO -> {
                    if (!existedModelNames.contains(modelDTO.getName())) {
                        result.add(modelDTO);
                        existedModelNames.add(modelDTO.getName());
                    }
                });
            }
        } catch (Exception e) {
            log.error("", e);
            throw PortalExceptionEnum.UNKNOWN_ERROR.newException(e);
        }
        return result;
    }

    private List<ModelDTO> createModel(ServiceModelVO serviceModelVO, String apiGroup) {
        if (Objects.isNull(serviceModelVO) || StringUtils.isEmpty(serviceModelVO.getModelRef())) {
            return Collections.emptyList();
        }
        if (modelQueryService.exist(serviceModelVO.getModelRef(), apiGroup)) {
            return Collections.emptyList();
        }
        List<ModelDTO> result = new ArrayList<>();
        List<String> modelNames = new ArrayList<>();
        ModelDTO modelDTO = new ModelDTO();
        modelDTO.setApiGroup(apiGroup);
        modelDTO.setDescription(DEFAULT_DESCRIPTION);
        modelDTO.setName(serviceModelVO.getModelRef());
        modelDTO.setSchema(serviceModelVO.getSchema());
        modelNames.add(modelDTO.getName());
        result.add(modelDTO);
        if (CollectionUtils.isNotEmpty(serviceModelVO.getNestModel())) {
            serviceModelVO.getNestModel().forEach(model -> {
                if (!modelQueryService.exist(model.getModelRef(), apiGroup) && !modelNames.contains(model.getModelRef())) {
                    ModelDTO nestModel = new ModelDTO();
                    nestModel.setApiGroup(apiGroup);
                    nestModel.setDescription(DEFAULT_DESCRIPTION);
                    nestModel.setName(model.getModelRef());
                    nestModel.setSchema(model.getSchema());
                    result.add(nestModel);
                    modelNames.add(nestModel.getName());
                }
            });
        }
        return result;
    }

    private String handleApiMethod(String apiMethod) {
        String paramStr = StringUtils.substringBetween(apiMethod, "(", ")");
        if (StringUtils.isEmpty(paramStr)) {
            return apiMethod;
        }
        StringBuilder builder = new StringBuilder();
        builder.append(StringUtils.substringBefore(apiMethod, "("));
        String[] params = StringUtils.split(paramStr, ",");
        builder.append("(");
        for (int i = 0; i < params.length; i++) {
            builder.append(StringUtils.substringBefore(params[i], " "));
            if (i < params.length - 1) {
                builder.append(",");
            }
        }
        builder.append(")");
        return builder.toString();
    }

    private Map<String, ServiceModelVO> buildRequestModel(io.swagger.models.Path path,
                                                          Map<String, Model> modelMap) {
        Map<String, ServiceModelVO> result = new HashMap<>();
        ServiceModelVO formModelVO = buildFormRequestModel(path, modelMap);
        result.put(ContentType.APPLICATION_FORM_URLENCODED, formModelVO);
        result.put(ContentType.MULTIPART_FORM_DATA, formModelVO);
        List<io.swagger.models.parameters.Parameter> swaggerParameters = path.getPost().getParameters();
        if (CollectionUtils.isNotEmpty(swaggerParameters) &&
                (swaggerParameters.get(0) instanceof FormParameter || swaggerParameters.get(0) instanceof BodyParameter)) {
            result.put(ContentType.APPLICATION_JSON, buildJsonRequestModel(path, modelMap));
        }
        return result;
    }

    private ServiceModelVO buildJsonRequestModel(io.swagger.models.Path path,
                                                 Map<String, Model> modelMap) {
        ServiceModelVO result = new ServiceModelVO();
        String paramTypeList = StringUtils.substringBeforeLast(StringUtils.substringBetween(path.getPost().getDescription(), "(", ")"), " ");
        String[] paramTypes = StringUtils.split(paramTypeList, ",");
        String modelName = StringUtils.substringAfterLast(StringUtils.substringBeforeLast(paramTypes[0], " "), ".");
        result.setModelRef(modelName);
        Model model = modelMap.get(modelName);
        if (Objects.isNull(model)) {
            return result;
        }
        if (MapUtils.isEmpty(model.getProperties())) {
            return result;
        }
        Map<String, Object> schemaMap = new HashMap<>();
        Map<String, Object> properties = new HashMap<>();
        schemaMap.put("type", "object");
        schemaMap.put("properties", properties);
        model.getProperties().forEach((k, v) -> {
            Map<String, Object> schemaItem = new HashMap<>();
            if (v instanceof RefProperty) {
                String nestModelName = StringUtils.substringAfterLast(((RefProperty) v).getSimpleRef(), ".");
                findNestModel(result, modelMap, nestModelName);
                schemaItem.put("$ref", "#/components/schemas/" + nestModelName);
            } else {
                buildSchemaItem(v, schemaItem, result, modelMap);
            }
            properties.put(k, schemaItem);
        });
        result.setSchema(JsonUtils.toJsonString(schemaMap));
        return result;

    }

    private void findNestModel(ServiceModelVO serviceModelVO, Map<String, Model> modelMap, String targetModelName) {
        if (serviceModelVO.getModelRef().equals(targetModelName)) {
            return;
        }
        boolean existed = false;
        for (int i = 0; i < serviceModelVO.getNestModel().size(); i++) {
            ServiceModelVO nestModelVO = serviceModelVO.getNestModel().get(i);
            if (nestModelVO.getModelRef().equals(targetModelName)) {
                existed = true;
                break;
            }
        }
        if (existed) {
            return;
        }

        Model model = modelMap.get(targetModelName);
        if (Objects.isNull(model)) {
            return;
        }
        ServiceModelVO nestServiceModelVO = new ServiceModelVO();
        nestServiceModelVO.setModelRef(targetModelName);
        serviceModelVO.getNestModel().add(nestServiceModelVO);
        Map<String, Object> schemaMap = new HashMap<>();
        Map<String, Object> properties = new HashMap<>();
        schemaMap.put("type", "object");
        schemaMap.put("properties", properties);

        if (MapUtils.isNotEmpty(model.getProperties())) {
            model.getProperties().forEach((k, v) -> {
                Map<String, Object> schemaItem = new HashMap<>();
                if (v instanceof RefProperty) {
                    String nestModelName = StringUtils.substringAfterLast(((RefProperty) v).getSimpleRef(), ".");
                    findNestModel(serviceModelVO, modelMap, nestModelName);
                    schemaItem.put("$ref", "#/components/schemas/" + nestModelName);
                } else {
                    buildSchemaItem(v, schemaItem, serviceModelVO, modelMap);
                }
                properties.put(k, schemaItem);
            });
        }

        nestServiceModelVO.setSchema(JsonUtils.toJsonString(schemaMap));
        if (MapUtils.isNotEmpty(model.getProperties())) {
            model.getProperties().forEach((k, v) -> {
                if (v instanceof RefProperty) {
                    String nestModelName = StringUtils.substringAfterLast(v.getDescription(), ".");
                    findNestModel(serviceModelVO, modelMap, nestModelName);
                }
            });
        }

    }

    private ServiceModelVO buildFormRequestModel(io.swagger.models.Path path,
                                                 Map<String, Model> modelMap) {
        ServiceModelVO result = new ServiceModelVO();
        Map<String, Object> schemaMap = new HashMap<>();
        Map<String, Object> properties = new HashMap<>();
        schemaMap.put("type", "object");
        schemaMap.put("properties", properties);
        String paramTypeList = StringUtils.substringBeforeLast(StringUtils.substringBetween(path.getPost().getDescription(), "(", ")"), " ");
        String[] paramTypes = StringUtils.split(paramTypeList, ",");
        List<io.swagger.models.parameters.Parameter> swaggerParameters = path.getPost().getParameters();
        if (CollectionUtils.isNotEmpty(swaggerParameters)) {
            for (int i = 0; i < swaggerParameters.size(); i++) {
                io.swagger.models.parameters.Parameter swaggerParameter = swaggerParameters.get(i);
                // 第一个参数为对象，则将第一个参数展开，其余参数不自动注册
                if (0 == i && (swaggerParameter instanceof FormParameter || swaggerParameter instanceof BodyParameter)) {
                    String modelName = StringUtils.substringAfterLast(StringUtils.substringBeforeLast(paramTypes[0], " "), ".");
                    Model model = modelMap.get(modelName);
                    if (Objects.isNull(model)) {
                        return result;
                    }
                    if (MapUtils.isEmpty(model.getProperties())) {
                        return result;
                    }
                    for (Map.Entry<String, Property> entry : model.getProperties().entrySet()) {
                        if (entry.getValue() instanceof RefProperty || entry.getValue() instanceof ArrayProperty) {
                            continue;
                        }
                        Map<String, Object> schemaItem = new HashMap<>();
                        if ("object".equals(entry.getValue().getType())) {
                            continue;
                        }
                        schemaItem.put("type", entry.getValue().getType());
                        if (StringUtils.isNotEmpty(entry.getValue().getFormat())) {
                            schemaItem.put("format", entry.getValue().getFormat());
                        }
                        schemaItem.put("exclusiveMaximum", true);
                        schemaItem.put("exclusiveMinimum", true);
                        properties.put(entry.getKey(), schemaItem);
                    }
                    break;
                }

                // form请求参数忽略复杂类型参数
                if (swaggerParameter instanceof FormParameter) {
                    continue;
                }
                Map<String, Object> schemaItem = new HashMap<>();
                String type = ((QueryParameter) swaggerParameter).getType();
                if ("object".equals(type)) {
                    continue;
                }
                schemaItem.put("type", type);
                if (StringUtils.isNotEmpty(((QueryParameter) swaggerParameter).getFormat())) {
                    schemaItem.put("format", ((QueryParameter) swaggerParameter).getFormat());
                }
                schemaItem.put("exclusiveMaximum", true);
                schemaItem.put("exclusiveMinimum", true);
                properties.put(swaggerParameter.getName(), schemaItem);
            }
        }
        String formSchema = JsonUtils.toJsonString(schemaMap);
        result.setSchema(formSchema);
        return result;
    }

    private ServiceModelVO buildResponseModel(io.swagger.models.Path path,
                                              Map<String, Model> modelMap) {
        ServiceModelVO result = new ServiceModelVO();
        String responseType = StringUtils.substringAfterLast(StringUtils.substringBefore(path.getPost().getDescription(), " "), ".");
        result.setModelRef(responseType);
        if (Objects.isNull(path.getPost().getResponses().get("200"))) {
            return result;
        }
        if (path.getPost().getResponses().get("200").getResponseSchema() instanceof RefModel) {
            Model responseModel = modelMap.get(responseType);
            Map<String, Object> schemaMap = new HashMap<>();
            Map<String, Object> properties = new HashMap<>();
            schemaMap.put("type", "object");
            schemaMap.put("properties", properties);
            if (MapUtils.isNotEmpty(responseModel.getProperties())) {
                responseModel.getProperties().forEach((k, v) -> {
                    Map<String, Object> schemaItem = new HashMap<>();
                    if (v instanceof RefProperty) {
                        String nestModelName = StringUtils.substringAfterLast(((RefProperty) v).getSimpleRef(), ".");
                        findNestModel(result, modelMap, nestModelName);
                        schemaItem.put("$ref", "#/components/schemas/" + nestModelName);
                    } else {
                        buildSchemaItem(v, schemaItem, result, modelMap);
                    }

                    properties.put(k, schemaItem);
                });
            }
            result.setSchema(JsonUtils.toJsonString(schemaMap));
        }
        return result;
    }

    private void buildSchemaItem(Property property, Map<String, Object> schemaItem, ServiceModelVO serviceModelVO, Map<String, Model> modelMap) {
        schemaItem.put("type", property.getType());
        if (property instanceof ArrayProperty) {
            ArrayProperty arrayProperty = (ArrayProperty) property;
            Map<String, Object> arrayItems = new HashMap<>();
            Property arrayItemProp = arrayProperty.getItems();
            if (arrayItemProp instanceof RefProperty) {
                String refName = StringUtils.substringAfterLast(((RefProperty) arrayItemProp).getSimpleRef(), ".");
                findNestModel(serviceModelVO, modelMap, refName);
                arrayItems.put("$ref", "#/components/schemas/" + refName);
            } else {
                arrayItems.put("type", arrayItemProp.getType());
                if (StringUtils.isNotEmpty(arrayItemProp.getFormat())) {
                    arrayItems.put("format", arrayItemProp.getFormat());
                }
                arrayItems.put("exclusiveMaximum", true);
                arrayItems.put("exclusiveMinimum", true);
            }
            schemaItem.put("items", arrayItems);
        } else {
            if (StringUtils.isNotEmpty(property.getFormat())) {
                schemaItem.put("format", property.getFormat());
            }
            schemaItem.put("exclusiveMaximum", true);
            schemaItem.put("exclusiveMinimum", true);
        }
    }

    @Autowired
    public void setModelQueryService(ModelQueryService modelQueryService) {
        this.modelQueryService = modelQueryService;
    }
}
