package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.enums.ApiVersionEnum;
import com.yeepay.g3.app.yop.portal.service.ApiRequestService;
import com.yeepay.g3.app.yop.portal.service.ErrorCodeQueryMgrService;
import com.yeepay.g3.app.yop.portal.service.ProductApiQueryService;
import com.yeepay.g3.app.yop.portal.service.UnifyApiService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.ApiListItemVO;
import com.yeepay.g3.app.yop.portal.vo.ApiQueryParamVO;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.UnifyApiPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.UnifyApiPageQueryParam;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 15:09
 */
@Component
public class UnifyApiServiceImpl implements UnifyApiService {

    @Resource(name = "v1ApiQueryService")
    private QueryService v1QueryService;

    @Resource(name = "v2ApiQueryService")
    private QueryService v2QueryService;

    @Resource(name = "apiQueryService")
    private QueryService apiQueryService;

    @Autowired
    private ApiRequestService apiRequestService;

    @Autowired
    private ProductApiQueryService productApiQueryService;

    @Autowired
    private ErrorCodeQueryMgrService errorCodeQueryMgrService;

    @Resource(name = "spiQueryService")
    private QueryService spiQueryService;

    private final PageItemConverter<UnifyApiPageItem> unifyApiPageItemConverter = new UnifyApiPageItemConverter();

    @Override
    public PageQueryResult<UnifyApiPageItem> findApisPage(UnifyApiPageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        QueryResult queryResult;
        if (param.getApiVersion() == ApiVersionEnum.V1) {
            queryResult = v1QueryService.query("findApisPage", queryParam);
        } else {
            queryResult = v2QueryService.query("findApisPage", queryParam);
        }
        final PageQueryResult<UnifyApiPageItem> result = PageQueryUtils.convertResult(queryResult, param.getPageNo(), unifyApiPageItemConverter);
        return result;
    }

    @Override
    public List<ApiListItemVO> findApis(ApiQueryParamVO param) {
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("keyword", param.getKeyword());
        List<Map<String, String>> queryResult  = apiQueryService.query("listApis", paramMap);
        if (CollectionUtils.isNotEmpty(queryResult)) {
            List<ApiListItemVO> result = new ArrayList<>(queryResult.size());
            for (Map<String, String> item : queryResult) {
                ApiListItemVO apiListItemVO = new ApiListItemVO();
                apiListItemVO.setApiTitle(item.get("api_title"));
                apiListItemVO.setApiUri(item.get("api_uri"));
                result.add(apiListItemVO);
            }
            return result;
        }

        return Collections.emptyList();
    }

    private Map<String, Object> getBizParams(UnifyApiPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiUri", param.getApiUri());
        bizParams.put("apiType", param.getApiType());
        bizParams.put("apiGroupCode", param.getApiGroupCode());
        bizParams.put("apiTitle", param.getApiTitle());
        bizParams.put("apiStatus", param.getApiStatus());
        switch (param.getJoinCode()) {
            case PRODUCT:
                final String productCode = (String) param.getJoinValue();
                bizParams.put("joinProduct", "1");
                bizParams.put("productCode", productCode);
                break;
            case ERRCODE:
                final Long errcodeId = Long.valueOf((String) param.getJoinValue());
                bizParams.put("joinErrcode", "1");
                bizParams.put("errcodeId", errcodeId);
                break;
            case SPINAME:
                final String spiName = (String) param.getJoinValue();
                bizParams.put("joinSpiName", "1");
                bizParams.put("spiName", spiName);
                break;
            default:
                ;
        }


        return bizParams;
    }

    class UnifyApiPageItemConverter extends BasePageItemConverter<UnifyApiPageItem> {

        @Override
        public UnifyApiPageItem convert(Map<String, Object> params) {
            UnifyApiPageItem item = new UnifyApiPageItem();
            item.setApiVersion((String) params.get("api_version"));
            item.setApiId((String) params.get("api_id"));
            item.setApiTitle((String) params.get("api_title"));
            item.setApiGroupCode((String) params.get("api_group_code"));
            item.setApiGroupName((String) params.get("api_group_name"));
            item.setApiUri((String) params.get("api_uri"));
            item.setApiType((String) params.get("api_type"));
            String status = (String) params.get("api_status");
            status = StringUtils.replace(status, "DOC_", "");
            item.setApiStatus(status);
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }
}
