package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;

import java.io.Serializable;

/**
 * title: Model覆盖检查项<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 16:59
 */
public class ModelOverrideCheckItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private ModelDTO model;

    private ModelDTO srcModel;

    public ModelDTO getModel() {
        return model;
    }

    public void setModel(ModelDTO model) {
        this.model = model;
    }

    public ModelOverrideCheckItem withModel(ModelDTO model) {
        this.model = model;
        return this;
    }

    public ModelDTO getSrcModel() {
        return srcModel;
    }

    public void setSrcModel(ModelDTO srcModel) {
        this.srcModel = srcModel;
    }

    public ModelOverrideCheckItem withSrcModel(ModelDTO srcModel) {
        this.srcModel = srcModel;
        return this;
    }
}
