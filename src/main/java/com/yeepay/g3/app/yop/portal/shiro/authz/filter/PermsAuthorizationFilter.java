package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.app.yop.portal.shiro.utils.Constants;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.StringUtils;
import org.apache.shiro.web.filter.authz.PermissionsAuthorizationFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/6/12 下午2:43
 */
@Component
public class PermsAuthorizationFilter extends PermissionsAuthorizationFilter implements CustomShiroFilter {

    @Override
    protected boolean isEnabled(ServletRequest request, ServletResponse response) throws ServletException, IOException {
        Object skip = request.getAttribute(Constants._PORTAL_SKIP_FILTER);
        if (skip != null && (Boolean) skip == true) {
            return false;
        }
        return ShiroUtils.isPlatformOperator();
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {
        Subject subject = this.getSubject(request, response);
        if (subject.getPrincipal() == null) {
            this.saveRequestAndRedirectToLogin(request, response);
        } else {
            String unauthorizedUrl = this.getUnauthorizedUrl();
            if (StringUtils.hasText(unauthorizedUrl)) {
                WebUtils.issueRedirect(request, response, unauthorizedUrl);
            } else {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                PrintWriter out = response.getWriter();
                out.println("{\"status\":\"error\",\"message\":\"您没有此操作的权限\"}");
                out.flush();
                out.close();
            }
        }
        return false;
    }

    @Override
    public String shiroName() {
        return "perms";
    }
}
