/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import com.yeepay.g3.app.yop.portal.vo.NotifyOrderQueryParam;
import com.yeepay.g3.app.yop.portal.vo.NotifyRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.NotifySendRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.PageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.notify.NotifyOrderVO;
import com.yeepay.g3.app.yop.portal.vo.notify.NotifyRecordVO;
import com.yeepay.g3.app.yop.portal.vo.notify.NotifySendRecordVO;
import com.yeepay.g3.facade.yop.monitor.enums.OrderStatusEnum;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/14 5:22 下午
 */
public class NotifyOrderQueryServiceTest extends SpringBootTests {
    private static Date defaultNotifyEndDate;
    private static Date defaultNotifyStartDate;
    private static String costumerNo = "1595815987915711";

    static {
        try {
            defaultNotifyEndDate = DateUtils.parseDate("2022-01-14 19:32:00", "yyyy-MM-dd HH:mm:ss");
            defaultNotifyStartDate = DateUtils.addDay(defaultNotifyEndDate, -7);
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    @Autowired
    private NotifyOrderQueryService notifyOrderQueryService;

    /**
     * 根据时间段查询
     */
    @Test
    public void testListNotifyOrder() {
        // 不设置任何查询条件 时间段查询
        List<NotifyOrderVO> notifyOrderVOS = notifyOrderQueryService.listNotifyOrder(NotifyOrderQueryParam.builder().notifyStartDate(defaultNotifyStartDate).notifyEndDate(defaultNotifyEndDate).build(), PageQueryParam.builder().build()).getItems();
        Assert.assertTrue(CollectionUtils.isNotEmpty(notifyOrderVOS));
        Assert.assertEquals(2, notifyOrderVOS.size());
    }

    /**
     * 根据商编进行查询
     */
    @Test
    public void testListByCostumerNo() {
        //有查询结果
        List<NotifyOrderVO> listByCostumerNo = notifyOrderQueryService.listNotifyOrder(NotifyOrderQueryParam.builder().customerNo(costumerNo).notifyStartDate(defaultNotifyStartDate).notifyEndDate(defaultNotifyEndDate).build(), PageQueryParam.builder().build()).getItems();
        Assert.assertTrue(CollectionUtils.isNotEmpty(listByCostumerNo));
        Assert.assertEquals(1, listByCostumerNo.size());
        Assert.assertEquals(costumerNo, listByCostumerNo.get(0).getCustomerNo());

        //无查询结果
        String errorCostumerNo = "error_cn";
        List<NotifyOrderVO> listByErrorCostumerNo = notifyOrderQueryService.listNotifyOrder(NotifyOrderQueryParam.builder().customerNo(errorCostumerNo).notifyStartDate(defaultNotifyStartDate).notifyEndDate(defaultNotifyEndDate).build(), PageQueryParam.builder().build()).getItems();
        Assert.assertTrue(CollectionUtils.isEmpty(listByErrorCostumerNo));
    }

    /**
     * 根据appId进行查询
     */
    @Test
    public void testListByAppId() {
        // 根据应用标识查询-有结果
        String appId = "app_15958159879157110002";
        List<NotifyOrderVO> listByAppId = notifyOrderQueryService.listNotifyOrder(NotifyOrderQueryParam.builder().appId(appId).notifyStartDate(defaultNotifyStartDate).notifyEndDate(defaultNotifyEndDate).build(), PageQueryParam.builder().build()).getItems();
        Assert.assertTrue(CollectionUtils.isNotEmpty(listByAppId));
        Assert.assertEquals(appId, listByAppId.get(0).getAppId());

        //无结果
        String errorAppId = "app_1595815987915711000";
        List<NotifyOrderVO> listByErrorAppId = notifyOrderQueryService.listNotifyOrder(NotifyOrderQueryParam.builder().appId(errorAppId).notifyStartDate(defaultNotifyStartDate).notifyEndDate(defaultNotifyEndDate).build(), PageQueryParam.builder().build()).getItems();
        Assert.assertTrue(CollectionUtils.isEmpty(listByErrorAppId));
    }

    /**
     * 根据通知订单查询
     */
    @Test
    public void testListByNotificationId() {
        // 有查询结果
        String notificationId = "wym_test_success1642156286443";
        List<NotifyOrderVO> listByNotificationId = notifyOrderQueryService.listNotifyOrder(NotifyOrderQueryParam.builder().notificationId(notificationId).notifyStartDate(defaultNotifyStartDate).notifyEndDate(defaultNotifyEndDate).build(), PageQueryParam.builder().build()).getItems();
        Assert.assertTrue(CollectionUtils.isNotEmpty(listByNotificationId));
        Assert.assertEquals(1, listByNotificationId.size());
        Assert.assertEquals(notificationId, listByNotificationId.get(0).getNotificationId());

        //无查询结果
        String errorNotificationId = "errorNotificationId";
        List<NotifyOrderVO> listByErrorNotificationId = notifyOrderQueryService.listNotifyOrder(NotifyOrderQueryParam.builder().notificationId(errorNotificationId).notifyStartDate(defaultNotifyStartDate).notifyEndDate(defaultNotifyEndDate).build(), PageQueryParam.builder().build()).getItems();
        Assert.assertTrue(CollectionUtils.isEmpty(listByErrorNotificationId));
    }

    /**
     * 根据通知状态查询
     */
    @Test
    public void testListByStatus() {
        OrderStatusEnum statusEnum = OrderStatusEnum.SUCCESS;
        List<NotifyOrderVO> listByNotificationId = notifyOrderQueryService.listNotifyOrder(NotifyOrderQueryParam.builder().status(statusEnum).notifyStartDate(defaultNotifyStartDate).notifyEndDate(defaultNotifyEndDate).build(), PageQueryParam.builder().build()).getItems();
        Assert.assertTrue(CollectionUtils.isNotEmpty(listByNotificationId));
        Assert.assertEquals(1, listByNotificationId.size());
        Assert.assertEquals(statusEnum, listByNotificationId.get(0).getStatus());
    }

    /**
     * 通过最近的处理记录所调用的后端系统进行查询
     */
    @Test
    public void testListByLatestRecordBackend() throws ParseException {
        List<NotifyOrderVO> listByBackend = notifyOrderQueryService.listNotifyOrder(
                NotifyOrderQueryParam.builder()
                        .notifyStartDate(DateUtils.parseDate("2022-11-10 00:00:00", "yyyy-MM-dd HH:mm:ss"))
                        .notifyEndDate(DateUtils.parseDate("2022-11-10 23:00:00", "yyyy-MM-dd HH:mm:ss"))
                        .backend("STNO")
                        .build(),
                PageQueryParam.builder().build()).getItems();
        Assert.assertTrue(CollectionUtils.isNotEmpty(listByBackend));
    }

    /**
     * 查询通知记录测试
     */
    @Test
    public void testListNotifyRecord() throws ParseException {
        String orderId = "1fbbe515c917418684638e717ce2c330";
        Date orderDate = DateUtils.parseDate("2022-11-10 16:56:49", "yyyy-MM-dd HH:mm:ss");
        List<NotifyRecordVO> recordVOS = notifyOrderQueryService.listNotifyRecord(NotifyRecordQueryParam.builder().orderId(orderId).orderDate(orderDate).build(), PageQueryParam.builder().build());
        Assert.assertTrue(CollectionUtils.isNotEmpty(recordVOS));
    }

    /**
     * 查询处理的发送记录
     * @throws ParseException
     */
    @Test
    public void testListNotifySendRecord() throws ParseException {
        String notifyRecordId = "dd3af5a80fe9447fa8af8d4668efe037";
        Date orderDate = DateUtils.parseDate("2023-06-01 20:05:00", "yyyy-MM-dd HH:mm:ss");
        List<NotifySendRecordVO> sendRecordVOList = notifyOrderQueryService.listNotifySendRecord(NotifySendRecordQueryParam.builder().notifyRecordId(notifyRecordId).orderDate(orderDate).build(), PageQueryParam.builder().build());
        Assert.assertTrue(CollectionUtils.isNotEmpty(sendRecordVOList));
        Assert.assertTrue(OrderStatusEnum.SUCCESS.getValue().equals(sendRecordVOList.get(0).getStatus().getValue()));
    }
}
