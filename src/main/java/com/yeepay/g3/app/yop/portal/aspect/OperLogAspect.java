/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.app.yop.portal.aspect;

import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/8/14 下午2:35
 */
@Aspect
@Component
public class OperLogAspect {

    private static final Logger LOGGER = LoggerFactory.getLogger(OperLogAspect.class);

    private static final String REQUEST_CONTENT = "requestContent";

    private static final int CONTENT_MAX_LENGTH = 65535;

    protected JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();

    @Pointcut("execution(* com.yeepay.g3.app.yop.portal.controller..*.*(..))")
    private void aspect() {
        // 定义一个切入点
    }

    @Around("aspect()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        Signature signature = pjp.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        String[] parameterNames = methodSignature.getParameterNames();
        Class[] parameterTypes = methodSignature.getParameterTypes();
        Object[] parameterContent = pjp.getArgs();
        Map<String, Object> parameters = new HashMap<>();
        List<String> ignoreParamLog = (ArrayList<String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_IGNORE_PARAM_LOG);
        if (parameterNames.length > 0) {
            int maxLength = CONTENT_MAX_LENGTH / parameterNames.length;
            for (int i = 0; i < parameterNames.length; i++) {
                if (parameterContent[i] == null || ignoreParamLog.contains(parameterTypes[i].getSimpleName())) {
                    continue;
                }
                Object content = parameterContent[i];
                if (StringUtils.length(content.toString()) > maxLength) {
                    content = StringUtils.substring(content.toString(), 0, maxLength);
                }
                parameters.put(parameterNames[i], content);
            }
        }
        try {
            String requestContent = JSON_MAPPER.toJson(parameters);
            if (StringUtils.length(requestContent) > CONTENT_MAX_LENGTH) {
                StringUtils.substring(requestContent, 0, CONTENT_MAX_LENGTH);
            }
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            request.setAttribute(REQUEST_CONTENT, requestContent);
        } catch (Exception e) {
            LOGGER.warn("error when aspect param:{}", parameterNames);
        }
        return pjp.proceed();
    }
}
