package com.yeepay.g3.app.yop.portal.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 11:20
 */
public class ApiAutoGenerateResultVO implements Serializable {

    private static final long serialVersionUID = 8096304284849796775L;

    private String apiUri;

    private ApiRequestParamsVO requestParams;

    private List<ApiResponseParamVO> responseParam;

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public ApiRequestParamsVO getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(ApiRequestParamsVO requestParams) {
        this.requestParams = requestParams;
    }

    public List<ApiResponseParamVO> getResponseParam() {
        return responseParam;
    }

    public void setResponseParam(List<ApiResponseParamVO> responseParam) {
        this.responseParam = responseParam;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
