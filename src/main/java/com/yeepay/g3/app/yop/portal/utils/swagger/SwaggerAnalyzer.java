package com.yeepay.g3.app.yop.portal.utils.swagger;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yeepay.g3.app.yop.portal.utils.swagger.deserialize.CustomDeserializationModule;
import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;
import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;
import com.yeepay.g3.facade.yop.sys.dto.SpiDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.*;
import com.yeepay.g3.facade.yop.sys.dto.models.*;
import com.yeepay.g3.facade.yop.sys.dto.models.utils.SwaggerExtensions;
import com.yeepay.g3.facade.yop.sys.dto.spi.SpiBasicDTO;
import com.yeepay.g3.facade.yop.sys.dto.spi.SpiRequestDTO;
import com.yeepay.g3.facade.yop.sys.enums.*;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import io.swagger.v3.core.util.Json;
import io.swagger.v3.core.util.Yaml;
import io.swagger.v3.oas.models.*;
import io.swagger.v3.oas.models.callbacks.Callback;
import io.swagger.v3.oas.models.media.ArraySchema;
import io.swagger.v3.oas.models.media.MediaType;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.responses.ApiResponse;
import io.swagger.v3.oas.models.responses.ApiResponses;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * title: Swagger解析器<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-06-18 16:49
 */
public class SwaggerAnalyzer {

    private static final Logger LOGGER = LoggerFactory.getLogger(SwaggerAnalyzer.class);

    public static final ObjectMapper JSON_MAPPER;

    public static final ObjectMapper YAML_MAPPER;

    private static final Set<String> AUTH_TYPES;

    static {
        JSON_MAPPER = Json.mapper();
        YAML_MAPPER = Yaml.mapper();

        JSON_MAPPER.registerModule(new CustomDeserializationModule());
        YAML_MAPPER.registerModule(new CustomDeserializationModule());
        AUTH_TYPES = Sets.newHashSetWithExpectedSize(AuthTypeEnum.values().length);
        for (AuthTypeEnum item : AuthTypeEnum.values()) {
            AUTH_TYPES.add(item.getValue());
        }
    }


    public static SwaggerAnalysisResult analysis(String data, SwaggerDataFormatEnum dataFormat) {
        return analysis(new ByteArrayInputStream(data.getBytes(Charset.forName("utf-8"))), dataFormat);
    }

    public static SwaggerAnalysisResult analysis(InputStream data, SwaggerDataFormatEnum dataFormat) {
        OpenAPI openAPI;
        try {
            if (dataFormat == SwaggerDataFormatEnum.JSON) {
                openAPI = JSON_MAPPER.readValue(data, OpenAPI.class);
            } else {
                openAPI = YAML_MAPPER.readValue(data, OpenAPI.class);
            }
        } catch (IOException ex) {
            throw new YeepayRuntimeException("parse swagger failed.", ex);
        } finally {
            IOUtils.closeQuietly(data);
        }
        SwaggerAnalysisResult result = new SwaggerAnalysisResult();
        analysisExtensions(openAPI.getExtensions(), result);

        Components components = openAPI.getComponents();
        analysisApis(openAPI.getPaths(), result);
        analysisModels(components == null ? Collections.emptyMap() : components.getSchemas(), result);
        analysisCallBacks(components == null ? Collections.emptyMap() : components.getCallbacks(), result);
        analysisCallbackApiRelations(components == null ? Collections.emptyMap() : components.getExtensions(), result);
        return result;
    }

    private static void analysisCallbackApiRelations(Map<String, Object> extensions, SwaggerAnalysisResult result) {
        if (MapUtils.isEmpty(extensions)) {
            return;
        }
        Map<String, List<ApiRequestKey>> callbackApiRelations = Collections.emptyMap();
        // 从扩展信息里取出来spi和api的关联关系
        if (MapUtils.isNotEmpty(extensions)) {
            callbackApiRelations = Optional
                    .ofNullable((Map<String, List<Map<String, String>>>) extensions.get(SwaggerExtensions.CALLBACK_API_RELATIONS))
                    .orElse(Collections.emptyMap())
                    .entrySet()
                    .stream()
                    .collect(Collectors.toMap(entry -> entry.getKey(), entry -> {
                        String spiName = entry.getKey();
                        List<Map<String, String>> apis = entry.getValue();
                        return apis.stream().map(map -> {
                            String apiPath = map.get("path");
                            if (StringUtils.isEmpty(apiPath)) {
                                throw new YeepayRuntimeException("relate api path is empty, callback[{0}]", spiName);
                            }
                            String httpMethod = map.get("httpMethod");
                            if (StringUtils.isEmpty(httpMethod)) {
                                throw new YeepayRuntimeException("relate api http method is empty, callback[{0}]", spiName);
                            }
                            return new ApiRequestKey().withPath(apiPath).withHttpMethod(httpMethod);
                        }).collect(Collectors.toList());
                    }));
        }
        result.setCallbackApiRelations(callbackApiRelations);
    }

    /**
     * 解析openAPI级别的扩展信息，如果没有有效扩展，则认为是原生Swagger导入
     *
     * @param extensions 扩展信息
     * @param result     返回结果
     */
    private static void analysisExtensions(Map<String, Object> extensions, SwaggerAnalysisResult result) {
        if (MapUtils.isEmpty(extensions)) {
            result.setNativeSwagger(true);
            return;
        }
        Object apiGroupValue = extensions.get(SwaggerExtensions.API_GROUP);
        if (apiGroupValue == null) {
            result.setNativeSwagger(true);
            return;
        }
        result.setNativeSwagger(false);
        if (apiGroupValue instanceof String) {
            String apiGroup = (String) apiGroupValue;
            if (StringUtils.isEmpty(apiGroup)) {
                throw new YeepayRuntimeException("api group must be not empty");
            }
            result.setApiGroup(apiGroup);
        } else {
            throw new YeepayRuntimeException("api group must be string.");
        }
    }

    private static void analysisModels(Map<String, Schema> schemas, SwaggerAnalysisResult result) {
        if (MapUtils.isEmpty(schemas)) {
            return;
        }
        Map<String, ModelDTO> models = Maps.newLinkedHashMap();
        List<ModelAnalysisFailedInfo> failedModels = new ArrayList<>();
        schemas.forEach((name, schema) -> {
            try {
                models.put(name, parseModel(name, schema, result.isNativeSwagger()));
            } catch (Exception ex) {
                LOGGER.warn("analysis model failure,name:" + name, ex);
                failedModels.add(new ModelAnalysisFailedInfo().withModelName(name).withErrorMsg(ex.getMessage()));
            }
        });
        result.withModels(models).withFailedModelInfo(failedModels);
    }

    private static ModelDTO parseModel(String modelName, Schema schema, Boolean nativeSwagger) throws IOException {
        schemaSubtractApiGroup(schema);
        ModelDTO model = new ModelDTO()
                .withName(StringUtils.substringAfter(modelName, "."))
                .withApiGroup(StringUtils.substringBefore(modelName, "."))
                .withDescription(schema.getDescription());
        schema.setDescription(null);
        if (BooleanUtils.isFalse(nativeSwagger) && schema.getExtensions() != null) {
            Object sensitiveVariables = schema.getExtensions().get(SwaggerExtensions.SENSITIVE_VARIABLE);
            if (sensitiveVariables instanceof List) {
                List<String> variables = new ArrayList<>(((List) sensitiveVariables).size());
                for (Object sensitiveVariable : (List) sensitiveVariables) {
                    if (sensitiveVariable instanceof String) {
                        variables.add((String) sensitiveVariable);
                    } else {
                        throw new YeepayRuntimeException("sensitive variable must be list of string.");
                    }
                }
                model.withSensitiveVariables(variables);
            }
            schema.setExtensions(null);
        }
        model.withSchema(JSON_MAPPER.writeValueAsString(schema));
        return model;
    }

    private static void analysisApis(Paths paths, SwaggerAnalysisResult result) {
        if (MapUtils.isEmpty(paths)) {
            return;
        }
        Map<ApiRequestKey, ApiDTO> apis = Maps.newLinkedHashMap();
        List<ApiAnalysisFailedInfo> failedApiInfo = new ArrayList<>();
        Set<String> operationIds = Sets.newHashSetWithExpectedSize(paths.size());
        paths.forEach((path, pathItem) -> {
            if (pathItem.getGet() != null) {
                analysisApi(path, pathItem.getGet(), "GET", operationIds, apis, failedApiInfo, result.isNativeSwagger());
            }
            if (pathItem.getPost() != null) {
                analysisApi(path, pathItem.getPost(), "POST", operationIds, apis, failedApiInfo, result.isNativeSwagger());
            }
            if (pathItem.getPut() != null) {
                analysisApi(path, pathItem.getPut(), "PUT", operationIds, apis, failedApiInfo, result.isNativeSwagger());
            }
            if (pathItem.getDelete() != null) {
                analysisApi(path, pathItem.getDelete(), "DELETE", operationIds, apis, failedApiInfo, result.isNativeSwagger());
            }
        });
        result.withApis(apis).withFailedApiInfo(failedApiInfo);
    }

    private static void analysisApi(String path, Operation operation, String httpMethod,
                                    Set<String> operationIds,
                                    Map<ApiRequestKey, ApiDTO> apis, List<ApiAnalysisFailedInfo> failedApiInfo, Boolean nativeSwagger) {
        try {
            ApiDTO api = parseApi(path, httpMethod, operation, nativeSwagger);
            //operationId唯一性检测
            if (StringUtils.isEmpty(api.getBasic().getName())) {
                api.getBasic().setName(null);
            } else {
                if (operationIds.contains(api.getBasic().getName())) {
                    throw new YeepayRuntimeException("operationId[{0}] duplicated", api.getBasic().getName());
                }
                operationIds.add(api.getBasic().getName());
            }
            apis.put(new ApiRequestKey()
                    .withPath(api.getRequest().getPath())
                    .withHttpMethod(api.getRequest().getHttpMethod()), api);
        } catch (Exception ex) {
            LOGGER.warn("api analysis failure", ex);
            failedApiInfo.add(new ApiAnalysisFailedInfo().withApiUri(path).withErrorMsg(ex.getMessage()));
        }
    }

    private static ApiDTO parseApi(String path, String httpMethod, Operation operation, Boolean nativeSwagger) throws IOException {
        //非原生Swagger导入的情况下，operation的扩展信息必然不为空
        if (BooleanUtils.isFalse(nativeSwagger) && MapUtils.isEmpty(operation.getExtensions())) {
            throw new YeepayRuntimeException("extensions for Path[{0}] Operation[{1}] is empty.", path, httpMethod);
        }
        return new ApiDTO()
                .withBasic(
                        new ApiBasicDTO()
                                .withName((String) operation.getExtensions().get(SwaggerExtensions.API_NAME))
                                .withTitle(operation.getSummary())
                                .withApiType(BooleanUtils.isFalse(nativeSwagger) ? parseApiType(operation.getExtensions()) : ApiTypeEnum.COMMON)
                                .withDescription(operation.getDescription())
                                .withOptionsRule(BooleanUtils.isFalse(nativeSwagger) ? parseApiOptions(operation.getExtensions()) : null))
                .withExampleScenes(BooleanUtils.isFalse(nativeSwagger) ? parseExampleScenes(operation.getExtensions()) : null)
                .withRequest(new ApiRequestDTO()
                        .withParameterHandlingType(BooleanUtils.isFalse(nativeSwagger) ? parseParameterHandlingType(operation.getExtensions()) : ApiParameterHandlingTypeEnum.PASSTHROUGH)
                        .withPath(path)
                        .withHttpMethod(httpMethod.toUpperCase())
                        .withParameters(parseParameter(operation.getParameters(), nativeSwagger))
                        //requestBody暂时不用区分原生非原生
                        .withRequestBody(parseRequestBody(operation.getRequestBody()))
                        .withEncrypt((Boolean) operation.getExtensions().get(SwaggerExtensions.REQUEST_ENCRYPT)))
                .withResponse(parseApiResponse(operation))
                .withCallbacks(MapUtils.isEmpty(operation.getCallbacks()) ? null : parseApiCallBack(operation.getCallbacks()))
                .withSensitiveVariables(BooleanUtils.isFalse(nativeSwagger) ? parseApiSensitiveVariables(operation.getExtensions()) : null);
    }

    private static List<ApiExampleSceneItemDTO> parseExampleScenes(Map<String, Object> extensions) {
        Object exampleScenes = extensions.get(SwaggerExtensions.API_EXAMPLES);
        if (exampleScenes instanceof List && ((List<?>) exampleScenes).size() > 0) {
            List<ApiExampleSceneItemDTO> result = new LinkedList<>();
            List<?> exampelSceneList = (List<?>) exampleScenes;
            for (Object exampleItem : exampelSceneList) {
                if (exampleItem instanceof ApiExampleSceneItemDTO) {
                    result.add((ApiExampleSceneItemDTO) exampleItem);
                    continue;
                }
                try {
                    final ApiExampleSceneItemDTO exampleScene = JSON_MAPPER.convertValue(exampleItem, ApiExampleSceneItemDTO.class);
                    result.add(exampleScene);
                } catch (Exception e) {
                    throw new YeepayRuntimeException("illegal api example, value:" + exampleItem);
                }
            }
            return result;
        }

        return null;
    }

    private static Map<ApiOptionEnum, Boolean> getDefaultOptions(String httpMethod) {
        Map<ApiOptionEnum, Boolean> result = Maps.newHashMapWithExpectedSize(1);
        result.put(ApiOptionEnum.IDEMPOTENT, StringUtils.equalsIgnoreCase(httpMethod, "GET"));
        return result;
    }

    private static ApiTypeEnum parseApiType(Map<String, Object> extensions) {
        Object apiTypeObj = extensions.get(SwaggerExtensions.API_TYPE);
        if (apiTypeObj == null) {
            return ApiTypeEnum.COMMON;
        }
        if (apiTypeObj instanceof String) {
            ApiTypeEnum result = ApiTypeEnum.parse(((String) apiTypeObj).toUpperCase());
            if (result == null) {
                throw new YeepayRuntimeException("Unsupported api type, apiType[{0}].", apiTypeObj);
            }
            return result;
        }
        throw new YeepayRuntimeException("api type must be string.");
    }

    private static ApiParameterHandlingTypeEnum parseParameterHandlingType(Map<String, Object> extensions) {
        String parameterHandlingType = (String) extensions.get(SwaggerExtensions.PARAMETER_HANDLING_TYPE);
        if (StringUtils.isEmpty(parameterHandlingType)) {
            throw new YeepayRuntimeException("parameterHandlingType not specified.");
        }
        return ApiParameterHandlingTypeEnum.parse(parameterHandlingType);
    }


    private static void analysisCallBacks(Map<String, Callback> callbacks, SwaggerAnalysisResult result) {
        if (MapUtils.isEmpty(callbacks)) {
            return;
        }
        Map<String, SpiDTO> success = Maps.newLinkedHashMap();
        List<CallbackAnalysisFailedInfo> failedCallbackInfo = new ArrayList<>();
        callbacks.forEach((name, callback) -> {
            try {
                SpiDTO spi = parseCallBack(name, callback);
                success.put(spi.getBasic().getName(), spi);
            } catch (Exception ex) {
                LOGGER.warn("parse callback failure, name:" + name, ex);
                failedCallbackInfo.add(new CallbackAnalysisFailedInfo().withCallbackName(name).withErrorMsg(ex.getMessage()));
            }
        });
        result.withCallbacks(success).withFailedCallBackInfo(failedCallbackInfo);
    }

    private static SpiDTO parseCallBack(String name, Callback callback) throws IOException {
        if (MapUtils.isEmpty(callback)) {
            throw new YeepayRuntimeException("no content specified for callback[{0}]", name);
        }
        String path = callback.keySet().toArray(new String[0])[0];
        PathItem pathItem = callback.get(path);
        Operation operation = pathItem.getPost();
        if (operation == null) {
            throw new YeepayRuntimeException("callback post content not specified., callback[{0}]", name);
        }
        return new SpiDTO()
                .withBasic(
                        new SpiBasicDTO()
                                .withName(name)
                                .withApiGroup(StringUtils.substringBefore(name, "."))
                                .withTitle(operation.getSummary())
                                .withSpiType(SpiTypeEnum.CALLBACK)
                                .withStatus(SpiStatusEnum.DISABLED)
                                .withDescription(operation.getDescription()))
                .withRequest(
                        new SpiRequestDTO()
                                .withHttpMethod(HttpMethod.POST.name())
                                .withRequestUrl(path)
                                .withRequestBody(parseRequestBody(operation.getRequestBody())));
    }

    private static List<String> parseApiCallBack(Map<String, Callback> callbacks) {
        return new ArrayList<>(callbacks.keySet());
    }

    private static List<ApiVariable> parseApiSensitiveVariables(Map<String, Object> extensions) {
        Object sensitiveVariables = extensions.get(SwaggerExtensions.SENSITIVE_VARIABLE);
        if (sensitiveVariables == null) {
            return null;
        }
        if (sensitiveVariables instanceof List) {
            List<ApiVariable> result = new ArrayList<>(((List) sensitiveVariables).size());
            for (Object sensitiveVariableObj : (List) sensitiveVariables) {
                if (sensitiveVariableObj instanceof String) {
                    result.add(ApiVariable.parse((String) sensitiveVariableObj));
                } else {
                    throw new YeepayRuntimeException("Unsupported sensitiveVariable type, only string supported.");
                }
            }
            return result;
        }
        throw new YeepayRuntimeException("Unsupported sensitiveVariables type, only array supported.");
    }

    private static ApiResponseDTO parseApiResponse(Operation operation) throws IOException {
        ApiResponses responses = operation.getResponses();
        if (MapUtils.isEmpty(responses)) {
            return null;
        }
        //TODO 目前只处理200和302返回码
        ApiResponse apiResponse = responses.get("200");
        if (apiResponse != null) {
            ApiResponseDTO apiResponseDTO = parseResponseWithContent(apiResponse);
            apiResponseDTO.setEncrypt((Boolean) operation.getExtensions().get(SwaggerExtensions.RESPONSE_ENCRYPT));
            return apiResponseDTO;
        }
        apiResponse = responses.get("302");
        if (apiResponse != null) {
            return parseRedirectResponse(apiResponse);
        }
        throw new YeepayRuntimeException("no illegal httpCode provided.");
    }


    private static ApiResponseDTO parseResponseWithContent(ApiResponse apiResponse) throws IOException {
        ApiResponseDTO result = new ApiResponseDTO();
        result.setHttpCode("200");
        io.swagger.v3.oas.models.media.Content content = apiResponse.getContent();
        if (MapUtils.isEmpty(content)) {
            return result;
        }
        MediaType jsonMediaType = content.get(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
        if (jsonMediaType != null) {
            result.setContentType(org.springframework.http.MediaType.APPLICATION_JSON_VALUE);
            result.setContent(parseContent(jsonMediaType));
            return result;
        }
        MediaType octetStreamMediaType = content.get(org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE);
        if (octetStreamMediaType != null) {
            result.setContentType(org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE);
            result.setContent(parseContent(octetStreamMediaType));
            return result;
        }
        throw new YeepayRuntimeException("no response contentType supported.");
    }

    private static ApiResponseDTO parseRedirectResponse(ApiResponse apiResponse) throws IOException {
        ApiResponseDTO result = new ApiResponseDTO();
        result.setHttpCode("302");
        result.setHeaders(parseHeaders(apiResponse.getHeaders()));
        return result;
    }

    private static List<Header> parseHeaders(Map<String, io.swagger.v3.oas.models.headers.Header> headers) throws IOException {
        if (MapUtils.isEmpty(headers)) {
            return null;
        }
        List<Header> result = new ArrayList<>(headers.size());
        for (Map.Entry<String, io.swagger.v3.oas.models.headers.Header> entry : headers.entrySet()) {
            io.swagger.v3.oas.models.headers.Header header = entry.getValue();
            Header item = new Header();
            item.setName(entry.getKey());
            item.setDescription(header.getDescription());
            item.setDeprecated(header.getDeprecated());
            item.setExamples(parseExample(header.getExamples()));
            item.setSchema(JSON_MAPPER.writeValueAsString(header.getSchema()));
            result.add(item);
        }
        return result;
    }


    private static List<Parameter> parseParameter(List<io.swagger.v3.oas.models.parameters.Parameter> parameters, Boolean nativeSwagger) throws JsonProcessingException {
        if (CollectionUtils.isEmpty(parameters)) {
            return null;
        }
        List<Parameter> result = new ArrayList<>(parameters.size());
        for (io.swagger.v3.oas.models.parameters.Parameter parameter : parameters) {
            Parameter item = new Parameter();
            item.setName(parameter.getName());
            item.setLocation(ParameterLocationEnum.parse(StringUtils.upperCase(parameter.getIn())));
            item.setDescription(parameter.getDescription());
            item.setRequired(parameter.getRequired());
            item.setDeprecated(parameter.getDeprecated());
            Schema schema = parameter.getSchema();
            schemaSubtractApiGroup(schema);
            item.setSchema(JSON_MAPPER.writeValueAsString(schema));
            item.setExamples(parseExample(parameter.getExamples()));
            if (BooleanUtils.isFalse(nativeSwagger) && parameter.getExtensions() != null) {
                Object backendName = parameter.getExtensions().get(SwaggerExtensions.PARAMETER_BACKEND_NAME);
                if (backendName instanceof String) {
                    item.setBackendName((String) backendName);
                }
                Object backendLocation = parameter.getExtensions().get(SwaggerExtensions.PARAMETER_BACKEND_LOCATION);
                if (backendLocation instanceof String) {
                    item.setBackendLocation(ParameterLocationEnum.parse(((String) backendLocation).toUpperCase()));
                }
            }
            result.add(item);
        }
        return result;
    }

    private static RequestBody parseRequestBody(io.swagger.v3.oas.models.parameters.RequestBody requestBody) throws IOException {
        if (requestBody == null || MapUtils.isEmpty(requestBody.getContent())) {
            return null;
        }
        RequestBody result = new RequestBody();
        result.setDescription(requestBody.getDescription());
        Map<String, Content> contents = Maps.newHashMapWithExpectedSize(requestBody.getContent().size());
        for (Map.Entry<String, MediaType> entry : requestBody.getContent().entrySet()) {
            contents.put(entry.getKey(), parseContent(entry.getValue()));
        }
        result.setContents(contents);
        return result;
    }

    private static Content parseContent(MediaType mediaType) throws IOException {
        Content result = new Content();
        Schema schema = mediaType.getSchema();
        schemaSubtractApiGroup(schema);
        result.setSchema(JSON_MAPPER.writeValueAsString(schema));
        result.setExamples(parseExample(mediaType.getExamples()));
        return result;
    }

    private static void schemaSubtractApiGroup(Schema schema) {
        if (schema instanceof ArraySchema) {
            arraySchemaSubtractApiGroup((ArraySchema) schema);
            return;
        }
        objectSchemaSubtractApiGroup(schema);
    }

    private static void arraySchemaSubtractApiGroup(ArraySchema arraySchema) {
        Schema<?> items = arraySchema.getItems();
        schemaSubtractApiGroup(items);
    }

    private static void objectSchemaSubtractApiGroup(Schema schema) {
        String ref = schema.get$ref();
        if (StringUtils.isNotEmpty(ref)) {
            ref = refSubtractApiGroup(ref);
            schema.set$ref(ref);
            return;
        }
        if (MapUtils.isNotEmpty(schema.getProperties())) {
            propertiesSubtractApiGroup(schema);
        }
    }

    private static void propertiesSubtractApiGroup(Schema schema) {
        Map<String, Schema> properties = schema.getProperties();
        properties.forEach((key, value) -> schemaSubtractApiGroup(value));
    }

    private static String refSubtractApiGroup(String ref) {
        if (!StringUtils.contains(ref, ".")) {
            return ref;
        }
        String modelName = StringUtils.substringAfterLast(ref, "/");
        return "#/components/schemas/" + StringUtils.substringAfterLast(modelName, ".");
    }


    private static List<Example> parseExample(Map<String, io.swagger.v3.oas.models.examples.Example> examples) {
        if (MapUtils.isEmpty(examples)) {
            return null;
        }
        List<Example> result = new ArrayList<>(examples.size());
        examples.forEach((name, example) -> {
            Example item = new Example();
            item.setName(name);
            item.setDescription(example.getDescription());
            item.setValue(example.getValue().toString());
            result.add(item);
        });
        return result;
    }

    private static List<ApiOption> parseApiOptions(Map<String, Object> extensions) {
        Object apiOptionRule = extensions.get(SwaggerExtensions.API_OPTION_RULE);
        if (apiOptionRule instanceof List && ((List<?>) apiOptionRule).size() > 0) {
            List<ApiOption> result = new LinkedList<>();
            List<?> optionRuleList = (List<?>) apiOptionRule;
            for (Object ruleItem : optionRuleList) {
                if (ruleItem instanceof ApiOption) {
                    result.add((ApiOption) ruleItem);
                    continue;
                }
                try {
                    final ApiOption apiOption = JSON_MAPPER.convertValue(ruleItem, ApiOption.class);
                    final ApiOptionEnum apiOptionEnum = ApiOptionEnum.parse(apiOption.getName());
                    apiOptionEnum.getValidator().validate(apiOption);
                    result.add(apiOption);
                } catch (Exception e) {
                    throw new YeepayRuntimeException("illegal apiOption name, value:" + ruleItem);
                }
            }
            return result;
        }

        return ApiOptionEnum.defaultApiOptions();
    }

}
