/**
 *
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 *
 */
package com.yeepay.g3.yop.frame;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.frame.context.Context;
import org.apache.commons.lang.StringUtils;

import java.lang.reflect.Method;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * Yop远程端点API类加载器<br>
 * 若api平台本地不存在类，支持动态创建<br>
 * 一个类多个方法,已加载的类还需再暴露一个方法，添加配置后需重启api平台应用才可使用
 *
 * @author：wang.bao
 * @since：2015年3月4日 上午11:20:21
 * @version:
 */
public class YopClassLoader {

    private static final Logger LOGGER = LoggerFactory.getLogger(YopClassLoader.class);

    /**
     * 参数类型缩写
     */
    private static final ConcurrentMap<String, Class<?>> SHORT_PARAM_TYPES = new ConcurrentHashMap<String, Class<?>>();

    private static final String SPLITTER = ",";


    /**
     * 判断方法是否带参数
     *
     * @param methodName
     * @return
     */
    private static boolean isNoneParamMethod(String methodName) {
        int start = methodName.indexOf("(");
        int end = methodName.indexOf(")");
        return start == -1 && end == -1 || end - start == 1
                || methodName.substring(start + 1, end).trim().length() == 0;
    }

    /**
     * 从已加载类中获取方法定义
     *
     * @return
     * @throws ClassNotFoundException
     * @throws NoSuchMethodException
     */
    public static Method getMethod(Class<?> clazz, String methodDetail)
            throws ClassNotFoundException, NoSuchMethodException {
        MethodDefine methodDefine = new MethodDefine(methodDetail);
        return clazz.getMethod(methodDefine.getMethodName(), methodDefine.getParamTypes());
    }

    /**
     * 描述： 获得方法参数类型
     *
     * @param classNames
     * @return
     * @throws ClassNotFoundException
     */
    private static Class<?>[] getParamsClass(String[] classNames)
            throws ClassNotFoundException {
        if (classNames == null || classNames.length < 1) {
            return null;
        }
        Class<?>[] paramsClass = new Class[classNames.length];
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        for (int i = 0; i < classNames.length; i++) {
            classNames[i] = StringUtils.trimToEmpty(classNames[i]);
            paramsClass[i] = findParamType(classNames[i]);
            if (paramsClass[i] == null) {
                paramsClass[i] = classLoader.loadClass(classNames[i]);
            }
        }
        return paramsClass;
    }

    static {
        SHORT_PARAM_TYPES.put("S", String.class);
        SHORT_PARAM_TYPES.put("b", Byte.class);
        SHORT_PARAM_TYPES.put("s", Short.class);
        SHORT_PARAM_TYPES.put("i", Integer.class);
        SHORT_PARAM_TYPES.put("l", Long.class);
        SHORT_PARAM_TYPES.put("f", Float.class);
        SHORT_PARAM_TYPES.put("d", Double.class);
        SHORT_PARAM_TYPES.put("dt", Date.class);
        SHORT_PARAM_TYPES.put("ctx", Context.class);

        Class<?>[] classes = {String.class, Short.class, Integer.class,
                Long.class, Double.class, Float.class, Byte.class, List.class,
                Map.class, Set.class, Date.class, Iterator.class,
                Context.class};
        for (Class<?> clazz : classes) {
            addShortParamType(clazz);
        }
    }

    private static void addShortParamType(Class<?> clazz) {
        SHORT_PARAM_TYPES.put(clazz.getSimpleName().toLowerCase(), clazz);
        SHORT_PARAM_TYPES.put(clazz.getName(), clazz);
    }

    public static Class<?> findParamType(String typeName) {
        Class<?> clazz = SHORT_PARAM_TYPES.get(typeName);
        if (clazz == null && !"S".equalsIgnoreCase(typeName)) {
            clazz = SHORT_PARAM_TYPES.get(typeName.toLowerCase());
        }
        return clazz;
    }

    public static String toShortType(Class<?> type) {
        String shorted = type.getName();
        for (Entry<String, Class<?>> shortType : SHORT_PARAM_TYPES.entrySet()) {
            if (shortType.getValue() == type
                    && shortType.getKey().length() < shorted.length()) {
                shorted = shortType.getKey();
            }
        }
        return shorted;
    }

    private static class MethodDefine {
        private String methodName;

        private String[] paramTypeNames;

        private Class<?>[] paramTypes;

        private String returnTypeName;

        public MethodDefine(String methodDetail) throws ClassNotFoundException {
            methodDetail = methodDetail.trim();
            String m = methodDetail.substring(0, methodDetail.indexOf("("));
            int p = m.indexOf(" ");
            if (p > 0) {
                // 指定返回值的场景，e.g int getManager(java.lang.String);
                returnTypeName = m.substring(0, m.indexOf(" "));
                if ("void".equalsIgnoreCase(returnTypeName)) {
                    // e.g void setValue(java.lang.String)
                    returnTypeName = null;
                }
                methodName = m.substring(p + 1).trim();
            } else {
                // 为指定返回值，默认为void
                methodName = m;
            }
            // 无参
            if (isNoneParamMethod(methodDetail)) {
                paramTypes = new Class<?>[0];
            } else {
                // 带参数
                paramTypeNames = methodDetail.substring(
                        methodDetail.indexOf("(") + 1,
                        methodDetail.indexOf(")")).split(SPLITTER);
                paramTypes = getParamsClass(paramTypeNames);
            }
        }

        public String getMethodName() {
            return methodName;
        }

        public String[] getParamTypeNames() {
            return paramTypeNames;
        }

        public Class<?>[] getParamTypes() {
            return paramTypes;
        }

        public String getReturnTypeName() {
            return returnTypeName;
        }
    }
}
