/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.config;

import com.yeepay.g3.utils.common.tomcat.ThreadContextValve;
import com.yeepay.infra.AccessLogValve;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Configuration;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/9/10 下午2:49
 */
@Configuration
public class WebServerConfig implements WebServerFactoryCustomizer<TomcatServletWebServerFactory> {

    private static final String ACCESS_PATTERN = "%{X-Forwarded-For}i %h %l %u %D %t \"%m %U %{_method}i\" %s %b";

    @Override
    public void customize(TomcatServletWebServerFactory factory) {
        ThreadContextValve threadContextValve = new ThreadContextValve();
        AccessLogValve accessLogValve = new AccessLogValve();
        accessLogValve.setPattern(ACCESS_PATTERN);
        factory.addEngineValves(threadContextValve, accessLogValve);
    }
}
