package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.IspMgrQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.SpCodeVO;
import com.yeepay.g3.app.yop.portal.vo.page.IspInfoPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.IspPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.facade.yop.perm.dto.IspInfoDTO;
import com.yeepay.g3.facade.yop.perm.enums.IspStatusEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: SpMgrQueryServiceImpl
 * @Description:
 * @date 2018年5月11日 上午11:16:42
 */
@Component
public class IspMgrQueryServiceImpl implements IspMgrQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(IspMgrQueryServiceImpl.class);

    @Resource(name = "ispInfoQueryService")
    private QueryService queryService;

    private PageItemConverter<IspInfoPageItem> pageItemConverter = new IspMgrPageItemConverter();

    @Override
    public List<SpCodeVO> queryAllSpCode() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_queryable", "true");
        QueryParam param = new QueryParam();
        param.setParams(paramMap);
        List<Map<String, String>> queryList = queryService.queryList("listAllSpCode", param);
        return convert(queryList);
    }

    @Override
    public List<SpCodeVO> queryAllSpCodeForSp(Set<String> spCodes) {
        if (Collections3.isEmpty(spCodes)) {
            return Collections.emptyList();
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_queryable", "true");
        paramMap.put("spCodes", spCodes);
        QueryParam param = new QueryParam();
        param.setParams(paramMap);
        List<Map<String, String>> queryList = queryService.queryList("listAllSpCodeForSp", param);
        return convert(queryList);
    }

    @Override
    public PageQueryResult<IspInfoPageItem> pageList(IspPageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        QueryResult queryResult = queryService.query("ispList", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
    }

    @Override
    public PageQueryResult<IspInfoPageItem> pageListForSp(IspPageQueryParam param, Set<String> spCodes) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> map = getBizParams(param);
        map.put("spCodes", spCodes);
        queryParam.setParams(map);
        QueryResult queryResult = queryService.query("ispListForSp", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
    }

    @Override
    public List<String> findTenantCodesBySpCodes(Set<String> spCodes) {
        if (Collections3.isEmpty(spCodes)) {
            return Collections.emptyList();
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("spCodes", spCodes);
        QueryParam param = new QueryParam();
        param.setParams(paramMap);
        List<Map<String, String>> queryList = queryService.queryList("listTenantCodesBySpCodes", param);
        return convertToListString(queryList);
    }

    @Override
    public IspInfoDTO findBySpCode(String spCode) {
        if (StringUtils.isEmpty(spCode)) {
            return null;
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("spCode", spCode);
        QueryParam param = new QueryParam();
        param.setParams(paramMap);
        Map<String, Object> map = queryService.queryUnique("findBySpCode", paramMap, true);
        return convert(map);
    }

    private IspInfoDTO convert(Map<String, Object> params) {
        IspInfoDTO ispInfoDTO = new IspInfoDTO();
        ispInfoDTO.setSpCode((String) params.get("sp_code"));
        ispInfoDTO.setStatus(IspStatusEnum.valueOf((String) params.get("status")));
        ispInfoDTO.setSpName((String) params.get("sp_name"));
        ispInfoDTO.setDescription((String) params.get("description"));
        ispInfoDTO.setTenantCode((String) params.get("tenant_code"));
        return ispInfoDTO;
    }

    private List<String> convertToListString(List<Map<String, String>> queryList) {
        if (Collections3.isEmpty(queryList)) {
            return Collections.emptyList();
        }
        List<String> list = new ArrayList<>(queryList.size());
        for (Map<String, String> map : queryList) {
            String tenantCode = map.get("tenant_code");
            list.add(tenantCode);
        }
        return list;
    }

    private List<SpCodeVO> convert(List<Map<String, String>> queryList) {
        if (Collections3.isEmpty(queryList)) {
            return Collections.emptyList();
        }
        List<SpCodeVO> list = new ArrayList<>(queryList.size());
        for (Map<String, String> map : queryList) {
            SpCodeVO spCodeVO = new SpCodeVO();
            spCodeVO.setSpCode(map.get("sp_code"));
            spCodeVO.setSpName(map.get("sp_name"));
            list.add(spCodeVO);
        }
        return list;
    }

    private Map<String, Object> getBizParams(IspPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("spCode", param.getSpCode());
        bizParams.put("status", param.getStatus());
        return bizParams;
    }

    class IspMgrPageItemConverter extends BasePageItemConverter<IspInfoPageItem> {

        @Override
        public IspInfoPageItem convert(Map<String, Object> params) {
            IspInfoPageItem item = new IspInfoPageItem();
            item.setSpCode((String) params.get("sp_code"));
            item.setStatus((String) params.get("status"));
            item.setSpName((String) params.get("sp_name"));
            item.setDescription((String) params.get("description"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            item.setTenantCode((String) params.get("tenant_code"));
            return item;
        }
    }

}
