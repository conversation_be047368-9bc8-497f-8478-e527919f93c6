/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.dto.spi;

import lombok.Data;
import lombok.NonNull;
import lombok.experimental.Tolerate;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/12/23 7:10 下午
 */
@Data
public class SpiApiVO implements Serializable {

    private static final long serialVersionUID = -1L;

    @NonNull
    private String spiName;
    @NonNull
    private List<String> apiIds;

    @Tolerate
    public SpiApiVO() {
    }
}
