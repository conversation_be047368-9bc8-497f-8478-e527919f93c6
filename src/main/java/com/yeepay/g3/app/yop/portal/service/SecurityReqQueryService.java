/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.SecurityReqQueryParam;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqDTO;

import java.util.List;

/**
 * title: 安全需求查询组件<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-05-19 22:17
 */
public interface SecurityReqQueryService {
    List<SecurityReqDTO> list(SecurityReqQueryParam queryParam);
}
