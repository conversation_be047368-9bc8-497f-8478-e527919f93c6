/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.ApiResponseModelVO;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/15 11:21 AM
 */
public interface ApiResponseService {

    /**
     * 根据apiid查询响应参数
     * @param apiId
     * @return
     */
    ApiResponseModelVO findOutputParams(String apiId);
}
