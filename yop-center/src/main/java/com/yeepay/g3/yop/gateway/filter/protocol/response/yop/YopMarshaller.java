package com.yeepay.g3.yop.gateway.filter.protocol.response.yop;

import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;

import javax.servlet.http.HttpServletResponse;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/1/10 上午1:35
 */
public interface YopMarshaller {

    /**
     * 将response序列化输出至servlet response
     *
     * @param exchange        上下文
     * @param response        内部返回结果
     * @param servletResponse http返回结果
     */
    void marshal(ServerWebExchange exchange, Response response, HttpServletResponse servletResponse);

}
