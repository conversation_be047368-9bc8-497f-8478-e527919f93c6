package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.yeepay.g3.facade.yop.sys.utils.jackson.ExtensionsDeserializer;
import com.yeepay.g3.facade.yop.sys.utils.jackson.ExtensionsSerializer;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * title: 安全需求视图<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-05-20 12:05
 */
public class NewSecurityReqVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private String name;
    private List<String> scopes;

    @JsonSerialize(using = ExtensionsSerializer.class)
    @JsonDeserialize(using = ExtensionsDeserializer.class)
    private Map<String, Object> extensions;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getScopes() {
        return scopes;
    }

    public void setScopes(List<String> scopes) {
        this.scopes = scopes;
    }

    public Map<String, Object> getExtensions() {
        return extensions;
    }

    public void setExtensions(Map<String, Object> extensions) {
        this.extensions = extensions;
    }
}
