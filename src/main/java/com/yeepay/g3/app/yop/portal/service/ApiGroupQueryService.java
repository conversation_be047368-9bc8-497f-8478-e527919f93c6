package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.ApiGroupVO;
import com.yeepay.g3.app.yop.portal.vo.GrayPolicyInfoVO;
import com.yeepay.g3.app.yop.portal.vo.page.ApiGroupPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ApiGroupPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.List;
import java.util.Set;

/**
 * title: Api分组查询service<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/9 14:37
 */
public interface ApiGroupQueryService {

    /**
     * 查询所有api分组编码和名称
     *
     * @return api分组列表
     */
    List<ApiGroupVO> list();

    /**
     * 查询操作员的api分组编码和名称
     *
     * @param spCodes
     * @return api分组列表
     */
    List<ApiGroupVO> listForSp(Set<String> spCodes);

    /**
     * 查询权限内的api分组编码
     *
     * @return api分组编码
     */
    List<String> listApiGroupCode();

    /**
     * 分页查询
     *
     * @return
     */
    PageQueryResult<ApiGroupPageItem> pageList(ApiGroupPageQueryParam param);

    /**
     * sp分页查询
     *
     * @param spCodes 操作员编码
     * @return
     */
    PageQueryResult<ApiGroupPageItem> pageListForSp(ApiGroupPageQueryParam param, Set<String> spCodes);

    /**
     * 查询api分组下灰度策略
     *
     * @return 灰度策略列表
     */
    List<GrayPolicyInfoVO> listApiGroupGrayPolicy(String apiGroup);

    /**
     * 查询api分组的服务提供方
     *
     * @param apiGroupCode
     * @return
     */
    String findSp(String apiGroupCode);
}
