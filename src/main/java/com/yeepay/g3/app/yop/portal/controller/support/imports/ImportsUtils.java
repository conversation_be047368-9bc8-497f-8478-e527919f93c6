package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.google.common.base.Predicates;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yeepay.g3.app.yop.portal.utils.swagger.SwaggerAnalysisResult;
import com.yeepay.g3.app.yop.portal.utils.swagger.SwaggerAnalyzer;
import com.yeepay.g3.app.yop.portal.utils.validate.SchemaTypeFormatUtils;
import com.yeepay.g3.app.yop.portal.utils.validate.SystemParameterUtils;
import com.yeepay.g3.facade.yop.sys.checker.*;
import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;
import com.yeepay.g3.facade.yop.sys.dto.ApiImportCheckResult;
import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;
import com.yeepay.g3.facade.yop.sys.dto.SpiDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.*;
import com.yeepay.g3.facade.yop.sys.dto.imports.ImportFailedInfo;
import com.yeepay.g3.facade.yop.sys.dto.models.Content;
import com.yeepay.g3.facade.yop.sys.dto.models.Parameter;
import com.yeepay.g3.facade.yop.sys.dto.models.RequestBody;
import com.yeepay.g3.facade.yop.sys.dto.models.utils.SchemaUtils;
import com.yeepay.g3.facade.yop.sys.dto.security.SecurityReq;
import com.yeepay.g3.facade.yop.sys.enums.ApiPartEnum;
import com.yeepay.g3.facade.yop.sys.enums.ImportItemEnum;
import com.yeepay.g3.facade.yop.sys.enums.ImportOperationEnum;
import com.yeepay.g3.facade.yop.sys.enums.ParameterLocationEnum;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import io.swagger.v3.oas.models.media.Schema;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * title: 导入工具类<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 11:15
 */
public class ImportsUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImportsUtils.class);


    public static List<ImportAnalysisFailedInfo> getFailedInfos(SwaggerAnalysisResult analysisResult) {
        if (CollectionUtils.isEmpty(analysisResult.getFailedApiInfo()) && CollectionUtils.isEmpty(analysisResult.getFailedCallBackInfo()) && CollectionUtils.isEmpty(analysisResult.getFailedModelInfo())) {
            return Collections.emptyList();
        }
        List<ImportAnalysisFailedInfo> result = new ArrayList<>(CollectionUtils.size(analysisResult.getFailedApiInfo())
                + CollectionUtils.size(analysisResult.getCallbacks())
                + CollectionUtils.size(analysisResult.getFailedModelInfo()));
        if (CollectionUtils.isNotEmpty(analysisResult.getFailedApiInfo())) {
            analysisResult.getFailedApiInfo().forEach(failedInfo -> result.add(new ImportAnalysisFailedInfo()
                    .withItem(ImportItemEnum.API)
                    .withValue(failedInfo.getApiUri())
                    .withReason(failedInfo.getErrorMsg())));
        }
        if (CollectionUtils.isNotEmpty(analysisResult.getFailedCallBackInfo())) {
            analysisResult.getFailedCallBackInfo().forEach(failedInfo -> result.add(new ImportAnalysisFailedInfo()
                    .withItem(ImportItemEnum.SPI)
                    .withValue(failedInfo.getCallbackName())
                    .withReason(failedInfo.getErrorMsg())));
        }
        if (CollectionUtils.isNotEmpty(analysisResult.getFailedModelInfo())) {
            analysisResult.getFailedModelInfo().forEach(failedInfo -> result.add(new ImportAnalysisFailedInfo()
                    .withItem(ImportItemEnum.MODEL)
                    .withValue(failedInfo.getModelName())
                    .withReason(failedInfo.getErrorMsg())));
        }
        return result;
    }

    public static List<ImportAnalysisFailedInfo> checkAnalysisResult(SwaggerAnalysisResult analysisResult) {
        List<ImportAnalysisFailedInfo> apiCheckResult = null, spiCheckResult = null, modelCheckResult = null;
        if (MapUtils.isNotEmpty(analysisResult.getApis())) {
            apiCheckResult = checkApis(analysisResult.getApis().values(), analysisResult.isNativeSwagger());
        }
        if (MapUtils.isNotEmpty(analysisResult.getCallbacks())) {
            spiCheckResult = checkSpis(analysisResult.getCallbacks().values());
        }
        if (MapUtils.isNotEmpty(analysisResult.getModels())) {
            modelCheckResult = checkModels(analysisResult.getModels().values());
        }
        if (CollectionUtils.isEmpty(apiCheckResult) && CollectionUtils.isEmpty(spiCheckResult) && CollectionUtils.isEmpty(modelCheckResult)) {
            return Collections.emptyList();
        }
        List<ImportAnalysisFailedInfo> result = new ArrayList<>(CollectionUtils.size(apiCheckResult)
                + CollectionUtils.size(spiCheckResult)
                + CollectionUtils.size(modelCheckResult));
        if (CollectionUtils.isNotEmpty(apiCheckResult)) {
            result.addAll(apiCheckResult);
        }
        if (CollectionUtils.isNotEmpty(spiCheckResult)) {
            result.addAll(spiCheckResult);
        }
        if (CollectionUtils.isNotEmpty(modelCheckResult)) {
            result.addAll(modelCheckResult);
        }
        return result;
    }

    private static List<ImportAnalysisFailedInfo> checkApis(Collection<ApiDTO> apis, Boolean nativeSwagger) {
        List<ImportAnalysisFailedInfo> result = new ArrayList<>();
        Set<String> apiNameSet = Sets.newHashSetWithExpectedSize(apis.size());
        for (ApiDTO api : apis) {
            String apiName = api.getBasic().getName();
            if (apiName != null) {
                if (apiNameSet.contains(apiName)) {
                    result.add(new ImportAnalysisFailedInfo()
                            .withItem(ImportItemEnum.API)
                            .withValue(apiName)
                            .withReason("apiName duplicate"));
                } else {
                    apiNameSet.add(apiName);
                }
            }
            try {
                ApiDependency dependency = new ApiDependency();

                ApiChecker checker = getApiChecker();
                if (BooleanUtils.isTrue(nativeSwagger)) {
                    checker.checkForImportNativeSwagger(api, dependency);
                } else {
                    checker.checkForCreate(api, dependency);
                }
                api.setCallbacks(dependency.getSpiNames());
                api.setDirectedRefModels(dependency.getModels());
            } catch (Exception ex) {
                LOGGER.error("api illegal.", ex);
                result.add(new ImportAnalysisFailedInfo()
                        .withItem(ImportItemEnum.API)
                        .withValue(apiName)
                        .withReason(ex.getMessage()));
            }
        }
        return result;
    }

    private static List<ImportAnalysisFailedInfo> checkSpis(Collection<SpiDTO> spis) {
        if (CollectionUtils.isEmpty(spis)) {
            return Collections.emptyList();
        }
        List<ImportAnalysisFailedInfo> result = new ArrayList<>();
        for (SpiDTO spi : spis) {
            try {
                SpiDependency dependency = new SpiDependency();
                getSpiChecker().checkForCreate(spi, dependency);
                spi.setDirectedRefModels(dependency.getModels());
            } catch (Exception ex) {
                LOGGER.warn("spi illegal.", ex);
                result.add(new ImportAnalysisFailedInfo()
                        .withItem(ImportItemEnum.SPI)
                        .withValue(spi.getBasic().getName())
                        .withReason(ex.getMessage()));
            }
        }
        return result;
    }

    private static List<ImportAnalysisFailedInfo> checkModels(Collection<ModelDTO> models) {
        if (CollectionUtils.isEmpty(models)) {
            return Collections.emptyList();
        }
        List<ImportAnalysisFailedInfo> result = new ArrayList<>();
        for (ModelDTO model : models) {
            String name = model.getName();
            try {
                getModelChecker().checkForCreate(model);
                model.setRefModels(SchemaUtils.getRefModels(model.getSchema()));
            } catch (Exception ex) {
                LOGGER.warn("model illegal.", ex);
                result.add(new ImportAnalysisFailedInfo()
                        .withItem(ImportItemEnum.MODEL)
                        .withValue(name)
                        .withReason(ex.getMessage()));
            }
        }
        return result;
    }


    /**
     * 获取导入检查的失败信息
     *
     * @param checkResult 检查结果
     * @return 失败信息
     */
    public static List<ImportAnalysisFailedInfo> getImportCheckFailedInfos(ApiImportCheckResult checkResult) {
        if (MapUtils.isNotEmpty(checkResult.getFailedApis()) || MapUtils.isNotEmpty(checkResult.getFailedSpis())) {
            int size = (checkResult.getFailedApis() == null ? 0 : checkResult.getFailedApis().size()) + (checkResult.getFailedSpis() == null ? 0 : checkResult.getFailedSpis().size());
            List<ImportAnalysisFailedInfo> result = new ArrayList<>(size);
            if (checkResult.getFailedApis() != null) {
                for (Map.Entry<ApiRequestKey, String> entry : checkResult.getFailedApis().entrySet()) {
                    result.add(new ImportAnalysisFailedInfo().withItem(ImportItemEnum.API).withValue(entry.getKey().toString())
                            .withReason(entry.getValue()));
                }
            }
            if (checkResult.getFailedSpis() != null) {
                for (Map.Entry<String, String> entry : checkResult.getFailedSpis().entrySet()) {
                    result.add(new ImportAnalysisFailedInfo().withItem(ImportItemEnum.SPI).withValue(entry.getKey())
                            .withReason(entry.getValue()));
                }
            }
            return result;
        }
        return null;
    }

    /**
     * 覆盖一致性检查
     *
     * @param dependencyAnalysisResult 依赖分析结果
     * @param checkResult              校验结果
     * @param nativeSwagger            是否是原生Swagger导入
     * @return 错误信息列表
     */
    public static List<ImportAnalysisFailedInfo> checkOverrideConsistence(ApiDependencyAnalysisResult dependencyAnalysisResult, ApiImportCheckResult checkResult, Boolean nativeSwagger) {
        if (BooleanUtils.isTrue(nativeSwagger)) {
            List<ApiDTO> recheckApis = mergeNativeSwagger(dependencyAnalysisResult, checkResult);
            if (CollectionUtils.isEmpty(recheckApis)) {
                return null;
            } else {
                return checkApis(recheckApis, false);
            }
        } else {
            if (MapUtils.isNotEmpty(checkResult.getApisToOverride())) {
                List<ImportAnalysisFailedInfo> result = new ArrayList<>();
                for (Map.Entry<ApiRequestKey, ApiDTO> entry : checkResult.getApisToOverride().entrySet()) {
                    ApiDTO target = dependencyAnalysisResult.getApis().get(entry.getKey());
                    if (entry.getValue().getBasic().getApiType() != target.getBasic().getApiType()) {
                        result.add(new ImportAnalysisFailedInfo().withItem(ImportItemEnum.API)
                                .withValue(entry.getKey().toString()).withValue("ApiType can't be override."));
                    }
                }
                return result;
            }
        }
        return null;

    }

    /**
     * 合并原生Swagger
     *
     * @param dependencyAnalysisResult 依赖分析结果
     * @param checkResult              校验结果
     * @return 需要重新校验的API列表
     */
    private static List<ApiDTO> mergeNativeSwagger(ApiDependencyAnalysisResult dependencyAnalysisResult, ApiImportCheckResult checkResult) {
        List<ApiDTO> result = null;
        if (MapUtils.isNotEmpty(checkResult.getApisToOverride())) {
            result = new ArrayList<>(checkResult.getApisToOverride().size());
            for (Map.Entry<ApiRequestKey, ApiDTO> entry : checkResult.getApisToOverride().entrySet()) {
                ApiDTO src = entry.getValue();
                ApiDTO target = dependencyAnalysisResult.getApis().get(entry.getKey());
                mergeApiForNativeSwagger(src, target);
                result.add(target);
            }
        }
        if (MapUtils.isNotEmpty(checkResult.getSpisToOverride())) {
            checkResult.getSpisToOverride().forEach((spiName, existedSpi) -> {
                SpiDTO spi = dependencyAnalysisResult.getSpis().get(spiName);
                spi.setId(existedSpi.getId());
                spi.setVersion(existedSpi.getVersion());
            });
        }
        if (MapUtils.isNotEmpty(checkResult.getModelsToOverride())) {
            checkResult.getModelsToOverride().forEach((name, existedModel) -> {
                ModelDTO model = dependencyAnalysisResult.getModels().get(name);
                try {
                    mergeModelForNativeSwagger(existedModel, model);
                } catch (Exception ex) {
                    throw new YeepayRuntimeException("unexpected exception occurred when mergeModelForNativeSwagger.", ex);
                }
            });
        }
        return result;
    }

    private static void mergeModelForNativeSwagger(ModelDTO src, ModelDTO target) throws IOException {
        target.setId(src.getId());
        target.setVersion(src.getVersion());
        Schema targetSchema = SwaggerAnalyzer.JSON_MAPPER.readValue(target.getSchema(), Schema.class);
        if (CollectionUtils.isNotEmpty(src.getSensitiveVariables())) {
            List<String> targetSensitiveVariables = new ArrayList<>(src.getSensitiveVariables().size());
            src.getSensitiveVariables().forEach(sensitiveVariable -> {
                if (targetSchema.getProperties().containsKey(sensitiveVariable)) {
                    targetSensitiveVariables.add(sensitiveVariable);
                }
            });
            if (CollectionUtils.isNotEmpty(targetSensitiveVariables)) {
                target.setSensitiveVariables(targetSensitiveVariables);
            }
        }
    }

    private static void mergeApiForNativeSwagger(ApiDTO src, ApiDTO target) {
        target.setApiId(src.getApiId());
        target.setVersion(src.getVersion());
        mergeApiBasicForNativeSwagger(src.getBasic(), target.getBasic());
        ApiRequestParameterIndexes targetRequestParameterIndexes = mergeApiRequestForNativeSwagger(src.getRequest(), target.getRequest());
        if (CollectionUtils.isNotEmpty(src.getSensitiveVariables())) {
            List<ApiVariable> targetSensitiveVariables = new ArrayList<>(src.getSensitiveVariables().size());
            src.getSensitiveVariables().forEach(sensitiveVariable -> {
                Pair<ParameterLocationEnum, Schema> targetParameterInfo = targetRequestParameterIndexes.getRequestParameter(sensitiveVariable.getVariableName());
                if (targetParameterInfo != null) {
                    ApiVariable targetSensitiveVariable = new ApiVariable()
                            .withPart(ApiPartEnum.REQUEST)
                            .withVariableName(sensitiveVariable.getVariableName())
                            .withLocation(targetParameterInfo.getLeft());
                    targetSensitiveVariables.add(targetSensitiveVariable);
                }
            });
            if (CollectionUtils.isNotEmpty(targetSensitiveVariables)) {
                target.setSensitiveVariables(targetSensitiveVariables);
            }
        }
        if (CollectionUtils.isNotEmpty(src.getCallbacks()) && target.getCallbacks() == null) {
            target.setCallbacks(Collections.emptyList());
        }
    }


    private static void mergeApiBasicForNativeSwagger(ApiBasicDTO src, ApiBasicDTO target) {
        target.setApiType(src.getApiType());
        target.setOptionsRule(src.getOptionsRule());
        mergeApiSecurityForNativeSwagger(src.getSecurity(), target.getSecurity());
    }

    private static void mergeApiSecurityForNativeSwagger(ApiSecurityReq src, ApiSecurityReq target) {
        //是否继承属性不同时，按照最新的为准
        if (BooleanUtils.isTrue(src.getInherited()) != BooleanUtils.isTrue(target.getInherited())) {
            return;
        }
        if (CollectionUtils.isNotEmpty(src.getSecurityReqs()) && CollectionUtils.isNotEmpty(target.getSecurityReqs())) {
            Map<String, SecurityReq> srcSecurityReqMapping = Maps.newHashMapWithExpectedSize(src.getSecurityReqs().size());
            src.getSecurityReqs().forEach(securityReq -> srcSecurityReqMapping.put(securityReq.getName(), securityReq));
            target.getSecurityReqs().forEach(securityReq -> {
                SecurityReq srcSecurityReq = srcSecurityReqMapping.get(securityReq.getName());
                if (srcSecurityReq != null) {
                    securityReq.setScopes(srcSecurityReq.getScopes());
                    securityReq.setExtensions(srcSecurityReq.getExtensions());
                }
            });
        }
    }


    private static ApiRequestParameterIndexes mergeApiRequestForNativeSwagger(ApiRequestDTO src, ApiRequestDTO target) {
        ApiRequestParameterIndexes targetParameterIndexes = new ApiRequestParameterIndexes();
        target.setParameterHandlingType(src.getParameterHandlingType());
        if (CollectionUtils.isNotEmpty(src.getParameters()) && CollectionUtils.isNotEmpty(target.getParameters())) {
            Map<String, Parameter> srcParameterMapping = Maps.newHashMapWithExpectedSize(src.getParameters().size());
            src.getParameters().forEach(parameter -> srcParameterMapping.put(parameter.getName(), parameter));
            target.getParameters().forEach(parameter -> {
                targetParameterIndexes.putRequestParameters(parameter.getName(), parameter.getLocation(), null);
                Parameter srcParameter = srcParameterMapping.get(parameter.getName());
                parameter.setBackendName(srcParameter.getBackendName());
                parameter.setBackendLocation(srcParameter.getBackendLocation());
            });
        }
        if (src.getRequestBody() != null && target.getRequestBody() != null) {
            mergeRequestBodyForNativeSwagger(src.getRequestBody(), target.getRequestBody(), targetParameterIndexes);
        }
        return targetParameterIndexes;
    }

    private static void mergeRequestBodyForNativeSwagger(RequestBody src, RequestBody target, ApiRequestParameterIndexes targetParameterIndexes) {
        if (MapUtils.isNotEmpty(src.getContents()) && MapUtils.isNotEmpty(target.getContents())) {
            target.getContents().forEach((contentType, content) -> {
                Content srcContent = src.getContents().get(contentType);
                if (srcContent == null) {
                    return;
                }
                try {
                    mergeContentForNativeSwagger(srcContent, content, targetParameterIndexes);
                } catch (IOException ex) {
                    throw new YeepayRuntimeException("unexpected exception occurred when mergeContentForNativeSwagger.", ex);
                }
            });
        }
    }

    private static void mergeContentForNativeSwagger(Content src, Content target, ApiRequestParameterIndexes targetParameterIndexes) throws IOException {
        Schema srcSchema = SwaggerAnalyzer.JSON_MAPPER.readValue(src.getSchema(), Schema.class);
        Schema targetSchema = SwaggerAnalyzer.JSON_MAPPER.readValue(target.getSchema(), Schema.class);
        if (StringUtils.equals(srcSchema.getType(), "object") && StringUtils.equals(targetSchema.getType(), "object")) {
            if (MapUtils.isNotEmpty(srcSchema.getProperties()) && MapUtils.isNotEmpty(targetSchema.getProperties())) {
                ((Map<String, Schema>) targetSchema.getProperties()).forEach((propertyName, propertySchema) -> {
                    targetParameterIndexes.putRequestParameters(propertyName, ParameterLocationEnum.BODY, null);
                    Schema srcPropertySchema = (Schema) srcSchema.getProperties().get(propertyName);
                    if (srcPropertySchema != null) {
                        propertySchema.setExtensions(srcPropertySchema.getExtensions());
                    }
                });
                target.setSchema(SwaggerAnalyzer.JSON_MAPPER.writeValueAsString(targetSchema));
            }
        }
    }

    public static List<ImportAnalysisFailedInfo> apiDependencyAnalysis(ApiDependencyAnalysisRequest request,
                                                                       ApiDependencyAnalysisResult result) {
        result.withApis(request.getApis());
        List<ImportAnalysisFailedInfo> failedInfos = new ArrayList<>();
        Map<String, SpiDTO> spiMapping = request.getSpis() == null ? Collections.emptyMap() : request.getSpis();
        Map<String, ModelDTO> modelMapping = request.getModels() == null ? Collections.emptyMap() : request.getModels();

        Map<String, SpiDTO> usedSpis = Maps.newHashMapWithExpectedSize(spiMapping.size());
        Map<String, ModelDTO> rootModelMapping = Maps.newHashMapWithExpectedSize(request.getApis().size() * 2 + spiMapping.size());

        //1、分析API直接引用是否存在
        request.getApis().forEach((key, api) -> {
            if (CollectionUtils.isNotEmpty(api.getCallbacks())) {
                api.getCallbacks().forEach(spiName -> {
                    if (usedSpis.containsKey(spiName)) {
                        return;
                    }
                    SpiDTO spi = spiMapping.get(spiName);
                    if (spi == null) {
                        failedInfos.add(new ImportAnalysisFailedInfo().withItem(ImportItemEnum.API).withValue(key.toString())
                                .withReason("Dependent spi:" + spiName + " does not exist."));
                    } else {
                        usedSpis.put(spiName, spi);
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(api.getDirectedRefModels())) {
                api.getDirectedRefModels().forEach(rootModelName -> {
                    rootModelName = api.getBasic().getApiGroup() + "." + rootModelName;
                    if (rootModelMapping.containsKey(rootModelName)) {
                        return;
                    }
                    ModelDTO rootModel = modelMapping.get(rootModelName);
                    if (rootModel == null) {
                        failedInfos.add(new ImportAnalysisFailedInfo().withItem(ImportItemEnum.API).withValue(key.toString())
                                .withReason("Dependent model:" + rootModelName + " does not exist."));
                    } else {
                        rootModelMapping.put(rootModelName, rootModel);
                    }
                });
            }
        });
        //2、API直接引用有问题的情况下，直接返回
        if (CollectionUtils.isNotEmpty(failedInfos)) {
            return failedInfos;
        }
        //3、分析SPI依赖，并设置结果中的spi项
        if (MapUtils.isNotEmpty(usedSpis)) {
            //3.1、分析SPI直接引用是否存在
            spiDirectDependencyAnalysis(usedSpis, rootModelMapping, modelMapping, failedInfos);
            //3.2、SPI直接引用有问题的情况下，直接返回
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return failedInfos;
            }
            //3.3、没有问题的情况下，设置结果中的spi
            result.withSpis(usedSpis)
                    .withUnusedSpis(new ArrayList<>(CollectionUtils.subtract(spiMapping.keySet(), usedSpis.keySet())));
        } else {
            //4、 没有有效的spi，则所有的spi均被忽略
            result.withUnusedSpis(new ArrayList<>(spiMapping.keySet()));
        }
        Map<String, Set<String>> modelDependencyCache;
        //5、分析model依赖，并设置结果中的model项
        if (MapUtils.isNotEmpty(rootModelMapping)) {
            //5.1、分析所有以直接引用model为根可达的模型，以及无效引用
            modelDependencyCache = modelDependencyAnalysis(rootModelMapping, modelMapping, failedInfos);
            //5.2、如果model引用有问题，直接返回，不再填充spi依赖关系
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return failedInfos;
            }
            //5.4、没有问题的情况下，设置结果中的model
            result.setModels(new HashMap<>(Maps.filterKeys(modelMapping, Predicates.in(modelDependencyCache.keySet()))));
            result.setUnusedModels(new ArrayList<>(CollectionUtils.subtract(modelMapping.keySet(), modelDependencyCache.keySet())));
        } else {
            modelDependencyCache = Collections.emptyMap();
            //6、root根结点不存在，则忽略所有model
            result.setUnusedModels(new ArrayList<>(modelMapping.keySet()));
        }
        //7、组装API依赖信息
        request.getApis().forEach((key, api) -> {
            ApiDependencyInfo dependencyInfo = new ApiDependencyInfo().withApiName(api.getBasic().getName())
                    .withRequestKey(key)
                    .withSpis(api.getCallbacks());
            Set<String> rootModels = Sets.newHashSet();
            if (CollectionUtils.isNotEmpty(api.getDirectedRefModels())) {
                rootModels.addAll(api.getDirectedRefModels()
                        .stream()
                        .map(model -> api.getBasic().getApiGroup() + "." + model)
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(api.getCallbacks())) {
                api.getCallbacks().forEach(spiName -> {
                    SpiDTO spi = spiMapping.get(spiName);
                    if (CollectionUtils.isNotEmpty(spi.getDirectedRefModels())) {
                        rootModels.addAll(spi.getDirectedRefModels()
                                .stream()
                                .map(model -> spi.getBasic().getApiGroup() + "." + model)
                                .collect(Collectors.toList()));
                    }
                });
            }
            Set<String> refModels = new HashSet<>(rootModels);
            rootModels.forEach(refModel -> refModels.addAll(modelDependencyCache.get(refModel)));
            dependencyInfo.withRefModels(new ArrayList<>(refModels));
            result.addDependencyInfo(dependencyInfo);
        });
        return failedInfos;
    }

    private static void spiDirectDependencyAnalysis(Map<String, SpiDTO> usedSpis, Map<String, ModelDTO> rootModelMapping, Map<String, ModelDTO> modelMapping, List<ImportAnalysisFailedInfo> failedInfos) {
        usedSpis.forEach((spiName, spi) -> {
            if (CollectionUtils.isNotEmpty(spi.getDirectedRefModels())) {
                spi.getDirectedRefModels().forEach(rootModelName -> {
                    rootModelName = spi.getBasic().getApiGroup() + "." + rootModelName;
                    if (rootModelMapping.containsKey(rootModelName)) {
                        return;
                    }
                    ModelDTO rootModel = modelMapping.get(rootModelName);
                    if (rootModel == null) {
                        failedInfos.add(new ImportAnalysisFailedInfo().withItem(ImportItemEnum.SPI).withValue(spiName)
                                .withReason("Dependent model:" + rootModelName + " does not exist."));
                    } else {
                        rootModelMapping.put(rootModelName, rootModel);
                    }
                });
            }
        });
    }

    private static Map<String, Set<String>> modelDependencyAnalysis(Map<String, ModelDTO> rootModelMapping, Map<String, ModelDTO> modelMapping, List<ImportAnalysisFailedInfo> failedInfos) {
        Map<String, Set<String>> modelDependencyCache = Maps.newHashMapWithExpectedSize(rootModelMapping.size());
        Set<String> modelAnalysisChain = Sets.newHashSet();
        rootModelMapping.forEach((rootModelName, rootModel) -> {
            if (modelDependencyCache.containsKey(rootModelName)) {
                return;
            }
            if (CollectionUtils.isEmpty(rootModel.getRefModels())) {
                modelDependencyCache.put(rootModelName, Collections.emptySet());
            } else {
                Set<String> rootModelDependency = Sets.newLinkedHashSetWithExpectedSize(rootModel.getRefModels().size());
                modelAnalysisChain.add(rootModelName);
                rootModel.getRefModels().forEach(refModelName -> {
                    refModelName = rootModel.getApiGroup() + "." + refModelName;
                    rootModelDependency.add(refModelName);
                    rootModelDependency.addAll(doModelAccessAnalysis(rootModelName, refModelName, modelAnalysisChain, modelDependencyCache, modelMapping, failedInfos));
                });
                modelAnalysisChain.remove(rootModelName);
                modelDependencyCache.put(rootModelName, rootModelDependency);
            }
        });
        return modelDependencyCache;
    }


    public static List<ImportAnalysisFailedInfo> spiDependencyAnalysis(SpiDependencyAnalysisRequest request, SpiDependencyAnalysisResult result) {
        result.withSpis(request.getSpis());
        List<ImportAnalysisFailedInfo> failedInfos = new ArrayList<>();
        Map<String, ModelDTO> modelMapping = request.getModels() == null ? Collections.emptyMap() : request.getModels();
        Map<String, ModelDTO> rootModelMapping = Maps.newHashMapWithExpectedSize(request.getSpis().size());
        //1、分析spi的直接引用是否存在
        spiDirectDependencyAnalysis(request.getSpis(), rootModelMapping, modelMapping, failedInfos);
        //2、SPI直接引用有问题的情况下，model内部可达性不再做进一步分析
        if (CollectionUtils.isNotEmpty(failedInfos)) {
            return failedInfos;
        }
        //3、分析所有以直接引用model为根可达的模型，以及无效引用
        Map<String, Set<String>> modelDependencyCache = modelDependencyAnalysis(rootModelMapping, modelMapping, failedInfos);
        //4、如果model引用有问题，直接返回，不再填充spi依赖关系
        if (CollectionUtils.isNotEmpty(failedInfos)) {
            return failedInfos;
        }
        //5、组装SPI依赖信息
        request.getSpis().forEach((spiName, spi) -> {
            SpiDependencyInfo dependencyInfo = new SpiDependencyInfo()
                    .withSpiName(spiName);
            if (CollectionUtils.isNotEmpty(spi.getDirectedRefModels())) {
                Set<String> refModels = new HashSet<>(spi.getDirectedRefModels().size());
                spi.getDirectedRefModels().forEach(refModel -> {
                    refModel = spi.getBasic().getApiGroup() + "." + refModel;
                    refModels.add(refModel);
                    refModels.addAll(modelDependencyCache.get(refModel));
                });
                dependencyInfo.withRefModels(new ArrayList<>(refModels));
            }
            result.addDependencyInfo(dependencyInfo);
        });
        //6、装配model信息
        result.withModels(new HashMap<>(Maps.filterKeys(modelMapping, Predicates.in(modelDependencyCache.keySet()))))
                .withUnusedModels(new ArrayList<>(CollectionUtils.subtract(modelMapping.keySet(), modelDependencyCache.keySet())));
        return failedInfos;
    }

    private static Set<String> doModelAccessAnalysis(String parentModel, String currentModel,
                                                     Set<String> chain, Map<String, Set<String>> refModels,
                                                     Map<String, ModelDTO> modelMapping, List<ImportAnalysisFailedInfo> failedInfos) {
        if (chain.contains(currentModel)) {
            return Collections.emptySet();
        }
        Set<String> dependency = refModels.get(currentModel);
        if (dependency != null) {
            return dependency;
        }
        ModelDTO model = modelMapping.get(currentModel);
        if (model == null) {
            failedInfos.add(new ImportAnalysisFailedInfo().withItem(ImportItemEnum.MODEL).withValue(parentModel).withReason("Dependent model:" + currentModel + " does not exist"));
            dependency = Collections.emptySet();
        } else if (model.getRefModels() == null) {
            dependency = Collections.emptySet();
        } else {
            dependency = Sets.newHashSet();
            chain.add(currentModel);
            for (String refModel : model.getRefModels()) {
                refModel = model.getApiGroup() + "." + refModel;
                dependency.add(refModel);
                dependency.addAll(doModelAccessAnalysis(currentModel, refModel, chain, refModels, modelMapping, failedInfos));
            }
            chain.remove(currentModel);
        }
        refModels.put(currentModel, dependency);
        return dependency;
    }

    public static ImportFailedInfo findFailedImportItem(List<String> itemsToCreate,
                                                        List<String> itemsToOverride,
                                                        Map<String, com.yeepay.g3.facade.yop.sys.dto.imports.ImportFailedInfo> failedImportItems) {
        if (CollectionUtils.isNotEmpty(itemsToCreate)) {
            for (String name : itemsToCreate) {
                ImportFailedInfo failedInfo = failedImportItems.get(name);
                if (failedInfo != null) {
                    return failedInfo;
                }
            }
        }
        if (CollectionUtils.isNotEmpty(itemsToOverride)) {
            for (String modelName : itemsToOverride) {
                ImportFailedInfo failedInfo = failedImportItems.get(modelName);
                if (failedInfo != null) {
                    return failedInfo;
                }
            }
        }
        return null;
    }


    public static <Item> Map<ImportOperationEnum, List<Item>> filterImportItems(List<String> itemsToCreate,
                                                                                List<String> itemsToOverride,
                                                                                List<String> legalItemsToOverride,
                                                                                Set<String> successImportItems,
                                                                                Map<String, Item> items) {
        List<String> targetModelsToCreate = null, targetModelsToOverride = null;
        legalItemsToOverride = legalItemsToOverride == null ? Collections.emptyList() : legalItemsToOverride;
        if (CollectionUtils.isNotEmpty(itemsToCreate)) {
            targetModelsToCreate = new ArrayList<>(CollectionUtils.subtract(itemsToCreate, successImportItems));
        }
        if (CollectionUtils.isNotEmpty(itemsToOverride)) {
            targetModelsToOverride = new ArrayList<>(CollectionUtils.subtract(CollectionUtils.intersection(itemsToOverride, legalItemsToOverride), successImportItems));
        }
        return getImportItems(targetModelsToCreate, targetModelsToOverride, items);
    }

    private static <Item> Map<ImportOperationEnum, List<Item>> getImportItems(List<String> itemNamesToCreate, List<String> itemNamesToOverride, Map<String, Item> items) {
        List<Item> itemsToCreate = getItemsFromImportRequest(itemNamesToCreate, items);
        List<Item> itemsToOverride = getItemsFromImportRequest(itemNamesToOverride, items);
        if (CollectionUtils.isEmpty(itemsToCreate) && CollectionUtils.isEmpty(itemsToOverride)) {
            return null;
        }
        Map<ImportOperationEnum, List<Item>> result = Maps.newHashMapWithExpectedSize(2);
        if (CollectionUtils.isNotEmpty(itemsToCreate)) {
            result.put(ImportOperationEnum.CREATE, itemsToCreate);
        }
        if (CollectionUtils.isNotEmpty(itemsToOverride)) {
            result.put(ImportOperationEnum.OVERRIDE, itemsToOverride);
        }
        return result;
    }

    private static <Item> List<Item> getItemsFromImportRequest(List<String> itemNames, Map<String, Item> items) {
        List<Item> result = null;
        if (CollectionUtils.isNotEmpty(itemNames)) {
            result = Lists.newArrayListWithCapacity(itemNames.size());
            for (String itemName : itemNames) {
                Item item = items.get(itemName);
                if (item == null) {
                    throw new YeepayRuntimeException("item not provided,name:{0}.", itemName);
                }
                result.add(item);
            }
        }
        return result;
    }

    private static ApiChecker getApiChecker() {
        return ApiChecker.getInstance(SchemaTypeFormatUtils.getSchemaTypeFormatChecker(), SystemParameterUtils.getSystemParameters());
    }

    private static SpiChecker getSpiChecker() {
        return SpiChecker.getInstance();
    }

    private static ModelChecker getModelChecker() {
        return ModelChecker.getInstance(SchemaTypeFormatUtils.getSchemaTypeFormatChecker());
    }

}
