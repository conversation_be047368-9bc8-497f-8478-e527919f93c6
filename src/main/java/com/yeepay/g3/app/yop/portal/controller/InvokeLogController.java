/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.exception.YopPortalRateLimiterException;
import com.yeepay.g3.app.yop.portal.service.InvokeLogQueryService;
import com.yeepay.g3.app.yop.portal.vo.InvokeLogRequestParamVO;
import com.yeepay.g3.app.yop.portal.vo.InvokeLogVO;
import com.yeepay.g3.app.yop.portal.vo.page.InvokeLogPageQueryParam;
import com.yeepay.g3.boot.autoconfigure.ratelimiter.annotations.GuavaRateLimiter;
import com.yeepay.g3.core.yop.utils.time.DateFormatUtil;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateStrategyEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Map;

import static com.yeepay.g3.core.yop.utils.time.DateFormatUtil.PATTERN_DEFAULT_ON_SECOND;

@Controller
@RequestMapping("/rest/invoke-logging")
public class InvokeLogController {

    private static Logger LOGGER = LoggerFactory.getLogger(InvokeLogController.class);

    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";

    private static final long MAX_TIME = 1L * 24 * 60 * 60 * 1000;

    @Autowired
    private InvokeLogQueryService invokeLogQueryService;

    private static final Map<String, String> authenticateStrategyMap = Maps.newHashMap();

    static {
        for (AuthenticateStrategyEnum strategy : AuthenticateStrategyEnum.values()) {
            authenticateStrategyMap.put(strategy.getValue(), strategy.getValue());
        }
    }

    @GuavaRateLimiter(key = "'yop-portal:invoke-logging:lock:' + #operatorCode", permitsPerSecond = 1.0 / 20, exception = YopPortalRateLimiterException.class)
    @RequestMapping(value = "list", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage list(@RequestParam(value = "apiUri", required = false) String apiUri,
                                @RequestParam(value = "appKey", required = false) String appKey,
                                @RequestParam(value = "apiGroup", required = false) String apiGroup,
                                @RequestParam(value = "requestStartDate") String requestStartDate,
                                @RequestParam(value = "requestEndDate") String requestEndDate,
                                @RequestParam(value = "requestId", required = false) String requestId,
                                @RequestParam(value = "requestIp", required = false) String requestIp,
                                @RequestParam(value = "dataCenter", required = false) String dataCenter,
                                @RequestParam(value = "securityStrategy", required = false) String securityStrategy,
                                @RequestParam(value = "status", required = false) String status,
                                @RequestParam(value = "errorCode", required = false) String errorCode,
                                @RequestParam(value = "guid", required = false) String guid,
                                @RequestParam(value = "bizOrderNo", required = false) String bizOrderNo,
                                @RequestParam(value = "subErrorCode", required = false) String subErrorCode,
                                @RequestParam(value = "minBackendLatency", required = false) Integer minBackendLatency,
                                @RequestParam(value = "maxBackendLatency", required = false) Integer maxBackendLatency,
                                @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        InvokeLogPageQueryParam param = InvokeLogPageQueryParam.Builder.anInvokeLogPageQueryParam()
                .withApiUri(apiUri)
                .withAppKey(appKey)
                .withApiGroupCode(apiGroup)
                .withRequestId(requestId)
                .withRequestIp(requestIp)
                .withDataCenter(dataCenter)
                .withGuid(guid)
                .withBizOrderNo(bizOrderNo)
                .withStatus(status)
                .withErrorCode(errorCode)
                .withSubErrorCode(subErrorCode)
                .withPageNo(pageNo)
                .withPageSize(pageSize)
                .withMinBackendLatency(minBackendLatency)
                .withMaxBackendLatency(maxBackendLatency)
                .build();
        try {
            Date start = DateFormatUtil.parseDate(PATTERN_DEFAULT_ON_SECOND, requestStartDate);
            param.setRequestStartDate(start);
            Date end = DateFormatUtil.parseDate(PATTERN_DEFAULT_ON_SECOND, requestEndDate);
            param.setRequestEndDate(end);
            if (end.getTime() - start.getTime() > MAX_TIME) {
                return new ResponseMessage("time range is too long;");
            }
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(start);
            int startDay = calendar.get(Calendar.DATE);
            calendar.setTime(end);
            int endDay = calendar.get(Calendar.DATE);
            if (startDay != endDay) {
                return new ResponseMessage("mast be the same day!");
            }
            if (StringUtils.isNotBlank(securityStrategy)) {
                securityStrategy = securityStrategy.replaceAll("-", "_");
                param.setSecurityStrategy(AuthenticateStrategyEnum.parse(securityStrategy));
            }
            return new ResponseMessage("page", invokeLogQueryService.pageQuery(param));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when query invoke log list with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @RequestMapping(value = "detail", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage detail(@RequestParam(value = "requestDatetime") String requestDatetime,
                                  @RequestParam(value = "invokeLogId") Long id) {
        InvokeLogRequestParamVO param = new InvokeLogRequestParamVO();
        param.setId(id);
        SimpleDateFormat sdf = new SimpleDateFormat(PATTERN);
        Date date;
        try {
            date = sdf.parse(requestDatetime);
        } catch (ParseException e) {
            LOGGER.error("Exception occurred when query invoke log list with requestDatetime:" + requestDatetime, e);
            return new ResponseMessage(e);
        }
        param.setRequestDatetime(date);
        try {
            InvokeLogVO invokeLogVO = invokeLogQueryService.detailQuery(param);
            return new ResponseMessage("result", invokeLogVO);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when query invoke log detail with param:" + param, e);
            return new ResponseMessage(e);
        }
    }
}
