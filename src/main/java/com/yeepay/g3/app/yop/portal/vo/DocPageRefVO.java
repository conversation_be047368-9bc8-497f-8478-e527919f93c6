package com.yeepay.g3.app.yop.portal.vo;

/**
 * title: 页面引用下拉列表VO<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-19 11:34
 */
public class DocPageRefVO extends BaseVO {

    private static final long serialVersionUID = -1L;

    private Long id;
    private String title;
    private String location;
    private String accessUrl;
    private String docTitle;

    public DocPageRefVO() {
    }

    public DocPageRefVO(Long id, String title, String location, String accessUrl, String docTitle) {
        this.id = id;
        this.title = title;
        this.location = location;
        this.accessUrl = accessUrl;
        this.docTitle = docTitle;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getAccessUrl() {
        return accessUrl;
    }

    public void setAccessUrl(String accessUrl) {
        this.accessUrl = accessUrl;
    }

    public String getDocTitle() {
        return docTitle;
    }

    public void setDocTitle(String docTitle) {
        this.docTitle = docTitle;
    }

    public DocPageRefVO(Long id, String title, String location, String accessUrl) {
        this(id, title, location, accessUrl, null);
    }
}
