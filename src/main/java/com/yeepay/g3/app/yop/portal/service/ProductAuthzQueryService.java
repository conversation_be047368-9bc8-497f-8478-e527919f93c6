/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.ProductAuthzPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ProductAuthzPageQueryParam;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/6 下午5:57
 */
public interface ProductAuthzQueryService {

    PageQueryResult<ProductAuthzPageItem> pageQuery(ProductAuthzPageQueryParam param);
}
