package com.yeepay.g3.app.yop.portal.vo.page;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 12:00
 */
public class ErrcodeApiPageItem implements Serializable {
    private static final long serialVersionUID = -1L;

    private Long id;

    private String apiId;

    private String apiUri;

    private String httpMethod;

    private boolean isOldApi;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApiId() {
        return apiId;
    }

    public void setApiId(String apiId) {
        this.apiId = apiId;
    }

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public String getHttpMethod() {
        return httpMethod;
    }

    public void setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
    }

    public boolean isOldApi() {
        return isOldApi;
    }

    public void setOldApi(boolean oldApi) {
        isOldApi = oldApi;
    }
}
