/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ApiRouteQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.page.ApiRouteDeployRecordPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ApiRouteDeployRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.ApiRouteListItem;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/11/5 17:49
 */
@Component
public class ApiRouteQueryServiceImpl implements ApiRouteQueryService {

    private final PageItemConverter<ApiRouteDeployRecordPageItem> deployRecordPageItemConverter = new ApiRouteDeployRecordPageItemConverter();
    @Resource(name = "apiRouteQueryService")
    private QueryService queryService;

    @Override
    public List<ApiRouteListItem> queryForApiList(String apiId) {
        List<Map<String, Object>> apiRoutes = queryService.query("queryForApiList", Collections.singletonMap("apiId", apiId));
        List<ApiRouteListItem> res = new ArrayList<>();
        for (int i = 0; i < apiRoutes.size(); i++) {
            res.add(convertToApiRouteList(apiRoutes.get(i)));
        }
        return res;
    }

    @Override
    public PageQueryResult<ApiRouteDeployRecordPageItem> pageQueryForDeploy(ApiRouteDeployRecordQueryParam param) {
        Map<String, Object> bizParams = getBizParamsForDeploy(param);
        List<Map> list = queryService.query("pageQueryForDeploy", bizParams);
        PageQueryResult<ApiRouteDeployRecordPageItem> result = new PageQueryResult<>();
        List<ApiRouteDeployRecordPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(deployRecordPageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    private ApiRouteListItem convertToApiRouteList(Map<String, Object> map) {
        return new ApiRouteListItem()
                .id((Long) map.get("id"))
                .name(ObjectUtils.isEmpty(map.get("name")) ? "默认路由" : ((String) map.get("name")))
                .serviceName((String) map.get("service_name"))
                .status((String) map.get("status"))
                .type((String) map.get("type"));
    }

    private Map<String, Object> getBizParamsForDeploy(ApiRouteDeployRecordQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiId", param.getApiId());
        bizParams.put("createdEndDate", param.getCreatedEndDate());
        bizParams.put("createdStartDate", param.getCreatedStartDate());
        bizParams.put("operator", param.getOperator());
        bizParams.put("opType", param.getOpType());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class ApiRouteDeployRecordPageItemConverter extends BasePageItemConverter<ApiRouteDeployRecordPageItem> {

        @Override
        public ApiRouteDeployRecordPageItem convert(Map<String, Object> params) {
            ApiRouteDeployRecordPageItem item = new ApiRouteDeployRecordPageItem();
            item.setCause((String) params.get("cause"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setId((Long) params.get("id"));
            item.setOperator((String) params.get("operator"));
            item.setOpType((String) params.get("op_type"));
            return item;
        }
    }
}
