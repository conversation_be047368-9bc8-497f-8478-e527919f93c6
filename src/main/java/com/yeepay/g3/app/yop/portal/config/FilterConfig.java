package com.yeepay.g3.app.yop.portal.config;

import com.yeepay.g3.app.yop.portal.filter.OptionsFilter;
import com.yeepay.g3.app.yop.portal.filter.RefusePostWrapperFilter;
import com.yeepay.g3.app.yop.portal.filter.RequestWrapperFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.DelegatingFilterProxy;

import javax.servlet.DispatcherType;
import javax.servlet.Filter;

/**
 * title: <br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/1/3 下午6:12
 */
@Configuration
public class FilterConfig {

    @Bean
    public FilterRegistrationBean shiroFilterProxyRegistrationBean() {
        FilterRegistrationBean<Filter> filterRegistration = new FilterRegistrationBean<Filter>();
        DelegatingFilterProxy proxy = new DelegatingFilterProxy();
        proxy.setTargetFilterLifecycle(true);
        proxy.setTargetBeanName("shiroFilter");
        filterRegistration.setFilter(proxy);
        filterRegistration.addUrlPatterns("/*");
        filterRegistration.setAsyncSupported(true);
        filterRegistration.setEnabled(true);
        filterRegistration.setOrder(4);
        filterRegistration.setDispatcherTypes(DispatcherType.REQUEST, DispatcherType.ASYNC);
        return filterRegistration;
    }

    @Bean
    public FilterRegistrationBean requestWrapperFilterRegistrationBean(RequestWrapperFilter requestWrapperFilter) {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(requestWrapperFilter);
        registration.addUrlPatterns("/*");
        registration.setName("requestWrapperFilter");
        registration.setOrder(3);
        return registration;
    }

    @Bean
    public FilterRegistrationBean refusePostWrapperFilterRegistrationBean(RefusePostWrapperFilter refusePostWrapperFilter) {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(refusePostWrapperFilter);
        registration.addUrlPatterns("/*");
        registration.setName("refusePostWrapperFilter");
        registration.setOrder(2);
        return registration;
    }

    @Bean
    public FilterRegistrationBean optionsFilterRegistrationBean(OptionsFilter optionsFilter) {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(optionsFilter);
        registration.addUrlPatterns("/*");
        registration.setName("optionsFilter");
        registration.setOrder(1);
        return registration;
    }

}
