/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/4 18:45
 */
@Data
public class CfcaCertVO implements Serializable {

    private static final long serialVersionUID = -1L;

    @ApiModelProperty("商编")
    private String customerNo;

    @ApiModelProperty("序列号")
    private String serialNo;

    @ApiModelProperty("dn")
    private String dn;

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("算法")
    private String keyAlg;

    @ApiModelProperty("申请时间")
    private String applyDate;

    @ApiModelProperty("生效时间")
    private String effectiveDate;

    @ApiModelProperty("过期时间")
    private String expiredDate;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("是否被使用")
    private boolean used;
}
