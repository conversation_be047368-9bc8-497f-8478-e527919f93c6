# YOP Portal 数据流图

## 1. 数据流概述

YOP Portal系统中的数据流描述了系统中数据的流动路径和处理过程。本文档展示了系统中的主要数据流，包括系统级数据流、详细数据流、数据实体流转和数据存储。通过数据流图，可以清晰地了解系统中数据的来源、处理过程和最终去向。

## 2. 系统级数据流

### 2.1 整体数据流图

```mermaid
graph LR
    Client[客户端] --> |请求数据| Portal[YOP Portal]
    Portal --> |处理请求| Service[业务服务]
    Service --> |读写数据| Storage[数据存储]
    Storage --> |返回数据| Service
    Service --> |处理结果| Portal
    Portal --> |响应数据| Client
    
    Service --> |发送消息| MQ[消息队列]
    MQ --> |消费消息| Consumer[消息消费者]
    Consumer --> |处理消息| Storage
```

### 2.2 主要数据流向

```mermaid
graph TB
    subgraph 输入数据流
        ClientReq[客户端请求] --> WebUI[Web界面]
        WebUI --> Controller[控制器]
        ApiGateway[API网关] --> |API调用| Service[服务]
    end
    
    subgraph 处理数据流
        Controller --> Service
        Service --> DAO[数据访问对象]
        Service --> Cache[缓存]
        Service --> MQ[消息队列]
    end
    
    subgraph 输出数据流
        DAO --> DB[(数据库)]
        Cache --> Redis[(Redis)]
        MQ --> RabbitMQ[(RabbitMQ)]
        Service --> Controller
        Controller --> WebUI
        WebUI --> ClientResp[客户端响应]
    end
```

## 3. 详细数据流

### 3.1 API管理数据流

```mermaid
graph TB
    subgraph 输入
        ApiReq[API请求] --> ApiController[API控制器]
    end
    
    subgraph 处理
        ApiController --> ApiService[API服务]
        ApiService --> ApiDAO[API数据访问对象]
        ApiService --> ApiCache[API缓存]
        ApiService --> ApiMQ[API消息队列]
    end
    
    subgraph 输出
        ApiDAO --> DB[(数据库)]
        ApiCache --> Redis[(Redis)]
        ApiMQ --> RabbitMQ[(RabbitMQ)]
        ApiService --> ApiController
        ApiController --> ApiResp[API响应]
    end
    
    subgraph 数据实体
        ApiEntity[API实体]
        ApiGroupEntity[API分组实体]
        ApiVersionEntity[API版本实体]
        ApiParamEntity[API参数实体]
    end
    
    ApiDAO --> ApiEntity & ApiGroupEntity & ApiVersionEntity & ApiParamEntity
```

### 3.2 认证授权数据流

```mermaid
graph TB
    subgraph 输入
        AuthReq[认证请求] --> AuthController[认证控制器]
    end
    
    subgraph 处理
        AuthController --> AuthService[认证服务]
        AuthService --> AuthDAO[认证数据访问对象]
        AuthService --> AuthCache[认证缓存]
    end
    
    subgraph 输出
        AuthDAO --> DB[(数据库)]
        AuthCache --> Redis[(Redis)]
        AuthService --> AuthController
        AuthController --> AuthResp[认证响应]
    end
    
    subgraph 数据实体
        UserEntity[用户实体]
        RoleEntity[角色实体]
        PermissionEntity[权限实体]
        TokenEntity[Token实体]
    end
    
    AuthDAO --> UserEntity & RoleEntity & PermissionEntity & TokenEntity
```

### 3.3 应用管理数据流

```mermaid
graph TB
    subgraph 输入
        AppReq[应用请求] --> AppController[应用控制器]
    end
    
    subgraph 处理
        AppController --> AppService[应用服务]
        AppService --> AppDAO[应用数据访问对象]
        AppService --> AppCache[应用缓存]
        AppService --> AppMQ[应用消息队列]
    end
    
    subgraph 输出
        AppDAO --> DB[(数据库)]
        AppCache --> Redis[(Redis)]
        AppMQ --> RabbitMQ[(RabbitMQ)]
        AppService --> AppController
        AppController --> AppResp[应用响应]
    end
    
    subgraph 数据实体
        AppEntity[应用实体]
        AppKeyEntity[应用密钥实体]
        AppPermEntity[应用权限实体]
    end
    
    AppDAO --> AppEntity & AppKeyEntity & AppPermEntity
```

### 3.4 统计监控数据流

```mermaid
graph TB
    subgraph 输入
        ApiLog[API调用日志] --> MQ[消息队列]
        StatReq[统计请求] --> StatController[统计控制器]
    end
    
    subgraph 处理
        MQ --> StatService[统计服务]
        StatController --> StatService
        StatService --> StatDAO[统计数据访问对象]
        StatService --> StatCache[统计缓存]
    end
    
    subgraph 输出
        StatDAO --> DB[(数据库)]
        StatCache --> Redis[(Redis)]
        StatService --> StatController
        StatController --> StatResp[统计响应]
    end
    
    subgraph 数据实体
        ApiStatEntity[API统计实体]
        AppStatEntity[应用统计实体]
        PerfStatEntity[性能统计实体]
    end
    
    StatDAO --> ApiStatEntity & AppStatEntity & PerfStatEntity
```

## 4. 数据实体流转

### 4.1 API实体流转

```mermaid
stateDiagram-v2
    [*] --> 创建
    创建 --> 编辑: 更新API
    编辑 --> 编辑: 继续编辑
    编辑 --> 审核: 提交审核
    审核 --> 编辑: 审核不通过
    审核 --> 发布: 审核通过
    发布 --> 下线: 下线API
    下线 --> 编辑: 重新编辑
    下线 --> [*]: 删除API
```

### 4.2 应用实体流转

```mermaid
stateDiagram-v2
    [*] --> 创建
    创建 --> 审核: 提交审核
    审核 --> 创建: 审核不通过
    审核 --> 激活: 审核通过
    激活 --> 禁用: 禁用应用
    禁用 --> 激活: 重新激活
    激活 --> [*]: 删除应用
    禁用 --> [*]: 删除应用
```

### 4.3 用户实体流转

```mermaid
stateDiagram-v2
    [*] --> 注册
    注册 --> 未激活: 创建用户
    未激活 --> 激活: 激活账号
    激活 --> 锁定: 多次登录失败
    激活 --> 禁用: 管理员禁用
    锁定 --> 激活: 解锁账号
    禁用 --> 激活: 管理员激活
    激活 --> [*]: 删除用户
    禁用 --> [*]: 删除用户
```

## 5. 数据存储

### 5.1 数据库存储

YOP Portal系统使用多个数据库进行数据存储，主要包括：

- `SOAYOP`：主数据库，存储核心业务数据
- `YOP_SYS_READ`：只读数据库，用于查询操作
- `MONITOR_TIDB_READ`：监控数据库，存储监控统计数据

### 5.2 缓存存储

系统使用Redis和EhCache进行缓存存储，主要包括：

- Redis：分布式缓存，存储会话信息、热点数据等
- EhCache：本地缓存，存储频繁访问的数据

### 5.3 消息队列存储

系统使用RabbitMQ进行消息队列存储，主要包括：

- API调用日志队列
- 统计数据队列
- 通知消息队列

## 6. 数据安全

### 6.1 数据加密

敏感数据在存储和传输过程中进行加密处理，主要包括：

- 用户密码：使用不可逆加密算法（如bcrypt）
- 应用密钥：使用可逆加密算法（如AES）
- 传输数据：使用HTTPS加密

### 6.2 数据访问控制

系统实现了严格的数据访问控制，主要包括：

- 基于角色的访问控制（RBAC）
- 数据行级权限控制
- API访问权限控制

### 6.3 数据备份与恢复

系统实现了完善的数据备份与恢复机制，主要包括：

- 定时全量备份
- 增量备份
- 数据恢复机制

## 7. 数据一致性

### 7.1 事务管理

系统使用事务管理确保数据一致性，主要包括：

- 本地事务：使用Spring事务管理
- 分布式事务：使用TCC事务模型

### 7.2 缓存一致性

系统实现了缓存一致性机制，主要包括：

- 缓存更新策略：先更新数据库，再更新缓存
- 缓存失效策略：设置合理的过期时间
- 缓存同步机制：使用消息队列进行缓存同步

### 7.3 最终一致性

对于非关键业务，系统采用最终一致性策略，主要包括：

- 异步处理：使用消息队列进行异步处理
- 重试机制：失败任务自动重试
- 补偿机制：定时任务进行数据补偿
