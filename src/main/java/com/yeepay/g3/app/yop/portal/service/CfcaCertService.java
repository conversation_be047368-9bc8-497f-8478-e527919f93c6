/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.CfcaCertParamVO;
import com.yeepay.g3.app.yop.portal.vo.CfcaCertVO;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/5 18:45
 */
public interface CfcaCertService {

    /**
     * 吊销
     *
     * @param serialNo
     */
    void revoke(String serialNo);

    /**
     * 列表查询
     *
     * @param param
     * @return
     */
    List<CfcaCertVO> list(CfcaCertParamVO param);
}
