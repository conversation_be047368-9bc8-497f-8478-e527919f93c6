# yop定制镜像，内含图片压缩软件，升级时须注意
FROM artifact.paas.yp/sys-docker-base-local/alpine-jdk:1.8.0_261-v1.1

ADD Dockerfile Dockerfile

ADD yop-portal/yop-portal.jar /apps/app.jar

ADD yop-portal/runtimecfg /apps/commoncfg/runtimecfg

ADD log4j2.xml /apps/commoncfg/log4j2.xml

ADD server-client.jks /var/yeepay/ra/server-client.jks
ADD yeepay-client.jks /var/yeepay/ra/yeepay-client.jks
ADD YOP_OAUTH2_KEY.txt /var/yeepay/yop/YOP_OAUTH2_KEY.txt

EXPOSE 8080

EXPOSE 8088

ADD operateFile.py gracefulstop.sh replaceConfigs.py startup.sh entrypoint.sh /

RUN chmod 755 /gracefulstop.sh /replaceConfigs.py /operateFile.py /startup.sh /entrypoint.sh

ENTRYPOINT ["/entrypoint.sh"]
