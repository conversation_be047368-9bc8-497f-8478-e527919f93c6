/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.authc.filter;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.app.yop.portal.shiro.authc.TenantToken;
import com.yeepay.g3.app.yop.portal.shiro.utils.TenantTokenUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.web.filter.authc.AuthenticatingFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Map;

/**
 * title: TenantAuthorizationFilter<br>
 * description: 租户认证拦截器<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/7/26 8:16 下午
 */
@Component
@Slf4j
public class TenantAuthorizationFilter extends AuthenticatingFilter implements CustomShiroFilter {
    private final static String TICKET = "ticket";

    private static final String LOGIN_URL_PREFIX = "/signin/sso/tenant";

    @Override
    public String shiroName() {
        return "tenant";
    }

    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) {
        return genTenantToken(request);
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        if (isTenantLogin(request)) {
            return executeLogin(request, response);
        }
        return true;
    }

    @SneakyThrows
    @Override
    protected boolean onLoginFailure(AuthenticationToken token, AuthenticationException e, ServletRequest request, ServletResponse response) {
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;
        final String redirectUrl = buildCasRedirectUrl(request);
        log.info("tenant login fail and redirect to url:{}", redirectUrl);
        httpServletResponse.sendRedirect(redirectUrl);
        return super.onLoginFailure(token, e, request, response);
    }

    private String buildCasRedirectUrl(ServletRequest request) throws UnsupportedEncodingException {
        String tenantCasUrl = null;
        String tenantCode = WebUtils.getCleanParam(request, "tenant");
        if (StringUtils.isNotEmpty(tenantCode)) {
            tenantCasUrl = TenantTokenUtils.getTenantQueryFacade().findConfig(tenantCode).getIspCasUrl();
        }
        tenantCasUrl = StringUtils.defaultIfEmpty(tenantCasUrl, (String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_TENANT_LOGIN_URL));

        Map<String, String[]> paramMap = request.getParameterMap();
        if (MapUtils.isEmpty(paramMap)) {
            return tenantCasUrl;
        }
        StringBuilder params = new StringBuilder();
        for (Map.Entry<String, String[]> entry : paramMap.entrySet()) {
            params.append(entry.getKey()).append("=").append(URLEncoder.encode(entry.getValue()[0], "UTF-8")).append("&");
        }

        return tenantCasUrl + "?" + params.substring(0, params.length() - 1);
    }


    private TenantToken genTenantToken(ServletRequest request) {
        String token = WebUtils.getCleanParam(request, TICKET);
        return new TenantToken(token);
    }

    private boolean isTenantLogin(ServletRequest request) {
        return (request instanceof HttpServletRequest) && WebUtils.toHttp(request).getRequestURI().startsWith(LOGIN_URL_PREFIX);
    }
}
