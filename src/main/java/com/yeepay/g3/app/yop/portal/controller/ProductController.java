/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.google.common.collect.Lists;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.AttachmentService;
import com.yeepay.g3.app.yop.portal.service.DocQueryService;
import com.yeepay.g3.app.yop.portal.service.ProductChangesQueryService;
import com.yeepay.g3.app.yop.portal.service.ProductQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.validation.group.ProductDelete;
import com.yeepay.g3.app.yop.portal.validation.group.ProductEdit;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.page.ProductChangePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.ProductPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ProductPageQueryParam;
import com.yeepay.g3.facade.yop.doc.dto.AttachmentUploadRequest;
import com.yeepay.g3.facade.yop.doc.enums.DocCategoryTypeEnum;
import com.yeepay.g3.facade.yop.doc.enums.StoreTypeEnum;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.enums.ProductOperateTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.ProductStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.ProductTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.ProductFacade;
import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/5 上午10:08
 */
@Controller
@RequestMapping("/rest/product")
@Slf4j
public class ProductController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductController.class);

    private ProductFacade productFacade = RemoteServiceFactory.getService(ProductFacade.class);

    @Autowired
    private ProductQueryService productQueryService;

    @Autowired
    private ProductChangesQueryService productChangesQueryService;

    @Autowired
    private DocQueryService docQueryService;

    @Autowired
    private AttachmentService attachmentService;

    @ResponseBody
    @GetMapping(value = "/category/find")
    public ResponseMessage find(@RequestParam String productCode) {
        try {
            Map<String, String> categories = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_DOCKING_PRODUCT_CATEGORY);
            if (MapUtils.isEmpty(categories)) {
                log.info("未配置对接产品的分类信息, productCode:{}", productCode);
                return new ResponseMessage("result", new ProductDockingCategoryVO());
            }
            ProductDockingCategoryVO vo = new ProductDockingCategoryVO();
            categories.forEach((k, v) -> {
                if (productCode.equals(k)) {
                    Map<String, String> map = JsonMapper.toMap(v);
                    String categoryCode = map.get("categoryCode");
                    vo.setCategoryCode(categoryCode);
                    vo.setCategoryName(map.get("categoryName"));
                    String pCategoryCode = map.get("pCategoryCode");
                    vo.setPCategoryCode(pCategoryCode);
                    vo.setPCategoryName(map.get("pCategoryName"));

                    DocCategoryQueryParam queryParam = new DocCategoryQueryParam()
                            .setCode(categoryCode)
                            .setParentCodes(Arrays.asList(pCategoryCode))
                            .setType(DocCategoryTypeEnum.DOC.name());
                    DocCategoryVO docCategoryVO = docQueryService.findCategoryByParentCode(queryParam);
                    if (null != docCategoryVO) {
                        vo.setId(docCategoryVO.getId());
                        vo.setPid(docCategoryVO.getPid());
                    }
                }
            });
            return new ResponseMessage("result", vo);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when find product category with code:" + productCode, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@Validated ProductPageQueryParam param,
                                @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "_pageSize", required = false) Integer pageSize
    ) {
        try {
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("product");
            param.setPageSize(appPageSize.intValue());
            return new ResponseMessage("page", productQueryService.pageQuery(param));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list product with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "list/for-authz", method = RequestMethod.GET)
    public ResponseMessage listForAuthz(@Validated ProductPageQueryParam param) {
        try {
            param.setStatus(ProductStatusEnum.NORMAL);
            param.setPageNo(1);
            param.setPageSize(Integer.MAX_VALUE);
            final List<ProductPageItem> items = productQueryService.pageQuery(param).getItems();
            final List<ProductBasicVo> result = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(items)) {
                items.forEach(item -> result.add(new ProductBasicVo(item.getCode(), item.getName())));
            }
            return new ResponseMessage("result", result);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list product for authz with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/change/list", method = RequestMethod.GET)
    public ResponseMessage change(@Validated ProductChangePageQueryParam param,
                                  @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                  @RequestParam(value = "_pageSize", required = false) Integer pageSize) {
        try {
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("product-changes");
            param.setPageSize(appPageSize.intValue());
            return new ResponseMessage("page", productChangesQueryService.pageQuery(param));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list product with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(ProductVO productVO) {
        Map<String, String> cephConfig = null;
        AttachmentUploadRequest uploadRequest = null;
        try {
            ProductDTO productDTO = new ProductDTO();
            BeanUtils.copyProperties(productVO, productDTO);
            ProductCreateRequest request = new ProductCreateRequest(ShiroUtils.getOperatorCode(), productVO.getCause());
            request.setProduct(productDTO);
            productFacade.create(request);
            if (ProductTypeEnum.DOCKING.getValue().equals(productVO.getType().getValue()) && null != productVO.getIcon()) {
                CheckUtils.notNull(productVO.getIcon(), "icon");
                AttachmentReqVo attachmentReqVo = new AttachmentReqVo();
                attachmentReqVo.setFileName(productVO.getCode());
                attachmentReqVo.setBizCode(productVO.getCode());
                attachmentReqVo.setStoreType(StoreTypeEnum.CEPH);
                attachmentReqVo.setFileStream(productVO.getIcon());
                attachmentService.create(cephConfig, uploadRequest, attachmentReqVo, false, "product_icon");
            }
            return new ResponseMessage();
        } catch (IOException e) {
            LOGGER.error("error when upload icon with bizCode:" + productVO.getCode() + ".", e);
            attachmentService.deleteCephFile(cephConfig, uploadRequest);
            return new ResponseMessage(e);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when create product with param:" + productVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public ResponseMessage edit(ProductVO productVO) {
        Map<String, String> cephConfig = null;
        AttachmentUploadRequest uploadRequest = null;
        try {
            ProductDTO productDTO = new ProductDTO();
            BeanUtils.copyProperties(productVO, productDTO);
            ProductUpdateRequest request = new ProductUpdateRequest(ShiroUtils.getOperatorCode(), productVO.getCause());
            request.setProduct(productDTO);
            productFacade.update(request);
            if (ProductTypeEnum.DOCKING.getValue().equals(productVO.getType().getValue()) && null != productVO.getIcon()) {
                AttachmentReqVo attachmentReqVo = new AttachmentReqVo();
                attachmentReqVo.setFileName(productVO.getCode());
                attachmentReqVo.setBizCode(productVO.getCode());
                attachmentReqVo.setStoreType(StoreTypeEnum.CEPH);
                attachmentReqVo.setFileStream(productVO.getIcon());
                attachmentService.create(cephConfig, uploadRequest, attachmentReqVo, false, "product_icon");
            }
            return new ResponseMessage();
        } catch (IOException ex) {
            LOGGER.error("error when modify icon.", ex);
            attachmentService.deleteCephFile(cephConfig, uploadRequest);
            return new ResponseMessage(ex);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when edit product with param:" + productVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam String code) {
        try {
            final ProductDTO product = productFacade.find(code);
            if (null != product) {
                ProductVO productVO = new ProductVO();
                BeanUtils.copyProperties(product, productVO);
                return new ResponseMessage("result", productVO);
            } else {
                throw new YeepayRuntimeException("product not exists, code[{0}]", code);
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred when create product with param:" + code, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/offline", method = RequestMethod.POST)
    public ResponseMessage offline(@RequestBody @Validated(ProductEdit.class) ProductVO productVO) {
        try {
            ProductOfflineRequest request = new ProductOfflineRequest(ShiroUtils.getOperatorCode(), productVO.getCause());
            request.setCode(productVO.getCode());
            productFacade.offline(request);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when offline product with param:" + productVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@Validated(ProductDelete.class) ProductVO productVO, @RequestParam(required = false) boolean force) {
        try {
            ProductDeleteRequest request = new ProductDeleteRequest(ShiroUtils.getOperatorCode(), productVO.getCause());
            request.setCode(productVO.getCode());
            request.setForce(force);
            productFacade.delete(request);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when delete product with param:" + productVO + ", force:" + force, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/normal", method = RequestMethod.POST)
    public ResponseMessage normal(@RequestBody @Validated(ProductEdit.class) ProductVO productVO) {
        try {
            ProductNormalRequest request = new ProductNormalRequest(ShiroUtils.getOperatorCode(), productVO.getCause());
            request.setCode(productVO.getCode());
            productFacade.normal(request);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when normal product with param:" + productVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/type", method = RequestMethod.GET)
    public ResponseMessage type() {
        List<CommonsVO> list = new ArrayList<>();
        ProductTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/status", method = RequestMethod.GET)
    public ResponseMessage status() {
        List<CommonsVO> list = new ArrayList<>();
        ProductStatusEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/oper-type", method = RequestMethod.GET)
    public ResponseMessage operType() {
        List<CommonsVO> list = new ArrayList<>();
        ProductOperateTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/list-for-select", method = RequestMethod.GET)
    public ResponseMessage productListForSelect() {
        try {
            final List<ProductSimpleVO> spProducts = productQueryService.spProducts();
            return new ResponseMessage("result", spProducts);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when productListForSelect, ex:", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/list-all-normal", method = RequestMethod.GET)
    public ResponseMessage productListAllProducts() {
        try {
            final List<ProductSimpleVO> spProducts = productQueryService.allNormalProducts();
            return new ResponseMessage("result", spProducts);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when productListAllProducts, ex:", e);
            return new ResponseMessage(e);
        }
    }
}
