/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.cache.ApiSwaggerLocalCache;
import com.yeepay.g3.app.yop.portal.vo.ApiSwaggerLocalCacheKey;
import com.yeepay.g3.boot.web.exception.enums.CommonResponseEnum;
import com.yeepay.g3.boot.web.pojo.response.BaseResponse;
import com.yeepay.g3.boot.web.pojo.response.ErrorResponse;
import com.yeepay.g3.boot.web.pojo.response.R;
import com.yeepay.g3.core.yop.utils.Exceptions;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * title: 公共服务<br>
 * description: 不需要权限校验就可以访问<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/8 6:08 PM
 */
@RestController
@RequestMapping("/apis/commons")
@Slf4j
public class CommonsController {

    @Autowired
    private ApiSwaggerLocalCache apiSwaggerLocalCache;

    @RequestMapping(value = "/api/swagger", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse apiSwagger(@RequestParam("path") String path,
                                   @RequestParam("httpMethod") String httpMethod) {
        try {
            CheckUtils.notEmpty(path, "path");
            CheckUtils.notEmpty(httpMethod, "httpMethod");
            String data = apiSwaggerLocalCache.get(new ApiSwaggerLocalCacheKey(path, httpMethod));
            if (StringUtils.isNotEmpty(data)) {
                return new R<>(data);
            }
            return new ErrorResponse(CommonResponseEnum.SERVER_ERROR.getCode(), "API不存在");
        } catch (Exception ex) {
            log.error("query api swagger failed.", ex);
            return new ErrorResponse(CommonResponseEnum.SERVER_ERROR.getCode(),
                    Exceptions.getRootCause(ex).getMessage());
        }
    }
}
