package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yeepay.g3.facade.yop.perm.dto.AclResourceAuthTreeDTO;
import com.yeepay.g3.facade.yop.perm.enums.RoleTypeEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午6:03
 */
public class AclRoleVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    @JsonProperty("code")
    private String roleCode;

    @JsonProperty("name")
    private String roleName;

    @JsonProperty("type")
    private RoleTypeEnum roleType;

    private String spName;

    private String spCode;

    private List<AclResourceAuthTreeDTO> resources;

    private Long version;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public RoleTypeEnum getRoleType() {
        return roleType;
    }

    public void setRoleType(RoleTypeEnum roleType) {
        this.roleType = roleType;
    }

    public String getSpName() {
        return spName;
    }

    public void setSpName(String spName) {
        this.spName = spName;
    }

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public List<AclResourceAuthTreeDTO> getResources() {
        return resources;
    }

    public void setResources(List<AclResourceAuthTreeDTO> resources) {
        this.resources = resources;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
