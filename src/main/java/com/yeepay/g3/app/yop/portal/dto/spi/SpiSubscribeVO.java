/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.dto.spi;

import com.yeepay.g3.app.yop.portal.vo.DestinationVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/7 18:15
 */
@Data
public class SpiSubscribeVO implements Serializable {
    private static final long serialVersionUID = -1L;
    private String spiName;
    private List<DestinationVO> destinations;
}
