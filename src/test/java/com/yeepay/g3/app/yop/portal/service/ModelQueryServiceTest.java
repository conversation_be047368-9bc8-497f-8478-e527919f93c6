/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import com.yeepay.g3.app.yop.portal.vo.ModelVO;
import com.yeepay.g3.utils.common.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/5 5:52 下午
 */
public class ModelQueryServiceTest extends SpringBootTests {
    @Autowired
    private ModelQueryService modelQueryService;

    @Test
    public void testSimpleList() {
        String apiGroup = "test-wdc";
        List<ModelVO> modelVOS = modelQueryService.simpleList(apiGroup);
        Assert.assertTrue(CollectionUtils.isNotEmpty(modelVOS));
        modelVOS.forEach(modelVO -> {
            Assert.assertNotNull(modelVO.getId());
        });
        System.out.println();
    }

    /**
     * 模型是否已存在 测试
     */
    @Test
    public void testExist() {
        String name = "ApWithdraw";
        String apiGroup = "airpay";
        Assert.assertTrue(modelQueryService.exist(name, apiGroup));

        Assert.assertFalse(modelQueryService.exist("ErrorName", apiGroup));
    }

}
