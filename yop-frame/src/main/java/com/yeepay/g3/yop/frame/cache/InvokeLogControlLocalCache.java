/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.frame.cache;

import com.yeepay.g3.event.yop.sys.invokelog.InvokeLogControlChangeEvent;
import com.yeepay.g3.facade.yop.sys.dto.InvokeLogControlDTO;
import com.yeepay.g3.facade.yop.sys.enums.InvokeLogControlTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.InvokeLogControlFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.yop.frame.cache.key.InvokeLogControlCacheKey;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/1/17 上午9:57
 */
@Component
public class InvokeLogControlLocalCache extends AbstractRefreshableLocalCache<InvokeLogControlCacheKey, Integer> {

    private static final Logger LOGGER = LoggerFactory.getLogger(InvokeLogControlLocalCache.class);

    private InvokeLogControlFacade invokeLogControlFacade = RemoteServiceFactory.getService(InvokeLogControlFacade.class, DEFAULT_SOA_CONFIG);

    @Override
    protected int getMaxSize() {
        return 1000;
    }

    @Override
    protected int getRefreshDuration() {
        return 86741;
    }

    @Override
    protected YopCacheLoader<InvokeLogControlCacheKey, Integer> getCacheLoader() {
        return new YopCacheLoader<InvokeLogControlCacheKey, Integer>() {
            @Override
            protected Integer doLoad(InvokeLogControlCacheKey key) throws Exception {
                InvokeLogControlDTO dto = new InvokeLogControlDTO();
                dto.setApiGroupCode(key.getApiGroupCode());
                dto.setControlType(InvokeLogControlTypeEnum.API_GROUP);
                dto = invokeLogControlFacade.findByCondition(dto);
                if (null != dto) {
                    return dto.getControlStrategy();
                }
                return null;
            }

            @Override
            protected String getCacheName() {
                return "invoke_log_control_cache";
            }
        };
    }

    @EventListener
    public void onEvent(InvokeLogControlChangeEvent event) {
        LOGGER.info("invoke log control change event received, event:{}.", event);
        refreshIfAbsent(new InvokeLogControlCacheKey(event.getApiGroup(), null));
    }
}
