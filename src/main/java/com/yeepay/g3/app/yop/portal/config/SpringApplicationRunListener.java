/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.app.yop.portal.config;

import com.yeepay.g3.app.yop.portal.utils.SpringContextUtil;
import org.springframework.boot.context.event.ApplicationPreparedEvent;
import org.springframework.context.ApplicationListener;

/**
 * title: <b>
 * description: 描述<br>¬
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/12 10:28 PM
 */
public class SpringApplicationRunListener implements ApplicationListener<ApplicationPreparedEvent> {

    @Override
    public void onApplicationEvent(ApplicationPreparedEvent event) {
        SpringContextUtil.setApplicationContext(event.getApplicationContext());
    }

}
