package com.yeepay.g3.yop.frame.protocol.request.decrypt;

import com.yeepay.g3.facade.yop.sys.enums.CertTypeEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.frame.context.EncryptContext;
import com.yeepay.g3.yop.frame.context.ParamContext;
import com.yeepay.g3.yop.frame.definition.authenticate.SecretKey;
import com.yeepay.g3.yop.frame.exception.authentication.EncryptionKeyUnavailableException;
import com.yeepay.g3.yop.frame.exception.authentication.RequestDescryptFailedException;
import com.yeepay.g3.yop.frame.exception.authentication.UnSupportedEncryptAlgException;
import com.yeepay.g3.yop.frame.security.EncryptAlgEnum;
import com.yeepay.g3.yop.frame.security.encryptor.Encryptor;
import com.yeepay.g3.yop.frame.security.encryptor.EncryptorFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * title: 参数解密抽象类<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-04-19 18:29
 */
public abstract class AbstractParamDecryptor<Context extends ParamContext> implements ParamDecryptor<Context> {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ParamDecryptorFactory paramDecryptorFactory;

    @Override
    public void decrypt(Context context, EncryptContext encryptContext, List<SecretKey> secretKeys) {
        if (CertTypeEnum.AES256 != encryptContext.getEncryptCertType()) {
            throw new UnSupportedEncryptAlgException();
        }
        Encryptor encryptor = EncryptorFactory.getEncryptor(EncryptAlgEnum.valueOf(encryptContext.getEncryptCertType().getValue()));
        int attempts = 0;
        for (SecretKey secretKey : secretKeys) {
            attempts++;
            try {
                doDecrypt(context, encryptor, secretKey);
                encryptContext.setEncryptKey(secretKey);
                return;
            } catch (Throwable ex) {
                logger.warn("decrypt paramContext failure", ex);
            }
        }
        if (attempts > 0) {
            throw new EncryptionKeyUnavailableException();
        } else {
            throw new RequestDescryptFailedException();
        }
    }

    protected abstract void doDecrypt(Context context, Encryptor encryptor, SecretKey secretKey) throws Throwable;

    @PostConstruct
    public void init() {
        paramDecryptorFactory.register(this);
    }
}
