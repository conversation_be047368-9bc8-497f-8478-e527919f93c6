/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.filter;

import com.yeepay.boot.components.utils.NetworkUtils;
import com.yeepay.g3.yop.ext.gateway.filter.AbstractGlobalFilter;
import com.yeepay.g3.yop.frame.cache.PlatformBlacklistRefreshableLocalCache;
import com.yeepay.g3.yop.frame.definition.BlacklistLimitRules;
import com.yeepay.g3.yop.frame.exception.limit.BlackListLimitedException;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils.GATEWAY_SOURCE_IP_ATTR;

/**
 * title: 平台黑名单拦截过滤器<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/8 6:05 下午
 */
@Slf4j
@Component
public class GatewayPlatformBlackListFilter extends AbstractGlobalFilter implements Ordered {
    private PlatformBlacklistRefreshableLocalCache platformBlacklistRefreshableLocalCache;

    public final static int ORDER = -99;

    @Override
    public void filter(ServerWebExchange exchange) {
        log.debug("调用黑名单过滤器");
        BlacklistLimitRules blacklistLimitRules = platformBlacklistRefreshableLocalCache.get("YOP");
        if (!blacklistLimitRules.isSignVerification()) {
            log.error("sign verification failed for blacklist");
            throw new BlackListLimitedException();
        }
        List<String> blacklist = blacklistLimitRules.getBlacklist();
        String sourceIp = exchange.getRequiredAttribute(GATEWAY_SOURCE_IP_ATTR);
        if (CollectionUtils.isNotEmpty(blacklist) && StringUtils.isNotEmpty(sourceIp) && inBlackList(sourceIp, blacklist)) {
            throw new BlackListLimitedException();
        }

    }

    @Override
    public int getOrder() {
        return ORDER;
    }

    @Override
    public boolean supportBackup() {
        return true;
    }

    private boolean inBlackList(String sourceIp, List<String> blacklist) {
        for (String ip : blacklist) {
            if (NetworkUtils.isInSubnet(sourceIp, ip)) {
                return true;
            }
        }
        return false;
    }

    @Autowired
    public void setPlatformBlacklistRefreshableLocalCache(PlatformBlacklistRefreshableLocalCache platformBlacklistRefreshableLocalCache) {
        this.platformBlacklistRefreshableLocalCache = platformBlacklistRefreshableLocalCache;
    }
}
