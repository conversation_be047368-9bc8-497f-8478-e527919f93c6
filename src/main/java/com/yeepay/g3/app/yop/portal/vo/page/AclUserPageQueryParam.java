package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.perm.enums.OperatorPositionEnum;
import com.yeepay.g3.facade.yop.perm.enums.OperatorStatusEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午5:40
 */
public class AclUserPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 用户状态
     */
    private OperatorStatusEnum status;

    /**
     * 用户职位
     */
    private OperatorPositionEnum position;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public OperatorStatusEnum getStatus() {
        return status;
    }

    public void setStatus(OperatorStatusEnum status) {
        this.status = status;
    }

    public OperatorPositionEnum getPosition() {
        return position;
    }

    public void setPosition(OperatorPositionEnum position) {
        this.position = position;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public static final class Builder {
        private Integer pageNo;
        private Integer pageSize;
        private String name;
        private OperatorStatusEnum status;
        private OperatorPositionEnum position;

        private Builder() {
        }

        public static Builder anAclUserPageQueryParam() {
            return new Builder();
        }


        public Builder withPageNo(Integer pageNo) {
            this.pageNo = pageNo;
            return this;
        }

        public Builder withPageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder withName(String name) {
            this.name = name;
            return this;
        }

        public Builder withStatus(OperatorStatusEnum status) {
            this.status = status;
            return this;
        }

        public Builder withPosition(OperatorPositionEnum position) {
            this.position = position;
            return this;
        }

        public AclUserPageQueryParam build() {
            AclUserPageQueryParam aclUserPageQueryParam = new AclUserPageQueryParam();
            aclUserPageQueryParam.setPageNo(pageNo);
            aclUserPageQueryParam.setPageSize(pageSize);
            aclUserPageQueryParam.setName(name);
            aclUserPageQueryParam.setPosition(position);
            aclUserPageQueryParam.setStatus(status);
            return aclUserPageQueryParam;
        }
    }

}
