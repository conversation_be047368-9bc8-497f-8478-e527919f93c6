/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.filter.param;

import com.yeepay.g3.yop.ext.gateway.context.param.ParamContext;
import com.yeepay.g3.yop.ext.gateway.filter.GateWayFilterOrder;
import com.yeepay.g3.yop.ext.gateway.filter.GlobalFilter;
import com.yeepay.g3.yop.gateway.filter.param.databind.ParamBinderFactory;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/12 6:44 下午
 */
@Component
@Slf4j
public class RequestParamMappingFilter implements GlobalFilter, Ordered {
    @Override
    public boolean isEnabled(ServerWebExchange exchange) {
        if (ServerWebExchangeUtils.internalExceptionHappened(exchange)) {
            return false;
        }

        if (ServerWebExchangeUtils.getApiContext(exchange).isApiV2() || ServerWebExchangeUtils.getApiContext(exchange).isGeneric()) {
            return false;
        }

        return true;
    }

    @Override
    public void filter(ServerWebExchange exchange) {
        log.debug("调用参数映射过滤器");
        ParamContext paramContext = ServerWebExchangeUtils.getParamContext(exchange);
        ParamBinderFactory.get(paramContext.getContentType()).bindParams(exchange);
    }

    @Override
    public int getOrder() {
        return GateWayFilterOrder.GatewayPlatformBlackListFilter_ORDER + 650;
    }
}
