/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.ext.gateway.douyin.config;

import com.yeepay.g3.yop.ext.gateway.filter.definition.FilterDefinition;
import com.yeepay.g3.yop.ext.gateway.route.definition.RouteDefinition;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * title: 抖音网关配置<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/1
 */
@Configuration
public class DouyinExtConfiguration {

    @Bean
    public RouteDefinition douyinRouteDefinition() {
        return RouteDefinition.builder()
                .httpMethod("*")
                .uri("douyin")
                .filter(new FilterDefinition("RequestFileUpload=douyin"))
                .filter(new FilterDefinition("RequestAuthc=douyin"))
                .filter(new FilterDefinition("ResponseEncrypt=douyin"))
                .build();
    }

}
