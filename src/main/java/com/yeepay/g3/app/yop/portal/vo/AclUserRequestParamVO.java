package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午6:57
 */
public class AclUserRequestParamVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private OperatorTypeEnum type;

    private List<String> optCodes;

    private List<String> roleCodes;

    public OperatorTypeEnum getType() {
        return type;
    }

    public void setType(OperatorTypeEnum type) {
        this.type = type;
    }

    public List<String> getOptCodes() {
        return optCodes;
    }

    public void setOptCodes(List<String> optCodes) {
        this.optCodes = optCodes;
    }

    public List<String> getRoleCodes() {
        return roleCodes;
    }

    public void setRoleCodes(List<String> roleCodes) {
        this.roleCodes = roleCodes;
    }

    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
