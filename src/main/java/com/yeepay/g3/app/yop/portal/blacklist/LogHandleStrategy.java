/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.blacklist;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * title: LogHandleStrategy<br>
 * description: 被拦截后打印日志<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/6/15 5:38 下午
 */
@Component
@Slf4j
public class LogHandleStrategy implements HandleStrategy {
    private final String name = "LOG";

    @Override
    public void handle(String appId, String url) {
        log.error("appId:{} notifyUrl:{} in blacklist,level is LOG", appId, url);
    }

    @Override
    public String name() {
        return name;
    }

    @PostConstruct
    public void register() {
        HandleStrategyFactory.register(this);
    }
}
