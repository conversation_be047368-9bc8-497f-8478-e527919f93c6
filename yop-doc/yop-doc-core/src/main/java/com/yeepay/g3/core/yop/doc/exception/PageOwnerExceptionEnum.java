package com.yeepay.g3.core.yop.doc.exception;

import com.yeepay.g3.facade.yop.doc.exceptions.PageOwnerException;

/**
 * 页面负责人管理异常枚举
 * 
 * <AUTHOR>
 * @since 2025-01-26
 */
public enum PageOwnerExceptionEnum {

    // 页面相关异常 (200001-200099)
    PAGE_NOT_FOUND("200001", "页面不存在，页面ID: {0}"),
    PAGE_ID_INVALID("200002", "页面ID无效: {0}"),
    
    // 负责人相关异常 (200101-200199)
    OWNER_NOT_FOUND("200101", "负责人不存在，负责人ID: {0}"),
    OWNER_ID_INVALID("200102", "负责人ID无效: {0}"),
    OWNER_NAME_INVALID("200103", "负责人姓名无效: {0}"),
    OWNER_ALREADY_EXISTS("200104", "负责人已存在，页面ID: {0}, 负责人ID: {1}"),
    OWNER_LIST_EMPTY("200105", "负责人列表不能为空"),
    OWNER_LIST_TOO_LARGE("200106", "负责人数量超过限制，最大允许: {0}"),
    
    // 数据库操作异常 (200201-200299)
    DATABASE_OPERATION_FAILED("200201", "数据库操作失败: {0}"),
    CONCURRENT_UPDATE_CONFLICT("200202", "数据已被其他用户修改，请刷新后重试"),
    TRANSACTION_ROLLBACK("200203", "事务回滚: {0}"),
    DUPLICATE_KEY_ERROR("200204", "数据重复，页面ID: {0}, 负责人ID: {1}"),
    
    // 权限相关异常 (200301-200399)
    PERMISSION_DENIED("200301", "没有权限执行此操作"),
    INSUFFICIENT_PRIVILEGES("200302", "权限不足，无法修改页面负责人"),
    
    // 参数验证异常 (200401-200499)
    PARAMETER_VALIDATION_FAILED("200401", "参数验证失败: {0}"),
    REQUIRED_PARAMETER_MISSING("200402", "必需参数缺失: {0}"),
    PARAMETER_FORMAT_INVALID("200403", "参数格式无效: {0}"),
    
    // 系统异常 (200501-200599)
    SYSTEM_ERROR("200501", "系统内部错误"),
    SERVICE_UNAVAILABLE("200502", "服务暂时不可用"),
    TIMEOUT_ERROR("200503", "操作超时"),
    
    // 业务逻辑异常 (200601-200699)
    BUSINESS_RULE_VIOLATION("200601", "违反业务规则: {0}"),
    INVALID_OPERATION("200602", "无效操作: {0}"),
    OPERATION_NOT_ALLOWED("200603", "不允许的操作: {0}");

    private final String code;
    private final String message;

    PageOwnerExceptionEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 格式化异常消息
     */
    public String formatMessage(Object... args) {
        String formattedMessage = message;
        for (int i = 0; i < args.length; i++) {
            formattedMessage = formattedMessage.replace("{" + i + "}", String.valueOf(args[i]));
        }
        return formattedMessage;
    }

    /**
     * 创建异常实例
     */
    public PageOwnerException createException(Object... args) {
        return new PageOwnerException(formatMessage(args));
    }

    /**
     * 创建异常实例（带原因）
     */
    public PageOwnerException createException(Throwable cause, Object... args) {
        return new PageOwnerException(formatMessage(args), cause);
    }
}