package com.yeepay.g3.app.yop.portal.git;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * title: 分支<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-12 14:34
 */
public class GitBranch implements Serializable {

    private static final long serialVersionUID = -1L;

    private String name;

    private Boolean merged;

    @JsonProperty("protected")
    private Boolean _protected;

    @JsonProperty("default")
    private Boolean _default;

    @JsonProperty("developers_can_push")
    private Boolean developersCanPush;

    @JsonProperty("developers_can_merge")
    private Boolean developersCanMerge;

    @JsonProperty("can_push")
    private Boolean canPush;

    private GitCommit commit;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getMerged() {
        return merged;
    }

    public void setMerged(Boolean merged) {
        this.merged = merged;
    }

    public Boolean getProtected() {
        return _protected;
    }

    public void setProtected(Boolean _protected) {
        this._protected = _protected;
    }

    public Boolean getDefault() {
        return _default;
    }

    public void setDefault(Boolean _default) {
        this._default = _default;
    }

    public Boolean getDevelopersCanPush() {
        return developersCanPush;
    }

    public void setDevelopersCanPush(Boolean developersCanPush) {
        this.developersCanPush = developersCanPush;
    }

    public Boolean getDevelopersCanMerge() {
        return developersCanMerge;
    }

    public void setDevelopersCanMerge(Boolean developersCanMerge) {
        this.developersCanMerge = developersCanMerge;
    }

    public Boolean getCanPush() {
        return canPush;
    }

    public void setCanPush(Boolean canPush) {
        this.canPush = canPush;
    }

    public GitCommit getCommit() {
        return commit;
    }

    public void setCommit(GitCommit commit) {
        this.commit = commit;
    }
}
