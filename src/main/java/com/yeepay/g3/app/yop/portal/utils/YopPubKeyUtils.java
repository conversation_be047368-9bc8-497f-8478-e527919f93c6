package com.yeepay.g3.app.yop.portal.utils;

import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.sdk.yop.encrypt.CertTypeEnum;
import com.yeepay.g3.sdk.yop.utils.RSAKeyUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;

import java.security.PublicKey;
import java.util.Map;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2016<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 19/05/23 15:00
 */
public class YopPubKeyUtils {

    private static volatile PublicKey publicKey;

    private static volatile Map<String, String> yopPublicKey;

    private YopPubKeyUtils() {
    }

    public static PublicKey getInstance() {
        if (publicKey == null) {
            synchronized (YopPubKeyUtils.class) {
                if (publicKey == null) {
                    Map<String, String> yopPublicKey = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PUBLIC_KEY);
                    try {
                        publicKey = RSAKeyUtils.string2PublicKey(yopPublicKey.get(CertTypeEnum.RSA2048.getValue()));
                    } catch (Throwable ex) {
                        throw new YeepayRuntimeException("yop public key not set", ex);
                    }
                }
            }
        }
        return publicKey;
    }

    public static Map<String, String> getYopPublicKey() {
        if (yopPublicKey == null || yopPublicKey.size() == 0) {
            synchronized (YopPubKeyUtils.class) {
                if (yopPublicKey == null || yopPublicKey.size() == 0) {
                    try {
                        yopPublicKey = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PUBLIC_KEY);
                    } catch (Throwable ex) {
                        throw new YeepayRuntimeException("yop public key not set", ex);
                    }
                }
            }
        }
        return yopPublicKey;
    }

}
