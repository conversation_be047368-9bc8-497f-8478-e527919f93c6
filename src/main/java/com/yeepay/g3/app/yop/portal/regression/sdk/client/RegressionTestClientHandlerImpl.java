/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.regression.sdk.client;

import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestRequest;
import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestResponse;
import com.yeepay.g3.app.yop.portal.utils.YopPubKeyUtils;
import com.yeepay.g3.core.yop.sdk.sample.auth.AuthorizationReq;
import com.yeepay.g3.core.yop.sdk.sample.auth.AuthorizationReqSupport;
import com.yeepay.g3.core.yop.sdk.sample.auth.SignerSupport;
import com.yeepay.g3.core.yop.sdk.sample.auth.cipher.DefaultEncryptor;
import com.yeepay.g3.core.yop.sdk.sample.auth.credentials.YopRSACredentials;
import com.yeepay.g3.core.yop.sdk.sample.client.ClientExecutionParams;
import com.yeepay.g3.core.yop.sdk.sample.client.ClientHandler;
import com.yeepay.g3.core.yop.sdk.sample.client.support.ClientConfigurationSupport;
import com.yeepay.g3.core.yop.sdk.sample.config.AppSdkConfig;
import com.yeepay.g3.core.yop.sdk.sample.config.SDKConfig;
import com.yeepay.g3.core.yop.sdk.sample.config.support.SDKConfigUtils;
import com.yeepay.g3.core.yop.sdk.sample.http.ExecutionContext;
import com.yeepay.g3.core.yop.sdk.sample.http.YopHttpClient;
import com.yeepay.g3.core.yop.sdk.sample.http.YopHttpClientFactory;
import com.yeepay.g3.core.yop.sdk.sample.internal.Request;
import com.yeepay.g3.core.yop.sdk.sample.model.BaseRequest;
import com.yeepay.g3.core.yop.sdk.sample.model.BaseResponse;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.net.URI;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/19 下午5:40
 */
public class RegressionTestClientHandlerImpl implements ClientHandler {

    private final URI endpoint;

    private final URI yosEndPoint;

    private final YopHttpClient client;

    public RegressionTestClientHandlerImpl(String configString) {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(configString.getBytes());
        SDKConfig sdkConfig = SDKConfigUtils.loadConfig(byteArrayInputStream);
        AppSdkConfig appSdkConfig = AppSdkConfig.Builder.anAppSdkConfig().withSDKConfig(sdkConfig).build();

        endpoint = URI.create(appSdkConfig.getServerRoot());
        yosEndPoint = URI.create(appSdkConfig.getYosServerRoot());
        client = YopHttpClientFactory.getClient(ClientConfigurationSupport.getClientConfiguration(appSdkConfig));
    }

    @Override
    public <Input extends BaseRequest, Output extends BaseResponse> Output execute(ClientExecutionParams<Input, Output> executionParams) {
        ClientExecutionParams<RegressionTestRequest, RegressionTestResponse> regressionExecutionParams = (ClientExecutionParams<RegressionTestRequest, RegressionTestResponse>) executionParams;
        RegressionTestRequest input = regressionExecutionParams.getInput();
        ExecutionContext executionContext = getExecutionContext(regressionExecutionParams);
        Request<RegressionTestRequest> request = regressionExecutionParams.getRequestMarshaller().marshall(input);
        if (request.isYosRequest()) {
            request.setEndpoint(yosEndPoint);
        } else {
            request.setEndpoint(endpoint);
        }

        return client.execute(request, request.getOriginalRequestObject().getRequestConfig(),
                executionContext,
                executionParams.getResponseHandler());
    }

    private ExecutionContext getExecutionContext(
            ClientExecutionParams<RegressionTestRequest, RegressionTestResponse> executionParams) {
        AuthorizationReq authorizationReq = AuthorizationReqSupport.getAuthorizationReq("YOP-RSA2048-SHA256");
        ExecutionContext.Builder builder = ExecutionContext.Builder.anExecutionContext()
                .withSigner(SignerSupport.getSigner(authorizationReq.getSignerType()))
                .withSignOptions(authorizationReq.getSignOptions())
                .withYopCredentials(executionParams.getInput().getRequestConfig().getCredentials())
                .withYopPublicKey(YopPubKeyUtils.getInstance());
        YopRSACredentials yopRSACredentials = (YopRSACredentials) (executionParams.getInput().getRequestConfig().getCredentials());
        String encryptKey = yopRSACredentials.getEncryptKey();
        if (StringUtils.isNotEmpty(encryptKey)) {
            builder.withEncryptor(new DefaultEncryptor(yopRSACredentials));
            return builder.build();
        } else {
            return builder.build();
        }
    }

    @Override
    public void shutdown() {
        client.shutdown();
    }

}
