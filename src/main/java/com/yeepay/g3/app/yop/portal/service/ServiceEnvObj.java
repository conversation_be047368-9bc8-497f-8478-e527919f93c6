/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * title: 服务环境信息对象<br>
 * description: 用于描述应用服务的环境部署信息<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/10/16 10:53 上午
 */
@Data
public class ServiceEnvObj {
    /**
     * 应用名称
     */
    private String application;

    /**
     * 服务（类）名称
     */
    private String service;

    /**
     * 服务提供方结构为
     * {"environment":"","methods":[""]}
     */
    private List<Map<String, Object>> providers;
}
