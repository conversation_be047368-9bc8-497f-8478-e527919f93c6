/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.frame.service;

import com.yeepay.g3.yop.frame.dto.SessionKey;

import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/12/3 3:08 下午
 */
public interface YopSecureLayerService {

    void saveIsvKey(String keyId, String keyType, String key, Date expiration);

    void deleteIsvKey(String keyId);

    SessionKey findIsvKey(String keyId);

    String findYopKey(String keyType);
}
