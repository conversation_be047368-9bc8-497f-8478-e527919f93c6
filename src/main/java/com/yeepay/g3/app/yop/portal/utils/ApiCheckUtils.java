package com.yeepay.g3.app.yop.portal.utils;

import com.yeepay.g3.utils.common.CheckUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 15:24
 */
public class ApiCheckUtils {

    private static final Pattern API_URI_PATTERN = Pattern.compile("^/(rest|yos)/v\\d+.\\d+/(([a-z0-9]|-)+)(/([a-z0-9]|-)+)+$");

    private static final int GROUP_CODE_INDEX = 2;

    public static void checkApiUri(String groupCode, String apiUri) {
        CheckUtils.notEmpty(apiUri, "apiUri");
        Matcher matcher = API_URI_PATTERN.matcher(apiUri);
        if (matcher.matches()) {
            if (!StringUtils.equals(groupCode, matcher.group(GROUP_CODE_INDEX))) {
                throw new IllegalArgumentException("Illegal Api Uri:groupCode not matches.");
            }
        } else {
            throw new IllegalArgumentException("Illegal Api Uri.");
        }
    }
}
