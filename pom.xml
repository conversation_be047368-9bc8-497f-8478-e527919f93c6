<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <parent>
        <groupId>com.yeepay.g3.boot</groupId>
        <artifactId>yeepay-boot-dependencies</artifactId>
        <version>2.3.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yeepay.g3.cloud.yop</groupId>
    <artifactId>yop-portal</artifactId>
    <version>1.0</version>

    <properties>
        <yop-codegen-facade.version>1.0.PORTAL</yop-codegen-facade.version>
        <yop-codegen-generator.version>1.0.PORTAL</yop-codegen-generator.version>
        <yop-monitor-facade.version>1.0-SNAPSHOT</yop-monitor-facade.version>
        <janus.version>1.1-SNAPSHOT</janus.version>
        <yop-frame.verison>1.0</yop-frame.verison>

        <hessian.version>4.3.3-yeepay</hessian.version>
        <yeepay-event.version>2.3.1.1-SNAPSHOT</yeepay-event.version>
        <yeepay-soa.version>2.26</yeepay-soa.version>
        <yeepay-rmi.version>2.11</yeepay-rmi.version>
        <yeepay-utils-common.version>4.2.1</yeepay-utils-common.version>
        <yeepay-thread-context.version>1.8</yeepay-thread-context.version>
        <object-storage-sdk.version>2.0.4-SNAPSHOT</object-storage-sdk.version>
        <yeepay-boot-starter-exception.version>2.5.0</yeepay-boot-starter-exception.version>
        <infra-log4j2.version>2.1.3</infra-log4j2.version>
        <log4j2.version>2.13.3</log4j2.version>
        <yeepay-log4j2-pattern.version>2.1.0</yeepay-log4j2-pattern.version>
        <hibernate-validator.version>5.3.6.Final</hibernate-validator.version>
        <druid.version>1.2.13</druid.version>
        <struggle-notifier-facade.version>1.3.0</struggle-notifier-facade.version>
        <yeepay-boot-starter-web.version>2.5.0-SNAPSHOT</yeepay-boot-starter-web.version>
        <protobuf-java.version>3.5.1</protobuf-java.version>
        <yeepay-boot-config.version>1.0-SNAPSHOT</yeepay-boot-config.version>

        <!--  spring-data-redis版本      -->
        <jedis.version>3.3.0</jedis.version>
        <fastjson.version>1.2.83</fastjson.version>

        <lombok.version>1.18.24</lombok.version>
        <mapstruct.version>1.5.0.Beta2</mapstruct.version>
        <mapstruct-lombok.version>0.2.0</mapstruct-lombok.version>
        <yeeworks-modeling.version>1.0.0-SNAPSHOT</yeeworks-modeling.version>
        <ra-toolkit.version>3.6.4.5</ra-toolkit.version>
        <yeepay-cache.version>2.0</yeepay-cache.version>
    </properties>

    <dependencies>
        <!--        老事件-->
        <dependency>
            <groupId>com.yeepay.boot.starters</groupId>
            <artifactId>yeepay-boot-mq-starter</artifactId>
            <version>2.6.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-cache</artifactId>
            <version>${yeepay-cache.version}</version>
        </dependency>
        <!--ra-->
        <dependency>
            <groupId>com.cfca</groupId>
            <artifactId>ra-toolkit</artifactId>
            <version>${ra-toolkit.version}</version>
        </dependency>
        <!--yeeworks-->
        <dependency>
            <groupId>com.yeepay.ng.yeeworks</groupId>
            <artifactId>yeeworks-modeling-client</artifactId>
            <version>${yeeworks-modeling.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.boot</groupId>
            <artifactId>yeepay-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yeepay.boot.components</groupId>
            <artifactId>yeepay-boot-config</artifactId>
            <version>${yeepay-boot-config.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--结果通知同步状态时会用到-->
        <dependency>
            <groupId>com.yeepay.g3.struggle.notifier</groupId>
            <artifactId>struggle-notifier-facade</artifactId>
            <version>${struggle-notifier-facade.version}</version>
        </dependency>

        <!--     yop子系统 自身依赖       -->
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-monitor-facade</artifactId>
            <version>${yop-monitor-facade.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-sys-facade</artifactId>
            <version>${yop-sys-facade.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yeepay.g3.event</groupId>
                    <artifactId>yeepay-event</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-sp-facade</artifactId>
            <version>${yop-sp-facade.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-ca-frame</artifactId>
            <version>${yop-ca-frame.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-ca-facade</artifactId>
            <version>${yop-ca-facade.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-notifier-facade</artifactId>
            <version>${yop-notifier-facade.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.janus</groupId>
            <artifactId>yop-doc-facade</artifactId>
            <version>${janus.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-frame</artifactId>
            <version>${yop-frame.verison}</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yeepay.g3.graphql</groupId>
                    <artifactId>yeepay-graphql-engine</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yeepay.g3.yop</groupId>
                    <artifactId>yop-ca-encryptor</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yeepay.g3.yop</groupId>
                    <artifactId>yop-ca-security</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yeepay.g3.yop</groupId>
                    <artifactId>yop-router</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yeepay.g3.yop</groupId>
                    <artifactId>yop-sys-event</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yeepay.janus</groupId>
            <artifactId>yop-doc-codegen-facade</artifactId>
            <version>${janus.version}</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-java-sdk</artifactId>
            <version>${yop-java-sdk.version}</version>
            <classifier>jdk18</classifier>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-codegen-generator</artifactId>
            <version>${yop-codegen-generator.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yeepay.g3.yop</groupId>
                    <artifactId>yop-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.yop</groupId>
            <artifactId>yop-oauth2-facade</artifactId>
        </dependency>
        <!--     yop子系统 自身依赖       -->

        <dependency>
            <groupId>com.yeepay.g3.cloud.yop</groupId>
            <artifactId>yop-utils</artifactId>
        </dependency>

        <dependency>
            <groupId>yeepay3g-app-boss</groupId>
            <artifactId>yeepay-boss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.hbird</groupId>
            <artifactId>hbird-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.employee</groupId>
            <artifactId>employee-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>object-storage</groupId>
            <artifactId>object-storage-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-core</artifactId>
        </dependency>

        <!-- Swagger UI 相关 -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.21</version>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.5.21</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>retrofit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-jackson</artifactId>
        </dependency>

        <!-- JWT -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jaxb-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jsonSchema</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>

        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${hibernate-validator.version}</version>
        </dependency>

        <!-- for jsp -->
        <!--<dependency>-->
        <!--<groupId>org.apache.tomcat.embed</groupId>-->
        <!--<artifactId>tomcat-embed-jasper</artifactId>-->
        <!--&lt;!&ndash; removed it lead to success! Amazing! &ndash;&gt;-->
        <!--&lt;!&ndash;<scope>provided</scope>&ndash;&gt;-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--<groupId>javax.servlet</groupId>-->
        <!--<artifactId>jstl</artifactId>-->
        <!--</dependency>-->

        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-thread-context</artifactId>
            <version>1.8</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-utils-common</artifactId>
            <version>4.2.1</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yeepay.infra</groupId>
                    <artifactId>yeepay-atlas-agent</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>yeepay-rmi</artifactId>
            <groupId>com.yeepay.g3.utils</groupId>
            <version>4.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.starter</groupId>
            <artifactId>yeepay-soa-starter</artifactId>
            <version>4.0.3.RELEASE</version>
        </dependency>
        <dependency>
            <artifactId>yeepay-soa</artifactId>
            <groupId>com.yeepay.g3.utils</groupId>
            <version>4.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.jboss.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
          <groupId>org.apache.zookeeper</groupId>
          <artifactId>zookeeper</artifactId>
          <version>3.4.8</version>
          <exclusions>
              <exclusion>
                  <artifactId>slf4j-log4j12</artifactId>
                  <groupId>org.slf4j</groupId>
              </exclusion>
          </exclusions>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.56.Final</version>
        </dependency>
        <dependency>
            <groupId>com.caucho</groupId>
            <artifactId>hessian</artifactId>
            <version>4.3.5-yeepay</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.53</version>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
            <version>3.28.0-GA</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.9.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>27.0.1-jre</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-framework</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-recipes</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-x-discovery</artifactId>
            <version>4.2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.curator</groupId>
            <artifactId>curator-client</artifactId>
            <version>4.2.0</version>
            <exclusions>
               <exclusion>
                  <artifactId>guava</artifactId>
                  <groupId>com.google.guava</groupId>
               </exclusion>
               <exclusion>
                  <artifactId>zookeeper</artifactId>
                  <groupId>org.apache.zookeeper</groupId>
               </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.9.7</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjrt</artifactId>
            <version>1.9.7</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>sentinel-soa-adapter</artifactId>
            <version>3.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.concurrentlinkedhashmap</groupId>
            <artifactId>concurrentlinkedhashmap-lru</artifactId>
            <version>1.4.2</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>fluent-log4j2-appender</artifactId>
             <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-log4j2-pattern</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>fluent-sender-base</artifactId>
            <version>2.1.4</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>stream-sender-api</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>metrics-agent-core</artifactId>
            <version>2.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-query</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ibm.db2</groupId>
            <artifactId>db2jcc4</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ibm.db2</groupId>
            <artifactId>db2jcc_license_cu</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-config</artifactId>
        </dependency>

        <dependency>
            <groupId>net.sf.dozer</groupId>
            <artifactId>dozer</artifactId>
        </dependency>

        <!-- Security begin -->
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-ehcache</artifactId>
        </dependency>
        <!-- Security end -->

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.5</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.1.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate.validator</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.53</version>
        </dependency>

        <!-- 验证码 -->
        <dependency>
            <groupId>org.patchca</groupId>
            <artifactId>patchca</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>infra-log4j2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.yeepay.g3.utils</groupId>
                    <artifactId>yeepay-log4j2-pattern</artifactId>
                </exclusion>
            </exclusions>
            <version>${infra-log4j2.version}</version>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.utils</groupId>
            <artifactId>yeepay-log4j2-pattern</artifactId>
            <version>2.1.0</version>
        </dependency>

        <!-- access-log -->
        <dependency>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>accesslog-valve</artifactId>
        </dependency>

        <!--<dependency>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-devtools</artifactId>-->
        <!--</dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Spring Cache start -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-cache</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.yeepay.g3.boot</groupId>
            <artifactId>yeepay-boot-starter-redissonlock</artifactId>
        </dependency>
        <!-- Spring Cache end -->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.yeepay.g3.mp</groupId>
            <artifactId>mp-facade</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.yeepay.g3.merchant</groupId>
                    <artifactId>merchant-platform-facade</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

        <!-- Mapstruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-jdk8</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <!-- Mapstruct End -->

    </dependencies>

    <build>
        <finalName>yop-portal</finalName>
        <plugins>
            <!-- 加入maven deploy插件，当在deploy时，忽略些model-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>

            <!--            <plugin>-->
            <!--                <groupId>org.apache.maven.plugins</groupId>-->
            <!--                <artifactId>maven-shade-plugin</artifactId>-->
            <!--                <version>3.2.2</version>-->
            <!--                <dependencies>-->
            <!--                    <dependency>-->
            <!--                        <groupId>org.springframework.boot</groupId>-->
            <!--                        <artifactId>spring-boot-maven-plugin</artifactId>-->
            <!--                        <version>${spring-boot.version}</version>-->
            <!--                    </dependency>-->
            <!--                </dependencies>-->
            <!--                <configuration>-->
            <!--                    &lt;!&ndash; 将所有不使用的类全部排除掉&ndash;&gt;-->
            <!--                    <minimizeJar>true</minimizeJar>-->
            <!--                    <keepDependenciesWithProvidedScope>true</keepDependenciesWithProvidedScope>-->
            <!--                    <createDependencyReducedPom>true</createDependencyReducedPom>-->
            <!--                    &lt;!&ndash; 打包时移除代码或者配置 &ndash;&gt;-->
            <!--                    <filters>-->
            <!--                        <filter>-->
            <!--                            <artifact>*:*</artifact>-->
            <!--                            <excludes>-->
            <!--                                <exclude>META-INF/*.SF</exclude>-->
            <!--                                <exclude>META-INF/*.DSA</exclude>-->
            <!--                                <exclude>META-INF/*.RSA</exclude>-->
            <!--                            </excludes>-->
            <!--                        </filter>-->
            <!--                        <filter>-->
            <!--                            <artifact>org.springframework.boot:**</artifact>-->
            <!--                            <includes>-->
            <!--                                <include>**</include>-->
            <!--                            </includes>-->
            <!--                        </filter>-->
            <!--                        <filter>-->
            <!--                            <artifact>com.baomidou:**</artifact>-->
            <!--                            <includes>-->
            <!--                                <include>**</include>-->
            <!--                            </includes>-->
            <!--                        </filter>-->
            <!--                        <filter>-->
            <!--                            <artifact>org.apache.dubbo:*autoconfigure*</artifact>-->
            <!--                            <includes>-->
            <!--                                <include>**</include>-->
            <!--                            </includes>-->
            <!--                        </filter>-->
            <!--                    </filters>-->
            <!--                    &lt;!&ndash; 打包时移除整个jar &ndash;&gt;-->
            <!--                    <artifactSet>-->
            <!--                        <excludes>-->
            <!--                            <exclude>classworlds:classworlds</exclude>-->
            <!--                            <exclude>junit:junit</exclude>-->
            <!--                            <exclude>jmock:*</exclude>-->
            <!--                            <exclude>*:xml-apis</exclude>-->
            <!--                            <exclude>org.apache.maven:lib:tests</exclude>-->
            <!--                            <exclude>log4j:log4j:jar:</exclude>-->
            <!--                        </excludes>-->
            <!--                    </artifactSet>-->
            <!--                </configuration>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <phase>package</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>shade</goal>-->
            <!--                        </goals>-->
            <!--                        <configuration>-->
            <!--                            <transformers>-->
            <!--                                <transformer-->
            <!--                                        implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">-->
            <!--                                    <resource>META-INF/spring.handlers</resource>-->
            <!--                                </transformer>-->
            <!--                                <transformer-->
            <!--                                        implementation="org.springframework.boot.maven.PropertiesMergingResourceTransformer">-->
            <!--                                    <resource>META-INF/spring.factories</resource>-->
            <!--                                </transformer>-->
            <!--                                <transformer-->
            <!--                                        implementation="org.apache.maven.plugins.shade.resource.AppendingTransformer">-->
            <!--                                    <resource>META-INF/spring.schemas</resource>-->
            <!--                                </transformer>-->
            <!--                                <transformer-->
            <!--                                        implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>-->
            <!--                                <transformer-->
            <!--                                        implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">-->
            <!--                                    <mainClass>${start-class}</mainClass>-->
            <!--                                </transformer>-->
            <!--                            </transformers>-->
            <!--                        </configuration>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->

            <!-- 主项目需要重新打包jar -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.1.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                            <goal>repackage</goal>
                        </goals>
                        <configuration>
                            <skip>false</skip>
                            <!--                            <includes>-->
                            <!--                                <include>-->
                            <!--                                    &lt;!&ndash; 排除所有Jar &ndash;&gt;-->
                            <!--                                    <groupId>nothing</groupId>-->
                            <!--                                    <artifactId>nothing</artifactId>-->
                            <!--                                </include>-->
                            <!--                            </includes>-->
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <fork>true</fork>
                    <layout>ZIP</layout><!-- 从 -Dloader.path 制定的路径读取jar和配置 -->
                    <mainClass>com.yeepay.g3.app.yop.portal.WebApplication</mainClass>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <failOnError>true</failOnError>
                    <verbose>true</verbose>
                    <fork>true</fork>
                    <compilerArgument>-nowarn</compilerArgument>
                    <compilerArgument>-XDignore.symbol.file</compilerArgument>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>UTF-8</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${mapstruct-lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>gpt</id>
            <properties>
                <yop-frame.verison>1.0-gpt</yop-frame.verison>
                <yop-monitor-facade.version>1.0-gpt-SNAPSHOT</yop-monitor-facade.version>
            </properties>
        </profile>
        <profile>
            <id>snp</id>
            <properties>
                <yop-sys-facade.version>1.1-SNAPSHOT</yop-sys-facade.version>
                <yop-sp-facade.version>2.1-SNAPSHOT</yop-sp-facade.version>
                <yop-ca-frame.version>1.0-SNAPSHOT</yop-ca-frame.version>
                <yop-ca-facade.version>1.0-SNAPSHOT</yop-ca-facade.version>
                <yop-notifier-facade.version>1.0-SNAPSHOT</yop-notifier-facade.version>
            </properties>
        </profile>
        <profile>
            <id>rls</id>
            <properties>
                <yop-sys-facade.version>1.1</yop-sys-facade.version>
                <yop-sp-facade.version>2.1</yop-sp-facade.version>
                <yop-ca-frame.version>1.0</yop-ca-frame.version>
                <yop-ca-facade.version>1.0</yop-ca-facade.version>
                <yop-notifier-facade.version>1.0</yop-notifier-facade.version>
            </properties>
        </profile>
    </profiles>
</project>
