package com.yeepay.g3.yop.frame.utils;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.yeepay.g3.facade.yop.api.enums.ApiParamDataFormatEnum;
import com.yeepay.g3.facade.yop.api.enums.ApiParamDataTypeEnum;
import io.swagger.models.properties.Property;
import org.apache.commons.lang3.ObjectUtils;

/**
 * title: Swagger工具类<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-12-13 11:46
 */
public class SwaggerUtils {

    private static final Table<String, String, ApiParamDataFormatEnum> FORMAT_MAPPING;

    static {
        FORMAT_MAPPING = HashBasedTable.create();
        FORMAT_MAPPING.put("integer", "int32", ApiParamDataFormatEnum.INTEGER);
        FORMAT_MAPPING.put("integer", "int64", ApiParamDataFormatEnum.LONG);
        FORMAT_MAPPING.put("number", "float", ApiParamDataFormatEnum.FLOAT);
        FORMAT_MAPPING.put("number", "double", ApiParamDataFormatEnum.DOUBLE);
        FORMAT_MAPPING.put("number", "", ApiParamDataFormatEnum.AMOUNT);
        FORMAT_MAPPING.put("string", "", ApiParamDataFormatEnum.STRING);
        FORMAT_MAPPING.put("string", "byte", ApiParamDataFormatEnum.BYTE);
        FORMAT_MAPPING.put("string", "binary", ApiParamDataFormatEnum.BINARY);
        FORMAT_MAPPING.put("string", "date", ApiParamDataFormatEnum.DATE);
        FORMAT_MAPPING.put("string", "date-time", ApiParamDataFormatEnum.DATETIME);
        FORMAT_MAPPING.put("string", "password", ApiParamDataFormatEnum.PASSWORD);
        FORMAT_MAPPING.put("boolean", "", ApiParamDataFormatEnum.BOOLEAN);
        FORMAT_MAPPING.put("boolean", "", ApiParamDataFormatEnum.BOOLEAN);

        FORMAT_MAPPING.put("array", "", ApiParamDataFormatEnum.ARRAY);
        FORMAT_MAPPING.put("object", "", ApiParamDataFormatEnum.OBJECT);
        FORMAT_MAPPING.put("file", "", ApiParamDataFormatEnum.FILE);
        FORMAT_MAPPING.put("ref", "", ApiParamDataFormatEnum.OBJECT);
    }

    /**
     * 获取api参数数据格式
     *
     * @param property property
     * @return 数据格式
     */
    public static ApiParamDataFormatEnum getApiParamDataFormat(Property property) {
        ApiParamDataFormatEnum dataFormat = FORMAT_MAPPING.get(property.getType(),
                ObjectUtils.defaultIfNull(property.getFormat(), ""));
        if (dataFormat == null) {
            ApiParamDataTypeEnum paramDataType = ApiParamDataTypeEnum.parse(property.getType().toUpperCase());
            if (paramDataType == ApiParamDataTypeEnum.REF) {
                paramDataType = ApiParamDataTypeEnum.OBJECT;
            }
            dataFormat = ApiParamDataFormatEnum.getDefaultFormat(paramDataType);
        }
        return dataFormat;
    }


}
