package com.yeepay.g3.yop.frame.exception;

import com.yeepay.g3.yop.frame.protocol.response.yop.v300rc1.exceptions.StatusEnum;

/**
 * title: 后端服务业务异常<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/1/25 下午5:16
 */
public class BackendBusinessException extends InternalException {

    private String code;

    private String message;

    public BackendBusinessException(Throwable throwable, String code) {
        super(throwable);
        this.code = code;
        this.message = throwable.getMessage();
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public StatusEnum getStatus() {
        return StatusEnum.BUSINESS_ERROR;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
