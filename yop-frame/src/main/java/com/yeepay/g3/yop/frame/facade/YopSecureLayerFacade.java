/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.frame.facade;


import com.yeepay.g3.yop.frame.dto.BreakHandshakeRequest;
import com.yeepay.g3.yop.frame.dto.BreakHandshakeResponse;
import com.yeepay.g3.yop.frame.dto.HandshakeRequest;
import com.yeepay.g3.yop.frame.dto.HandshakeResponse;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/12/3 3:30 下午
 */
public interface YopSecureLayerFacade {

    HandshakeResponse handshake(HandshakeRequest request);

    BreakHandshakeResponse breakHandshake(BreakHandshakeRequest request);
}
