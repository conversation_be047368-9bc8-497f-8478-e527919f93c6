package com.yeepay.g3.app.yop.portal.dto;

import com.yeepay.g3.app.yop.portal.controller.support.imports.ApiImportItem;
import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;
import com.yeepay.g3.facade.yop.sys.dto.SpiDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title: Api导入上下文<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-16 11:25
 */
public class ApiImportContext implements Serializable {

    private static final long serialVersionUID = 2250079399549392737L;

    private String apiGroup;

    private List<ApiImportItem> apisToCreate;

    private List<ApiImportItem> apisToOverride;

    private Map<String, SpiDTO> spis;

    private Map<String, List<ApiRequestKey>> callbackApiRelations;

    private Map<String, ModelDTO> models;

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    public ApiImportContext withApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public List<ApiImportItem> getApisToCreate() {
        return apisToCreate;
    }

    public void setApisToCreate(List<ApiImportItem> apisToCreate) {
        this.apisToCreate = apisToCreate;
    }

    public ApiImportContext addApisToCreateItem(ApiImportItem item) {
        if (this.apisToCreate == null) {
            this.apisToCreate = new ArrayList<>();
        }
        this.apisToCreate.add(item);
        return this;
    }

    public List<ApiImportItem> getApisToOverride() {
        return apisToOverride;
    }

    public void setApisToOverride(List<ApiImportItem> apisToOverride) {
        this.apisToOverride = apisToOverride;
    }

    public ApiImportContext addApiToOverrideItem(ApiImportItem item) {
        if (this.apisToOverride == null) {
            this.apisToOverride = new ArrayList<>();
        }
        this.apisToOverride.add(item);
        return this;
    }

    public Map<String, SpiDTO> getSpis() {
        return spis;
    }

    public void setSpis(Map<String, SpiDTO> spis) {
        this.spis = spis;
    }

    public ApiImportContext withSpis(Map<String, SpiDTO> spis) {
        this.spis = spis;
        return this;
    }

    public Map<String, List<ApiRequestKey>> getCallbackApiRelations() {
        return callbackApiRelations;
    }

    public void setCallbackApiRelations(Map<String, List<ApiRequestKey>> callbackApiRelations) {
        this.callbackApiRelations = callbackApiRelations;
    }

    public ApiImportContext withCallbackApiRelations(Map<String, List<ApiRequestKey>> callbackApiRelations) {
        this.callbackApiRelations = callbackApiRelations;
        return this;
    }

    public Map<String, ModelDTO> getModels() {
        return models;
    }

    public void setModels(Map<String, ModelDTO> models) {
        this.models = models;
    }

    public ApiImportContext withModels(Map<String, ModelDTO> models) {
        this.models = models;
        return this;
    }
}
