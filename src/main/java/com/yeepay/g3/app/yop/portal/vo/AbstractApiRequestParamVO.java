package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.dto.ValRuleDto;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 11:29
 */
public abstract class AbstractApiRequestParamVO extends AbstractApiParamVO {

    private static final long serialVersionUID = -4541876555343218873L;

    protected String defaultValue;

    protected Boolean internal;

    protected ValRuleDto constrains;

    protected Integer endParamIndex;

    protected String endParamName;

    public String getDefaultValue() {
        return defaultValue;
    }

    public void setDefaultValue(String defaultValue) {
        this.defaultValue = defaultValue;
    }

    public Boolean getInternal() {
        return internal;
    }

    public void setInternal(Boolean internal) {
        this.internal = internal;
    }

    public ValRuleDto getConstrains() {
        return constrains;
    }

    public void setConstrains(ValRuleDto constrains) {
        this.constrains = constrains;
    }

    public Integer getEndParamIndex() {
        return endParamIndex;
    }

    public void setEndParamIndex(Integer endParamIndex) {
        this.endParamIndex = endParamIndex;
    }

    public String getEndParamName() {
        return endParamName;
    }

    public void setEndParamName(String endParamName) {
        this.endParamName = endParamName;
    }
}
