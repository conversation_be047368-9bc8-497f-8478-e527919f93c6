package com.yeepay.g3.app.yop.portal.vo.page;

import java.util.List;

/**
 * title: ApiManagePageQueryParam<br/>
 * description: spi分页查询参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:33
 */
public class ApiManagePageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private String path;

    private String method;

    private String name;

    private String title;

    private String routeStatus;

    private String apiType;

    private String apiGroup;

    private String status;

    private List<String> apiGroupCodes;

    public String getPath() {
        return path;
    }

    public ApiManagePageQueryParam setPath(String path) {
        this.path = path;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public ApiManagePageQueryParam setMethod(String method) {
        this.method = method;
        return this;
    }

    public String getName() {
        return name;
    }

    public ApiManagePageQueryParam setName(String name) {
        this.name = name;
        return this;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getRouteStatus() {
        return routeStatus;
    }

    public ApiManagePageQueryParam setRouteStatus(String routeStatus) {
        this.routeStatus = routeStatus;
        return this;
    }

    public String getApiType() {
        return apiType;
    }

    public ApiManagePageQueryParam setApiType(String apiType) {
        this.apiType = apiType;
        return this;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public ApiManagePageQueryParam setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public ApiManagePageQueryParam setStatus(String status) {
        this.status = status;
        return this;
    }

    public List<String> getApiGroupCodes() {
        return apiGroupCodes;
    }

    public ApiManagePageQueryParam setApiGroupCodes(List<String> apiGroupCodes) {
        this.apiGroupCodes = apiGroupCodes;
        return this;
    }

    @Override
    public String toString() {
        return "ApiManagePageQueryParam{" +
                "path='" + path + '\'' +
                ", name='" + name + '\'' +
                ", title='" + routeStatus + '\'' +
                ", apiType='" + apiType + '\'' +
                ", apiGroup='" + apiGroup + '\'' +
                ", status='" + status + '\'' +
                ", apiGroupCodes=" + apiGroupCodes +
                '}';
    }
}
