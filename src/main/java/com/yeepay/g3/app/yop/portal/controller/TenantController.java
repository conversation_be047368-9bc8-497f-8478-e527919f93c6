/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.utils.common.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/10/12 19:14
 */
@Controller
@RequestMapping("/rest/tenant")
public class TenantController {

    @ResponseBody
    @RequestMapping(value = "/commons/tenant-codes", method = RequestMethod.GET)
    public ResponseMessage tenantCodes() {
        List<CommonsVO> list = new ArrayList<>();
        Map<String, String> map = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_TENANT_CODES);
        if (ShiroUtils.isPlatformOperator()) {
            map.forEach((key, value) -> list.add(new CommonsVO(key, value)));
            return new ResponseMessage("result", list);
        } else {
            //如果是sp用户，按租户查询数据
            Set<String> tenantCodes = ShiroUtils.getShiroUser().getTenantScopes();
            if (CollectionUtils.isEmpty(tenantCodes)) {
                return new ResponseMessage("result", list);
            }
            tenantCodes.forEach(tenantCode -> list.add(new CommonsVO(tenantCode, map.get(tenantCode))));
            return new ResponseMessage("result", list);
        }
    }
}
