/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.cache;


import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeepay.g3.facade.yop.perm.facade.AclRoleMgrFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * title:根据用户查询角色（用在判断用户是否有审核权限） <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-28 14:46
 */
@Component("aclUserRoleCache")
public class AclUserRoleCache {

    private static final Logger LOGGER = LoggerFactory.getLogger(AclUserRoleCache.class);

    private AclRoleMgrFacade aclRoleMgrFacade = RemoteServiceFactory.getService(AclRoleMgrFacade.class);

    public LoadingCache<String, List<String>> aclUserRoleCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(61, TimeUnit.SECONDS)
            .build(new CacheLoader<String, List<String>>() {
                @Override
                public List<String> load(String key) throws Exception {
                    try {
                        List<String> roleCodes = aclRoleMgrFacade.findRoleCodeByOperatorCode(key);
                        return roleCodes;
                    } catch (Exception e) {
                        LOGGER.error("load roleCodes by operatorCode failed.", e);
                        return null;
                    }
                }
            });

}
