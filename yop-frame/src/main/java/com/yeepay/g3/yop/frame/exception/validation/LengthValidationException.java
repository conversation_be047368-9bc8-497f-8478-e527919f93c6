package com.yeepay.g3.yop.frame.exception.validation;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/1/25 下午6:20
 */
public class LengthValidationException extends ValidationException {

    private boolean tooLong = false;

    private Integer max;

    private Integer min;

    public LengthValidationException(String argumentName, Integer value, boolean tooLong, Integer max, Integer min) {
        super(argumentName, value);
        this.tooLong = tooLong;
        this.max = max;
        this.min = min;
    }

    public boolean isTooLong() {
        return tooLong;
    }

    public Integer getMax() {
        return max;
    }

    public Integer getMin() {
        return min;
    }

    @Override
    public String getCode() {
        return super.getCode() + ".length";
    }
}
