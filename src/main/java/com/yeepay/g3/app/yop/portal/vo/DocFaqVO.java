/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * title: 常见问题详情<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/12/21
 */
@Data
public class DocFaqVO extends BaseVO {

    /**
     * 问题标识
     */
    private Long id;

    /**
     * 问题标题
     */
    private String title;

    /**
     * 问题答案
     */
    private String answer;

    /**
     * 问题状态
     */
    private String status;

    /**
     * 问题分值
     */
    private Integer score;

    /**
     * 是否置顶
     * true：是，false：否
     */
    private Boolean top;

    /**
     * 乐观锁版本
     */
    private Long version;

    /**
     * 问题创建时间
     */
    private Date createdDatetime;

    /**
     * 问题最后修改时间
     */
    private Date lastModifiedDatetime;

    /**
     * 已关联的文章列表
     */
    private List<DocFaqRelateItemVO> relateItems;

}
