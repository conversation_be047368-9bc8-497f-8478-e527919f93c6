/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.service.AuditQueryService;
import com.yeepay.g3.app.yop.portal.utils.MapUtils;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-29 20:04
 */
@Component
public class AuditQueryServiceImpl implements AuditQueryService {
    @Resource(name = "auditRecordQueryService")
    private QueryService recordQueryService;

    @Resource(name = "auditRequisitionQueryService")
    private QueryService requisitionQueryService;

    private PageItemConverter<AuditRecordPageItem> recordPageItemConverter = new AuditQueryServiceImpl.AuditRecordItemConverter();

    private PageItemConverter<AuditRequisitionPageItem> requisitionPageItemConverter = new AuditQueryServiceImpl.AuditRequisitionItemConverter();

    @Override
    public PageQueryResult<AuditRecordPageItem> pageQueryRecordList(AuditRecordPageQueryParam param) {
        Map<String, Object> bizParams = MapUtils.objectToMap(param);
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        List<Map> list = recordQueryService.query("pageQueryRecordList", bizParams);
        PageQueryResult<AuditRecordPageItem> result = new PageQueryResult<>();
        List<AuditRecordPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        Date endTime = param.getArrivedEndDate();
        Date startTime = param.getArrivedStartDate();
        if (CollectionUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                AuditRecordPageItem item = recordPageItemConverter.convert(list.get(i));
                if (endTime != null && item.getArrivedDate().after(endTime)) {
                    continue;
                }
                if (startTime != null && item.getArrivedDate().before(startTime)) {
                    continue;
                }
                items.add(item);
            }
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public PageQueryResult<AuditRecordPageItem> pageQueryRecordForSp(AuditRecordPageQueryParam param) {
        Map<String, Object> bizParams = MapUtils.objectToMap(param);
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        bizParams.put("spCodes", ShiroUtils.getShiroUser().getSpScopes());
        List<Map> list = recordQueryService.query("pageQueryRecordListForSp", bizParams);
        PageQueryResult<AuditRecordPageItem> result = new PageQueryResult<>();
        List<AuditRecordPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        Date endTime = param.getArrivedEndDate();
        Date startTime = param.getArrivedStartDate();
        if (CollectionUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                AuditRecordPageItem item = recordPageItemConverter.convert(list.get(i));
                if (endTime != null && item.getArrivedDate().after(endTime)) {
                    continue;
                }
                if (startTime != null && item.getArrivedDate().before(startTime)) {
                    continue;
                }
                items.add(item);
            }
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public PageQueryResult<AuditRequisitionPageItem> pageQueryRequisitionList(AuditRequisitionPageQueryParam param) {
        Map<String, Object> bizParams = MapUtils.objectToMap(param);
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        List<Map> list = requisitionQueryService.query("pageQueryRequisitionList", bizParams);
        PageQueryResult<AuditRequisitionPageItem> result = new PageQueryResult<>();
        List<AuditRequisitionPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(requisitionPageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public PageQueryResult<AuditRequisitionPageItem> pageQueryRequisitionForSp(AuditRequisitionPageQueryParam param) {
        Map<String, Object> bizParams = MapUtils.objectToMap(param);
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        bizParams.put("spCodes", ShiroUtils.getShiroUser().getSpScopes());
        List<Map> list = requisitionQueryService.query("pageQueryRequisitionListForSp", bizParams);
        PageQueryResult<AuditRequisitionPageItem> result = new PageQueryResult<>();
        List<AuditRequisitionPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(requisitionPageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    class AuditRecordItemConverter extends BasePageItemConverter<AuditRecordPageItem> {
        @Override
        public AuditRecordPageItem convert(Map<String, Object> params) {
            AuditRecordPageItem item = new AuditRecordPageItem();
            item.setCode((String) params.get("code"));
            item.setOperator((String) params.get("current_operator"));
            item.setOriginator((String) params.get("originator"));
            item.setResourceName((String) params.get("resource_name"));
            item.setStatus((String) params.get("status"));
            item.setArrivedDate((Date) params.get("arrived_datetime"));
            return item;
        }
    }

    class AuditRequisitionItemConverter extends BasePageItemConverter<AuditRequisitionPageItem> {
        @Override
        public AuditRequisitionPageItem convert(Map<String, Object> params) {
            AuditRequisitionPageItem item = new AuditRequisitionPageItem();
            item.setCode((String) params.get("code"));
            item.setOperator((String) params.get("current_operator"));
            item.setOriginator((String) params.get("originator"));
            item.setResourceName((String) params.get("resource_name"));
            item.setStatus((String) params.get("status"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            return item;
        }
    }
}
