/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.g3.app.yop.portal.exception.PortalExceptionEnum;
import com.yeepay.g3.app.yop.portal.service.ExamineService;
import com.yeepay.g3.app.yop.portal.utils.HttpUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.ApiExamineVO;
import com.yeepay.g3.app.yop.portal.vo.ProductApiExamineVO;
import com.yeepay.g3.app.yop.portal.vo.ProductExamineVO;
import com.yeepay.g3.facade.yop.sys.checker.ContentType;
import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;
import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiResponseDTO;
import com.yeepay.g3.facade.yop.sys.dto.models.Content;
import com.yeepay.g3.facade.yop.sys.dto.models.utils.SchemaUtils;
import com.yeepay.g3.facade.yop.sys.facade.ApiMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.ModelQueryFacade;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.ng.yeeworks.modeling.client.dto.YopGlossaryAliasDTO;
import com.yeepay.ng.yeeworks.modeling.client.facade.GlossaryFacade;
import io.swagger.v3.oas.models.media.ObjectSchema;
import io.swagger.v3.oas.models.media.Schema;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/5/17 7:00 下午
 */
@Component
@Slf4j
public class ExamineServiceImpl implements ExamineService {

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();
    private GlossaryFacade glossaryFacade = RemoteServiceFactory.getService(GlossaryFacade.class);
    private ModelQueryFacade modelQueryFacade = RemoteServiceFactory.getService(ModelQueryFacade.class);
    private ApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(ApiMgrFacade.class);
    private final static String START_PROCESS_URI = "/fmc-boss/modules/wfl/def/autoStartApro";
    private final static String PRO_DEF_KEY = "procDefKey";
    private final static String LOGIN_USER_NAME = "loginUserName";
    private final static String FORM_DATA = "formData";
    private final static int FMC_RESPONSE_SUCCESS_CODE = 200;
    private final static String CODE = "code";
    private static final ObjectMapper OBJECT_MAPPER = JsonMapper.nonDefaultMapper().getMapper();
    @Value("${fmc.address}")
    private String address;

    @Override
    public void productApi(ProductApiExamineVO productApiExamineVO, String operator) {
        String processNo = (String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PRODUCT_API_EXAMINE_PROCESS_NO);
        Map<String, Object> params = new HashMap<>();
        params.put(PRO_DEF_KEY, processNo);
        params.put(LOGIN_USER_NAME, operator);
        params.put(FORM_DATA, buildFormData(productApiExamineVO));
        int fmcCode = 0;
        try {
            String content = OBJECT_MAPPER.writeValueAsString(params);
            String url = address + START_PROCESS_URI;
            String fmcResult = HttpUtils.postJson(url, content, null);
            log.info("invoke fmc ,address:{},result:{}", url, fmcResult);
            Map<String, Object> resultMap = OBJECT_MAPPER.readValue(fmcResult, Map.class);
            fmcCode = (int) resultMap.get(CODE);
            log.info("submit a process to fmc,processId:{}", ((Map) resultMap.get("data")).get("processNo"));
        } catch (Exception e) {
            log.error("", e);
            PortalExceptionEnum.UNKNOWN_ERROR.assertFail();
        }
        PortalExceptionEnum.SUBMIT_FAILED.assertEquals(FMC_RESPONSE_SUCCESS_CODE, fmcCode);
    }

    private Map<String, Object> buildFormData(ProductApiExamineVO productApiExamineVO) {
        Map<String, Object> result = new HashMap<>();
        List<String> middleGroundProducts = (List<String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_MIDDLE_GROUND_PRODUCTS);
        boolean middleProduct = false;
        if (CollectionUtils.isNotEmpty(productApiExamineVO.getProducts()) && CollectionUtils.isNotEmpty(middleGroundProducts)) {
            for (ProductExamineVO product : productApiExamineVO.getProducts()) {
                if (middleGroundProducts.contains(product.getProductCode())) {
                    middleProduct = true;
                    break;
                }
            }
        }
        result.put("middleProduct", middleProduct);
        result.put("products", parseProduct(productApiExamineVO.getProducts()));
        result.put("apis", parseApi(productApiExamineVO.getApis()));
        return result;
    }

    private List<Object> parseProduct(List<ProductExamineVO> products) {
        List<Object> result = new ArrayList<>();
        products.forEach(productVO -> {
            Map map = new HashMap();
            map.put("productCode", productVO.getProductCode());
            map.put("productName", productVO.getProductName());
            map.put("productType", productVO.getProductType());
            map.put("productDesc", productVO.getDesc());
            map.put("spCode", productVO.getSpCode());
            map.put("newProduct", Boolean.TRUE.equals(productVO.getNewProduct()) ? "true" : "false");
            result.add(map);
        });
        return result;
    }

    private static Map<String, List<String>> parseModelSchema(List<ModelDTO> modelDTOS) {
        Map<String, List<String>> modelParams = new HashMap<>();
        for (ModelDTO modelDTO : modelDTOS) {
            Schema schema = SchemaUtils.parseSchema(modelDTO.getSchema());
            if (ObjectUtils.isEmpty(schema) || MapUtils.isEmpty(schema.getProperties())) {
                continue;
            }
            Map<String, Object> properties = schema.getProperties();
            //遍历模型参数
            for (Map.Entry<String, Object> entry : properties.entrySet()) {
                //过滤object类型参数
                Schema propertySchema = (Schema) entry.getValue();
                if ("object".equals(propertySchema.getType()) || StringUtils.isNotEmpty(propertySchema.get$ref())) {
                    continue;
                }
                //存储包含该参数的所有模型名称
                String paramName = entry.getKey();
                modelParams.computeIfAbsent(paramName, k -> new ArrayList<>()).add(modelDTO.getName());
            }
        }
        return modelParams;
    }

    private List<Object> parseApi(List<ApiExamineVO> apiExamineVOS) {
        if (CollectionUtils.isEmpty(apiExamineVOS)) {
            return Collections.EMPTY_LIST;
        }
        List<Object> result = new ArrayList<>();
        for (ApiExamineVO api : apiExamineVOS) {
            Map map = new HashMap();
            List<String> relateErrorCodes = new ArrayList<>();
            if (api.isRelateErrorCode()) {
                relateErrorCodes.add("api");
            }
            if (api.isGroupRelateErrorCode()) {
                relateErrorCodes.add("group");
            }
            //所有参数核对术语表，返回不合法的参数
            List<Map<String, Object>> illegalParams = findIllegalParams(api.getApiId());
            map.put("illegalParams", illegalParams);
            map.put("apiTitle", api.getApiTitle());
            map.put("methodAndPath", api.getHttpMethod() + " " + api.getApiUri());
            map.put("relateErrorCode", relateErrorCodes);
            map.put("relateCallback", api.isRelateCallback() ? "true" : "false");
            result.add(map);
        }
        return result;
    }

    private List<Map<String, Object>> findIllegalParams(String apiId) {
        ApiDTO apiDTO = apiMgrFacade.find(apiId);
        PortalExceptionEnum.API_NOT_FOUND.assertIsTrue(ObjectUtils.isNotEmpty(apiDTO), apiId);
        //查找所有form形式的参数
        List<String> formParamNames = findFormParams(apiDTO);
        Set<String> aliaes = new HashSet<>();
        if (CollectionUtils.isNotEmpty(formParamNames)) {
            Set<String> formParamNameSet = new HashSet<>();
            for (String formParamName : formParamNames) {
                formParamNameSet.add(formParamName);
            }
            aliaes.addAll(formParamNameSet);
        }
        //查找所有json形式的dto模型参数
        Map<String, List<String>> jsonParams = findJsonParams(apiDTO);
        if (MapUtils.isNotEmpty(jsonParams)) {
            aliaes.addAll(jsonParams.keySet());
        }
        if (CollectionUtils.isEmpty(aliaes)) {
            return Collections.emptyList();
        }
        //根据别名，查术语
        List<YopGlossaryAliasDTO> yopGlossaryAliases = glossaryFacade.queryGlossaryByAlias(new ArrayList<>(aliaes));
        if (CollectionUtils.isEmpty(yopGlossaryAliases)) {
            return Collections.emptyList();
        }
        return getGlossaryInfo(formParamNames, jsonParams, yopGlossaryAliases);
    }

    private List<Map<String, Object>> getGlossaryInfo(List<String> formParamNames, Map<String, List<String>> jsonParams, List<YopGlossaryAliasDTO> yopGlossaryAliases) {
        //遍历所有form参数和json参数，找到对应的参数，封装数据
        List<Map<String, Object>> result = new ArrayList<>();
        for (YopGlossaryAliasDTO yopGlossaryAlias : yopGlossaryAliases) {
            String alias = yopGlossaryAlias.getAlias();
            String fieldCode = yopGlossaryAlias.getFieldCode();
            String chineseName = yopGlossaryAlias.getChineseName();
            //过滤
            if (StringUtils.isEmpty(yopGlossaryAlias.getFieldCode())) {
                continue;
            }
            if (StringUtils.isNotEmpty(alias) && StringUtils.isNotEmpty(yopGlossaryAlias.getFieldCode()) && StringUtils.equals(alias, fieldCode)) {
                continue;
            }
            //从form形式的参数里查找是否存在该别名
            if (CollectionUtils.isNotEmpty(formParamNames) && formParamNames.contains(alias)) {
                Map<String, Object> map = new HashMap<>();
                map.put("paramName", alias);
                map.put("glossaryCode", fieldCode);
                map.put("glossaryName", chineseName);
                map.put("modelName", null);
                result.add(map);
            }
            //从json形式的参数里查找是否存在该别名
            if (MapUtils.isNotEmpty(jsonParams) && jsonParams.containsKey(alias)) {
                List<String> modelNames = jsonParams.get(alias);
                if (CollectionUtils.isNotEmpty(modelNames)) {
                    for (String modelName : modelNames) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("paramName", alias);
                        map.put("glossaryCode", fieldCode);
                        map.put("glossaryName", chineseName);
                        map.put("modelName", modelName);
                        result.add(map);
                    }
                }
            }
        }
        return result;
    }

    private Map<String, List<String>> findJsonParams(ApiDTO apiDTO) {
        String apiGroup = apiDTO.getBasic().getApiGroup();
        ApiRequestDTO request = apiDTO.getRequest();
        ApiResponseDTO response = apiDTO.getResponse();
        //解析出入参和出参的模型
        Set<String> modelNames = new HashSet<>();
        if (ObjectUtils.isNotEmpty(request.getRequestBody()) && MapUtils.isNotEmpty(request.getRequestBody().getContents())) {
            Content content = request.getRequestBody().getContents().get(ContentType.APPLICATION_JSON);
            if (content != null) {
                String refModelName = getRefModelName(content);
                if (StringUtils.isNotEmpty(refModelName)) {
                    modelNames.add(refModelName);
                }
            }
        }
        if (ObjectUtils.isNotEmpty(response.getContent())) {
            Content content = response.getContent();
            if (content != null) {
                String refModelName = getRefModelName(content);
                if (StringUtils.isNotEmpty(refModelName)) {
                    modelNames.add(refModelName);
                }
            }
        }
        if (CollectionUtils.isEmpty(modelNames)) {
            log.info("json类型参数为空");
            return Collections.emptyMap();
        }
        //递归查所有模型
        Map<String, List<String>> apiGroupModels = new HashMap<>();
        apiGroupModels.put(apiGroup, new ArrayList<>(modelNames));
        List<ModelDTO> modelDTOS = modelQueryFacade.findModelsRecursively(apiGroupModels);
        //解析模型参数
        Map<String, List<String>> modelParams = parseModelSchema(modelDTOS);
        return modelParams;
    }

    private String getRefModelName(Content content) {
        String schema = content.getSchema();
        ObjectSchema objectSchema = JSON_MAPPER.fromJson(schema, ObjectSchema.class);
        String refModelName = SchemaUtils.getRefModelName(objectSchema.get$ref());
        return refModelName;
    }

    public List<String> findFormParams(ApiDTO apiDTO) {
        ApiRequestDTO request = apiDTO.getRequest();
        List<String> res = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(request.getParameters())) {
            request.getParameters().stream().forEach(parameter -> {
                res.add(parameter.getName());
            });
        } else if (MapUtils.isNotEmpty(request.getRequestBody().getContents())) {
            Content content = request.getRequestBody().getContents().get(ContentType.APPLICATION_FORM_URLENCODED);
            if (content != null) {
                String schema = content.getSchema();
                ObjectSchema objectSchema = JSON_MAPPER.fromJson(schema, ObjectSchema.class);
                for (String key : objectSchema.getProperties().keySet()) {
                    res.add(key);
                }
            }
        }
        return res;
    }
}
