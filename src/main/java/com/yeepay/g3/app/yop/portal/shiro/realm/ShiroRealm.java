package com.yeepay.g3.app.yop.portal.shiro.realm;

import com.google.common.base.Objects;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.decoration.HireUserDecoration;
import com.yeepay.g3.app.yop.portal.service.IspMgrQueryService;
import com.yeepay.g3.app.yop.portal.shiro.authc.strategy.AuthenticationContext;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.facade.yop.perm.dto.AclPermDTO;
import com.yeepay.g3.facade.yop.perm.dto.IspInfoDTO;
import com.yeepay.g3.facade.yop.perm.dto.IspOperatorDTO;
import com.yeepay.g3.facade.yop.perm.enums.OperatorStatusEnum;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.facade.yop.perm.facade.AclPrivilegeMgrFacade;
import com.yeepay.g3.facade.yop.perm.facade.IspMgrFacade;
import com.yeepay.g3.facade.yop.perm.facade.IspOperatorMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.ApiGroupFacade;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.DisabledAccountException;
import org.apache.shiro.authc.credential.AllowAllCredentialsMatcher;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.*;

/**
 * title: <br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/5/31 下午2:51
 */
@Component
public class ShiroRealm extends AuthorizingRealm {
    private static final String TENANT_CODE = "tenantCode:";

    private static final Logger LOGGER = LoggerFactory.getLogger(ShiroRealm.class);

    private static HireUserDecoration hireUserDecoration;

    @Autowired
    private IspMgrQueryService ispMgrQueryService;

    @Autowired
    public ShiroRealm(HireUserDecoration hireUserDecoration) {
        setAuthenticationTokenClass(AuthenticationToken.class);
        this.hireUserDecoration = hireUserDecoration;
    }

    public ShiroUser getShiroUserInfo(String userId, String token) {
        //查找或创建用户
        IspOperatorMgrFacade ispOperatorMgrFacade = RemoteServiceFactory.getService(IspOperatorMgrFacade.class);
        IspOperatorDTO ispOperatorDTO = ispOperatorMgrFacade.findOrCreate(userId);
        //检测本系统的用户状态
        if (ispOperatorDTO.getStatus().equals(OperatorStatusEnum.FROZEN)) {
            LOGGER.warn("user is frozen, userId:{}.", userId);
            throw new DisabledAccountException("用户已被冻结");
        }
        String userName = ispOperatorDTO.getOperatorName();

        if (!hireUserDecoration.checkHireStatus(userId)) {
            LOGGER.warn("hire status unusual, userId:{}.", userId);
            throw new DisabledAccountException("用户状态异常");
        }

        // 包含 PLATFORM_ 角色则该用户不应该是 SP 级别的
        OperatorTypeEnum userType = ispOperatorDTO.getOperatorType();
        IspMgrFacade ispMgrFacade = RemoteServiceFactory.getService(IspMgrFacade.class);
        List<String> spCodes = ispMgrFacade.findSpCodeByOperatorCode(userId);
        List<String> apiGroupCodes = new ArrayList<>();
        List<String> tenantCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(spCodes)) {
            ApiGroupFacade apiGroupFacade = RemoteServiceFactory.getService(ApiGroupFacade.class);
            apiGroupCodes = apiGroupFacade.findBySpCodes(spCodes);
            tenantCodes = ispMgrQueryService.findTenantCodesBySpCodes(new HashSet<>(spCodes));
        }
        Set<String> spScope = new HashSet<>(spCodes);
        Set<String> apiGroupScope = new HashSet<>(apiGroupCodes);
        Set<String> tenantScope = new HashSet<>(tenantCodes);

        ShiroUser shiroUser = new ShiroUser(userId, userName, userType, spScope, apiGroupScope, token, tenantScope);
        return shiroUser;
    }

    /**
     * 认证回调函数,登录时调用.
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken authcToken) throws AuthenticationException {
        AuthenticationContext authenticationContext = new AuthenticationContext(authcToken);
        AuthenticationInfo authenticationInfo = authenticationContext.doGetAuthenticationInfo();
        return authenticationInfo;
    }

    /**
     * 授权查询回调函数, 进行鉴权但缓存中无用户的授权信息时调用.
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        ShiroUser shiroUser = (ShiroUser) principals.getPrimaryPrincipal();
        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        AclPrivilegeMgrFacade aclPrivilegeMgrFacade = RemoteServiceFactory.getService(AclPrivilegeMgrFacade.class);
        AclPermDTO aclPermDTO = aclPrivilegeMgrFacade.findAclParamByOperatorCode(shiroUser.getUserId());
        info.addRoles(aclPermDTO.getRolePermRules().keySet());
        if (shiroUser.getUserType().equals(OperatorTypeEnum.PLATFORM)) {
            for (String role : aclPermDTO.getRolePermRules().keySet()) {
                if (CollectionUtils.isNotEmpty(aclPermDTO.getRolePermRules().get(role))) {
                    info.addStringPermissions(aclPermDTO.getRolePermRules().get(role));
                }
            }
            if (aclPermDTO.getPermRules() != null) {
                info.addStringPermissions(aclPermDTO.getPermRules());
            }
            Map<String, String> map = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_TENANT_CODES);
            if (MapUtils.isNotEmpty(map)) {
                List<String> allTenantCodes = new ArrayList<>();
                map.forEach((key, value) -> allTenantCodes.add(TENANT_CODE + key));
                info.addStringPermissions(allTenantCodes);
            }
        } else {
            for (String role : aclPermDTO.getRolePermRules().keySet()) {
                List<String> rolePerms = new ArrayList<>();
                String prefix = StringUtils.split(role, ":")[0];
                Iterator iterRolePerms = aclPermDTO.getRolePermRules().get(role).iterator();
                while (iterRolePerms.hasNext()) {
                    String rolePerm = (String) iterRolePerms.next();
                    if (StringUtils.isNotEmpty(rolePerm)) {
                        for (String rolePerm1 : StringUtils.split(rolePerm, ",")) {
                            rolePerms.add(StringUtils.upperCase(prefix) + ":" + rolePerm1);
                        }
                    }
                }
                info.addStringPermissions(rolePerms);
                //保存spCode对应的租户code
                List<String> tenantList = new ArrayList<>();
                IspInfoDTO ispInfoDTO = ispMgrQueryService.findBySpCode(prefix);
                tenantList.add(TENANT_CODE + ispInfoDTO.getTenantCode());
                info.addStringPermissions(tenantList);
            }
        }
        return info;
    }

    @PostConstruct
    public void initCredentialsMatcher() {
        setCredentialsMatcher(new AllowAllCredentialsMatcher());
    }

    public static class ShiroUser implements Serializable {

        private static final long serialVersionUID = -1373760761780840081L;

        private String userId;
        private String username;
        private OperatorTypeEnum userType;
        private Set<String> spScopes;
        private Set<String> apiGroupScopes;
        private String accessToken;
        private Set<String> tenantScopes;

        public ShiroUser() {
            // do nothing
        }

        public ShiroUser(String accessToken) {
            this.accessToken = accessToken;
        }

        public ShiroUser(String userId, String username, OperatorTypeEnum userType, Set<String> spScopes, Set<String> apiGroupScopes, String accessToken, Set<String> tenantScopes) {
            this.userId = userId;
            this.username = username;
            this.userType = userType;
            this.spScopes = spScopes;
            this.apiGroupScopes = apiGroupScopes;
            this.accessToken = accessToken;
            this.tenantScopes = tenantScopes;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public void setSpScopes(Set<String> spScopes) {
            this.spScopes = spScopes;
        }

        public void setApiGroupScopes(Set<String> apiGroupScopes) {
            this.apiGroupScopes = apiGroupScopes;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public String getUserId() {
            return userId;
        }

        public String getUsername() {
            return username;
        }

        public OperatorTypeEnum getUserType() {
            return userType;
        }

        public void setUserType(OperatorTypeEnum userType) {
            this.userType = userType;
        }

        public boolean isPlatformOperator() {
            return OperatorTypeEnum.PLATFORM == this.userType;
        }

        public boolean isSpOperator() {
            return OperatorTypeEnum.SP_BASED == this.userType;
        }

        public Set<String> getSpScopes() {
            return spScopes;
        }

        public Set<String> getApiGroupScopes() {
            return apiGroupScopes;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public Set<String> getTenantScopes() {
            return tenantScopes;
        }

        public void setTenantScopes(Set<String> tenantScopes) {
            this.tenantScopes = tenantScopes;
        }

        /**
         * 本函数输出将作为默认的<shiro:principal/>输出.
         */
        @Override
        public String toString() {
            return getUsername();
        }

        @Override
        public int hashCode() {
            return Objects.hashCode(getUserId());
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            ShiroUser other = (ShiroUser) obj;
            if (getUserId() == null) {
                if (other.getUserId() != null) {
                    return false;
                }
            } else if (!getUserId().equals(other.getUserId())) {
                return false;
            }
            return true;
        }
    }

}
