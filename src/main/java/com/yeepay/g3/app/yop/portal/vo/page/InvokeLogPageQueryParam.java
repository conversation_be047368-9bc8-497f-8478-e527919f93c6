package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateStrategyEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

public class InvokeLogPageQueryParam extends BasePageQueryParam implements Serializable {

    private static final long serialVersionUID = -1;

    private String apiUri;

    private String appKey;

    private String apiGroupCode;

    private Date requestStartDate;

    private Date requestEndDate;

    private String requestIp;

    private String dataCenter;

    private String requestId;

    private String guid;

    private String bizOrderNo;

    private AuthenticateStrategyEnum securityStrategy;

    private String status;

    private String errorCode;

    private String subErrorCode;

    private Integer minBackendLatency;

    private Integer maxBackendLatency;

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public Date getRequestStartDate() {
        return requestStartDate;
    }

    public void setRequestStartDate(Date requestStartDate) {
        this.requestStartDate = requestStartDate;
    }

    public Date getRequestEndDate() {
        return requestEndDate;
    }

    public void setRequestEndDate(Date requestEndDate) {
        this.requestEndDate = requestEndDate;
    }

    public String getRequestIp() {
        return requestIp;
    }

    public void setRequestIp(String requestIp) {
        this.requestIp = requestIp;
    }

    public String getDataCenter() {
        return dataCenter;
    }

    public void setDataCenter(String dataCenter) {
        this.dataCenter = dataCenter;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getGuid() {
        return guid;
    }

    public void setGuid(String guid) {
        this.guid = guid;
    }

    public String getBizOrderNo() {
        return bizOrderNo;
    }

    public void setBizOrderNo(String bizOrderNo) {
        this.bizOrderNo = bizOrderNo;
    }

    public AuthenticateStrategyEnum getSecurityStrategy() {
        return securityStrategy;
    }

    public void setSecurityStrategy(AuthenticateStrategyEnum securityStrategy) {
        this.securityStrategy = securityStrategy;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getSubErrorCode() {
        return subErrorCode;
    }

    public void setSubErrorCode(String subErrorCode) {
        this.subErrorCode = subErrorCode;
    }

    public Integer getMinBackendLatency() {
        return minBackendLatency;
    }

    public void setMinBackendLatency(Integer minBackendLatency) {
        this.minBackendLatency = minBackendLatency;
    }

    public Integer getMaxBackendLatency() {
        return maxBackendLatency;
    }

    public void setMaxBackendLatency(Integer maxBackendLatency) {
        this.maxBackendLatency = maxBackendLatency;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public static final class Builder {
        private Integer pageNo;
        private Integer pageSize;
        private String apiUri;
        private String appKey;
        private String apiGroupCode;
        private Date requestStartDate;
        private Date requestEndDate;
        private String requestIp;
        private String dataCenter;
        private String requestId;
        private String guid;
        private String bizOrderNo;
        private AuthenticateStrategyEnum securityStrategy;
        private String status;
        private String errorCode;
        private String subErrorCode;
        private Integer minBackendLatency;
        private Integer maxBackendLatency;

        private Builder() {
        }

        public static Builder anInvokeLogPageQueryParam() {
            return new Builder();
        }

        public Builder withPageNo(Integer pageNo) {
            this.pageNo = pageNo;
            return this;
        }

        public Builder withPageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder withApiUri(String apiUri) {
            this.apiUri = apiUri;
            return this;
        }

        public Builder withAppKey(String appKey) {
            this.appKey = appKey;
            return this;
        }

        public Builder withApiGroupCode(String apiGroupCode) {
            this.apiGroupCode = apiGroupCode;
            return this;
        }

        public Builder withRequestStartDate(Date requestStartDate) {
            this.requestStartDate = requestStartDate;
            return this;
        }

        public Builder withRequestEndDate(Date requestEndDate) {
            this.requestEndDate = requestEndDate;
            return this;
        }

        public Builder withRequestIp(String requestIp) {
            this.requestIp = requestIp;
            return this;
        }

        public Builder withDataCenter(String dataCenter) {
            this.dataCenter = dataCenter;
            return this;
        }

        public Builder withRequestId(String requestId) {
            this.requestId = requestId;
            return this;
        }

        public Builder withGuid(String guid) {
            this.guid = guid;
            return this;
        }

        public Builder withBizOrderNo(String bizOrderNo) {
            this.bizOrderNo = bizOrderNo;
            return this;
        }

        public Builder withSecurityStrategy(AuthenticateStrategyEnum securityStrategy) {
            this.securityStrategy = securityStrategy;
            return this;
        }

        public Builder withStatus(String status) {
            this.status = status;
            return this;
        }

        public Builder withErrorCode(String errorCode) {
            this.errorCode = errorCode;
            return this;
        }

        public Builder withSubErrorCode(String subErrorCode) {
            this.subErrorCode = subErrorCode;
            return this;
        }

        public Builder withMinBackendLatency(Integer minBackendLatency) {
            this.minBackendLatency = minBackendLatency;
            return this;
        }

        public Builder withMaxBackendLatency(Integer maxBackendLatency) {
            this.maxBackendLatency = maxBackendLatency;
            return this;
        }

        public InvokeLogPageQueryParam build() {
            InvokeLogPageQueryParam invokeLogPageQueryParam = new InvokeLogPageQueryParam();
            invokeLogPageQueryParam.setPageNo(pageNo);
            invokeLogPageQueryParam.setPageSize(pageSize);
            invokeLogPageQueryParam.setApiUri(apiUri);
            invokeLogPageQueryParam.setAppKey(appKey);
            invokeLogPageQueryParam.setApiGroupCode(apiGroupCode);
            invokeLogPageQueryParam.setRequestStartDate(requestStartDate);
            invokeLogPageQueryParam.setRequestEndDate(requestEndDate);
            invokeLogPageQueryParam.setRequestIp(requestIp);
            invokeLogPageQueryParam.setDataCenter(dataCenter);
            invokeLogPageQueryParam.setRequestId(requestId);
            invokeLogPageQueryParam.setGuid(guid);
            invokeLogPageQueryParam.setBizOrderNo(bizOrderNo);
            invokeLogPageQueryParam.setSecurityStrategy(securityStrategy);
            invokeLogPageQueryParam.setStatus(status);
            invokeLogPageQueryParam.setErrorCode(errorCode);
            invokeLogPageQueryParam.setSubErrorCode(subErrorCode);
            invokeLogPageQueryParam.setMinBackendLatency(minBackendLatency);
            invokeLogPageQueryParam.setMaxBackendLatency(maxBackendLatency);
            return invokeLogPageQueryParam;
        }
    }
}
