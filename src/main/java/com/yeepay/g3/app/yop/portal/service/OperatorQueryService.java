/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.OperatorPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.OperatorPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/7/19 下午5:41
 */
public interface OperatorQueryService {

    PageQueryResult<OperatorPageItem> pageQuery(OperatorPageQueryParam param);
}
