/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ProductApiQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.ProductApiPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ProductApiPageQueryParam;
import com.yeepay.g3.facade.yop.sys.enums.ProductApiTypeEnum;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/5 下午6:17
 */
@Component
public class ProductApiQueryServiceImpl implements ProductApiQueryService {

    private final PageItemConverter<ProductApiPageItem> pageItemConverter = new ProductApiPageItemConverter();

    @Resource(name = "productApiQueryService")
    private QueryService productApiQueryService;

    @Override
    public PageQueryResult<ProductApiPageItem> pageQuery(ProductApiPageQueryParam param) {
        Map<String, Object> bizParams = getBizParams(param);
        ProductApiTypeEnum type = param.getType();
        List<Map> list = null;
        if (Boolean.TRUE.equals(param.isRelated())) {
            if (ProductApiTypeEnum.API.equals(type)) {
                list = productApiQueryService.query("pageListApi", bizParams);
            } else if (ProductApiTypeEnum.API_V2.equals(type)) {
                list = productApiQueryService.query("pageListNewApi", bizParams);
            } else {
                list = productApiQueryService.query("pageListApiGroup", bizParams);
            }
        }
        PageQueryResult<ProductApiPageItem> result = new PageQueryResult<>();
        List<ProductApiPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public List<Map> listAllByProductCode(String productCode) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("productCode", productCode);
        return productApiQueryService.query("listAllByProductCode", bizParams);
    }

    private Map<String, Object> getBizParams(ProductApiPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("productCode", param.getProductCode());
        bizParams.put("sceneId", param.getSceneId());
        bizParams.put("type", param.getType());
        bizParams.put("value", param.getValue());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class ProductApiPageItemConverter extends BasePageItemConverter<ProductApiPageItem> {

        @Override
        public ProductApiPageItem convert(Map<String, Object> params) {
            ProductApiPageItem item = new ProductApiPageItem();
            item.setValue((String) params.get("value"));
            item.setName((String) params.get("name"));
            item.setMethod((String) params.get("method"));
            item.setPath((String) params.get("path"));
            item.setStatus((String) params.get("status"));
            return item;
        }
    }
}
