/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.AttachmentPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.doc.dto.AttachmentDTO;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018-12-21 18:09
 */
public interface AttachmentQueryService {
    /**
     * 分页获取附件列表
     * @param queryParam 分页参数
     * @return 页数据
     */
    PageQueryResult<AttachmentDTO> pageQuery(AttachmentPageQueryParam queryParam);
}
