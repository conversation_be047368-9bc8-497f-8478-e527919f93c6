package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ApiRequestService;
import com.yeepay.g3.app.yop.portal.service.DocFaqQueryService;
import com.yeepay.g3.app.yop.portal.service.DocQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.DocFaqRelateItemVO;
import com.yeepay.g3.app.yop.portal.vo.DocFaqVO;
import com.yeepay.g3.app.yop.portal.vo.page.DocFaqItem;
import com.yeepay.g3.app.yop.portal.vo.page.DocFaqPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocPageTypeEnum;
import com.yeepay.g3.facade.yop.doc.util.DocUtils;
import com.yeepay.g3.utils.query.QueryService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum.YOP_DOC_FAQ_RELATE_QUERY_LIMIT;

/**
 * title: 常见问题服务<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-29 10:43
 */
@Component
@Slf4j
public class DocFaqQueryServiceImpl implements DocFaqQueryService {

    private static final String RELATE_PAIR_SEPERATOR = "####";
    private static final String API_PREFIX = "api:", PAGE_PREFIX = "page:";
    private final PageItemConverter<DocFaqItem> pageItemConverter = new DocFaqItemConverter();

    @Resource(name = "docFaqQueryService")
    private QueryService queryService;

    @Resource(name = "apiManageQueryService")
    private QueryService apiManageQueryService;

    @Autowired
    private ApiRequestService apiRequestService;

    @Autowired
    private DocQueryService docQueryService;

    @Override
    public PageQueryResult<DocFaqItem> faqPageQuery(DocFaqPageQueryParam param) {
        PageQueryResult<DocFaqItem> result = new PageQueryResult<>();
        result.setPageNo(param.getPageNo());

        Map<String, Object> bizParams = getBizParams(param);
        if (StringUtils.isNotBlank(param.getPageTitle())) {
            // 根据pageTitle，获取faqId列表，加入到过滤条件
            List<Map<String, Object>> relatedList = relatedList(param.getPageTitle());
            if (CollectionUtils.isEmpty(relatedList)) {
                result.setItems(Collections.emptyList());
                return result;
            } else {
                bizParams.put("faqIds", relatedList.stream().map(map ->
                        (Long) map.get("faq_id")).collect(Collectors.toList()));
            }
        }
        bizParams.put("relateSeperator", RELATE_PAIR_SEPERATOR);
        List<Map> list = queryService.query("pageList", bizParams);
        List<DocFaqItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> pageIds = new LinkedList<>();
            List<String> apiIds = new LinkedList<>();
            for (Map map : list) {
                final DocFaqItem pageItem = pageItemConverter.convert(map);
                String relates = (String) map.get("relates");
                final String[] relateStrs = relates.split(",");
                List<String> relatedTitles = new ArrayList<>(relateStrs.length);
                Arrays.stream(relateStrs).forEach(relatePair -> {
                    final String[] split = relatePair.split(RELATE_PAIR_SEPERATOR);
                    if (split[0].equals("PAGE")) {
                        relatedTitles.add(PAGE_PREFIX + split[1]);
                        pageIds.add(Long.valueOf(split[1]));
                    } else {
                        relatedTitles.add(API_PREFIX + split[1]);
                        apiIds.add(split[1]);
                    }
                });
                pageItem.setRelatedTitle(relatedTitles);
                items.add(pageItem);
            }
            final Map<Long, String> pageTitles = CollectionUtils.isNotEmpty(pageIds)
                    ? relatePageInfo(pageIds) : Collections.emptyMap();
            final Map<String, String> apiTitles = CollectionUtils.isNotEmpty(apiIds)
                    ? relateApiInfo(apiIds) : Collections.emptyMap();
            items.forEach(item -> item.setRelatedTitle(item.getRelatedTitle().stream().map(relatedTitle -> {
                if (StringUtils.startsWith(relatedTitle, API_PREFIX)) {
                    final String apiId = StringUtils.substringAfter(relatedTitle, API_PREFIX);
                    return apiTitles.containsKey(apiId) ? apiTitles.get(apiId) : apiId + "(未知)";
                } else {
                    final Long pageId = Long.valueOf(StringUtils.substringAfter(relatedTitle, PAGE_PREFIX));
                    return pageTitles.containsKey(pageId) ? pageTitles.get(pageId) : pageId + "(未知)";
                }
            }).collect(Collectors.toList())));
        }
        result.setItems(items);
        return result;
    }

    private List<Map<String, Object>> relatedList(String pageTitle) {
        final List<Map<String, Object>> articlesForRelate = articlesForRelate(pageTitle);
        if (CollectionUtils.isEmpty(articlesForRelate)) return Collections.emptyList();

        List<String> relateIds = toRelateIds(articlesForRelate);
        if (CollectionUtils.isNotEmpty(relateIds)) {
            return queryService.query("relatedList",
                    Collections.singletonMap("relateIds", relateIds));
        }
        return Collections.emptyList();
    }

    private List<String> toRelateIds(List<Map<String, Object>> articlesForRelate) {
        // 区分API、PAGE
        Map<String, List<FaqRelateArticleItem>> articleMapByType = Maps.newHashMapWithExpectedSize(2);
        articlesForRelate.forEach(articleMap -> articleMapByType.computeIfAbsent((String) articleMap.get("page_type")
                , p -> new ArrayList<>(articlesForRelate.size()))
                .add(new FaqRelateArticleItem((Long) articleMap.get("page_id")
                        , (String) articleMap.get("page_type"), (String) articleMap.get("page_no"))));

        // 分别计算relateId
        List<String> relateIds = new ArrayList<>(articlesForRelate.size());
        articleMapByType.forEach((pageType, articleList) -> {
            List<String> converted = convertToRelateIds(pageType, articleList);
            if (CollectionUtils.isNotEmpty(converted)) {
                relateIds.addAll(converted);
            }
        });

        return relateIds;
    }

    private final String defaultRelateIdConverter = "default";
    private final Map<String, RelateIdConverter> converterMap;

    {
        converterMap = Maps.newHashMapWithExpectedSize(2);
        converterMap.put(DocPageTypeEnum.API.name(), new ApiRelateIdConverter());
        converterMap.put(defaultRelateIdConverter, new PageRelateIdConverter());
    }

    private List<String> convertToRelateIds(String pageType, List<FaqRelateArticleItem> articleList) {
        RelateIdConverter converter = converterMap.get(pageType);
        if (null == converter) {
            converter = converterMap.get(defaultRelateIdConverter);
        }
        return converter.convert(articleList);
    }

    interface RelateIdConverter {
        List<String> convert(List<FaqRelateArticleItem> articles);
    }

    class PageRelateIdConverter implements RelateIdConverter {

        @Override
        public List<String> convert(List<FaqRelateArticleItem> articles) {
            return articles.stream().map(p -> p.getPageId() + "").collect(Collectors.toList());
        }
    }

    class ApiRelateIdConverter implements RelateIdConverter {

        @Override
        public List<String> convert(List<FaqRelateArticleItem> articles) {
            List<String> result = new ArrayList<>(articles.size());
            List<String> oldApiUris = new ArrayList<>(articles.size());
            articles.forEach(article -> {
                final String[] methodApiUri = DocUtils.fromApiPageNo(article.getPageNo());
                // 旧版api
                if (DocUtils.isOldApi(article.getPageNo())) {
                    oldApiUris.add(methodApiUri[1]);
                    return;
                }

                // 新版api
                String apiId = apiRequestService.findApiIdForNewApi(methodApiUri[0], methodApiUri[1]);
                if (StringUtils.isBlank(apiId)) {
                    log.warn("apiId not found for api page, pageNo:{}", article.getPageNo());
                    return;
                }
                result.add(apiId);
            });
            if (CollectionUtils.isNotEmpty(oldApiUris)) {
                final Map<String, String> apiIdsForOldApi = apiRequestService.findApiIdsForOldApi(oldApiUris);
                if (MapUtils.isNotEmpty(apiIdsForOldApi)) {
                    result.addAll(apiIdsForOldApi.values());
                }
            }
            return result;
        }
    }

    @Data
    private class FaqRelateArticleItem {

        /**
         * 页面id
         */
        private Long pageId;

        /**
         * 页面类型
         */
        private String pageType;

        /**
         * 页面编码
         */
        private String pageNo;


        public FaqRelateArticleItem(Long pageId, String pageType, String pageNo) {
            this.pageId = pageId;
            this.pageType = pageType;
            this.pageNo = pageNo;
        }
    }

    private Map<String, Object> getBizParams(DocFaqPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("title", param.getFaqTitle());
        bizParams.put("status", param.getFaqStatus());
        bizParams.put("top", param.getTop());
        bizParams.put("createdDateStart", param.getCreatedDateStart());
        bizParams.put("createdDateEnd", param.getCreatedDateEnd());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    @Override
    public List<DocFaqRelateItemVO> listForRelate(String keywords) {
        final List<Map<String, Object>> articlesForRelate = articlesForRelate(keywords);
        if (CollectionUtils.isNotEmpty(articlesForRelate)) {
            List<DocFaqRelateItemVO> root = new ArrayList<>(2);
            Map<String, List<DocFaqRelateItemVO>> docPageRelates = Maps.newHashMapWithExpectedSize(articlesForRelate.size());
            Map<String, Map<String, Object>> oldApiItemMap = Maps.newHashMapWithExpectedSize(articlesForRelate.size());
            Map<String, Map<String, Object>> newApiItemMap = Maps.newHashMapWithExpectedSize(articlesForRelate.size());
            articlesForRelate.forEach(map -> {
                if (map.get("page_type").equals(DocPageTypeEnum.API.name())) {
                    final String apiPageNo = (String) map.get("page_no");
                    if (DocUtils.isOldApi(apiPageNo)) {
                        oldApiItemMap.put(DocUtils.fromApiPageNo(apiPageNo)[1], map);
                    } else {
                        newApiItemMap.put(apiPageNo, map);
                    }
                } else {
                    docPageRelates.computeIfAbsent(map.get("doc_no") + ":" + map.get("doc_title"),
                            p -> new LinkedList<>()).add(DocFaqRelateItemVO.builder()
                            .id(map.get("page_id") + "").type("PAGE").title((String) map.get("page_title")).build());
                }
            });
            if (!docPageRelates.isEmpty()) {
                final DocFaqRelateItemVO.DocFaqRelateItemVOBuilder pageParent = DocFaqRelateItemVO.builder()
                    .id("page").type("NONE").title("文章");
                docPageRelates.forEach((docPageStr, items) -> {
                    final String[] split = docPageStr.split(":");
                    final DocFaqRelateItemVO docRelate = DocFaqRelateItemVO.builder().id(split[0])
                            .type("NONE").title(split[1]).children(items).build();
                    pageParent.child(docRelate);
                });
                root.add(pageParent.build());
            }
            Map<String, DocFaqRelateItemVO> apiRelateItem = Maps.newHashMapWithExpectedSize(
                    oldApiItemMap.size() + newApiItemMap.size());
            if (!oldApiItemMap.isEmpty()) {
                Map<String, DocFaqRelateItemVO> oldApiRelateItemMap =
                        Maps.newHashMapWithExpectedSize(oldApiItemMap.size());
                oldApiItemMap.forEach((apiUri, apiItem) ->
                        oldApiRelateItemMap.put(apiUri, DocFaqRelateItemVO.builder().id(apiUri)
                                .type("API").title((String) apiItem.get("page_title")).version("API_V1").build()));
                final Map<String, String> apiIdsForOldApi = apiRequestService.
                        findApiIdsForOldApi(new ArrayList<>(oldApiRelateItemMap.keySet()));
                apiIdsForOldApi.forEach((apiUri, apiId) -> {
                    final DocFaqRelateItemVO existItem = oldApiRelateItemMap.get(apiUri);
                    if (null != existItem) {
                        existItem.setId(apiId);
                        apiRelateItem.put(apiId, existItem);
                    }
                });
            }

            if (!newApiItemMap.isEmpty()) {
                newApiItemMap.forEach((apiPageNo, apiItem) -> {
                    final String[] methodApiUri = DocUtils.fromApiPageNo(apiPageNo);
                    String apiId = apiRequestService.findApiIdForNewApi(methodApiUri[0], methodApiUri[1]);
                    if (StringUtils.isNotBlank(apiId)) {
                        apiRelateItem.put(apiId, DocFaqRelateItemVO.builder().id(apiId)
                                .type("API").title((String) apiItem.get("page_title")).version("API_V2").build());
                    }
                });
            }

            if (!apiRelateItem.isEmpty()) {
                root.add(DocFaqRelateItemVO.builder()
                        .id("api").type("NONE").title("API").children(apiRelateItem.values()).build());
            }

            return root;
        }
        return Collections.emptyList();
    }

    private List<Map<String, Object>> articlesForRelate(String keywords) {
        final Long queryLimit = (Long) ConfigUtils.getAppConfigParam(YOP_DOC_FAQ_RELATE_QUERY_LIMIT);
        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("queryLimit", queryLimit);
        queryMap.put("keywords", keywords);
        return (List<Map<String, Object>>) queryService.query("listForRelate", queryMap);
    }

    @Override
    public DocFaqVO faqDetail(Long faqId) {
        final Map<String, Object> faqMap = queryService.queryUnique("faqDetail",
                Collections.singletonMap("id", faqId), true);
        DocFaqVO result = new DocFaqVO();
        result.setId((Long) faqMap.get("id"));
        result.setTitle((String) faqMap.get("title"));
        result.setAnswer((String) faqMap.get("answer"));
        result.setStatus((String) faqMap.get("status"));
        result.setScore((Integer) faqMap.get("score"));
        result.setTop(((Integer) faqMap.get("top")) > 0);
        result.setVersion((Long) faqMap.get("version"));
        result.setCreatedDatetime(toLocalDateTime(faqMap, "created_date"));
        result.setLastModifiedDatetime(toLocalDateTime(faqMap, "last_modified_date"));
        // 关联项列表
        final List<Map<String, Object>> relateList = queryService.query("relatedList",
                Collections.singletonMap("faqId", faqId));
        result.setRelateItems(resolveRelateInfo(relateList));
        return result;
    }

    private List<DocFaqRelateItemVO> resolveRelateInfo(List<Map<String, Object>> relateList) {
        if (CollectionUtils.isEmpty(relateList)) return Collections.emptyList();
        List<DocFaqRelateItemVO> relateItemVOS = new ArrayList<>(relateList.size());
        List<String> apiRelateIds = new ArrayList<>(relateList.size());
        List<Long> pageRelateIds = new ArrayList<>(relateList.size());
        Map<String, Integer> relateIndex = Maps.newHashMapWithExpectedSize(relateList.size());
        for (int i = 0; i < relateList.size(); i++) {
            Map<String, Object> relateMap = relateList.get(i);
            final String relateType = (String) relateMap.get("relate_type");
            final String relateId = (String) relateMap.get("relate_id");
            final DocFaqRelateItemVO docFaqRelateItemVO =  DocFaqRelateItemVO.builder()
                    .id(relateId).type(relateType).title(relateId).build();
            String indexPrefix;
            if (relateType.equals("API")) {
                apiRelateIds.add(relateId);
                indexPrefix = API_PREFIX;
            } else {
                pageRelateIds.add(Long.valueOf(relateId));
                indexPrefix = PAGE_PREFIX;
            }
            relateItemVOS.add(docFaqRelateItemVO);
            relateIndex.put(indexPrefix + relateId, i);
        }

        // 关联的apis
        Map<String, String> apiInfoMap = relateApiInfo(apiRelateIds);
        apiRelateIds.forEach(apiId -> {
            if (apiInfoMap.containsKey(apiId)) {
                relateItemVOS.get(relateIndex.get(API_PREFIX + apiId)).setTitle(apiInfoMap.get(apiId));
            } else {
                log.warn("api not found when query docFaqDetail, apiId:{}", apiId);
            }
        });

        // 关联的pages
        Map<Long, String> pageInfoMap = relatePageInfo(pageRelateIds);
        pageRelateIds.forEach(pageId -> {
            if (pageInfoMap.containsKey(pageId)) {
                relateItemVOS.get(relateIndex.get(PAGE_PREFIX + pageId)).setTitle(pageInfoMap.get(pageId));
            } else {
                log.warn("page not found when query docFaqDetail, pageId:{}", pageId);
            }
        });
        return relateItemVOS;
    }

    private Map<String, String> relateApiInfo(List<String> apiRelateIds) {
        if (CollectionUtils.isNotEmpty(apiRelateIds)) {
            final List<Map<String, Object>> relateApiInfo = apiManageQueryService.query("relateApiInfo",
                    Collections.singletonMap("apis", apiRelateIds));
            if (CollectionUtils.isNotEmpty(relateApiInfo)) {
                Map<String, String> apiInfoMap = Maps.newHashMapWithExpectedSize(relateApiInfo.size());
                relateApiInfo.forEach(apiInfo -> {
                    String apiId = (String) apiInfo.get("api_id"), apiTitle = (String) apiInfo.get("api_title");
                    apiInfoMap.put(apiId, apiTitle);
                });
                return apiInfoMap;
            }
        }
        return Collections.emptyMap();
    }

    private Map<Long, String> relatePageInfo(List<Long> pageRelateIds) {
        if (!CollectionUtils.isEmpty(pageRelateIds)) {
            final List<Map<String, Object>> relatePageInfo = queryService.query("relatePageInfo",
                    Collections.singletonMap("pages", pageRelateIds));
            if (CollectionUtils.isNotEmpty(relatePageInfo)) {
                Map<Long, String> pageInfoMap = Maps.newHashMapWithExpectedSize(relatePageInfo.size());
                relatePageInfo.forEach(pageInfo -> {
                    Long pageId = (Long) pageInfo.get("page_id");
                    String pageTitle = (String) pageInfo.get("page_title");
                    pageInfoMap.put(pageId, pageTitle);
                });
                return pageInfoMap;
            }
        }
        return Collections.emptyMap();
    }

    @Override
    public boolean checkAcl(Long docFaqId) {
        if (ShiroUtils.isSpOperator()) {
            final List<Map<String, Object>> relateList = queryService.query("relatedList",
                    Collections.singletonMap("faqId", docFaqId));
            List<String> apis = new ArrayList<>(relateList.size());
            List<Long> pages = new ArrayList<>(relateList.size());
            relateList.forEach(relateMap -> {
                final String relateType = (String) relateMap.get("relate_type");
                final String relateId = (String) relateMap.get("relate_id");
                if (relateType.equals("API")) {
                    apis.add(relateId);
                } else{
                    pages.add(Long.valueOf(relateId));
                }
            });
            boolean apiAcl = CollectionUtils.isEmpty(apis), pageAcl = CollectionUtils.isEmpty(pages);
            if (!apiAcl) {
                Map<String, Object> paramsMap = Maps.newHashMap();
                paramsMap.put("operator", ShiroUtils.getOperatorCode());
                paramsMap.put("apis", apis);
                if (CollectionUtils.size(apiManageQueryService.query("aclByApis", paramsMap)) == 0) {
                    log.warn("data access deny，operator:{}, apis:{}", ShiroUtils.getOperatorCode(), apis);
                    return false;
                }
                apiAcl = true;
            }
            if (!pageAcl) {
                final List<String> spDocNos = docQueryService.getSpDocNos();
                if (CollectionUtils.isEmpty(spDocNos)) {
                    log.warn("data access deny，operator:{}, pages:{}", ShiroUtils.getOperatorCode(), pages);
                    return false;
                }
                Map<String, Object> paramsMap = Maps.newHashMap();
                paramsMap.put("pages", pages);
                paramsMap.put("docNos", spDocNos);
                pageAcl = CollectionUtils.size(queryService.query("aclByPages", paramsMap)) > 0;
            }
            return apiAcl && pageAcl;
        }
        return true;
    }

    private Date toLocalDateTime(Map<String, Object> resultMap, String columnName) {
        return (Date) resultMap.get(columnName);
    }

    class DocFaqItemConverter extends BasePageItemConverter<DocFaqItem> {

        @Override
        public DocFaqItem convert(Map<String, Object> params) {
            DocFaqItem item = new DocFaqItem();
            item.setId((Long) params.get("id"));
            item.setTitle((String) params.get("title"));
            item.setStatus((String) params.get("status"));
            item.setScore((Integer) params.get("score"));
            item.setTop(((Integer) params.get("top")) > 0);
            item.setCreatedDate(toLocalDateTime(params, "created_date"));
            item.setLastModifiedDate(toLocalDateTime(params, "last_modified_date"));
            return item;
        }
    }
}
