/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.json.JSONUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;

import static java.lang.Thread.sleep;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/2/11 15:51
 */
@RestController
@RequestMapping("/rest/notify-receive")
public class NotifyReceiveController {
    @PostMapping("/rsa")
    public void rsa(HttpServletResponse response) {
        Long sleepMillis = (Long) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_NOTIFY_RECEIVE_SLEEP_MILLIS);
        try {
            sleep(sleepMillis);
        } catch (InterruptedException e) {
            // ignore
        }
        response.setContentType("text/plain");
        try {
            response.getOutputStream().write("SUCCESS".getBytes("UTF-8"));
        } catch (IOException e) {
            throw new YeepayRuntimeException(e);
        }
    }

    @PostMapping("/sm2")
    public void sm(HttpServletResponse response) {
        Long sleepMillis = (Long) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_NOTIFY_RECEIVE_SLEEP_MILLIS);
        try {
            sleep(sleepMillis);
        } catch (InterruptedException e) {
            // ignore
        }
        HashMap<String, String> map = new HashMap<>();
        map.put("result", "SUCCESS");
        String respStr = JSONUtils.toJsonString(map);

        response.setContentType("application/json");
        try {
            response.getOutputStream().write(respStr.getBytes("UTF-8"));
        } catch (IOException e) {
            throw new YeepayRuntimeException(e);
        }
    }

}
