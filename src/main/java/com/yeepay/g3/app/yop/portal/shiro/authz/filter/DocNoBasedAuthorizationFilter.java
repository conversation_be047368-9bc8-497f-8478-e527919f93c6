package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * title: 基于产品授权记录id的权限控制<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-27 20:24
 */
@Component
public class DocNoBasedAuthorizationFilter extends AbstractDocAuthorizationFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(DocNoBasedAuthorizationFilter.class);

    @Override
    protected String[] getSpCodes(String[] params) {
        if (null != params && params.length > 0) {
            try {
                Set<String> spCodesSet = new LinkedHashSet<>();
                for (int i = 0; i < params.length; i++) {
                    final List<String> ownerSpCodes = docNoLocalCache.get(params[i]);
                    if (CollectionUtils.isNotEmpty(ownerSpCodes)) {
                        spCodesSet.addAll(ownerSpCodes);
                    }
                }
                return resolveSpCodes(spCodesSet);
            } catch (ExecutionException e) {
                LOGGER.info("get spCode by docNo wrong, params are :{},exception is :{}", params, e);
            }
        }
        return EMPTY;
    }

    @Override
    public String shiroName() {
        return "doc_no_based";
    }
}
