/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.ServletRequest;
import java.io.IOException;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-29 14:33
 */
public class RequestUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(RequestUtils.class);

    private static final String CONTENT_TYPE_JSON = "application/json";

    private static final ObjectMapper OBJECT_MAPPER = JsonMapper.nonEmptyMapper().getMapper();

    public static String[] getParamValue(ServletRequest request, String paramName) {
        String[] params;
        if (StringUtils.startsWith(request.getContentType(), CONTENT_TYPE_JSON)) {
            try {
                String json;
                try {
                    json = IOUtils.toString(request.getInputStream());
                } catch (IOException e) {
                    LOGGER.error("json类型输入流获取异常,", e);
                    throw new RuntimeException("json类型输入流获取异常");
                }

//                "/store/book/title"
                JsonNode rootNode = OBJECT_MAPPER.readTree(json);
                if (rootNode.at(paramName).getClass() == ArrayNode.class) {
                    ArrayNode arrayNode = (ArrayNode) rootNode.at(paramName);
                    params = new String[arrayNode.size()];
                    for (int i = 0; i < arrayNode.size(); i++) {
                        params[i] = arrayNode.get(i).toString();
                    }
                } else {
                    params = new String[]{rootNode.at(paramName).asText()};
                }
            } catch (Exception e) {
                LOGGER.error("入参json解析异常,", e);
                return null;
            }
        } else {
            params = request.getParameterValues(paramName);
        }
        return params;
    }

}
