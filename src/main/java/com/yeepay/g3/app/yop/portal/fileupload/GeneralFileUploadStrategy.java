/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.fileupload;

import com.yeepay.g3.core.yop.utils.time.DateFormatUtil;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/15 11:51
 */
@Component
public class GeneralFileUploadStrategy implements FileUploadStrategy {
    @Override
    public String resolveFileName(String bizCode, String fileId, String extName) {
        Date now = new Date();
        return "attachments/" + DateFormatUtil.formatDate("yyyy/MM/dd", now) + "/" + bizCode + "_" + fileId + "_" + now.getTime() + extName;
    }

    @Override
    public String name() {
        return "general";
    }

    @PostConstruct
    public void register() {
        FileUploadFactory.register(this);
    }
}
