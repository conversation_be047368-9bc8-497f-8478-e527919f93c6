package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yeepay.g3.facade.yop.perm.enums.ResourceStatusEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * title: 用户拥有的资源-菜单树<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2016<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/7/23 14:44
 */
public class AclMenuRefVO implements Serializable {

    private static final long serialVersionUID = -2165465906389094103L;

    private Long id;

    @JsonProperty("name")
    private String resourceName;

    private String url;

    private String icon;

    private ResourceStatusEnum status;

    private List<AclMenuRefVO> children;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public ResourceStatusEnum getStatus() {
        return status;
    }

    public void setStatus(ResourceStatusEnum status) {
        this.status = status;
    }

    public List<AclMenuRefVO> getChildren() {
        return children;
    }

    public void setChildren(List<AclMenuRefVO> children) {
        this.children = children;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
