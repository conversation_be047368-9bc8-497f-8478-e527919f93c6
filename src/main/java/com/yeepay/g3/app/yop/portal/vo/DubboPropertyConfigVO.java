package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * DubboPropertyConfigVO
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.v3.generators.java.SpringCodegen", date = "2020-11-03T07:28:20.629Z[GMT]")


public class DubboPropertyConfigVO implements OneOfApiRouteVOProperties, Serializable {

    private static final long serialVersionUID = -1;

    @JsonProperty("endClass")
    private String endClass = null;

    @JsonProperty("method")
    private String method = null;

    @JsonProperty("readTimeout")
    private Integer readTimeout = null;

    @JsonProperty("connectTimeout")
    private Integer connectTimeout = null;

    public DubboPropertyConfigVO endClass(String endClass) {
        this.endClass = endClass;
        return this;
    }

    /**
     * 端点类名
     *
     * @return endClass
     **/
    @Schema(required = true, description = "端点类名")
    @NotNull

    public String getEndClass() {
        return endClass;
    }

    public void setEndClass(String endClass) {
        this.endClass = endClass;
    }

    public DubboPropertyConfigVO method(String method) {
        this.method = method;
        return this;
    }

    /**
     * 端点方法
     *
     * @return method
     **/
    @Schema(required = true, description = "端点方法")
    @NotNull

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public DubboPropertyConfigVO readTimeout(Integer readTimeout) {
        this.readTimeout = readTimeout;
        return this;
    }

    /**
     * 读超时
     *
     * @return readTimeout
     **/
    @Schema(description = "读超时")

    public Integer getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(Integer readTimeout) {
        this.readTimeout = readTimeout;
    }

    public DubboPropertyConfigVO connectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
        return this;
    }

    /**
     * 连接超时
     *
     * @return connectTimeout
     **/
    @Schema(description = "连接超时")

    public Integer getConnectTimeout() {
        return connectTimeout;
    }

    public void setConnectTimeout(Integer connectTimeout) {
        this.connectTimeout = connectTimeout;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DubboPropertyConfigVO dubboPropertyConfigVO = (DubboPropertyConfigVO) o;
        return Objects.equals(this.endClass, dubboPropertyConfigVO.endClass) &&
                Objects.equals(this.method, dubboPropertyConfigVO.method) &&
                Objects.equals(this.readTimeout, dubboPropertyConfigVO.readTimeout) &&
                Objects.equals(this.connectTimeout, dubboPropertyConfigVO.connectTimeout);
    }

    @Override
    public int hashCode() {
        return Objects.hash(endClass, method, readTimeout, connectTimeout);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DubboPropertyConfigVO {\n");

        sb.append("    endClass: ").append(toIndentedString(endClass)).append("\n");
        sb.append("    method: ").append(toIndentedString(method)).append("\n");
        sb.append("    readTimeout: ").append(toIndentedString(readTimeout)).append("\n");
        sb.append("    connectTimeout: ").append(toIndentedString(connectTimeout)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }
}
