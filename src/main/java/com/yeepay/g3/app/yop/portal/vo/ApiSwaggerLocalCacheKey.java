/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/8 7:04 PM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiSwaggerLocalCacheKey implements Serializable {

    private static final long serialVersionUID = -1L;

    private String path;

    private String method;
}
