/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.CustomSolutionCreate;
import com.yeepay.g3.app.yop.portal.validation.group.CustomSolutionEdit;
import com.yeepay.g3.facade.yop.sys.enums.CustomSolutionStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.CustomSolutionTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * title: 自定义解决方案<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/14
 */
@Data
public class CustomSolutionVO implements Serializable {

    private static final long serialVersionUID = -1L;

    @NotNull(groups = {CustomSolutionEdit.class})
    private Long id;

    @NotNull(groups = {CustomSolutionCreate.class, CustomSolutionEdit.class})
    @Size(min = 1, max = 32, groups = {CustomSolutionCreate.class})
    private String spCode;

    @NotNull(groups = {CustomSolutionCreate.class, CustomSolutionEdit.class})
    @Size(min = 1, max = 32, groups = {CustomSolutionCreate.class, CustomSolutionEdit.class})
    private String solutionCode;

    @NotNull(groups = {CustomSolutionCreate.class, CustomSolutionEdit.class})
    @Size(min = 1, max = 64, groups = {CustomSolutionCreate.class, CustomSolutionEdit.class})
    private String solutionName;

    private CustomSolutionTypeEnum type = CustomSolutionTypeEnum.CUSTOM;

    @Size(max = 512, groups = {CustomSolutionCreate.class, CustomSolutionEdit.class})
    private String description;

    private CustomSolutionStatusEnum status;

    private String operatorCode;
}
