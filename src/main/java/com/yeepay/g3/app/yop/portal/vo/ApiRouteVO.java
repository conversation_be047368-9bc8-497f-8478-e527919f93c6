package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yeepay.g3.facade.yop.sys.dto.route.ApiRouteDTO;
import com.yeepay.g3.facade.yop.sys.dto.route.ParamConfig;
import com.yeepay.g3.facade.yop.sys.dto.route.ResponseConfig;
import com.yeepay.g3.facade.yop.sys.enums.ApiRouteTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.ParamCategoryEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ApiRouteVO
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.v3.generators.java.SpringCodegen", date = "2020-11-03T07:28:20.629Z[GMT]")


public class ApiRouteVO implements Serializable {

    private static final long serialVersionUID = -1;

    private ApiRouteBasicVO basic;

    private ApiRouteRequestVO request;

    private ApiRouteResponseVO response;

    public ApiRouteBasicVO getBasic() {
        return basic;
    }

    public void setBasic(ApiRouteBasicVO basic) {
        this.basic = basic;
    }

    public ApiRouteRequestVO getRequest() {
        return request;
    }

    public void setRequest(ApiRouteRequestVO request) {
        this.request = request;
    }

    public ApiRouteResponseVO getResponse() {
        return response;
    }

    public void setResponse(ApiRouteResponseVO response) {
        this.response = response;
    }

    public static ApiRouteDTO convert(ApiRouteVO apiRouteVO) {
        ApiRouteDTO apiRouteDTO = new ApiRouteDTO();
        //1、基础信息
        ApiRouteBasicVO apiRouteBasicVO = apiRouteVO.getBasic();
        apiRouteDTO.setId(apiRouteBasicVO.getId());
        apiRouteDTO.setName(apiRouteBasicVO.getName());
        apiRouteDTO.setApiId(apiRouteBasicVO.getApiId());
        apiRouteDTO.setPredicate(apiRouteBasicVO.getPredicate());
        apiRouteDTO.setType(ApiRouteTypeEnum.valueOf(apiRouteBasicVO.getType()));
        apiRouteDTO.setFilters(apiRouteBasicVO.getFilters());
        apiRouteDTO.setServiceName(apiRouteBasicVO.getServiceName());
        apiRouteDTO.setProperties(apiRouteBasicVO.getProperties());
        Map<String, Object> properties = apiRouteDTO.getProperties();

        //2、入参信息
        ApiRouteRequestVO apiRouteRequestVO = apiRouteVO.getRequest();
        List<ParamConfig> alls = new ArrayList<>();
        if (MapUtils.isNotEmpty(properties)) {
            if (properties.containsKey("parameterHandlingType") && "MAPPING".equals(properties.get("parameterHandlingType"))) {
                //如果入参请求模式是映射，接业务参数
                apiRouteRequestVO.getServiceParameters().stream().forEach(param -> param.setCategory(ParamCategoryEnum.INPUT));
                checkParamConfigDuplicate(apiRouteRequestVO.getServiceParameters());
            } else if (properties.containsKey("parameterHandlingType") && "PASSTHROUGH".equals(properties.get("parameterHandlingType"))) {
                //如果入参请求模式是映射，bu接业务参数
                apiRouteRequestVO.setServiceParameters(new ArrayList<>());
            }
        }
        apiRouteRequestVO.getConstantParameters().stream().forEach(param -> param.setCategory(ParamCategoryEnum.CONSTANT));
        //校验name和value重复
        checkParamConfigDuplicate(apiRouteRequestVO.getConstantParameters());
        apiRouteRequestVO.getSystemParameters().stream().forEach(param -> param.setCategory(ParamCategoryEnum.SYSTEM));
        checkParamConfigDuplicate(apiRouteRequestVO.getSystemParameters());
        alls.addAll(apiRouteRequestVO.getConstantParameters());
        alls.addAll(apiRouteRequestVO.getServiceParameters());
        alls.addAll(apiRouteRequestVO.getSystemParameters());
        apiRouteDTO.setParameters(alls);

        //3、出参信息
        ApiRouteResponseVO apiRouteResponseVO = apiRouteVO.getResponse();
        ArrayList<ResponseConfig> responseConfigs = new ArrayList<>();
        if (MapUtils.isNotEmpty(properties)) {
            if (properties.containsKey("responseHandlingType") && "MAPPING".equals(properties.get("responseHandlingType"))) {
                //如果出参请求模式是映射，接业务参数
                apiRouteResponseVO.getServiceResponse().stream().forEach(responseConfig -> responseConfig.setCategory(ParamCategoryEnum.OUTPUT));
                //校验name和value重复
                checkResponseConfigDuplicate(apiRouteResponseVO.getServiceResponse());
            } else if (properties.containsKey("responseHandlingType") && "PASSTHROUGH".equals(properties.get("responseHandlingType"))) {
                //如果入参请求模式是映射，bu接业务参数
                apiRouteResponseVO.setServiceResponse(new ArrayList<>());
            }
        }
        responseConfigs.addAll(apiRouteResponseVO.getServiceResponse());
        apiRouteDTO.setResponse(responseConfigs);

        return apiRouteDTO;
    }

    public static ApiRouteVO convert(ApiRouteDTO apiRouteDTO) {
        ApiRouteVO apiRouteVO = new ApiRouteVO();
        //1、基础信息
        ApiRouteBasicVO apiRouteBasicVO = new ApiRouteBasicVO();
        apiRouteBasicVO.setId(apiRouteDTO.getId());
        apiRouteBasicVO.setName(StringUtils.isEmpty(apiRouteDTO.getName()) ? "默认路由" : apiRouteDTO.getName());
        apiRouteBasicVO.setApiId(apiRouteDTO.getApiId());
        apiRouteBasicVO.setPredicate(apiRouteDTO.getPredicate());
        apiRouteBasicVO.setServiceName(apiRouteDTO.getServiceName());
        apiRouteBasicVO.setType(apiRouteDTO.getType().name());
        apiRouteBasicVO.setProperties(apiRouteDTO.getProperties());
        apiRouteBasicVO.setFilters(apiRouteDTO.getFilters());

        //2、入参信息
        ApiRouteRequestVO apiRouteRequestVO = new ApiRouteRequestVO();
        List<ParamConfig> constantParameters = new ArrayList<>();
        List<ParamConfig> systemParameters = new ArrayList<>();
        List<ParamConfig> serviceParameters = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(apiRouteDTO.getParameters())) {
            constantParameters = apiRouteDTO.getParameters().stream().filter(paramConfig -> ParamCategoryEnum.CONSTANT == paramConfig.getCategory()).collect(Collectors.toList());
            systemParameters = apiRouteDTO.getParameters().stream().filter(paramConfig -> ParamCategoryEnum.SYSTEM == paramConfig.getCategory()).collect(Collectors.toList());
            serviceParameters = apiRouteDTO.getParameters().stream().filter(paramConfig -> ParamCategoryEnum.INPUT == paramConfig.getCategory()).collect(Collectors.toList());
        }
        apiRouteRequestVO.setConstantParameters(constantParameters);
        apiRouteRequestVO.setSystemParameters(systemParameters);
        apiRouteRequestVO.setServiceParameters(serviceParameters);

        //3、出参信息
        ApiRouteResponseVO apiRouteResponseVO = new ApiRouteResponseVO();
        List<ResponseConfig> serviceResponse = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(apiRouteDTO.getResponse())) {
            serviceResponse = apiRouteDTO.getResponse().stream().filter(responseConfig -> ParamCategoryEnum.OUTPUT == responseConfig.getCategory()).collect(Collectors.toList());
        }
        apiRouteResponseVO.setServiceResponse(serviceResponse);

        apiRouteVO.setBasic(apiRouteBasicVO);
        apiRouteVO.setRequest(apiRouteRequestVO);
        apiRouteVO.setResponse(apiRouteResponseVO);
        return apiRouteVO;
    }

    public static void checkParamConfigDuplicate(List<ParamConfig> list) {
        Map<String, String> cache = new HashMap();
        for (ParamConfig p : list) {
            String key = p.getName() + p.getIn().name() + p.getIndex();
            if (MapUtils.isNotEmpty(cache) && cache.containsKey(key)) {
                throw new IllegalArgumentException(p.getName() + "-" + p.getIn().name() + "-" + p.getIndex() + ", is duplicated");
            } else {
                cache.put(key, p.getValue());
            }
        }
    }

    public static void checkResponseConfigDuplicate(List<ResponseConfig> list) {
        Map<String, String> cache = new HashMap();
        for (ResponseConfig p : list) {
            if (MapUtils.isNotEmpty(cache) && cache.containsKey(p.getName())) {
                throw new IllegalArgumentException(p.getName() + " is duplicated");
            } else {
                cache.put(p.getName(), p.getValue());
            }
            if (CollectionUtils.isNotEmpty(p.getSub())) {
                Map<String, String> subCache = new HashMap();
                List<ResponseConfig> sub = p.getSub();
                for (ResponseConfig s : sub) {
                    if (MapUtils.isNotEmpty(subCache) && subCache.containsKey(s.getName())) {
                        throw new IllegalArgumentException(s.getName() + " is duplicated");
                    } else {
                        subCache.put(s.getName(), s.getValue());
                    }
                }
            }
        }
    }

    public static class ApiRouteBasicVO implements Serializable {
        private static final long serialVersionUID = -1;
        @JsonProperty("apiId")
        private String apiId;

        @JsonProperty("id")
        private Long id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("type")
        private String type;

        @JsonProperty("serviceName")
        private String serviceName;

        @JsonProperty("predicate")
        private String predicate;

        @JsonProperty("filters")
        private String filters;

        @JsonProperty("properties")
        private Map<String, Object> properties;

        /**
         * Get id
         *
         * @return id
         **/
        @Schema(description = "")
        public String getApiId() {
            return apiId;
        }

        public void setApiId(String apiId) {
            this.apiId = apiId;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        /**
         * 后端类型
         *
         * @return type
         **/
        @Schema(required = true, description = "后端类型")
        @NotNull
        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        /**
         * 后端服务
         *
         * @return serviceName
         **/
        @Schema(required = true, description = "后端服务")
        @NotNull
        public String getServiceName() {
            return serviceName;
        }

        public void setServiceName(String serviceName) {
            this.serviceName = serviceName;
        }

        /**
         * 谓词
         *
         * @return predicate
         **/
        @Schema(description = "谓词")
        public String getPredicate() {
            return predicate;
        }

        public void setPredicate(String predicate) {
            this.predicate = predicate;
        }

        /**
         * 策略
         *
         * @return filters
         **/
        @Schema(description = "策略")
        public String getFilters() {
            return filters;
        }

        public void setFilters(String filters) {
            this.filters = filters;
        }

        /**
         * Get id
         *
         * @return id
         **/
        @Schema(description = "")
        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        /**
         * Get properties
         *
         * @return properties
         **/
        @Schema(description = "")
        public Map<String, Object> getProperties() {
            return properties;
        }

        public void setProperties(Map<String, Object> properties) {
            this.properties = properties;
        }
    }

    public static class ApiRouteRequestVO implements Serializable {
        private static final long serialVersionUID = -1;

        @JsonProperty("constantParameters")
        @Valid
        private List<ParamConfig> constantParameters;

        @JsonProperty("systemParameters")
        @Valid
        private List<ParamConfig> systemParameters;

        @JsonProperty("serviceParameters")
        @Valid
        private List<ParamConfig> serviceParameters;

        /**
         * 常量参数
         *
         * @return constantParameters
         **/
        @Schema(description = "常量参数")
        @Valid
        public List<ParamConfig> getConstantParameters() {
            return constantParameters;
        }

        public void setConstantParameters(List<ParamConfig> constantParameters) {
            this.constantParameters = constantParameters;
        }

        /**
         * 系统参数
         *
         * @return systemParameters
         **/
        @Schema(description = "系统参数")
        @Valid
        public List<ParamConfig> getSystemParameters() {
            return systemParameters;
        }

        public void setSystemParameters(List<ParamConfig> systemParameters) {
            this.systemParameters = systemParameters;
        }

        /**
         * 后端服务入参映射
         *
         * @return serviceParameters
         **/
        @Schema(description = "后端服务入参映射")
        @Valid
        public List<ParamConfig> getServiceParameters() {
            return serviceParameters;
        }

        public void setServiceParameters(List<ParamConfig> serviceParameters) {
            this.serviceParameters = serviceParameters;
        }


    }

    public static class ApiRouteResponseVO implements Serializable {
        private static final long serialVersionUID = -1;

        @JsonProperty("serviceResponse")
        @Valid
        private List<ResponseConfig> serviceResponse;

        /**
         * 后端服务出参映射
         *
         * @return serviceResponse
         **/
        @Schema(description = "后端服务出参映射")
        @Valid
        public List<ResponseConfig> getServiceResponse() {
            return serviceResponse;
        }

        public void setServiceResponse(List<ResponseConfig> serviceResponse) {
            this.serviceResponse = serviceResponse;
        }
    }
}
