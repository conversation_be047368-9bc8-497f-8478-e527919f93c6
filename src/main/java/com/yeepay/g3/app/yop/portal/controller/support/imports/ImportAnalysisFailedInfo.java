package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.enums.ImportItemEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * title: 导入分析失败信息<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 11:02
 */
public class ImportAnalysisFailedInfo implements Serializable {

    private static final long serialVersionUID = -1L;

    private ImportItemEnum item;

    private String value;

    private String reason;

    public ImportItemEnum getItem() {
        return item;
    }

    public void setItem(ImportItemEnum item) {
        this.item = item;
    }

    public ImportAnalysisFailedInfo withItem(ImportItemEnum item) {
        this.item = item;
        return this;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public ImportAnalysisFailedInfo withValue(String value) {
        this.value = value;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public ImportAnalysisFailedInfo withReason(String reason) {
        this.reason = reason;
        return this;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
