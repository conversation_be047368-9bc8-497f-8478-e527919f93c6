/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.fileupload;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/15 11:47
 */
public class FileUploadFactory {
    private static Map<String, FileUploadStrategy> map = new HashMap<>();

    public static void register(FileUploadStrategy fileUploadStrategy) {
        map.put(fileUploadStrategy.name(), fileUploadStrategy);
    }

    public static FileUploadStrategy get(String strategyName) {
        return map.get(strategyName);
    }
}
