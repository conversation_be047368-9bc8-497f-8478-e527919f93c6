/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.cache.AclResourceLocalCache;
import com.yeepay.g3.app.yop.portal.enums.AclOperStatusEnum;
import com.yeepay.g3.app.yop.portal.service.AclLogQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.AclLogDetailVO;
import com.yeepay.g3.app.yop.portal.vo.AclResourceVO;
import com.yeepay.g3.app.yop.portal.vo.page.AclLogPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.AclLogPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.query.QueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.yeepay.g3.app.yop.portal.utils.Constants.NON_EXISTS_OPERATOR_CODE;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-12-17 10:46
 */
@Component
public class AclLogQueryServiceImpl implements AclLogQueryService {

    private final PageItemConverter<AclLogPageItem> pageItemConverter = new AclLogQueryServiceImpl.AclLogPageItemConverter();
    @Resource(name = "aclLogQueryService")
    private QueryService queryService;

    @Resource(name = "aclResourceQueryService")
    private QueryService resourceQueryService;

    @Resource(name = "aclUserQueryService")
    private QueryService userQueryService;

    @Autowired
    private AclResourceLocalCache aclResourceLocalCache;

    @Override
    public PageQueryResult<AclLogPageItem> pageQuery(AclLogPageQueryParam param) {
        Map<String, String> resource = aclResourceLocalCache.get(param.getId() != null ? param.getId() : 0);
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("operatorCode", param.getOperator());
        if (param.getId() != null) {
            List<String> resourceLists = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(resource.keySet())) {
                resourceLists = new ArrayList<>(resource.keySet());
            }
            bizParams.put("resourceUrls", resourceLists);
        }
        bizParams.put("operStartDate", param.getOperStartDate());
        bizParams.put("operEndDate", param.getOperEndDate());
        if (ShiroUtils.isSpOperator()) {
            if (CollectionUtils.isEmpty(ShiroUtils.getShiroUser().getSpScopes())) {
                bizParams.put("operatorCodes", Collections.singletonList(NON_EXISTS_OPERATOR_CODE));
            } else {
                Map<String, Object> query = Maps.newHashMap();
                query.put("spCodes", ShiroUtils.getShiroUser().getSpScopes());
                List<Map> userMap = userQueryService.query("pageListForSp", query);
                List<String> operators = new ArrayList<>();
                for (Map map : userMap) {
                    operators.add((String) map.get("operator_code"));
                }
                bizParams.put("operatorCodes", operators);
            }
        }
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        List<Map> resultMap = queryService.query("pageList", bizParams);

        PageQueryResult<AclLogPageItem> result = new PageQueryResult<>();
        List<AclLogPageItem> items = new ArrayList<>(CollectionUtils.size(resultMap));
        if (CollectionUtils.isNotEmpty(resultMap)) {
            for (Map map : resultMap) {
                AclLogPageItem item = pageItemConverter.convert(map);
                item.setResourceName(resource.get(map.get("url")) != null ? resource.get(map.get("url")) : (String) map.get("url"));
                items.add(item);
            }
        }
        result.setItems(items);
        result.setPageNo(pageNo);
        return result;
    }

    @Override
    public List<AclResourceVO> queryTopReosurce() {
        List<Map> resourceMap = resourceQueryService.query("topResourceQuery", new HashMap<>());
        List<AclResourceVO> lists = new ArrayList<>();
        for (int i = 0; i < resourceMap.size(); i++) {
            Map map = resourceMap.get(i);
            AclResourceVO resourceVO = new AclResourceVO();
            resourceVO.setResourceName((String) map.get("resource_name"));
            resourceVO.setId((Long) map.get("id"));
            resourceVO.setUrl((String) map.get("url"));
            lists.add(resourceVO);
        }
        return lists;
    }

    @Override
    public AclResourceVO queryResources() {
        List<AclResourceVO> resourceStack = new ArrayList<>();
        AclResourceVO resourceRoot = new AclResourceVO();
        resourceRoot.setId(0L);
        resourceStack.add(resourceRoot);
        int header = 0;
        while (header < resourceStack.size()) {
            AclResourceVO currentResource = resourceStack.get(header);
            long parentId = currentResource.getId();
            List<AclResourceVO> children = queryNextLevelResources(parentId);
            currentResource.setChildren(children);
            for (AclResourceVO aclResource : children) {
                resourceStack.add(aclResource);
            }
            header++;
        }
        return resourceRoot;
    }

    @Cacheable(value = "yp:res", key = "#id")
    public List<AclResourceVO> queryNextLevelResources(long id) {
        if (id == 0L) {
            return queryTopReosurce();
        }
        Map<String, Object> queryParam = new HashMap<>(3);
        queryParam.put("id", id);
        List<Map> resourceMap = resourceQueryService.query("nextLevelResourceQuery", queryParam);
        return nextLevelResourceConvert(resourceMap);
    }

    @Override
    public AclLogDetailVO findDetail(Long id, Date date) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("id", id);
        Map resultMap = queryService.queryUnique("queryDetail", bizParams, false);
        AclLogDetailVO detailVO = new AclLogDetailVO();
        detailVO.setContent((String) resultMap.get("request_content"));
        detailVO.setDetail((String) resultMap.get("detail"));
        detailVO.setStatus(AclOperStatusEnum.parse((String) resultMap.get("status")));
        return detailVO;
    }

    private List<AclResourceVO> nextLevelResourceConvert(List<Map> resourceMap) {
        List<AclResourceVO> lists = new ArrayList<>();
        for (Map resource : resourceMap) {
            AclResourceVO aclResourceVO = new AclResourceVO();
            aclResourceVO.setId((Long) resource.get("id"));
            aclResourceVO.setResourceName((String) resource.get("resource_name"));
            lists.add(aclResourceVO);
        }
        return lists;
    }

    class AclLogPageItemConverter extends BasePageItemConverter<AclLogPageItem> {

        @Override
        public AclLogPageItem convert(Map<String, Object> params) {
            AclLogPageItem item = new AclLogPageItem();
            item.setOperator((String) params.get("operator_code"));
            item.setLatency((Integer) params.get("latency"));
            item.setOperDate((Date) params.get("oper_datetime"));
            item.setRequestIp((String) params.get("request_ip"));
            item.setResourceName((String) params.get("resource_name"));
            item.setStatus(AclOperStatusEnum.parse((String) params.get("status")));
            item.setId((Long) params.get("id"));
            return item;
        }
    }
}
