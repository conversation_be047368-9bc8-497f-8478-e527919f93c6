package com.yeepay.g3.yop.gateway.context.authenticate.impl;

import com.yeepay.g3.facade.yop.ca.enums.CertTypeEnum;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateProtocolVersion;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateStrategyEnum;

import java.util.List;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 16/11/30 下午3:29
 */
public class HmacContext extends AbstractYopAuthenticateContext {

    protected boolean encrypt;

    /**
     * 密钥，不管是应用是一个密钥抑或处于过渡期有多个密钥，只要此值不为空，
     * 说明密钥已被确定下来，后续的加密签名均需使用此密钥
     */
    protected String appSecret;

    /**
     * 如果有多组密钥（可能处于迁移阶段），将多组密钥列于此
     */
    protected List<String> appSecrets;

    protected String sign;

    protected CertTypeEnum certType;

    /**
     * 是否加密
     *
     * @return
     */
    public boolean isEncrypt() {
        return encrypt;
    }

    public void setEncrypt(boolean encrypt) {
        this.encrypt = encrypt;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public List<String> getAppSecrets() {
        return appSecrets;
    }

    public void setAppSecrets(List<String> appSecrets) {
        this.appSecrets = appSecrets;
    }

    public String getSign() {
        return sign;
    }

    public CertTypeEnum getCertType() {
        return certType;
    }

    @Override
    public void initialize(String protocolPrefix, String protocolContent) {
        // 返回V2版本，因为hmac、basic、oauth2等实际都还没有认证协议版本控制
        authenticateProtocolVersion = AuthenticateProtocolVersion.YOP_V2;
        sign = protocolContent;
        authenticateStrategy = AuthenticateStrategyEnum.YOP_HMAC_AES128;
        certType = CertTypeEnum.AES128;
    }
}
