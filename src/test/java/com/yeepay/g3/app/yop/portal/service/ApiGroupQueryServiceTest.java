/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/13 10:02 上午
 */
public class ApiGroupQueryServiceTest extends SpringBootTests {
    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    @Test
    public void testQuerySp() {
        String apiGroup = "test";
        String spCode = apiGroupQueryService.findSp(apiGroup);
        Assert.assertEquals("PLATFORM", spCode);
    }
}
