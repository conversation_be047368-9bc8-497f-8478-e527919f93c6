/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import java.util.List;

/**
 * title: 常见问题待关联项<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/12/21
 */
@Builder
@AllArgsConstructor
@Data
public class DocFaqRelateItemVO extends BaseVO {

    /**
     * 关联文章的标识(apiId/pageId)
     */
    private String id;

    /**
     * 关联类型
     */
    private String type;

    /**
     * 文章标题
     */
    private String title;

    /**
     * 文章版本
     */
    private String version = "";

    /**
     * 关联文章的子集
     */
    @Singular
    private List<DocFaqRelateItemVO> children;

}
