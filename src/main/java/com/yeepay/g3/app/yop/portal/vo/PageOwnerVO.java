/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import java.time.LocalDateTime;

/**
 * title: 页面负责人VO<br>
 * description: 页面负责人视图对象<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-26
 */
public class PageOwnerVO {
    
    private String id;
    private String name;
    private LocalDateTime assignedTime;
    
    public PageOwnerVO() {
    }
    
    public PageOwnerVO(String id, String name, LocalDateTime assignedTime) {
        this.id = id;
        this.name = name;
        this.assignedTime = assignedTime;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public LocalDateTime getAssignedTime() {
        return assignedTime;
    }
    
    public void setAssignedTime(LocalDateTime assignedTime) {
        this.assignedTime = assignedTime;
    }
    
    @Override
    public String toString() {
        return "PageOwnerVO{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", assignedTime=" + assignedTime +
                '}';
    }
}