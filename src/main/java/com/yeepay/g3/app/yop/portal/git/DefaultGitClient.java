package com.yeepay.g3.app.yop.portal.git;

import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.frame.yop.ca.utils.Encodes;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import okhttp3.OkHttpClient;
import org.springframework.stereotype.Component;
import retrofit2.Call;
import retrofit2.Response;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;
import retrofit2.http.GET;
import retrofit2.http.HEAD;
import retrofit2.http.Path;
import retrofit2.http.Query;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * title: GitClient<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-12 14:14
 */
@Component
public class DefaultGitClient implements GitClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(DefaultGitClient.class);

    private GitTransport gitTransport;

    @Override
    public String getLatestCommit(String gitRepository, String gitBranch, String filePath) {
        Call<Void> call = gitTransport.getFileMetaData(gitRepository, filePath, gitBranch);
        try {
            Response<Void> result = call.execute();
            return result.headers().get("X-Gitlab-Commit-Id");
        } catch (IOException ex) {
            throw new YeepayRuntimeException("get branches failed.", ex);
        }
    }

    @Override
    public List<GitBranch> getBranches(String gitRepository) {
        Call<List<GitBranch>> call = gitTransport.listBranches(gitRepository);
        try {
            return call.execute().body();
        } catch (IOException ex) {
            throw new YeepayRuntimeException("get branches failed.", ex);
        }
    }

    @Override
    public GitFile getFile(String gitRepository, String filePath, String commitId) {
        Call<GitFile> call = gitTransport.getFile(gitRepository, filePath, commitId);
        try {
            GitFile result = call.execute().body();
            if (result != null) {
                result.setContent(new String(Encodes.decodeBase64(result.getContent())));
            }
            return result;
        } catch (IOException ex) {
            throw new YeepayRuntimeException("get file failed.", ex);
        }
    }

    @PostConstruct
    public void init() {
        Map<String, String> gitClientConfig = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_GIT_SYNC_CLIENT_CONFIG);
        Retrofit retrofit = new Retrofit.Builder().baseUrl(gitClientConfig.get("baseUrl"))
                .addConverterFactory(JacksonConverterFactory.create(JsonMapper.nonNullMapper().getMapper()))
                .client(new OkHttpClient().newBuilder().connectTimeout(10, TimeUnit.SECONDS).build())
                .build();
        gitTransport = retrofit.create(GitTransport.class);
    }


    public interface GitTransport {

        @GET("projects/{id}/repository/branches")
        Call<List<GitBranch>> listBranches(@Path(value = "id") String repository);


        @HEAD("projects/{id}/repository/files/{filePath}/raw")
        Call<Void> getFileMetaData(@Path(value = "id") String repository,
                                   @Path(value = "filePath") String filePath,
                                   @Query("ref") String ref);


        @GET("projects/{id}/repository/files/{filePath}")
        Call<GitFile> getFile(@Path(value = "id") String repository,
                              @Path(value = "filePath") String filePath,
                              @Query("ref") String ref);

    }

}
