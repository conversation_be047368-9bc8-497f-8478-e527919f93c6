package com.yeepay.g3.app.yop.portal.shiro.authc;

import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.app.yop.portal.shiro.utils.OAuth2TokenUtils;
import com.yeepay.g3.app.yop.portal.utils.SpringContextUtil;
import com.yeepay.g3.facade.employee.dto.sso.SSOLoginInfoDTO;
import com.yeepay.g3.facade.employee.facade.UserLoginFacade;
import com.yeepay.g3.facade.employee.sso.exception.SSOException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.*;


/**
 * title: BOSS3g 颁发的令牌<br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/5/31 下午2:51
 */
public class Boss3gToken implements AuthenticationToken {

    private static final long serialVersionUID = 1L;

    private static final Logger LOGGER = LoggerFactory.getLogger(Boss3gToken.class);

    private String token;

    public Boss3gToken() {
        super();
    }

    public Boss3gToken(String token) {
        this.token = token;
    }

    @Override
    public Object getPrincipal() {
        return token;
    }

    @Override
    public Object getCredentials() {
        return token;
    }

    public AuthenticationInfo doGetAuthenticationInfo() throws AuthenticationException {
        try {
            UserLoginFacade userLoginFacade = RemoteServiceFactory.getService(UserLoginFacade.class);
            SSOLoginInfoDTO loginInfo = userLoginFacade.getUserIdByToken(token);
            String userId = loginInfo.getUserid();
            ShiroRealm shiroRealm = (ShiroRealm) SpringContextUtil.getBean("shiroRealm");
            ShiroRealm.ShiroUser shiroUser = shiroRealm.getShiroUserInfo(userId, token);
            String accessToken = OAuth2TokenUtils.generate(shiroUser.getUserId(), shiroUser.getUsername(), shiroUser.getUserType(), shiroUser.getSpScopes(), shiroUser.getApiGroupScopes());
            //此处定义shiroUser controller中判断时，可以判断用户类型 shiroUtils
            return new SimpleAuthenticationInfo(new ShiroRealm.ShiroUser(shiroUser.getUserId(), shiroUser.getUsername(), shiroUser.getUserType(), shiroUser.getSpScopes(), shiroUser.getApiGroupScopes(), accessToken, shiroUser.getTenantScopes()), userId, userId);
        } catch (SSOException e) {
            if (StringUtils.equals(e.getDefineCode(), "LoginExpire")) {
                throw new ExpiredCredentialsException("凭证已过期");
            }
            throw new IncorrectCredentialsException(e.getMessage());
        } catch (Throwable e) {
            LOGGER.error("boss3g login failed, token:" + token, e);
            throw e;
        }
    }
}
