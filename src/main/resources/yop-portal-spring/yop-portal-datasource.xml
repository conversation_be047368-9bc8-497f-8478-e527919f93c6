<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd"
       default-lazy-init="true">
    <bean id="pooledDataSourceFactory" name="pooledDataSourceFactory"
          class="com.yeepay.g3.utils.common.datasource.impl.DruidPooledDataSourceFactory"/>

    <bean id="yopMysqlDataSource" class="com.yeepay.g3.utils.common.datasource.DataSourceFactoryBean">
        <property name="name" value="YOP_SYS_READ"/>
        <property name="pooledDataSourceFactory" ref="pooledDataSourceFactory"/>
    </bean>

    <bean id="mysqlDataSource" class="com.yeepay.g3.utils.common.datasource.DataSourceFactoryBean">
        <property name="name" value="MONITOR_TIDB_READ"/>
        <property name="pooledDataSourceFactory" ref="pooledDataSourceFactory"/>
    </bean>

    <bean id="yopDocMysqlDataSource" class="com.yeepay.g3.utils.common.datasource.DataSourceFactoryBean">
        <property name="name" value="YOP_DOC_READ"/>
        <property name="pooledDataSourceFactory" ref="pooledDataSourceFactory"/>
    </bean>
    <bean id="notifierDataSource" class="com.yeepay.g3.utils.common.datasource.DataSourceFactoryBean">
        <property name="name" value="YOP_NOTIFIER_READ"/>
        <property name="pooledDataSourceFactory" ref="pooledDataSourceFactory"/>
    </bean>
</beans>