/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.AttachmentReqVo;
import com.yeepay.g3.facade.yop.doc.dto.AttachmentUploadRequest;

import java.io.IOException;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/15 11:09
 */
public interface AttachmentService {
    String create(Map<String, String> cephConfig, AttachmentUploadRequest uploadRequest, AttachmentReqVo reqVo, boolean persis, String strategyName) throws IOException;

    void update(Map<String, String> cephConfig, AttachmentUploadRequest uploadRequest, AttachmentReqVo reqVo, boolean persis, String strategyName) throws IOException;

    void deleteCephFile(Map<String, String> cephConfig, AttachmentUploadRequest uploadRequest);

}
