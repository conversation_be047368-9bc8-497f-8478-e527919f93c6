package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import org.springframework.stereotype.Component;

/**
 * title: 基于 Sp 编码的鉴权拦截器<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/6/11 下午1:39
 */
@Component
public class IspBasedAuthorizationFilter extends CacheAbstractAuthorizationFilter {

    @Override
    protected String[] getSpCodes(String[] params) {
        return params;
    }

    @Override
    public String shiroName() {
        return "isp_based";
    }
}
