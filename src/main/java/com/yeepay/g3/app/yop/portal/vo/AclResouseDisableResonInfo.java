package com.yeepay.g3.app.yop.portal.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午1:10
 */
public class AclResouseDisableResonInfo implements Serializable {

    private static final long serialVersionUID = -1L;

    private String name;

    private String disableReason;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDisableReason() {
        return disableReason;
    }

    public void setDisableReason(String disableReason) {
        this.disableReason = disableReason;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
