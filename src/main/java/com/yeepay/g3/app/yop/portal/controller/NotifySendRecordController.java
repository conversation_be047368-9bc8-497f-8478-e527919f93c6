/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.exception.PortalExceptionEnum;
import com.yeepay.g3.app.yop.portal.service.NotifySendRecordQueryService;
import com.yeepay.g3.app.yop.portal.vo.NotifySendRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.PageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.notify.NotifySendRecordVO;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.boot.web.pojo.response.R;
import com.yeepay.g3.facade.yop.monitor.enums.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.ParseException;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/28 20:33
 */
@RestController
@RequestMapping("/rest/send-record")
@Slf4j
public class NotifySendRecordController {
    private static final long MAX_TIME = 7L * 24 * 60 * 60 * 1000;
    public static final String ENCODING = "UTF-8";
    private static final Logger LOGGER = LoggerFactory.getLogger(NotifySendRecordController.class);


    private FastDateFormat fastDateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private NotifySendRecordQueryService notifySendRecordQueryService;

    @GetMapping("/list")
    public R<PageQueryResult<NotifySendRecordVO>> listSendRecord(@RequestParam(value = "_pageNo", required = false) Integer pageNo, @RequestParam(value = "_pageSize", required = false) Integer pageSize,
                                                                 @RequestParam(value = "notificationId", required = false) String notificationId,
                                                                 @RequestParam(value = "customerNo", required = false) String customerNo,
                                                                 @RequestParam(value = "appId", required = false) String appId,
                                                                 @RequestParam(value = "status", required = false) OrderStatusEnum status,
                                                                 @RequestParam(value = "spiName", required = false) String spiName,
                                                                 @RequestParam(value = "notifyStartDate") String notifyStartDate,
                                                                 @RequestParam(value = "notifyEndDate") String notifyEndDate,
                                                                 @RequestParam(value = "notifyRule", required = false) String notifyRule,
                                                                 @RequestParam(value = "url", required = false) String url,
                                                                 @RequestParam(value = "errorCode", required = false) String errorCode) throws ParseException {
        Date startDate = fastDateFormat.parse(notifyStartDate);
        Date endDate = fastDateFormat.parse(notifyEndDate);
        PortalExceptionEnum.TIME_INTERVAL_TOO_LONG.assertIsTrue(startDate.getTime() - startDate.getTime() <= MAX_TIME, 7);
        if (StringUtils.isNotEmpty(url)) {
            try {
                url = URLDecoder.decode(url, ENCODING);
            } catch (UnsupportedEncodingException e) {
                LOGGER.error("Exception occurred when list send records with param:" + url, e);
                return new R(e);
            }
        }
        NotifySendRecordQueryParam notifySendRecordQueryParam = NotifySendRecordQueryParam.builder()
                .customerNo(customerNo)
                .status(status)
                .appId(appId)
                .spiName(spiName)
                .notificationId(notificationId)
                .notifyRule(notifyRule)
                .url(url)
                .errorCode(errorCode)
                .notifyStartDate(startDate)
                .notifyEndDate(endDate)
                .build();

        PageQueryParam pageQueryParam = getPageQueryParam(pageSize, pageNo);
        return new R(notifySendRecordQueryService.listSendRecord(notifySendRecordQueryParam, pageQueryParam));
    }

    private PageQueryParam getPageQueryParam(Integer pageSize, Integer pageNo) {
        PageQueryParam pageQueryParam = PageQueryParam.builder().build();
        if (null != pageNo) {
            pageQueryParam.setPageNo(pageNo);
        }
        if (null != pageSize) {
            pageQueryParam.setPageSize(pageSize);
        }
        return pageQueryParam;
    }

}
