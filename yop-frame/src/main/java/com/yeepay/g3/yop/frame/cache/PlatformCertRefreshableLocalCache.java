/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.frame.cache;

import com.yeepay.g3.encryptor.yop.platform.PlatformCert;
import com.yeepay.g3.encryptor.yop.platform.PlatformCertChecker;
import com.yeepay.g3.event.yop.sys.cert.platform.PlatformCertChangeEvent;
import com.yeepay.g3.facade.yop.sys.dto.cert.platform.PlatformCertDTO;
import com.yeepay.g3.facade.yop.sys.dto.cert.platform.PlatformCertQueryActiveParam;
import com.yeepay.g3.facade.yop.sys.dto.cert.platform.PlatformCertQueryBySerialNoParam;
import com.yeepay.g3.facade.yop.sys.facade.PlatformCertQueryFacade;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.yop.frame.cache.key.PlatformCertCacheKey;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/2/8 4:25 下午
 */
@Component("platformCertRefreshableLocalCache")
public class PlatformCertRefreshableLocalCache extends AbstractRefreshableLocalCache<PlatformCertCacheKey, List<PlatformCert>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(PlatformCertRefreshableLocalCache.class);

    private PlatformCertQueryFacade platformCertQueryFacade = RemoteServiceFactory.getService(PlatformCertQueryFacade.class);

    @Autowired
    private PlatformCertChecker platformCertChecker;

    @Override
    protected int getMaxSize() {
        return 100;
    }

    @Override
    protected int getRefreshDuration() {
        return 86477;
    }

    @Override
    protected YopCacheLoader<PlatformCertCacheKey, List<PlatformCert>> getCacheLoader() {
        return new YopCacheLoader<PlatformCertCacheKey, List<PlatformCert>>() {
            @Override
            protected List<PlatformCert> doLoad(PlatformCertCacheKey platformCertCacheKey) throws Exception {
                String serialNo = platformCertCacheKey.getSerialNo();
                if (StringUtils.isNotEmpty(serialNo)) {
                    PlatformCertQueryBySerialNoParam param = new PlatformCertQueryBySerialNoParam();
                    param.setSerialNo(serialNo);
                    PlatformCertDTO dto = platformCertQueryFacade.findBySerialNo(param);
                    return Collections.singletonList(convert(dto));
                } else {
                    List<PlatformCertDTO> dtos;
                    PlatformCertQueryActiveParam param = new PlatformCertQueryActiveParam();
                    param.setType(platformCertCacheKey.getCertType());
                    if (platformCertCacheKey.isBuffer()) {
                        dtos = platformCertQueryFacade.findBufferActive(param);
                    } else {
                        dtos = platformCertQueryFacade.findActive(param);
                    }
                    if (CollectionUtils.isEmpty(dtos)) {
                        return null;
                    }
                    return dtos.stream()
                            .map(this::convert)
                            .filter(platformCert -> platformCertChecker.checkAll(platformCert))
                            .collect(Collectors.toList());
                }
            }

            private PlatformCert convert(PlatformCertDTO dto) {
                PlatformCert platformCert = new PlatformCert();
                platformCert.setSerialNo(dto.getSerialNo());
                platformCert.setEffectiveDate(dto.getEffectiveDate());
                platformCert.setExpiredDate(dto.getExpiredDate());
                platformCert.setEncryptor(dto.getEncryptor());
                platformCert.setConfig(dto.getConfig());
                platformCert.setDigest(dto.getDigest());
                return platformCert;
            }

            @Override
            protected String getCacheName() {
                return "platform_cert_cache";
            }
        };
    }

    @EventListener
    public void onEvent(PlatformCertChangeEvent event) {
        LOGGER.info("platform cert change event received, event:{}.", event);
        refreshIfAbsent(new PlatformCertCacheKey(true, null, event.getSerialNo()));
        refreshIfAbsent(new PlatformCertCacheKey(false, event.getCertType(), null));
        refreshIfAbsent(new PlatformCertCacheKey(true, event.getCertType(), null));
    }
}
