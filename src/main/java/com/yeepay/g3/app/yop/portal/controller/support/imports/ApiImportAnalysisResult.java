package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * title: Api导入分析结果<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-19 11:21
 */
public class ApiImportAnalysisResult implements Serializable {

    private static final long serialVersionUID = -1L;

    private String requestId;

    private List<ApiCreateCheckItem> apisToCreate;

    private List<ApiOverrideCheckItem> apisToOverride;

    private List<ApiRequestKey> apisToIgnore;

    private List<SpiCreateCheckItem> spisToCreate;

    private List<SpiOverrideCheckItem> spisToOverride;

    private List<String> spisToIgnore;

    private List<String> unusedSpis;

    private List<ModelCreateCheckItem> modelsToCreate;

    private List<ModelOverrideCheckItem> modelsToOverride;

    private List<String> modelsToIgnore;

    private List<String> unusedModels;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public ApiImportAnalysisResult withRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    public List<ApiCreateCheckItem> getApisToCreate() {
        return apisToCreate;
    }

    public void setApisToCreate(List<ApiCreateCheckItem> apisToCreate) {
        this.apisToCreate = apisToCreate;
    }

    public ApiImportAnalysisResult addApisToCreateItem(ApiCreateCheckItem item) {
        if (this.apisToCreate == null) {
            this.apisToCreate = new ArrayList<>();
        }
        this.apisToCreate.add(item);
        return this;
    }

    public List<ApiOverrideCheckItem> getApisToOverride() {
        return apisToOverride;
    }

    public void setApisToOverride(List<ApiOverrideCheckItem> apisToOverride) {
        this.apisToOverride = apisToOverride;
    }

    public ApiImportAnalysisResult addApisToOverrideItem(ApiOverrideCheckItem item) {
        if (this.apisToOverride == null) {
            this.apisToOverride = new ArrayList<>();
        }
        this.apisToOverride.add(item);
        return this;
    }

    public List<ApiRequestKey> getApisToIgnore() {
        return apisToIgnore;
    }

    public void setApisToIgnore(List<ApiRequestKey> apisToIgnore) {
        this.apisToIgnore = apisToIgnore;
    }

    public ApiImportAnalysisResult withApisToIgnore(List<ApiRequestKey> apisToIgnore) {
        this.apisToIgnore = apisToIgnore;
        return this;
    }

    public ApiImportAnalysisResult addApisToIgnoreItem(ApiRequestKey requestKey) {
        if (this.apisToIgnore == null) {
            this.apisToIgnore = new ArrayList<>();
        }
        this.apisToIgnore.add(requestKey);
        return this;
    }

    public List<SpiCreateCheckItem> getSpisToCreate() {
        return spisToCreate;
    }

    public void setSpisToCreate(List<SpiCreateCheckItem> spisToCreate) {
        this.spisToCreate = spisToCreate;
    }

    public ApiImportAnalysisResult addSpisToCreateItem(SpiCreateCheckItem item) {
        if (this.spisToCreate == null) {
            this.spisToCreate = new ArrayList<>();
        }
        this.spisToCreate.add(item);
        return this;
    }


    public List<SpiOverrideCheckItem> getSpisToOverride() {
        return spisToOverride;
    }

    public void setSpisToOverride(List<SpiOverrideCheckItem> spisToOverride) {
        this.spisToOverride = spisToOverride;
    }

    public ApiImportAnalysisResult addSpisToOverrideItem(SpiOverrideCheckItem item) {
        if (this.spisToOverride == null) {
            this.spisToOverride = new ArrayList<>();
        }
        this.spisToOverride.add(item);
        return this;
    }

    public List<String> getSpisToIgnore() {
        return spisToIgnore;
    }

    public void setSpisToIgnore(List<String> spisToIgnore) {
        this.spisToIgnore = spisToIgnore;
    }

    public ApiImportAnalysisResult withSpisToIgnore(List<String> spisToIgnore) {
        this.spisToIgnore = spisToIgnore;
        return this;
    }

    public ApiImportAnalysisResult addSpisToIgnoreItem(String spi) {
        if (this.spisToIgnore == null) {
            this.spisToIgnore = new ArrayList<>();
        }
        this.spisToIgnore.add(spi);
        return this;
    }

    public List<String> getUnusedSpis() {
        return unusedSpis;
    }

    public void setUnusedSpis(List<String> unusedSpis) {
        this.unusedSpis = unusedSpis;
    }

    public ApiImportAnalysisResult withUnusedSpis(List<String> unusedSpis) {
        this.unusedSpis = unusedSpis;
        return this;
    }

    public List<ModelCreateCheckItem> getModelsToCreate() {
        return modelsToCreate;
    }

    public void setModelsToCreate(List<ModelCreateCheckItem> modelsToCreate) {
        this.modelsToCreate = modelsToCreate;
    }

    public ApiImportAnalysisResult addModelsToCreateItem(ModelCreateCheckItem item) {
        if (this.modelsToCreate == null) {
            this.modelsToCreate = new ArrayList<>();
        }
        this.modelsToCreate.add(item);
        return this;
    }

    public List<ModelOverrideCheckItem> getModelsToOverride() {
        return modelsToOverride;
    }

    public void setModelsToOverride(List<ModelOverrideCheckItem> modelsToOverride) {
        this.modelsToOverride = modelsToOverride;
    }

    public ApiImportAnalysisResult addModelsToOverrideItem(ModelOverrideCheckItem item) {
        if (this.modelsToOverride == null) {
            this.modelsToOverride = new ArrayList<>();
        }
        this.modelsToOverride.add(item);
        return this;
    }

    public List<String> getModelsToIgnore() {
        return modelsToIgnore;
    }

    public void setModelsToIgnore(List<String> modelsToIgnore) {
        this.modelsToIgnore = modelsToIgnore;
    }

    public ApiImportAnalysisResult withModelsToIgnore(List<String> modelsToIgnore) {
        this.modelsToIgnore = modelsToIgnore;
        return this;
    }

    public ApiImportAnalysisResult addModelsToIgnoreItem(String model) {
        if (this.modelsToIgnore == null) {
            this.modelsToIgnore = new ArrayList<>();
        }
        this.modelsToIgnore.add(model);
        return this;
    }

    public List<String> getUnusedModels() {
        return unusedModels;
    }

    public void setUnusedModels(List<String> unusedModels) {
        this.unusedModels = unusedModels;
    }

    public ApiImportAnalysisResult withUnusedModels(List<String> unusedModels) {
        this.unusedModels = unusedModels;
        return this;
    }


}
