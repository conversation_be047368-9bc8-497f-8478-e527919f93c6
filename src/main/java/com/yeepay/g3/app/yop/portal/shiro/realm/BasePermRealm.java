/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.realm;

import com.yeepay.g3.facade.yop.perm.dto.AclPermDTO;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.facade.yop.perm.facade.AclPrivilegeMgrFacade;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * title: BasePermRealm<br>
 * description: YOP鉴权realm父类<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/8 5:39 下午
 */
abstract class BasePermRealm extends AuthorizingRealm {
    /**
     * 授权查询回调函数, 进行鉴权但缓存中无用户的授权信息时调用.
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        ShiroRealm.ShiroUser shiroUser = (ShiroRealm.ShiroUser) principals.getPrimaryPrincipal();
        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        AclPrivilegeMgrFacade aclPrivilegeMgrFacade = RemoteServiceFactory.getService(AclPrivilegeMgrFacade.class);
        AclPermDTO aclPermDTO = aclPrivilegeMgrFacade.findAclParamByOperatorCode(shiroUser.getUserId());
        info.addRoles(aclPermDTO.getRolePermRules().keySet());
        if (shiroUser.getUserType().equals(OperatorTypeEnum.PLATFORM)) {
            for (String role : aclPermDTO.getRolePermRules().keySet()) {
                if (CollectionUtils.isNotEmpty(aclPermDTO.getRolePermRules().get(role))) {
                    info.addStringPermissions(aclPermDTO.getRolePermRules().get(role));
                }
            }
            if (aclPermDTO.getPermRules() != null) {
                info.addStringPermissions(aclPermDTO.getPermRules());
            }
        } else {
            for (String role : aclPermDTO.getRolePermRules().keySet()) {
                List<String> rolePerms = new ArrayList<>();
                String prefix = StringUtils.split(role, ":")[0];
                Iterator iterRolePerms = aclPermDTO.getRolePermRules().get(role).iterator();
                while (iterRolePerms.hasNext()) {
                    String rolePerm = (String) iterRolePerms.next();
                    if (StringUtils.isNotEmpty(rolePerm)) {
                        for (String rolePerm1 : StringUtils.split(rolePerm, ",")) {
                            rolePerms.add(StringUtils.upperCase(prefix) + ":" + rolePerm1);
                        }
                    }
                }
                info.addStringPermissions(rolePerms);
            }
        }
        return info;
    }
}
