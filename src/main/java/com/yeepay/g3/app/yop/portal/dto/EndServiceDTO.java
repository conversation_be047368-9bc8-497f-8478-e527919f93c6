/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.dto;

import com.yeepay.g3.app.yop.portal.vo.ServiceModelVO;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * title: EndServiceDTO<br>
 * description: 后端服务DTO<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/5/25 11:38 上午
 */
@Setter
@Getter
public class EndServiceDTO extends BaseDTO {
    private static final long serialVersionUID = -1L;

    private Map<String, MethodModel> methods;

    public static class MethodModel {

        // 无返回类型的方法名称
        private String simpleMethod;

        // 请求模型
        private Map<String, ServiceModelVO> requestModel;

        // 响应结果模型名称
        private ServiceModelVO responseModel;

        public String getSimpleMethod() {
            return simpleMethod;
        }

        public void setSimpleMethod(String simpleMethod) {
            this.simpleMethod = simpleMethod;
        }

        public Map<String, ServiceModelVO> getRequestModel() {
            return requestModel;
        }

        public void setRequestModel(Map<String, ServiceModelVO> requestModel) {
            this.requestModel = requestModel;
        }

        public ServiceModelVO getResponseModel() {
            return responseModel;
        }

        public void setResponseModel(ServiceModelVO responseModel) {
            this.responseModel = responseModel;
        }
    }
}
