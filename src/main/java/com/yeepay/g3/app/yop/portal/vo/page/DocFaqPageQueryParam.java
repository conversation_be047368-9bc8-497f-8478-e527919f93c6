/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * title: 常见问题分页参数<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/12/21
 */
@Data
public class DocFaqPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    /**
     * 问题标题
     */
    private String faqTitle;

    /**
     * 文章标题
     */
    private String pageTitle;

    /**
     * 问题状态
     */
    private String faqStatus;

    /**
     * 创建时间-起始值
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdDateStart;

    /**
     * 创建时间-结束值
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdDateEnd;

    /**
     * 是否置顶
     * true：是，false：否
     */
    private Boolean top;
}
