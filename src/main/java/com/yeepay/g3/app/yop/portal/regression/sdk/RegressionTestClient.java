/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.regression.sdk;

import com.yeepay.g3.app.yop.portal.regression.sdk.client.RegressionTestClientHandlerImpl;
import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestRequest;
import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestResponse;
import com.yeepay.g3.app.yop.portal.regression.sdk.model.transform.RegressionTestMarshaller;
import com.yeepay.g3.core.yop.sdk.sample.client.ClientExecutionParams;
import com.yeepay.g3.core.yop.sdk.sample.client.ClientHandler;
import com.yeepay.g3.core.yop.sdk.sample.exception.YopClientException;
import com.yeepay.g3.core.yop.sdk.sample.http.HttpResponseAnalyzer;
import com.yeepay.g3.core.yop.sdk.sample.http.HttpResponseAnalyzerSupport;
import com.yeepay.g3.core.yop.sdk.sample.http.HttpResponseHandler;

import com.yeepay.g3.core.yop.sdk.sample.http.handler.DefaultHttpResponseHandler;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/19 下午7:29
 */
public class RegressionTestClient {

    private static final HttpResponseAnalyzer[] ANALYZER_CHAIN = HttpResponseAnalyzerSupport.getAnalyzerChain();


    private ClientHandler clientHandler;

    public RegressionTestClient(String configString) {
        this.clientHandler = new RegressionTestClientHandlerImpl(configString);
    }

    public RegressionTestResponse execute(RegressionTestRequest request) {
        if (request == null) {
            throw new YopClientException("request is required.");
        }
        RegressionTestMarshaller requestMarshaller = RegressionTestMarshaller.getInstance();
        HttpResponseHandler<RegressionTestResponse> responseHandler =
                new DefaultHttpResponseHandler<>(RegressionTestResponse.class,
                        ANALYZER_CHAIN);

        return clientHandler.execute(new ClientExecutionParams<RegressionTestRequest, RegressionTestResponse>()
                .withInput(request)
                .withRequestMarshaller(requestMarshaller)
                .withResponseHandler(responseHandler));
    }
}
