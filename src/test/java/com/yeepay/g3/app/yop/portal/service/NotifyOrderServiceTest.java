/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import com.yeepay.g3.app.yop.portal.exception.YopNPortalException;
import com.yeepay.g3.app.yop.portal.vo.NotifyOrderQueryParam;
import com.yeepay.g3.app.yop.portal.vo.NotifyRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.PageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.notify.NotifyRecordVO;
import com.yeepay.g3.app.yop.portal.vo.notify.ResendVO;
import com.yeepay.g3.app.yop.portal.vo.notify.SyncVO;
import com.yeepay.g3.utils.common.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/9 2:07 下午
 */
public class NotifyOrderServiceTest extends SpringBootTests {

    private static Date defaultNotifyEndDate;
    private static Date defaultNotifyStartDate;

    static {
        try {
            defaultNotifyEndDate = DateUtils.parseDate("2022-01-14 19:32:00", "yyyy-MM-dd HH:mm:ss");
            defaultNotifyStartDate = DateUtils.addDay(defaultNotifyEndDate, -7);
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    @Autowired
    private NotifyOrderService notifyOrderService;
    @Autowired
    private NotifyOrderQueryService notifyOrderQueryService;

    /**
     * 订单状态同步测试
     */
    @Test
    public void testSync() throws ParseException {
        // 订单状态为发送中
        String orderId = "2aaec1b9fbcb4291a6147be7d09c1cb1";
        Date orderDate = DateUtils.parseDate("2022-02-15 18:50:12", "yyyy-MM-dd HH:mm:ss");
        SyncVO syncVO = new SyncVO();
        syncVO.setOrderId(orderId);
        syncVO.setOrderDate(orderDate);
        String status = notifyOrderService.sync(syncVO);
        Assert.assertTrue(StringUtils.isNotEmpty(status));
    }

    /**
     * 同步并不存在的订单的状态
     *
     * @throws ParseException
     */
    @Test(expected = YopNPortalException.class)
    public void testSyncNotExistedOrder() throws ParseException {
        // 订单不存在 预计查询报错
        String errorOrderId = "errorOrderId";
        Date errorOrderDate = DateUtils.parseDate("2021-11-09 16:51:00", "yyyy-MM-dd HH:mm:ss");
        SyncVO errorSyncVO = new SyncVO();
        errorSyncVO.setOrderId(errorOrderId);
        errorSyncVO.setOrderDate(errorOrderDate);
        notifyOrderService.sync(errorSyncVO);
    }

    /**
     * 重发通知测试
     */
    @Test
    public void testResend() throws ParseException {
        String orderId = "a1bd65ab021646b39d2b3a09ef7b88e8";
        Date orderDate = DateUtils.parseDate("2023-06-02 10:06:23", "yyyy-MM-dd HH:mm:ss");
        String url = "";
        ResendVO resendVO = new ResendVO();
        resendVO.setOrderId(orderId);
        resendVO.setOrderDate(orderDate);
        resendVO.setUrl(url);
        notifyOrderService.resend(resendVO);
        // 记录开始重发的时间便于断言
        Date recordCreateStartDate = new Date();
        NotifyRecordQueryParam notifyRecordQueryParam = NotifyRecordQueryParam.builder()
                .orderId(orderId)
                .orderDate(orderDate)
                .build();
        List<NotifyRecordVO> notifyRecordVOS = notifyOrderQueryService.listNotifyRecord(notifyRecordQueryParam, PageQueryParam.builder().build());
        Assert.assertTrue(notifyRecordVOS.get(0).getSendDate().getTime() > recordCreateStartDate.getTime());
    }

    /**
     * 批量重发测试
     */
    @Test
    public void testBatchResend() throws ParseException, InterruptedException {
        String orderId = "c40664d7b22e4520bc3b5470e918a1e0";
        Date orderDate = DateUtils.parseDate("2022-01-14 18:31:27", "yyyy-MM-dd HH:mm:ss");

        List<ResendVO> resendVOList = new ArrayList<>();
        ResendVO resendVO = new ResendVO();
        resendVO.setOrderId(orderId);
        resendVO.setOrderDate(orderDate);
        resendVOList.add(resendVO);

        // 记录开始重发的时间便于断言
        Date recordCreateStartDate = new Date();
        notifyOrderService.batchResend(resendVOList);

        Thread.sleep(2000);
        NotifyRecordQueryParam notifyRecordQueryParam = NotifyRecordQueryParam.builder()
                .orderId(orderId)
                .orderDate(orderDate)
                .build();
        List<NotifyRecordVO> notifyRecordVOS = notifyOrderQueryService.listNotifyRecord(notifyRecordQueryParam, PageQueryParam.builder().build());
        Assert.assertTrue(notifyRecordVOS.get(0).getSendDate().getTime() > recordCreateStartDate.getTime());
    }

    /**
     * 根据查询条件重发
     */
    @Test
    public void testQueryAndResend() throws ParseException, InterruptedException {
        String costumerNo = "1595815987915711";

        String orderId = "c40664d7b22e4520bc3b5470e918a1e0";
        Date orderDate = DateUtils.parseDate("2022-01-14 18:31:27", "yyyy-MM-dd HH:mm:ss");

        // 记录开始重发的时间便于断言
        Date recordCreateStartDate = new Date();

        NotifyOrderQueryParam notifyOrderQueryParam = NotifyOrderQueryParam.builder()
                .notifyStartDate(defaultNotifyStartDate)
                .notifyEndDate(defaultNotifyEndDate)
                .customerNo(costumerNo)
                .build();
        notifyOrderService.queryAndResend(notifyOrderQueryParam);

        Thread.sleep(2000);
        NotifyRecordQueryParam notifyRecordQueryParam = NotifyRecordQueryParam.builder()
                .orderId(orderId)
                .orderDate(orderDate)
                .build();
        List<NotifyRecordVO> notifyRecordVOS = notifyOrderQueryService.listNotifyRecord(notifyRecordQueryParam, PageQueryParam.builder().build());
        Assert.assertTrue(notifyRecordVOS.get(0).getSendDate().getTime() > recordCreateStartDate.getTime());
    }
}
