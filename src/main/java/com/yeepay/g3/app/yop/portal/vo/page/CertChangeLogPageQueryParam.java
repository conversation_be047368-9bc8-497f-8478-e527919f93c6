/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.CertOperateTypeEnum;

import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/14 下午3:09
 */
public class CertChangeLogPageQueryParam  extends BasePageQueryParam{
    private static final long serialVersionUID = -1L;

    private String certId;

    private String appId;

    private Date operatedStartDate;

    private Date operatedEndDate;

    private CertOperateTypeEnum type;

    private String operator;

    public String getCertId() {
        return certId;
    }

    public void setCertId(String certId) {
        this.certId = certId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public Date getOperatedStartDate() {
        return operatedStartDate;
    }

    public void setOperatedStartDate(Date operatedStartDate) {
        this.operatedStartDate = operatedStartDate;
    }

    public Date getOperatedEndDate() {
        return operatedEndDate;
    }

    public void setOperatedEndDate(Date operatedEndDate) {
        this.operatedEndDate = operatedEndDate;
    }

    public CertOperateTypeEnum getType() {
        return type;
    }

    public void setType(CertOperateTypeEnum type) {
        this.type = type;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
}
