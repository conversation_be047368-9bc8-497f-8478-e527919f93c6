package com.yeepay.g3.yop.frame.databind.form;

import com.yeepay.g3.yop.frame.backend.SpringBackend;
import com.yeepay.g3.yop.frame.backend.handler.ApiHandler;
import com.yeepay.g3.yop.frame.context.Context;
import com.yeepay.g3.yop.frame.context.impl.FormDataContext;
import com.yeepay.g3.yop.frame.databind.AbstractParamBinder;
import com.yeepay.g3.yop.frame.databind.MethodInvokeDataBinder;
import org.springframework.stereotype.Component;

/**
 * title: 本地参数绑定，迁移自LocalApiHandler<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/12/17 下午3:26
 */
@Component
public class LocalParamBinder extends AbstractParamBinder<FormDataContext> {

    @Override
    protected Object[] bindInvokeParams(Context context) {
        ApiHandler handler = context.getApiContext().getApiHandler();
        SpringBackend backend = (SpringBackend) handler.getApiBackend();
        return MethodInvokeDataBinder.localBind(context, backend.getTargetMethod(), handler.getApiDefinition().getFormParamDefinitionsDetail().getEndParamMapping());
    }
}
