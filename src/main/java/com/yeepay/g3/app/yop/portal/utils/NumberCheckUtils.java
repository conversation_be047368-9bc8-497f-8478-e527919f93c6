package com.yeepay.g3.app.yop.portal.utils;

import org.apache.commons.lang3.time.DateUtils;
import org.springframework.cache.Cache;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * title: 尝试次数检查<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2016<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/5/4 13:14
 */
public class NumberCheckUtils {

    private static final int DEFAULT_HOURS_BEFORE_LOGIN_DATA_EXPIRE = 24;

    /**
     * 检查某操作最近24小时尝试的次数
     *
     * @param cache 缓存
     * @param key   操作key
     */
    public static int getRetryTimes(Cache cache, String key) {
        Cache.ValueWrapper retryTimesObject = cache.get(key);
        List<Long> retryTimesList;
        final Long timestampOneDayBefore = DateUtils.addHours(new Date(), -1 * DEFAULT_HOURS_BEFORE_LOGIN_DATA_EXPIRE).getTime();
        if (null != retryTimesObject) {
            retryTimesList = (List) retryTimesObject.get();
            // TODO 如果用户尝试次数很多效率会比较低
            Iterator<Long> it = retryTimesList.iterator();
            while (it.hasNext()) {
                if (it.next() < timestampOneDayBefore) {
                    it.remove();
                }
            }
        } else {
            retryTimesList = new ArrayList<Long>();
        }
        cache.put(key, retryTimesList);
        return retryTimesList.size();
    }

    /**
     * 更新尝试次数
     *
     * @param cache        缓存
     * @param key          操作key
     * @param loginSuccess 是否成功
     */
    public static int updateRetryTimes(Cache cache, String key, boolean loginSuccess) {
        Cache.ValueWrapper retryTimesObject = cache.get(key);
        List<Long> retryTimesList = (List) retryTimesObject.get();
        if (loginSuccess) {
            cache.evict(key);
        } else {
            retryTimesList.add(new Date().getTime());
            cache.put(key, retryTimesList);
        }
        return retryTimesList.size();
    }

}
