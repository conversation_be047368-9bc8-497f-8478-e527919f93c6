package com.yeepay.g3.app.yop.portal.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * title:
 * description:文件上传失败列表DTO
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  2019年5月15 上午10:57
 */
public class FailedUploadFileDTO implements Serializable {

    private static final Long serialVersionUID = -1L;

    private List<String> failedList;

    public List <String> getFailedList() {
        return failedList;
    }

    public void setFailedList(List <String> failedList) {
        this.failedList = failedList;
    }

    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
