package com.yeepay.g3.app.yop.portal.config;

import com.yeepay.g3.yop.frame.storage.checksum.CheckSumCalculatingMultipartResolver;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-06-29 13:25
 */
@Configuration
public class MultipartConfig {
    private static final int MAX_IN_MEMORY_SIZE = 10 * 1024;
    private static final int MAX_UPLOAD_SIZE = 100 * 1024 * 1024;

    @Bean
    public CommonsMultipartResolver multipartResolver() {
        CommonsMultipartResolver multipartResolver = new CheckSumCalculatingMultipartResolver();
        multipartResolver.setResolveLazily(false);
        multipartResolver.setDefaultEncoding("utf-8");
        multipartResolver.setMaxInMemorySize(MAX_IN_MEMORY_SIZE);
        multipartResolver.setMaxUploadSize(MAX_UPLOAD_SIZE);
        return multipartResolver;
    }
}
