/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.app.yop.portal.vo;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: ApiGroupGrayPolicyVO<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-06-26 16:44
 */
public class ApiGroupGrayPolicyVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private String apiGroup;

    private List<GrayPolicyInfoVO> policyList;

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    public List<GrayPolicyInfoVO> getPolicyList() {
        return policyList;
    }

    public void setPolicyList(List<GrayPolicyInfoVO> policyList) {
        this.policyList = policyList;
    }

    @Override
    public String toString() {
        return "ApiGroupGrayPolicyVO{" +
                "apiGroup='" + apiGroup + '\'' +
                ", policyList=" + policyList +
                '}';
    }
}