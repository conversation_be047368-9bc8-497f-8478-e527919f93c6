package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.doc.dto.v2.DocPageDTO;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-19 20:38
 */
public class DocPageVO extends DocPageDTO {

    private static final long serialVersionUID = -1L;

    private String accessUrl;

    private DocPageRefVO refPage;

    public String getAccessUrl() {
        return accessUrl;
    }

    public void setAccessUrl(String accessUrl) {
        this.accessUrl = accessUrl;
    }

    public DocPageRefVO getRefPage() {
        return refPage;
    }

    public void setRefPage(DocPageRefVO refPage) {
        this.refPage = refPage;
    }
}
