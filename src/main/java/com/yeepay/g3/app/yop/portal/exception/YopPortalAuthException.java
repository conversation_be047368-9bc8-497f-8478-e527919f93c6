package com.yeepay.g3.app.yop.portal.exception;

import org.apache.shiro.authz.UnauthenticatedException;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/3/14 下午2:10
 */
public class YopPortalAuthException extends UnauthenticatedException {

    private static final long serialVersionUID = -9085416005820812953L;

    public YopPortalAuthException() {
    }

    public YopPortalAuthException(String message) {
        super(message);
    }

    public YopPortalAuthException(Throwable cause) {
        super(cause);
    }

    public YopPortalAuthException(String message, Throwable cause) {
        super(message, cause);
    }

}
