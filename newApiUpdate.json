{"createdByExitedInterface": false, "appName": "", "className": "", "method": "", "apiId": "1650fc81e077480daa0316dee9961bd8", "version": 0, "basic": {"name": "pojoParam", "apiType": "COMMON", "apiGroup": "test-wdc", "title": "pojoParam", "description": "a", "optionsRule": [{"name": "IDEMPOTENT", "config": {"supported": false, "useAllParams": true, "params": [], "hasBizError": false, "bizErrorCode": ""}}, {"name": "SANDBOX", "config": {"supported": false}}]}, "request": {"httpMethod": "POST", "path": "/rest/v1.0/test-wdc//rest/v1.0/test-wdc/pojo-param", "requestBody": {"contents": {"application/x-www-form-urlencoded": {"schema": "{\"type\":\"object\",\"properties\":{\"intNum\":{\"type\":\"integer\",\"format\":\"int32\",\"description\":\"<p>数值</p>\",\"maximum\":\"1000\",\"example\":\"10\",\"examples\":[]}}}", "examples": []}}}, "encrypt": false}, "response": {"httpCode": 200, "contentType": "application/json", "encrypt": false, "content": {"schema": "{\"$ref\":\"#/components/schemas/String\"}", "examples": []}}, "callbacks": [], "sensitiveVariables": []}