/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.app.yop.portal.shiro.cache.AclAuditMaxStageCache;
import com.yeepay.g3.app.yop.portal.shiro.cache.AclUserRoleCache;
import com.yeepay.g3.app.yop.portal.shiro.utils.Constants;
import com.yeepay.g3.app.yop.portal.shiro.utils.SpringContextUtil;
import com.yeepay.g3.facade.yop.perm.dto.AclAuditRecordDTO;
import com.yeepay.g3.facade.yop.perm.dto.AclAuditStageDTO;
import com.yeepay.g3.facade.yop.perm.facade.AclAuditFacade;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.web.filter.authz.AuthorizationFilter;
import org.apache.shiro.web.servlet.ShiroHttpServletRequest;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.List;

/**
 * title:审核拦截器，在所有拦截器之前 <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-27 16:34
 */
@Component
public class AclAuditAuthorizationFilter extends AuthorizationFilter implements CustomShiroFilter {

    @Override
    protected boolean isAccessAllowed(ServletRequest servletRequest, ServletResponse servletResponse, Object o) throws Exception {
        String auditRecordCode = ((ShiroHttpServletRequest) servletRequest).getHeader(Constants._AUDIT_RECORD_CODE);
        if (StringUtils.isEmpty(auditRecordCode)) {
            return true;
        }
        AclAuditFacade aclAuditFacade = RemoteServiceFactory.getService(AclAuditFacade.class);
        AclAuditRecordDTO recordDTO = aclAuditFacade.findRecordByCode(auditRecordCode);
        AclAuditStageDTO aclAuditStageDTO = ((AclAuditMaxStageCache) SpringContextUtil.getBean("aclAuditMaxStageCache")).aclAuditCache.get(recordDTO.getResourceId());
        if (recordDTO == null || aclAuditStageDTO.getStage() != recordDTO.getStage()) {
            return false;
        }
        String operator = ((ShiroHttpServletRequest) servletRequest).getHeader(Constants._OPERATOR);
        switch (recordDTO.getStatus()) {
            case UNAUDITED:
                AclUserRoleCache aclUserRoleCache = ((AclUserRoleCache) SpringContextUtil.getBean("aclUserRoleCache"));
                List<String> operators = aclAuditStageDTO.getOperators();
                List<String> roles = aclUserRoleCache.aclUserRoleCache.get(operator);
                if (CollectionUtils.isNotEmpty(operators) && operators.contains(operator)) {
                    servletRequest.setAttribute(Constants._PORTAL_SKIP_FILTER, true);
                } else if (CollectionUtils.isNotEmpty(aclAuditStageDTO.getRoles())) {
                    for (String roleCode : aclAuditStageDTO.getRoles()) {
                        if (roles.contains(roleCode) || roles.contains(recordDTO.getSpCode() + ":" + roleCode)) {
                            servletRequest.setAttribute(Constants._PORTAL_SKIP_FILTER, true);
                        }
                    }
                } else {
                    return false;
                }
                break;
            case AUDITING:
                if (StringUtils.equals(recordDTO.getCurrentOperator(), operator)) {
                    servletRequest.setAttribute(Constants._PORTAL_SKIP_FILTER, true);
                } else {
                    return false;
                }
                break;
            default:
                return false;
        }
        return true;
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        PrintWriter out = response.getWriter();
        out.println("{\"status\":\"error\",\"message\":\"非法的操作\"}");
        out.flush();
        out.close();
        return false;
    }

    @Override
    public String shiroName() {
        return "audit";
    }
}
