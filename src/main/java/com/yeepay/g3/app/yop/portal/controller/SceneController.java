/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.SceneQueryService;
import com.yeepay.g3.app.yop.portal.validation.group.SceneCreate;
import com.yeepay.g3.app.yop.portal.validation.group.SceneDelete;
import com.yeepay.g3.app.yop.portal.validation.group.SceneOrder;
import com.yeepay.g3.app.yop.portal.vo.SceneParamVO;
import com.yeepay.g3.app.yop.portal.vo.SceneVO;
import com.yeepay.g3.facade.yop.sys.dto.SceneDTO;
import com.yeepay.g3.facade.yop.sys.facade.SceneFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/7 上午10:29
 */
@Controller
@RequestMapping("/rest/product/scene")
public class SceneController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SceneController.class);

    private SceneFacade sceneFacade = RemoteServiceFactory.getService(SceneFacade.class);

    @Autowired
    private SceneQueryService sceneQueryService;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@Validated SceneParamVO param) {
        try {
            return new ResponseMessage("result", sceneQueryService.list(param));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list scene with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody @Validated(SceneCreate.class) SceneVO sceneVO) {
        try {
            SceneDTO sceneDTO = new SceneDTO();
            sceneDTO.setProductCode(sceneVO.getProductCode());
            sceneDTO.setName(sceneVO.getName());
            sceneFacade.create(sceneDTO);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when create scene with param:" + sceneVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestBody @Validated(SceneDelete.class) SceneVO sceneVO) {
        try {
            sceneFacade.delete(sceneVO.getId());
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when delete scene with param:" + sceneVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/order", method = RequestMethod.POST)
    public ResponseMessage order(@RequestBody @Validated(SceneOrder.class) List<SceneVO> sceneVOs) {
        try {
            if (CollectionUtils.isNotEmpty(sceneVOs)) {
                List<SceneDTO> sceneDTOS = new ArrayList<>(sceneVOs.size());
                for (SceneVO sceneVO : sceneVOs) {
                    SceneDTO sceneDTO = new SceneDTO();
                    sceneDTO.setId(sceneVO.getId());
                    sceneDTO.setSeq(sceneVO.getSeq());
                    sceneDTOS.add(sceneDTO);
                }
                //过滤掉非该sp下的场景
                final SceneParamVO sceneQueryParam = new SceneParamVO();
                sceneQueryParam.setIds(sceneDTOS.stream().map(SceneDTO::getId).collect(Collectors.toList()));
                final List<SceneVO> filtered = sceneQueryService.list(sceneQueryParam);
                if (CollectionUtils.isNotEmpty(filtered)) {
                    sceneDTOS = sceneDTOS.stream().filter(sceneDTO ->
                            filtered.stream().anyMatch(sceneVO ->
                                    sceneVO.getId().equals(sceneDTO.getId()))).collect(Collectors.toList());
                }
                if (CollectionUtils.isNotEmpty(sceneDTOS)) {
                    sceneFacade.order(sceneDTOS);
                }
            }
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when order scene with param:" + sceneVOs, e);
            return new ResponseMessage(e);
        }
    }

}
