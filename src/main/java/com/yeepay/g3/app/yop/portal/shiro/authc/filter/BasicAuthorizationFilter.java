package com.yeepay.g3.app.yop.portal.shiro.authc.filter;

import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.app.yop.portal.shiro.authc.BasicAuthToken;
import com.yeepay.g3.app.yop.portal.shiro.utils.ShiroFilterUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.web.filter.authc.AuthenticatingFilter;
import org.apache.shiro.web.servlet.ShiroHttpServletRequest;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * title: basic auth拦截器<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/03/27 下午1:39
 */
@Component
public class BasicAuthorizationFilter extends AuthenticatingFilter implements CustomShiroFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(OAuth2TokenAuthenticationFilter.class);

    private static final String AUTHORIZATION_HEADER = "authorization";

    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) {
        String token = getBasicTokenStr(request);
        return new BasicAuthToken(token);
    }

    protected String getBasicTokenStr(ServletRequest request) {
        return ((ShiroHttpServletRequest) request).getHeader(AUTHORIZATION_HEADER);
    }

    @Override
    protected boolean onLoginFailure(AuthenticationToken token, AuthenticationException e, ServletRequest request, ServletResponse response) {
        HttpServletResponse res = WebUtils.toHttp(response);
        try {
            res.setContentType("application/json");
            res.setCharacterEncoding("UTF-8");
            LOGGER.error("login fail, detail:{}", e.getMessage());
            if (ShiroFilterUtils.isAjax(request)) {
                res.sendError(HttpServletResponse.SC_UNAUTHORIZED);
            } else {
                PrintWriter out = response.getWriter();
                out.println("{\"status\":\"error\",\"message\":\"请输入正确的令牌\",\"detail\":\"" + e.getMessage() + "\"}");
                out.flush();
                out.close();
            }
        } catch (IOException e1) {
            LOGGER.error(" basic auth onLoginFailure exception", e1);
        }

        return super.onLoginFailure(token, e, request, res);
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        return executeLogin(request, response);
    }

    @Override
    public String shiroName() {
        return "basic_auth";
    }
}
