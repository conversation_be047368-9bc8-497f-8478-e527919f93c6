/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.filter.route;

import com.google.common.collect.ImmutableMap;
import com.yeepay.boot.components.utils.RandomUtil;
import com.yeepay.boot.components.utils.concurrent.ThreadUtil;
import com.yeepay.g3.event.yop.sys.api.old.ApiChangeEvent;
import com.yeepay.g3.event.yop.sys.backend.app.BackendAppRebuildEvent;
import com.yeepay.g3.event.yop.sys.backend.service.BackendServiceChangeEvent;
import com.yeepay.g3.event.yop.sys.route.ApiRouteDeployEvent;
import com.yeepay.g3.event.yop.sys.security.SecurityReqChangeEvent;
import com.yeepay.g3.facade.yop.api.dto.ApiDefineDTO;
import com.yeepay.g3.facade.yop.api.facade.OldApiMgrFacade;
import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqDTO;
import com.yeepay.g3.facade.yop.sys.facade.ApiPublishQueryFacade;
import com.yeepay.g3.facade.yop.sys.facade.ApiQueryFacade;
import com.yeepay.g3.facade.yop.sys.facade.SecurityReqQueryFacade;
import com.yeepay.g3.utils.common.ThreadContextUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.yop.ext.gateway.backend.handler.ApiHandler;
import com.yeepay.g3.yop.frame.cache.ApiV2RefreshableLocalCache;
import com.yeepay.g3.yop.frame.definition.SecurityReqs;
import com.yeepay.g3.yop.frame.exception.InternalException;
import com.yeepay.g3.yop.frame.http.Headers;
import com.yeepay.g3.yop.frame.monitor.MonitorLogTemplate;
import com.yeepay.g3.yop.frame.utils.EnvUtil;
import com.yeepay.g3.yop.frame.utils.YopCanaryUtils;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;
import com.yeepay.g3.yop.gateway.backend.BackendAppFactory;
import com.yeepay.g3.yop.gateway.backend.ScalableBackendApp;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;

/**
 * title: 基于事件的api路由器<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 2.0.0
 * @since 18-11-20 下午4:54
 */
@Lazy
@Component("lazyApiRouter")
public class EventBasedLazyApiRouter implements ApiRouter {

    private static final Logger LOGGER = LoggerFactory.getLogger(EventBasedLazyApiRouter.class);

    private static final OldApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(OldApiMgrFacade.class);

    private static final ApiQueryFacade apiQueryFacade = RemoteServiceFactory.getService(ApiQueryFacade.class);

    private static final SecurityReqQueryFacade securityReqQueryFacade = RemoteServiceFactory.getService(SecurityReqQueryFacade.class);

    private static final ApiPublishQueryFacade apiPublishQueryFacade = RemoteServiceFactory.getService(ApiPublishQueryFacade.class);

    private static final String OLD = "OLD";
    private static final String NEW = "NEW";
    private static final String OLD_GENERIC = "OLD_GENERIC";

    private final Map<String, ConcurrentMap<String, ApiHandler>> apiHandlerRegistryMap = ImmutableMap
            .of(OLD, new ConcurrentHashMap<>(),
                    NEW, new ConcurrentHashMap<>(),
                    OLD_GENERIC, new ConcurrentHashMap<>());

    @Autowired
    private ApiV2RefreshableLocalCache apiV2RefreshableLocalCache;

    @Autowired
    private BackendAppFactory backendAppFactory;

    @Autowired
    private ApiHandlerFactory apiHandlerFactory;

    private static final int PERCENTAGE_MAX = 10000;

    @Override
    public ApiHandler register(String key, String type) {
        ApiHandler apiHandler;
        try {
            ConcurrentMap<String, ApiHandler> handlerMap = apiHandlerRegistryMap.get(type);
            if ("true".equals(ThreadContextUtils.getContext().getValue(Headers.YOP_ROUTER_CANARY))
                    || "true".equals(ThreadContextUtils.getContext().getValue(Headers.YOP_API_CANARY))) {
                apiHandler = apiHandlerFactory.getApiHandler(type, key);
            } else {
                apiHandler = handlerMap.computeIfAbsent(key, s -> apiHandlerFactory.getApiHandler(type, key));
            }
            LOGGER.info(MonitorLogTemplate.LOAD_API_SUCCESS, key);
        } catch (InternalException e) {
            LOGGER.info(MonitorLogTemplate.LOAD_API_FAILURE, key);
            throw e;
        }
        return apiHandler;
    }

    @Override
    public ApiHandler route(String apiGroup, String path, String method, String appId) {
        // 兼容老接口，customerNo取null
        return route(apiGroup, path, method, appId, null);
    }

    @Override
    public ApiHandler route(String apiGroup, String path, String method, String appId, String customerNo) {
        path = getActualApiUri(path);
        // 根据开关选择合适的HandlerType
        String apiHandlerType = choiceApiHandlerType(apiGroup, path, appId, customerNo);
        String key = path;
        if (null == apiHandlerType || NEW.equals(apiHandlerType) || !apiHandlerRegistryMap.containsKey(apiHandlerType)) {
            //如果开关计算有问题，判断一下，是不是新版api
            //如果HandlerType是新版API的，需要判断一下，是不是真的是新版API
            ApiDTO apiDTO = apiV2RefreshableLocalCache.get(path);

            if (apiDTO != null && !StringUtils.equals("OLD_API", apiDTO.getBasic().getApiGroup())) {
                LOGGER.info("api is v2, apiId is {}", apiDTO.getApiId());
                apiHandlerType = NEW;
                key = apiDTO.getApiId();
            } else {
                LOGGER.info("api is old, apiUri is {}", path);
                apiHandlerType = OLD;
                key = path;
            }
        }
        LOGGER.info("api handler type choice result, path: {}, type:{}", path, apiHandlerType);
        return register(key, apiHandlerType);
    }

    /**
     * @param apiGroup
     * @param path
     * @param appId
     * @param customerNo
     * @return OLD, NEW, OLD_GENERIC 分别对应旧版API，新版API，旧版API泛化
     */
    private String choiceApiHandlerType(String apiGroup, String path, String appId, String customerNo) {
        try {
            if ("true".equals(ThreadContextUtils.getContext().getValue(Headers.YOP_API_GENERIC))) {
                return OLD_GENERIC;
            }
            if ("true".equals(ThreadContextUtils.getContext().getValue(Headers.YOP_ROUTER_CANARY))) {
                return NEW;
            }
            if ("true".equals(ThreadContextUtils.getContext().getValue(Headers.YOP_API_CANARY))) {
                return NEW;
            }

            String appSwitch = getAppSwitch(apiGroup, path, appId);
            if (StringUtils.isNotEmpty(appSwitch)) {
                return computeApiHandlerType(appSwitch);
            }

            // 商编灰度逻辑
            if (null != customerNo) {
                String customerSwitch = YopCanaryUtils.getCustomerApiHandlerSwitch(path, customerNo);
                if (StringUtils.isNotEmpty(customerSwitch)) {
                    return customerSwitch;
                }
            }
            String value = getPathSwitch(apiGroup, path);
            return computeApiHandlerType(value);
        } catch (Exception e) {
            LOGGER.error("choiceApiHandlerType error!", e);
        }
        return null;
    }

    /**
     * 从统一配置获取app开关
     *
     * @param appId
     * @return
     */
    private String getAppSwitch(String apiGroup, String path, String appId) {
        Map<String, String> apiHandlerSwitch = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_API_HANDLER_APP_SWITCH, Map.class);
        String value = apiHandlerSwitch.get(appId + "#" + path);
        if (StringUtils.isEmpty(value)) {
            value = apiHandlerSwitch.get(appId + "#" + apiGroup);
        }
        return value;
    }

    /**
     * 从统一配置获取apiHandler开关
     *
     * @param path
     * @return
     */
    private String getPathSwitch(String apiGroup, String path) {
        Map<String, String> apiHandlerSwitch = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_API_HANDLER_SWITCH, Map.class);
        String value;
        if (apiHandlerSwitch.get(path) != null) {
            value = apiHandlerSwitch.get(path);
        } else if (apiHandlerSwitch.get(apiGroup) != null) {
            value = apiHandlerSwitch.get(apiGroup);
        } else if (apiHandlerSwitch.get("*") != null) {
            value = apiHandlerSwitch.get("*");
        } else {
            value = OLD;
        }
        return value;
    }

    /**
     * 根据开关计算实际要走的apiHandlerType
     *
     * @param value
     * @return
     */
    // 格式 type1[:percentage1][,type2[:percentage2]]
    // 例子 NEW  全部走新版
    // 例子 NEW:123  全部走新版，只有一条配置，后面的123比例不生效
    // 例子 NEW:1000,OLD  1000/10000的量走新版，剩下的走旧版
    // 例子 NEW:1000,OLD:123  1000/10000的量走新版，剩下的走旧版
    private String computeApiHandlerType(String value) {
        // 只有一个配置，就直接走它
        if (!StringUtils.contains(value, ",")) {
            return StringUtils.split(value, ":")[0];
        }
        // 多个配置，依次计算概率
        String[] typeConfigs = StringUtils.split(value, ",");
        for (String typeConfig : typeConfigs) {
            if (!StringUtils.contains(typeConfig, ":")) {
                return typeConfig;
            }
            String[] config = StringUtils.split(typeConfig, ":");
            long percentage = Long.parseLong(config[1]);
            if (percentage == PERCENTAGE_MAX || RandomUtil.nextInt(0, PERCENTAGE_MAX) < percentage) {
                return config[0];
            }
        }
        // 如果所有配置的都没走（正常最后一个配置不应该配概率，肯定能走，这里做容错）
        // 就走最后一个
        return StringUtils.split(typeConfigs[typeConfigs.length - 1], ":")[0];
    }

    private String getActualApiUri(String apiUri) {
        Map<String, String> apiUriCompatibilityMap = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_API_URI_CASE_SENSITIVE_COMPATIBILITY);
        if (MapUtils.isNotEmpty(apiUriCompatibilityMap)) {
            String targetApiUri = apiUriCompatibilityMap.get(apiUri);
            if (StringUtils.isNotBlank(targetApiUri)) {
                return targetApiUri;
            }
        }
        return apiUri;
    }

    @EventListener
    public void onEvent(com.yeepay.g3.event.yop.sys.api.ApiChangeEvent apiChangeEvent) {
        LOGGER.info("new api change event received, event:{}.", apiChangeEvent);
        ConcurrentMap<String, ApiHandler> handlerMap = apiHandlerRegistryMap.get(NEW);
        String apiId = apiChangeEvent.getApiId();
        ApiDTO publishedApi = apiPublishQueryFacade.findLatestPublishedApi(apiId);
        Optional.ofNullable(handlerMap)
                .map(map -> map.get(apiId))
                .map(ApiHandler::getApiDTO)
                .ifPresent(apiNewDTO -> {
                    apiNewDTO.getRequest().setEncrypt(publishedApi.getRequest().getEncrypt());
                    apiNewDTO.getResponse().setEncrypt(publishedApi.getResponse().getEncrypt());
                });
    }

    @EventListener
    public void onEvent(BackendAppRebuildEvent event) {
        LOGGER.info("backend app rebuild event received, event:{}.", event);
        String currentEnv = EnvUtil.getCurrentEnv();
        String expectEnv = event.getEnv();
        if (!StringUtils.equalsAny(expectEnv, currentEnv, "ALL")) {
            LOGGER.info("skip upgrade backend.");
            return;
        }
        String backendCode = event.getBackendCode();
        ScalableBackendApp backendApp = backendAppFactory.getRemoteBackendApp(backendCode, null);
        if (backendApp == null) {
            LOGGER.error("remote backendApp not exist, backendCode:{}.", backendCode);
            return;
        }

        String backendVersion = event.getVersion();
        if (StringUtils.isBlank(backendVersion)) {
            LOGGER.error("backend version is black, backendCode:{}.", backendCode);
            return;
        }
        backendApp.init(backendVersion);

        List<ApiDefineDTO> list = apiMgrFacade.queryByBackendApp(backendCode);
        for (ApiDefineDTO dto : list) {
            String apiUri = dto.getApiUri();
            try {
                // TODO 只预热当前机器最近1天有调用量的 && 调用量或tps大于指定阈值的 API || 热门API（避免夜晚升级）
                ConcurrentMap<String, ApiHandler> handlerMap = apiHandlerRegistryMap.get(OLD);
                boolean needWarmingUp = handlerMap.containsKey(apiUri);
                handlerMap.remove(apiUri);

                if (needWarmingUp) {
                    LOGGER.info("need warming up backendCode:{}, api:{}", backendCode, apiUri);
                }
            } catch (Throwable e) {
                LOGGER.debug("reload apiUri failed, api:" + apiUri, e);
            }
        }

        // 延迟销毁
        ThreadUtil.sleep(60, TimeUnit.SECONDS);
        backendApp.destroyPrevVersion();
    }

    @EventListener(condition = "\"API\".equals(#event.type)")
    public void onEvent(SecurityReqChangeEvent event) {
        LOGGER.info("security req change event received, event:{}.", event);
        if (MapUtils.isEmpty(apiHandlerRegistryMap)) {
            return;
        }

        apiHandlerRegistryMap.forEach((k, v) -> {
            final ApiHandler apiHandler = v.get(event.getValue());
            if (null != apiHandler) {
                Map<String, SecurityReqDTO> securityReqs = null;
                if (apiHandler.isApiV2()) {
                    securityReqs = securityReqQueryFacade.findByApiId(event.getValue());
                    apiHandler.getApiDTO().setSecurityReqs(new SecurityReqs(securityReqs));
                } else {
                    securityReqs = securityReqQueryFacade.findByApiUri(event.getValue());
                    apiHandler.getApiDefinition().setSecurityReqs(new SecurityReqs(securityReqs));
                }

            }
        });

    }

    @EventListener
    public void onEvent(ApiChangeEvent apiChangeEvent) {
        String apiUri = apiChangeEvent.getUri();
        LOGGER.info("apiChangeEvent received,apiUri:{}", apiUri);
        ConcurrentMap<String, ApiHandler> handlerMap = apiHandlerRegistryMap.get(OLD);
        if (null != handlerMap.remove(apiUri)) {
            register(apiUri, OLD);
        }
        handlerMap = apiHandlerRegistryMap.get(OLD_GENERIC);
        if (null != handlerMap.remove(apiUri)) {
            register(apiUri, OLD_GENERIC);
        }
    }

    @EventListener
    public void onEvent(ApiRouteDeployEvent apiRouteDeployEvent) {
        String apiId = apiRouteDeployEvent.getApiId();
        LOGGER.info("apiRouteDeployEvent received,apiId:{},env:{}", apiRouteDeployEvent.getApiId());
        ConcurrentMap<String, ApiHandler> handlerMap = apiHandlerRegistryMap.get(NEW);
        if (null != handlerMap.remove(apiId)) {
            register(apiId, NEW);
        }
    }

    /**
     * HTTP类型的后端服务发生变更（basePath发生变更）
     *
     * @param event
     */
    @EventListener
    public void onEvent(BackendServiceChangeEvent event) {
        String serviceName = event.getServiceName();
        List<String> apiIds = apiQueryFacade.findApiIdsByBackendService(serviceName);
        ConcurrentMap<String, ApiHandler> handlerMap = apiHandlerRegistryMap.get(NEW);
        for (String apiId : apiIds) {
            try {
                if (null != handlerMap.remove(apiId)) {
                    register(apiId, NEW);
                }
            } catch (Throwable e) {
                LOGGER.debug("reload apiUri failed, apiId:" + apiId, e);
            }
        }
    }
}
