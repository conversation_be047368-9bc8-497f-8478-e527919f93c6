/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.ext.gateway.xmgj.context.authenticate;

import com.yeepay.g3.yop.ext.gateway.context.authenticate.AuthenticateContext;
import com.yeepay.g3.yop.ext.gateway.context.authenticate.AuthenticateContextParser;
import com.yeepay.g3.yop.ext.gateway.context.protocol.ProtocolMakerEnum;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import com.yeepay.g3.yop.ext.gateway.xmgj.context.authenticate.iml.XmgjAuthenticateContext;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_BODY_ATTR;

/**
 * title: 厦门国际认证上下文解析器<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/4
 */
@Component
public class XmgjAuthenticateContextParser implements AuthenticateContextParser {

    @Override
    public AuthenticateContext parse(ServerWebExchange exchange) {
        final Map<String, Object> requestBodyMap = exchange.getAttribute(GATEWAY_REQUEST_BODY_ATTR);
        XmgjAuthenticateContext authenticateContext = new XmgjAuthenticateContext(ServerWebExchangeUtils.getApiUri(exchange), requestBodyMap);
        authenticateContext.initialize("", "");
        return authenticateContext;
    }

    @Override
    public ProtocolMakerEnum getProtocolMaker() {
        return ProtocolMakerEnum.XMGJ;
    }
}
