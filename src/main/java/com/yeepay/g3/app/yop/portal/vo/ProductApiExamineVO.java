/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * title: ProductApiExamineVO<br>
 * description: 产品-API审核信息<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/5/17 4:59 下午
 */
@Setter
@Getter
public class ProductApiExamineVO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 产品列表
     */
    private List<ProductExamineVO> products;


    /**
     * api列表
     */
    private List<ApiExamineVO> apis;
}
