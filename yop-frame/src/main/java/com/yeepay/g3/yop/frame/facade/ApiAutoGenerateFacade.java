package com.yeepay.g3.yop.frame.facade;

import com.yeepay.g3.facade.yop.api.dto.ApiDefineDTO;
import com.yeepay.g3.yop.frame.facade.dto.ApiGenerateDTO;

/**
 * title: <br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017/5/3 下午1:35
 */
public interface ApiAutoGenerateFacade {

    /**
     * 根据类名、方法名等信息，解析类中信息，生成默认注册的api信息
     *
     * @param apiExpand
     * @return api信息
     */
    ApiDefineDTO autoGenerateSimpleApi(ApiGenerateDTO apiExpand) throws ClassNotFoundException;

    @Deprecated
    ApiDefineDTO autoGenerateSimpleApi(String className, String methodSignature, Integer level) throws ClassNotFoundException;
}
