/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.CertChangeLogQueryService;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.BaseChangeDetailVO;
import com.yeepay.g3.app.yop.portal.vo.page.CertChangeLogPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.CertChangeLogPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.mapper.deser.FastDateJsonDeserializer;
import com.yeepay.g3.core.yop.utils.mapper.ser.FastDateJsonSerializer;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/14 下午3:14
 */
@Component
public class CertChangeLogQueryServiceImpl implements CertChangeLogQueryService {

    @Resource(name = "certChangeLogQueryService")
    private QueryService queryService;

    private JsonMapper mapper;

    {
        mapper = JsonMapper.nonEmptyMapper();
        ObjectMapper objectMapper = mapper.getMapper();
        objectMapper.setTimeZone(TimeZone.getDefault());
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        String pattern = "yyyy-MM-dd HH:mm:ss";
        javaTimeModule.addSerializer(Date.class, new FastDateJsonSerializer(pattern));
        javaTimeModule.addDeserializer(Date.class, new FastDateJsonDeserializer(pattern));
        objectMapper.registerModule(javaTimeModule);
        objectMapper.configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);
    }

    private final PageItemConverter<CertChangeLogPageItem> pageItemConverter = new CertChangeLogPageItemConverter();

    @Override
    public PageQueryResult<CertChangeLogPageItem> pageQuery(CertChangeLogPageQueryParam param) {
        List<Map> queryResult = queryService.query("list", getBizParams(param));
        PageQueryResult<CertChangeLogPageItem> result = new PageQueryResult();
        List<CertChangeLogPageItem> items = new ArrayList<>(CollectionUtils.size(queryResult));
        if (CollectionUtils.isNotEmpty(queryResult)) {
            queryResult.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    private Map<String, Object> getBizParams(CertChangeLogPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("certId", param.getCertId());
        bizParams.put("appId", param.getAppId());
        bizParams.put("operatedStartDate", param.getOperatedStartDate());
        bizParams.put("operatedEndDate", param.getOperatedEndDate());
        bizParams.put("type", param.getType());
        bizParams.put("operator", param.getOperator());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class CertChangeLogPageItemConverter extends BasePageItemConverter<CertChangeLogPageItem> {

        @Override
        public CertChangeLogPageItem convert(Map<String, Object> params) {
            CertChangeLogPageItem item = new CertChangeLogPageItem();
            item.setAppId((String) params.get("app_id"));
            item.setCause((String) params.get("cause"));
            BaseChangeDetailVO detailVO = mapper.fromJson(params.get("detail").toString(), BaseChangeDetailVO.class);
            if (detailVO != null && null != detailVO.getContent()) {
                item.setDetail(StringUtils.defaultIfEmpty(detailVO.getContent().toString(), ""));
            }
            item.setOperator((String) params.get("operator"));
            item.setType((String) params.get("type"));
            item.setOperatedDate((Date) params.get("operated_datetime"));
            return item;
        }
    }
}
