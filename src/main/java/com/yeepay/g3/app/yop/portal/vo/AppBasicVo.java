package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.sys.enums.AppTypeEnum;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-21 13:57
 */
public class AppBasicVo extends BaseVO {

    private static final long serialVersionUID = -1;

    public AppBasicVo(String appId, String name) {
        this.appId = appId;
        this.name = name;
    }

    private String appId;

    private String name;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
