/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * title: ServiceModelVO<br>
 * description: 根据已有java类解析是方法对应对模型VO<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/15 6:16 下午
 */
@Setter
@Getter
public class ServiceModelVO implements Serializable {
    private static final long serialVersionUID = -1L;
    private final List<ServiceModelVO> nestModel = new ArrayList<>();
    private String modelRef;
    private String schema;
}
