spring:
  application:
    name: yop-portal
  profiles:
    active: dev
  lifecycle:
    timeout-per-shutdown-phase: 10s
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: non_empty
    locale: zh_CN
    time-zone: GMT+8
  mvc:
    locale: zh_CN
    throw-exception-if-no-handler-found: true
  cache:
    type: REDIS
  servlet:
    multipart:
      max-file-size: 2MB
      max-request-size: 2MB
  resources:
    add-mappings: false
    cache:
      period: 3153600
swagger:
  enabled: true
server:
  port: 8088
  shutdown: graceful
  tomcat:
    uri-encoding: UTF-8
    basedir: /var/tmp
    background-processor-delay: 30
    accesslog:
      enabled: true
      buffered: true
      directory: ./log
      file-date-format: .yyyy-MM-dd
      pattern: '%{X-Forwarded-For}i %h %l %u %D %t "%m %U %{_method}i" %s %b'
      prefix: access_log
      rename-on-rotate: false
      request-attributes-enabled: false
      rotate: true
      suffix: .log
    max-http-form-post-size: 10485760
    connection-timeout: 20000
    threads:
      max: 500
    remoteip:
      protocol-header: X-Forwarded-Proto
      remote-ip-header: X-Forwarded-For
      port-header: X-Forwarded-Port
  servlet:
    context-parameters:
      soa_app_name: yop-portal
    encoding:
      force: true
#debug: true
management:
  health:
    redis:
      enabled: false
---
spring:
  profiles: dev
logging:
  config: classpath:log4j2.xml
swagger:
  enabled: true
---
spring:
  profiles: prod
  resources:
    chain:
      strategy:
        content:
          enabled: true
      cache: true
      compressed: true
  servlet:
    multipart:
      max-file-size: 15MB
      max-request-size: 15MB
server:
  port: 8080
  compression:
    enabled: true
    min-response-size: 2048
    mime-types: text/html,text/xml,text/javascript,application/javascript,text/css,text/plain
logging:
  config: /apps/commoncfg/log4j2.xml
swagger:
  enabled: false

---
spring:
  profiles: qa
swagger:
  enabled: true

