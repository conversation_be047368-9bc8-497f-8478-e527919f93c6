/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.PageOwnerQueryService;
import com.yeepay.g3.app.yop.portal.vo.PageOwnerVO;
import com.yeepay.g3.utils.query.QueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * title: 页面负责人查询服务实现<br>
 * description: 页面负责人查询服务实现类<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-26
 */
@Slf4j
@Component
public class PageOwnerQueryServiceImpl implements PageOwnerQueryService {
    
    @Resource(name = "pageOwnerQueryService")
    private QueryService queryService;
    
    @Override
    public List<PageOwnerVO> getPageOwners(Long pageId) {
        if (pageId == null) {
            log.warn("pageId is null, returning empty list");
            return Collections.emptyList();
        }
        
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("pageId", pageId);
        
        try {
            List<Map> list = queryService.query("getPageOwners", bizParams);
            if (CollectionUtils.isNotEmpty(list)) {
                List<PageOwnerVO> result = new ArrayList<>(list.size());
                for (Map<String, Object> map : list) {
                    PageOwnerVO vo = convertToVO(map);
                    if (vo != null) {
                        result.add(vo);
                    }
                }
                return result;
            }
        } catch (Exception e) {
            log.error("Failed to query page owners for pageId: {}", pageId, e);
        }
        
        return Collections.emptyList();
    }
    
    /**
     * 将查询结果转换为VO对象
     */
    private PageOwnerVO convertToVO(Map<String, Object> map) {
        if (map == null) {
            return null;
        }
        
        try {
            String id = (String) map.get("id");
            String name = (String) map.get("name");
            Object assignedTimeObj = map.get("assignedTime");
            
            LocalDateTime assignedTime = null;
            if (assignedTimeObj instanceof Timestamp) {
                assignedTime = ((Timestamp) assignedTimeObj).toLocalDateTime();
            } else if (assignedTimeObj instanceof java.util.Date) {
                assignedTime = new Timestamp(((java.util.Date) assignedTimeObj).getTime()).toLocalDateTime();
            }
            
            return new PageOwnerVO(id, name, assignedTime);
        } catch (Exception e) {
            log.error("Failed to convert map to PageOwnerVO: {}", map, e);
            return null;
        }
    }
}