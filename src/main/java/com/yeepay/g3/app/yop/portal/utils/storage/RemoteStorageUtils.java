/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.utils.storage;

import com.google.common.collect.Maps;
import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.storage.sdk.ApiStorageClient;
import com.yeepay.storage.sdk.S3StorageClient;
import com.yeepay.storage.sdk.StorageInterface;
import com.yeepay.storage.sdk.common.Result;
import com.yeepay.storage.sdk.enums.Protocol;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * title: 远端存储工具类<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/1/30
 */
@Slf4j
public class RemoteStorageUtils {

    private static final Map<String, StorageInterface> STORAGE_MAP = Maps.newConcurrentMap();
    private static final JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();

    /**
     * 文件是否存在
     *
     * @param storageConfig 存储配置
     * @param fileKey 文件标识
     * @return true: 存在, false: 不存在
     */
    public static boolean exists(RemoteStorageConfig storageConfig, String fileKey) {
        if (StringUtils.isNotBlank(fileKey)) {
            try {
                StorageInterface storage = getStorage(storageConfig);
                String realFileKey = getRealFileKey(storageConfig, fileKey);
                return storage.doesObjectExist(storageConfig.getBucket(), realFileKey);
            } catch (Exception e) {
                log.error("fail to check ceph file exists, ", e);
            }
        }
        return false;
    }

    /**
     * 获取文件key
     *
     * @param storageConfig 存储配置
     * @param fileKey 文件标识
     * @return 带子桶名前缀的文件标识
     */
    public static String getRealFileKey(RemoteStorageConfig storageConfig, String fileKey) {
        if (StringUtils.isNotBlank(storageConfig.getSubBucket()) && !fileKey.startsWith(storageConfig.getSubBucket() + "/")) {
            return storageConfig.getSubBucket() + "/" + fileKey;
        }
        return fileKey;
    }

    /**
     * 下载文件
     *
     * @param storageConfig 存储配置
     * @param fileKey 文件标识
     * @return 文件流
     */
    public static InputStream download(RemoteStorageConfig storageConfig, String fileKey) {
        if (StringUtils.isNotBlank(fileKey)) {
            try {
                StorageInterface storage = getStorage(storageConfig);
                final String realFileKey = getRealFileKey(storageConfig, fileKey);
                return storage.get(storageConfig.getBucket(), realFileKey);
            } catch (Exception e) {
                log.error("fail to downloadFile from ceph, ", e);
            }
        }
        return null;
    }

    private static StorageInterface getStorage(RemoteStorageConfig storageConfig) {
        if (StringUtils.isNotBlank(storageConfig.getAccessKey())) {
            return STORAGE_MAP.computeIfAbsent(storageConfig.getBucket(),
                    p -> new S3StorageClient(storageConfig.getServerUrl(), Protocol.HTTPS, storageConfig.getAccessKey(), storageConfig.getSecretKey()));
        }
        return STORAGE_MAP.computeIfAbsent(storageConfig.getBucket(),
                p -> new ApiStorageClient(storageConfig.getServerUrl(), storageConfig.getSecretKey()));
    }

    /**
     * 下载文件为字符串
     *
     * @param storageConfig 存储配置
     * @param fileKey 文件标识
     * @return 文件内容字符串
     */
    public static String downloadAsString(RemoteStorageConfig storageConfig, String fileKey) {
        if (StringUtils.isNotBlank(fileKey)) {
            try {
                StorageInterface storage = getStorage(storageConfig);
                final String realFileKey = getRealFileKey(storageConfig, fileKey);
                return storage.getString(storageConfig.getBucket(), realFileKey);
            } catch (Exception e) {
                log.error("fail to getString from ceph, ", e);
            }
        }
        return "";
    }

    /**
     * 上传文件
     *
     * @param storageConfig 存储配置
     * @param fileKey 文件标识
     * @param fileContent 文件内容
     * @return true: 成功，false：失败
     */
    public static boolean upload(RemoteStorageConfig storageConfig, String fileKey, InputStream fileContent) {
        try {
            StorageInterface storage = getStorage(storageConfig);
            final String realFileKey = getRealFileKey(storageConfig, fileKey);
            final Result updateResult = storage.put(storageConfig.getBucket(), realFileKey, fileContent, storageConfig.getCannedAcl());
            log.info("update ceph file result:{}", JSON_MAPPER.toJson(updateResult));
            if (null != updateResult && null != updateResult.getCode() && updateResult.getCode().equals(0)) {
                return true;
            }
        } catch (Exception e) {
            log.error("fail to update ceph file, ", e);
        }
        return false;
    }

    /**
     * 上传文件
     *
     * @param storageConfig 存储配置
     * @param fileKey 文件标识
     * @param fileContent 文件内容
     * @return true: 成功，false：失败
     */
    public static boolean upload(RemoteStorageConfig storageConfig, String fileKey, File fileContent) {
        try {
            StorageInterface storage = getStorage(storageConfig);
            final String realFileKey = getRealFileKey(storageConfig, fileKey);
            final Result updateResult = storage.put(storageConfig.getBucket(), realFileKey, fileContent, storageConfig.getCannedAcl());
            log.info("update ceph file result:{}", JSON_MAPPER.toJson(updateResult));
            if (null != updateResult && null != updateResult.getCode() && updateResult.getCode().equals(0)) {
                return true;
            }
        } catch (Exception e) {
            log.error("fail to update ceph file, ", e);
        }
        return false;
    }

    /**
     * 上传文件为字符串
     *
     * @param storageConfig 存储配置
     * @param fileKey 文件标识
     * @param fileContent 文件内容
     * @return true: 成功，false：失败
     */
    public static boolean uploadString(RemoteStorageConfig storageConfig, String fileKey, String fileContent) {
        return upload(storageConfig, fileKey, new ByteArrayInputStream(fileContent.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * 批量上传并解压
     *
     * @param storageConfig 存储配置
     * @param filePath 文件目录
     * @param zipFile 压缩文件
     * @return true: 成功，false：失败
     */
    public static boolean batchUpload(RemoteStorageConfig storageConfig, String filePath, InputStream zipFile) {
        try {
            StorageInterface storage = getStorage(storageConfig);
            final String realFilePath = getRealFileKey(storageConfig, filePath);
            final Result updateResult = storage.batchPut(storageConfig.getBucket(), realFilePath, zipFile, storageConfig.getCannedAcl());
            log.info("batchUpload ceph file result:{}", JSON_MAPPER.toJson(updateResult));
            if (null != updateResult && null != updateResult.getCode() && updateResult.getCode().equals(0)) {
                return true;
            }
        } catch (Exception e) {
            log.error("fail to batchUpload ceph file, ", e);
        }
        return false;
    }

    /**
     * 删除文件
     *
     * @param storageConfig 存储配置
     * @param fileKey 文件标识
     * @return true: 成功，false：失败
     */
    public static boolean delete(RemoteStorageConfig storageConfig, String fileKey) {
        try {
            StorageInterface storage = getStorage(storageConfig);
            final String realFileKey = getRealFileKey(storageConfig, fileKey);
            storage.delete(storageConfig.getBucket(), realFileKey);
            return true;
        } catch (Exception e) {
            log.error("ceph delete file fail, ", e);
        }
        return false;
    }

    /**
     * 删除文件目录
     *
     * @param storageConfig 存储配置
     * @param filePath 文件目录
     * @return true: 成功，false：失败
     */
    public static boolean batchDelete(RemoteStorageConfig storageConfig, String filePath) {
        try {
            StorageInterface storage = getStorage(storageConfig);
            final String realFilePath = getRealFileKey(storageConfig, filePath);
            storage.deleteObjectsWithPrefix(storageConfig.getBucket(), realFilePath);
            return true;
        } catch (Exception e) {
            log.error("ceph delete directory fail, ", e);
        }
        return false;
    }

    /**
     * 复制文件
     *
     * @param storageConfig 存储配置
     * @param srcFilePath 源文件路径
     * @param targetFilePath 目标文件路径
     * @return true: 成功，false：失败
     */
    public static boolean copy(RemoteStorageConfig storageConfig, String srcFilePath, String targetFilePath) {
        try {
            StorageInterface storage = getStorage(storageConfig);
            final String srcFile = getRealFileKey(storageConfig, srcFilePath);
            final String targetFile = getRealFileKey(storageConfig, targetFilePath);
            final Result copyResult = storage.copyWithAcl(storageConfig.getBucket(), srcFile, targetFile, storageConfig.getCannedAcl());
            log.info("copy ceph file result:{}", JSON_MAPPER.toJson(copyResult));
            if (null != copyResult && null != copyResult.getCode() && copyResult.getCode().equals(0)) {
                return true;
            }
        } catch (Exception e) {
            log.error("fail to copy ceph file, ", e);
        }
        return false;
    }

    /**
     * 批量复制文件
     *
     * @param storageConfig 存储配置
     * @param srcFilePath 源目录路径
     * @param targetFilePath 目标目录路径
     * @return true: 成功，false：失败
     */
    public static boolean batchCopy(RemoteStorageConfig storageConfig, String srcFilePath, String targetFilePath) {
        try {
            StorageInterface storage = getStorage(storageConfig);
            final String srcFile = getRealFileKey(storageConfig, srcFilePath);
            final String targetFile = getRealFileKey(storageConfig, targetFilePath);
            final Result copyResult = storage.batchCopyWithAcl(storageConfig.getBucket(), srcFile,
                    storageConfig.getBucket(), targetFile, storageConfig.getCannedAcl());
            log.info("batchCopy ceph file result:{}", JSON_MAPPER.toJson(copyResult));
            if (null != copyResult && null != copyResult.getCode() && copyResult.getCode().equals(0)) {
                return true;
            }
        } catch (Exception e) {
            log.error("fail to batchCopy ceph file, ", e);
        }
        return false;
    }

}
