package com.yeepay.g3.yop.frame.definition.authenticate;


import com.yeepay.g3.facade.yop.sys.enums.CertTypeEnum;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-04-19 16:26
 */
public class Sm4Key extends AbstractSecretKey<String> {

    public Sm4Key(String s, CertTypeEnum certType, String digest) {
        super(s, certType, digest);
    }
}
