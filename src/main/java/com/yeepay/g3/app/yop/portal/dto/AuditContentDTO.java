/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.dto;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * title:需审核资源的提交内容 <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-29 11:46
 */
public class AuditContentDTO extends BaseDTO {

    private static final long serialVersionUID = -1L;

    private Map<String, String[]> params;

    private String contentType;

    private String body;

    private String httpMethod;

    public Map<String, String[]> getParams() {
        return params;
    }

    public void setParams(Map<String, String[]> params) {
        this.params = params;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        if (StringUtils.isEmpty(contentType)) {
            contentType = "application/x-www-form-urlencoded";
        }
        this.contentType = contentType;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getHttpMethod() {
        return httpMethod;
    }

    public void setHttpMethod(String httpMethod) {
        if (StringUtils.isEmpty(httpMethod)) {
            httpMethod = "POST";
        }
        this.httpMethod = httpMethod;
    }
}
