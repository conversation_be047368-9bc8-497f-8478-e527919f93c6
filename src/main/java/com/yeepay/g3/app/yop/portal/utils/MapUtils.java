/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.utils;

import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.TypeVO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/11/5 10:56
 */
public class MapUtils {
    protected static JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();

    public static List<TypeVO> mapToTypeVOs(Map<String, String> typeMap) {
        List<TypeVO> roleTypes = new ArrayList<>();
        for (String status : typeMap.keySet()) {
            TypeVO roleType = new TypeVO();
            roleType.setCode(status);
            roleType.setName(typeMap.get(status));
            roleTypes.add(roleType);
        }
        return roleTypes;
    }

    public static Map<String, Object> objectToMap(Object obj) {
        if (obj instanceof Map) {
            return (Map<String, Object>) obj;
        }
        String json = JSON_MAPPER.toJson(obj);
        if (StringUtils.isEmpty(json)) {
            new HashMap<>();
        }
        return JSON_MAPPER.fromJson(json, Map.class);
    }

}
