/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.frame.security.encryptor;

import com.yeepay.boot.components.utils.Encodes;
import com.yeepay.g3.facade.yop.ca.exceptions.DecryptFailedException;
import com.yeepay.g3.frame.yop.ca.utils.Sm4Utils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.frame.exception.InternalException;
import com.yeepay.g3.yop.frame.exception.encrypt.IllegalEncryptFormatException;
import com.yeepay.g3.yop.frame.security.EncryptAlgEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;
import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/1/20 2:12 下午
 */
@Component
@Slf4j
public class Sm4Encryptor implements Encryptor {

    public static final String ALGORITHM_NAME = "SM4";

    private static final Logger LOGGER = LoggerFactory.getLogger(Sm4Encryptor.class);

    public static final String ALGORITHM_NAME_CBC_PADDING = "SM4/CBC/PKCS5Padding";
    public static final String ALGORITHM_NAME_CBC_NOPADDING = "SM4/CBC/NoPadding";

    public static final int ENCRYPT_MODE = 1;
    public static final int DECRYPT_MODE = 2;

    private static final ThreadLocal<Map<String, Cipher>> cipherThreadLocal = new ThreadLocal<Map<String, Cipher>>() {
        @SneakyThrows
        @Override
        protected Map<String, Cipher> initialValue() {
            Map<String, Cipher> map = new HashMap<>();
            map.put(ALGORITHM_NAME_CBC_PADDING, Cipher.getInstance(ALGORITHM_NAME_CBC_PADDING, BouncyCastleProvider.PROVIDER_NAME));
            map.put(ALGORITHM_NAME_CBC_NOPADDING, Cipher.getInstance(ALGORITHM_NAME_CBC_NOPADDING, BouncyCastleProvider.PROVIDER_NAME));
            return map;
        }
    };

    @Override
    public List<EncryptAlgEnum> encryptAlg() {
        return Collections.singletonList(EncryptAlgEnum.SM4);
    }

    @Override
    public String encrypt(String content, String key) {
        try {
            return Sm4Utils.encrypt_GCM_NoPadding(key, content);
        } catch (Exception e) {
            LOGGER.error("encrypt failed", e);
            throw new YeepayRuntimeException(e);
        }
    }

    @Override
    public String encrypt(String content, EncryptAlg encryptAlg) {
        try {
            byte[] data = content.getBytes();
            Cipher initializedCipher = getInitializedCipher(ENCRYPT_MODE, encryptAlg, false);
            return Encodes.encodeUrlSafeBase64(initializedCipher.doFinal(data));
        } catch (Exception e) {
            LOGGER.warn("encrypt param failure", e);
            throw new DecryptFailedException();
        }

    }

    @Override
    public String decrypt(String content, String key) {
        try {
            return Sm4Utils.decrypt_GCM_NoPadding(key, content);
        } catch (Exception e) {
            LOGGER.warn("decrypt param failure", e);
            throw new DecryptFailedException();
        }
    }

    @Override
    public String decrypt(String cipher, EncryptAlg encryptAlg) {
        try {
            byte[] data = Encodes.decodeBase64(cipher);
            Cipher initializedCipher = getInitializedCipher(DECRYPT_MODE, encryptAlg, false);
            return new String(initializedCipher.doFinal(data));
        } catch (Throwable t) {
            log.error("error happened when encrypt data", t);
            throw new YeepayRuntimeException(t);
        }

    }

    @Override
    public Cipher getInitializedCipher(int mode, EncryptAlg encryptAlg, boolean isNew) {
        try {
            byte[] key = Encodes.decodeBase64(encryptAlg.getKey());
            Cipher cipher = isNew ? Cipher.getInstance(encryptAlg.getAlgorithmName()) : cipherThreadLocal.get().get(encryptAlg.getAlgorithmName());
            if (Objects.isNull(cipher)) {
                throw new IllegalEncryptFormatException();
            }
            Key sm4Key = new SecretKeySpec(key, ALGORITHM_NAME);

            if (StringUtils.isNotEmpty(encryptAlg.getIv())) {
                byte[] ivBytes = Encodes.decodeBase64(encryptAlg.getIv());
                IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);
                cipher.init(mode, sm4Key, ivParameterSpec);
                return cipher;
            }
            cipher.init(mode, sm4Key);
            return cipher;
        } catch (InternalException internalException) {
            throw internalException;
        } catch (Throwable throwable) {
            log.error("error happened when initialize cipher", throwable);
            throw new YeepayRuntimeException(throwable);
        }

    }

    @PostConstruct
    public void register() {
        EncryptorFactory.register(this);
    }
}
