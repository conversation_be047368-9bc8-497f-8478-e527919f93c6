/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.OperatorAuthStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.OperatorStatusEnum;

import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/7/19 下午5:41
 */
public class OperatorPageItem extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private Long id;

    private String customerNo;

    private String customerName;

    private String email;

    private OperatorAuthStatusEnum emailStatus;

    private String mobile;

    private OperatorAuthStatusEnum mobileStatus;

    private OperatorStatusEnum status;

    private Date createdDate;

    private Date lastModifiedDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public OperatorAuthStatusEnum getEmailStatus() {
        return emailStatus;
    }

    public void setEmailStatus(OperatorAuthStatusEnum emailStatus) {
        this.emailStatus = emailStatus;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public OperatorAuthStatusEnum getMobileStatus() {
        return mobileStatus;
    }

    public void setMobileStatus(OperatorAuthStatusEnum mobileStatus) {
        this.mobileStatus = mobileStatus;
    }

    public OperatorStatusEnum getStatus() {
        return status;
    }

    public void setStatus(OperatorStatusEnum status) {
        this.status = status;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }
}
