/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yeepay.g3.facade.yop.sys.enums.ProductAuthzStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.ProductTypeEnum;

import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/6 下午5:42
 */
public class ProductAuthzPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    @Size(max = 32)
    private String customerNo;

    @Size(max = 64)
    private String appId;

    @Size(max = 32)
    private String productCode;

    private ProductAuthzStatusEnum status;

    @Min(0)
    private Integer overdueTime;

    @JsonIgnore
    private Date overdueDate;

    @JsonIgnore
    private Date overdueDateStart;

    private ProductTypeEnum type;

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public ProductAuthzStatusEnum getStatus() {
        return status;
    }

    public void setStatus(ProductAuthzStatusEnum status) {
        this.status = status;
    }

    public Integer getOverdueTime() {
        return overdueTime;
    }

    public void setOverdueTime(Integer overdueTime) {
        this.overdueTime = overdueTime;
    }

    public Date getOverdueDate() {
        return overdueDate;
    }

    public void setOverdueDate(Date overdueDate) {
        this.overdueDate = overdueDate;
    }

    public Date getOverdueDateStart() {
        return overdueDateStart;
    }

    public void setOverdueDateStart(Date overdueDateStart) {
        this.overdueDateStart = overdueDateStart;
    }

    public ProductTypeEnum getType() {
        return type;
    }

    public void setType(ProductTypeEnum type) {
        this.type = type;
    }
}
