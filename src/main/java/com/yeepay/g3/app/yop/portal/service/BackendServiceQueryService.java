package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.BackendServiceVO;
import com.yeepay.g3.app.yop.portal.vo.page.BackendServicePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.BackendServicePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.List;
import java.util.Map;

/**
 * title: BackendServiceQueryService<br/>
 * description: 后端服务查询service<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:43
 */
public interface BackendServiceQueryService {

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<BackendServicePageItem> pageList(BackendServicePageQueryParam param);

    /**
     * sp分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<BackendServicePageItem> pageListForSp(BackendServicePageQueryParam param);

    /**
     * backendService列表查询
     *
     * @return
     */
    List<BackendServiceVO> simpleList();

    /**
     * 后端服务详情(简版)
     *
     * @param id
     * @return
     */
    Map<String, Object> simpleDetail(Long id);

    /**
     * 是否存在后端服务
     *
     * @param name
     * @return
     */
    boolean exist(String name);
}
