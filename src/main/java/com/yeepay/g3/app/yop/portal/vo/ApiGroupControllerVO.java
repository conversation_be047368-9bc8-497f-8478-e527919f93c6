package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.facade.yop.api.dto.ApiDefineDTO;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.List;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/8/6 上午1:24
 */
public class ApiGroupControllerVO {

    /**
     * @see com.yeepay.g3.app.yop.controller.ApiGroupController#queryList(boolean, HttpServletRequest)
     */
    public static class ApiGroupListItemVO implements Serializable {

        private static final long serialVersionUID = 1L;

        @JsonProperty
        String groupCode;

        @JsonProperty
        String groupTitle;

        @JsonProperty
        List<ApiVO> children;

        public ApiGroupListItemVO(ApiGroupVO apiGroupVO, List<ApiVO> apiVOList) {
            groupCode = apiGroupVO.getApiGroupCode();
            groupTitle = apiGroupVO.getApiGroupName();
            children = apiVOList != null ? apiVOList : Lists.newArrayList();
        }

        public void clearChildren() {
            this.children = null;
        }

        public static class ApiVO implements Serializable {

            @JsonProperty
            Long apiId;

            @JsonProperty
            String apiUri;

            @JsonProperty
            String apiTitle;

            @JsonProperty
            Boolean idempotent;

            public ApiVO(Long apiId, String apiUri, String apiTitle, Boolean idempotent) {
                this.apiId = apiId;
                this.apiUri = apiUri;
                this.apiTitle = apiTitle;
                this.idempotent = idempotent;
            }

            public ApiVO(ApiDefineDTO apiDefineDTO) {
                this.apiId = apiDefineDTO.getId();
                this.apiUri = apiDefineDTO.getApiUri();
                this.apiTitle = apiDefineDTO.getApiTitle();
                List<String> tags = apiDefineDTO.getTags();
                boolean idempotent = false;
                if (Collections3.isNotEmpty(tags) && tags.contains("idempotent")) {
                    idempotent = true;
                }
                this.idempotent = idempotent;
            }
        }
    }
}
