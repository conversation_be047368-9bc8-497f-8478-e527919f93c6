/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.yeepay.g3.facade.yop.perm.dto.BaseDTO;

import java.util.List;

/**
 * title:审核内容 <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-18 13:19
 */
public class AclAuditVO extends BaseDTO {

    private static final long serialVersionUID = -1L;

    private Long resourceId;

    @JsonProperty("auditStage")
    private List<AclAuditStageVO> aclAuditStages;

    public Long getResourceId() {
        return resourceId;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public List<AclAuditStageVO> getAclAuditStages() {
        return aclAuditStages;
    }

    public void setAclAuditStages(List<AclAuditStageVO> aclAuditStages) {
        this.aclAuditStages = aclAuditStages;
    }
}
