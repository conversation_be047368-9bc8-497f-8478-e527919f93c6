/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.enums;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/10/10 下午5:59
 */
public enum ApiParamFormatEnum {
    INTEGER("INTEGER", "int32"),
    LONG("LONG", "int64"),
    FLOAT("FLOAT", "float"),
    DOUBLE("DOUBLE", "double"),
    AMOUNT("AMOUNT", "bigDecimal"),
    STRING("STRING", "string") {
        @Override
        public String doSensitive(String string) {
            return sensitiveString;
        }
    },
    BYTE("BYTE", "byte"),
    DATE("DATE", "date"),
    DATETIME("DATETIME", "date-time"),
    PASSWORD("PASSWORD", "password") {
        @Override
        public String doSensitive(String string) {
            return sensitiveString;
        }
    },
    EMAIL("EMAIL", "email") {
        @Override
        public String doSensitive(String string) {
            int index = string.indexOf("@");
            if (index > -1) {
                return StringUtils.truncate(string, 0, 3) + getSensitive(3) + StringUtils.truncate(string, index, string.length() - index);
            } else {
                return sensitiveString;
            }
        }
    },
    IDCARD("IDCARD", "idcard") {
        @Override
        public String doSensitive(String string) {
            int length = string.length();
            if (length > 10) {
                return StringUtils.truncate(string, 0, 1) + getSensitive(length - 2) + StringUtils.truncate(string, length - 1, 1);
            } else {
                return sensitiveString;
            }
        }
    },
    BANKCARD("BANKCARD", "bankcard") {
        @Override
        public String doSensitive(String string) {
            final int length = string.length();
            if (length > 10) {
                return StringUtils.truncate(string, 0, 6) + getSensitive(length - 10) + StringUtils.truncate(string, length - 4, 4);
            } else {
                return sensitiveString;
            }
        }
    },
    CVV("CVV", "cvv") {
        @Override
        public String doSensitive(String string) {
            return getSensitive(string.length());
        }
    },
    UUID("UUID", "uuid"),
    BINARY("BINARY", "binary"),
    BOOLEAN("BOOLEAN", "boolean"),
    FILE("FILE", "file"),
    ARRAY("ARRAY", "array"),
    OBJECT("OBJECT", "object"),
    MAP("MAP", "map"),
    REF("REF", "ref");

    public final String sensitive(String string) {
        if (StringUtils.isBlank(string)) {
            return "";
        }
        try {
            return doSensitive(StringUtils.trim(string));
        } catch (Throwable e) {
            LOGGER.error("sensitive invoke log error!", e);
        }
        return sensitiveString;
    }

    protected String doSensitive(String string) {
        return string;
    }

    private static final String getSensitive(int length) {
        char[] c = new char[length];
        for (int i = 0; i < length; i++) {
            c[i] = sensitiveChar;
        }
        return new String(c);
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiParamFormatEnum.class);
    private static final String sensitiveString = "********";
    private static final char sensitiveChar = '*';

    private String value;

    private String format;

    private static final Map<String, ApiParamFormatEnum> VALUE_MAP;

    static {
        VALUE_MAP = new HashMap<>();
        for (ApiParamFormatEnum apiParamFormatEnum : ApiParamFormatEnum.values()) {
            VALUE_MAP.put(apiParamFormatEnum.getValue(), apiParamFormatEnum);
        }
    }

    ApiParamFormatEnum(String value, String format) {
        this.value = value;
        this.format = format;
    }

    public static Map<String, ApiParamFormatEnum> getValueMap() {
        return VALUE_MAP;
    }

    public static ApiParamFormatEnum parse(String value) {
        return VALUE_MAP.get(value);
    }

    public String getValue() {
        return value;
    }

    public String getFormat() {
        return format;
    }


}
