/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.CustomerQueryService;
import com.yeepay.g3.app.yop.portal.utils.PageUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.page.CustomerPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.CustomerPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.facade.yop.sys.enums.CustomerStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.CustomerTypeEnum;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/7/19 下午7:02
 */
@Component
public class CustomerQueryServiceImpl implements CustomerQueryService {

    @Resource(name = "customerQueryService")
    private QueryService queryService;

    private final PageItemConverter<CustomerPageItem> pageItemConverter = new CustomerPageItemConverter();

    @Override
    public PageQueryResult<CustomerPageItem> pageQuery(CustomerPageQueryParam param) {
        Map<String, Object> bizParams = getBizParams(param);
        List<Map> list = queryService.query("pageList", bizParams);
        PageQueryResult<CustomerPageItem> result = new PageQueryResult<>();
        List<CustomerPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public PageQueryResult<CustomerPageItem> pageQueryForSp(CustomerPageQueryParam param, List<String> providerCodes) {
        if (Collections3.isEmpty(providerCodes)) {
            return PageUtils.getEmptyPage(param.getPageNo());
        }
        Map<String, Object> bizParams = getBizParams(param);
        bizParams.put("providerCodes", providerCodes);
        List<Map> list = queryService.query("pageListForSp", bizParams);
        PageQueryResult<CustomerPageItem> result = new PageQueryResult<>();
        List<CustomerPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public Map<String, Object> findByCustomerNo(String customerNo) {
        if (StringUtils.isEmpty(customerNo)) {
            throw new YeepayRuntimeException("customerNo must be not empty.");
        }
        Map<String, Object> paramMap = Maps.newHashMap();
        paramMap.put("customerNo", customerNo);
        List<Map<String, Object>> list = queryService.query("findByCustomerNo", paramMap);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return Collections.emptyMap();
    }

    private Map<String, Object> getBizParams(CustomerPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("customerNo", param.getCustomerNo());
        bizParams.put("name", param.getName());
        bizParams.put("providerCode", param.getProviderCode());
        bizParams.put("type", param.getType());
        bizParams.put("status", param.getStatus());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        bizParams.put("tenantCode", param.getTenantCode());
        return bizParams;
    }

    class CustomerPageItemConverter extends BasePageItemConverter<CustomerPageItem> {

        @Override
        public CustomerPageItem convert(Map<String, Object> params) {
            CustomerPageItem item = new CustomerPageItem();
            item.setProvideCode((String) params.get("provider_code"));
            item.setCustomerNo((String) params.get("customer_no"));
            item.setName((String) params.get("customer_name"));
            item.setType(CustomerTypeEnum.parse((String) params.get("customer_type")));
            item.setStatus(CustomerStatusEnum.parse((String) params.get("status")));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            Map<String, String> map = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_CUSTOMER_PROVIDER_CODES_TENANT_CODES);
            String tenantCode = map.get((String) params.get("provider_code"));
            item.setTenantCode(tenantCode);
            return item;
        }
    }
}
