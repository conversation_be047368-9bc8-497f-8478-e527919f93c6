/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.NotifyOrderQueryParam;
import com.yeepay.g3.app.yop.portal.vo.NotifyRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.NotifySendRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.PageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.notify.NotifyOrderVO;
import com.yeepay.g3.app.yop.portal.vo.notify.NotifyRecordVO;
import com.yeepay.g3.app.yop.portal.vo.notify.NotifySendRecordVO;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/7 5:06 下午
 */
public interface NotifyOrderQueryService {
    /**
     * 查询通知订单
     *
     * @param notifyOrderQueryParam
     * @param pageQueryParam
     * @return
     */
    PageQueryResult<NotifyOrderVO> listNotifyOrder(NotifyOrderQueryParam notifyOrderQueryParam, PageQueryParam pageQueryParam);

    /**
     * 查询订单的发送记录
     *
     * @param notifyRecordQueryParam
     * @param pageQueryParam
     * @return
     */
    List<NotifyRecordVO> listNotifyRecord(NotifyRecordQueryParam notifyRecordQueryParam, PageQueryParam pageQueryParam);

    /**
     * 查询订单最新的通知报文
     *
     * @param orderId
     * @param orderDate
     * @return
     */
    Map<String, Object> findNotifyContent(String orderId, String orderDate);

    /**
     * 查询处理的发送记录
     * @param notifySendRecordQueryParam
     * @param pageQueryParam
     * @return
     */
    List<NotifySendRecordVO> listNotifySendRecord(NotifySendRecordQueryParam notifySendRecordQueryParam, PageQueryParam pageQueryParam);
}
