/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.dto;

import javax.validation.constraints.NotNull;

/**
 * title: 页面负责人查询请求<br>
 * description: 页面负责人查询请求包装类<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-26
 */
public class PageOwnerQueryRequest extends BaseDTO {

    private static final long serialVersionUID = -1L;

    /**
     * 页面ID
     */
    @NotNull(message = "页面ID不能为空")
    private Long pageId;

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }
}