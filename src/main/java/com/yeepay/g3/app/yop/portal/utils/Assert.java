/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.utils;

/**
 * 类名称：Assert <br>
 * 类描述：对象非空检测，表达式布尔检测。<br>
 * 对于对象检测时，非空情况下会返回对象本身，为空报空指针异常。<br>
 * 对于布尔表达式，不为真时会报违法参数异常。<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2015-04-07 上午11:04:02
 */
public final class Assert {
    private Assert() {
        //Utils组件不提供任何可被外部使用的构造方法
    }

    public static void isTrue(boolean expression) {
        if (!expression) {
            throw new IllegalArgumentException();
        }
    }

    public static void isTrue(boolean expression, String errorMessageFormat, Object... args) {
        if (!expression) {
            throw new IllegalArgumentException(String.format(errorMessageFormat, args));
        }
    }

    public static <T> T notNull(T reference) {
        if (reference == null) {
            throw new NullPointerException();
        }
        return reference;
    }

    public static <T> T notNull(T reference, String parameterName) {
        if (reference == null) {
            throw new NullPointerException(parameterName + " cannot be null");
        }
        return reference;
    }

    public static void state(boolean expression) {
        if (!expression) {
            throw new IllegalStateException();
        }
    }

    public static void state(boolean expression, String errorMessageFormat, Object... args) {
        if (!expression) {
            throw new IllegalStateException(String.format(errorMessageFormat, args));
        }
    }
}