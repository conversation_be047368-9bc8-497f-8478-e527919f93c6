/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.CustomerOperateTypeEnum;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Size;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/7/19 下午5:43
 */
public class CustomerChangePageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    @Size(max = 32)
    private String customerNo;

    private CustomerOperateTypeEnum type;

    @Size(max = 64)
    private String operator;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createdStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createdEndDate;

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public CustomerOperateTypeEnum getType() {
        return type;
    }

    public void setType(CustomerOperateTypeEnum type) {
        this.type = type;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getCreatedStartDate() {
        return createdStartDate;
    }

    public void setCreatedStartDate(Date createdStartDate) {
        this.createdStartDate = createdStartDate;
    }

    public Date getCreatedEndDate() {
        return createdEndDate;
    }

    public void setCreatedEndDate(Date createdEndDate) {
        this.createdEndDate = createdEndDate;
    }
}
