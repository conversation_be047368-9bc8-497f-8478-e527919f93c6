package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.ApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.service.ModelQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.page.ModelChangeRecordPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ModelChangeRecordPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.ModelPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.enums.ModelOperationTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.ModelMgrFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * title: ModelController<br/>
 * description: 模型管理<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午3:30
 */
@Controller
@RequestMapping("/rest/model")
public class ModelController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModelController.class);

    private ModelMgrFacade modelMgrFacade = RemoteServiceFactory.getService(ModelMgrFacade.class);

    @Autowired
    private ModelQueryService modelQueryService;

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(ModelPageQueryParam param) {
        try {
            if (ShiroUtils.isPlatformOperator()) {
                return new ResponseMessage("page", modelQueryService.pageList(param));
            } else {
                List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
                if (apiGroupCodes.size() == 0) {
                    return new ResponseMessage("page", new PageQueryResult());
                }
                param.setApiGroupCodes(apiGroupCodes);
                return new ResponseMessage("page", modelQueryService.pageListForSp(param));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list model with param: " + param, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam(value = "id") Long id) {
        try {
            CheckUtils.notNull(id, "id");
            return new ResponseMessage("result", modelMgrFacade.find(id));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when query model detail with id: " + id, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody ModelDTO modelDTO) {
        try {
            CheckUtils.notEmpty(modelDTO.getApiGroup(), "apiGroup");
            CheckUtils.notEmpty(modelDTO.getName(), "name");
            CheckUtils.notEmpty(modelDTO.getDescription(), "description");
            CheckUtils.notEmpty(modelDTO.getSchema(), "schema");
            ModelCreateRequest modelCreateRequest = new ModelCreateRequest();
            modelCreateRequest.withModel(modelDTO).withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(null));
            modelMgrFacade.create(modelCreateRequest);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when create model with param: " + modelDTO, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody ModelDTO modelDTO) {
        try {
            CheckUtils.notNull(modelDTO.getId(), "id");
            CheckUtils.notNull(modelDTO.getVersion(), "version");
            CheckUtils.notEmpty(modelDTO.getDescription(), "description");
            CheckUtils.notEmpty(modelDTO.getSchema(), "schema");
            ModelUpdateRequest modelUpdateRequest = new ModelUpdateRequest();
            modelUpdateRequest.withModel(modelDTO).withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(null));
            modelMgrFacade.update(modelUpdateRequest);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when update model with param: " + modelDTO, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam(value = "id") Long id, @RequestParam(value = "cause") String cause) {
        try {
            CheckUtils.notNull(id, "id");
            ModelDeleteRequest modelDeleteRequest = new ModelDeleteRequest();
            modelDeleteRequest.withModelId(id).withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(cause));
            modelMgrFacade.delete(modelDeleteRequest);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when delete model with id: " + id, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/simple-list", method = RequestMethod.GET)
    public ResponseMessage simpleList(@RequestParam(value = "apiGroup") String apiGroup) {
        try {
            CheckUtils.notEmpty(apiGroup, "apiGroup");
            return new ResponseMessage("result", modelQueryService.simpleList(apiGroup));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list model with apiGroup: " + apiGroup, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/change-record", method = RequestMethod.GET)
    public ResponseMessage list(ModelChangeRecordPageQueryParam param) {
        try {
            if (ShiroUtils.isPlatformOperator()) {
                PageQueryResult<ModelChangeRecordPageItem> pageQueryResult = modelQueryService.changeRecordPageList(param);
                return new ResponseMessage("page", pageQueryResult);
            } else {
                List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
                if (apiGroupCodes.size() == 0) {
                    return new ResponseMessage("page", new PageQueryResult());
                }
                param.setApiGroupCodes(apiGroupCodes);
                return new ResponseMessage("page", modelQueryService.changeRecordPageListForSp(param));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list model change record with param: " + param, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/change-record/detail", method = RequestMethod.GET)
    public ResponseMessage changeRecordDetail(@RequestParam(value = "id") Long id) {
        try {
            CheckUtils.notNull(id, "id");
            return new ResponseMessage("result", modelMgrFacade.findChangeRecord(id));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when query model change record detail with id: " + id, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/change-record/rollback", method = RequestMethod.POST)
    public ResponseMessage rollback(@RequestParam(value = "id") Long id, @RequestParam(value = "cause") String cause) {
        try {
            CheckUtils.notNull(id, "id");
            ModelRollbackRequest modelRollbackRequest = new ModelRollbackRequest();
            modelRollbackRequest.withChangeRecordId(id).withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(cause));
            modelMgrFacade.rollback(modelRollbackRequest);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when model change record rollback with id: " + id, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/op-type", method = RequestMethod.GET)
    public ResponseMessage opTypes() {
        List<CommonsVO> list = new ArrayList<>(ModelOperationTypeEnum.values().length);
        ModelOperationTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }


    @ResponseBody
    @RequestMapping(value = "/codes", method = RequestMethod.GET)
    public ResponseMessage codes(@RequestParam("apiId") String apiId) {
        Object codes = modelQueryService.codes(apiId);
        return new ResponseMessage("codes", codes);
    }
}
