/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.cache.NotifyErrorSolutionLocalCache;
import com.yeepay.g3.app.yop.portal.exception.PortalExceptionEnum;
import com.yeepay.g3.app.yop.portal.service.AppQueryService;
import com.yeepay.g3.app.yop.portal.service.NotifyOrderQueryService;
import com.yeepay.g3.app.yop.portal.service.NotifyOrderService;
import com.yeepay.g3.app.yop.portal.service.SpiService;
import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.app.yop.portal.utils.DateUtils;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.notify.*;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.concurrent.threadpool.ThreadPoolBuilder;
import com.yeepay.g3.core.yop.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.struggle.notifier.NotifyFacade;
import com.yeepay.g3.facade.struggle.notifier.dto.NotificationIdentityDTO;
import com.yeepay.g3.facade.struggle.notifier.dto.NotificationStatusDTO;
import com.yeepay.g3.facade.yop.monitor.dto.NotifierOrderUpdateEvent;
import com.yeepay.g3.facade.yop.monitor.enums.OrderStatusEnum;
import com.yeepay.g3.facade.yop.notifier.dto.BatchRenotifyRequestDTO;
import com.yeepay.g3.facade.yop.notifier.dto.ReNotifyRequestDTO;
import com.yeepay.g3.facade.yop.notifier.facade.DigitalNotifyFacade;
import com.yeepay.g3.facade.yop.sys.dto.AppAliasDTO;
import com.yeepay.g3.facade.yop.sys.dto.AppDTO;
import com.yeepay.g3.facade.yop.sys.dto.NotifyConfigDTO;
import com.yeepay.g3.facade.yop.sys.facade.AppAliasFacade;
import com.yeepay.g3.facade.yop.sys.facade.AppFacade;
import com.yeepay.g3.facade.yop.sys.facade.NotifyConfigFacade;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.SynchronousQueue;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/9 1:56 下午
 */
@Component
@Slf4j
public class NotifyOrderServiceImpl implements NotifyOrderService, NotifyOrderQueryService {
    private static final String YOP_PROTAL = "yop-portal";
    private static final String DEFAULT_NOTIFY_RULE_CODE = "yop_m_callback_rule";
    private static final String NOTIFY_ORDER_UPDATE_EXCHANGE = "exchange.direct.yop.notifier.record.update";
    private static final String NOTIFY_ORDER_STATUS_UPDATE_TOPIC = "notify_order_status_update";
    private final JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();
    private static final long MAX_TIME = 7L * 24 * 60 * 60 * 1000;
    @Resource(name = "notifyOrderQueryService")
    private QueryService queryService;
    @Resource(name = "notifySendRecordQueryService")
    private QueryService notifySendRecordQueryService;
    @Autowired
    private AppQueryService appQueryService;
    @Autowired
    private SpiService spiService;

    @Autowired
    private Optional<RabbitTemplate> rabbitTemplate;

    @Autowired
    private NotifyOrderQueryService notifyOrderQueryService;

    @Autowired
    private Optional<DefaultMQProducer> defaultMQProducer;

    @Autowired
    private NotifyErrorSolutionLocalCache notifyErrorSolutionLocalCache;

    private ExecutorService executorService = ThreadPoolBuilder
            .cachedPool()
            .setThreadNamePrefix("notify-order-batch-resend")
            .setMinSize(1)
            .setMaxSize(1)
            .setQueue(new SynchronousQueue<>())
            .setKeepAliveSecs(60)
            .setShutdownTimeoutSecs(60)
            .setAllowCoreThreadTimeout(true)
            .setRejectHanlder((r, executor) -> {
                throw new RejectedExecutionException("Only one notify resend task is allowed at the same time");
            })
            .build();

    @Override
    public PageQueryResult<NotifyOrderVO> listNotifyOrder(NotifyOrderQueryParam notifyOrderQueryParam, PageQueryParam pageQueryParam) {
        Map<String, Object> paramMap = com.yeepay.g3.app.yop.portal.utils.MapUtils.objectToMap(notifyOrderQueryParam);
        paramMap.put("_startIndex", pageQueryParam.getPageNo() <= 1 ? 0 : (pageQueryParam.getPageNo() - 1) * pageQueryParam.getPageSize());
        paramMap.put("_maxSize", pageQueryParam.getPageSize());
        List<Map> list = queryService.query("listNotifyOrder", paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return new PageQueryResult<>();
        }
        PageQueryResult<NotifyOrderVO> result = new PageQueryResult<>();
        List<NotifyOrderVO> data = new ArrayList<>(list.size());
        // spi标题需要单独查询，若本次查询中有多条记录使用了相同的spi则缓存其spi名称减少多余的开销
        Map<String, String> spiTitleCache = new HashMap<>();
        list.forEach(map -> data.add(convertToNotifyOrderVO(map, spiTitleCache)));
        result.setItems(data);
        result.setPageNo(pageQueryParam.getPageNo());
        return result;
    }

    @Override
    public List<NotifyRecordVO> listNotifyRecord(NotifyRecordQueryParam notifyRecordQueryParam, PageQueryParam pageQueryParam) {
        Map<String, Object> paramMap = new HashMap<>(7);
        paramMap.put("orderId", notifyRecordQueryParam.getOrderId());
        paramMap.put("orderDate", notifyRecordQueryParam.getOrderDate());
        paramMap.put("status", notifyRecordQueryParam.getStatus());
        if (!Objects.isNull(pageQueryParam)) {
            paramMap.put("_startIndex", pageQueryParam.getPageNo() <= 1 ? 0 : (pageQueryParam.getPageNo() - 1) * pageQueryParam.getPageSize());
            paramMap.put("_maxSize", pageQueryParam.getPageSize());
        }
        List<Map> list = queryService.query("listNotifyRecord", paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<NotifyRecordVO> result = new ArrayList<>(list.size());
        list.forEach(map -> result.add(convertToNotifyRecordVO(map)));
        return result;
    }

    @Override
    public Map<String, Object> findNotifyContent(String orderId, String orderDate) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderId", orderId);
        paramMap.put("orderDate", orderDate);
        Map<String, String> list = queryService.queryUnique("findRecordContent", paramMap, true);
        //封装为map
        HashMap<String, Object> result = new HashMap<>();
        if (MapUtils.isEmpty(list)) {
            return result;
        }
        Map<String, String> contentJson = null;
        if (list.containsKey("content")) {
            String content = list.get("content");
            contentJson = JSON_MAPPER.fromJson(content, Map.class);
        }
        Map<String, String> plaintextJson = null;
        if (list.containsKey("plaintext")) {
            String plaintext = list.get("plaintext");
            plaintextJson = JSON_MAPPER.fromJson(plaintext, Map.class);
        }
        result.put("cliphertext", contentJson);
        result.put("plaintext", plaintextJson);
        return result;
    }

    @Override
    public List<NotifySendRecordVO> listNotifySendRecord(NotifySendRecordQueryParam notifySendRecordQueryParam, PageQueryParam pageQueryParam) {
        Map<String, Object> paramMap = new HashMap<>(7);
        paramMap.put("notifyRecordId", notifySendRecordQueryParam.getNotifyRecordId());
        paramMap.put("orderDate", notifySendRecordQueryParam.getOrderDate());
        if (!Objects.isNull(pageQueryParam)) {
            paramMap.put("_startIndex", pageQueryParam.getPageNo() <= 1 ? 0 : (pageQueryParam.getPageNo() - 1) * pageQueryParam.getPageSize());
            paramMap.put("_maxSize", pageQueryParam.getPageSize());
        }
        List<Map> list = notifySendRecordQueryService.query("listNotifySendRecord", paramMap);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<NotifySendRecordVO> result = new ArrayList<>(list.size());
        list.forEach(map -> result.add(convertToNotifySendRecordVO(map)));
        return result;
    }

    private NotifySendRecordVO convertToNotifySendRecordVO(Map<String, Object> dataMap) {
        NotifySendRecordVO notifySendRecordVO = new NotifySendRecordVO();
        notifySendRecordVO.setId((String) dataMap.get("id"));
        notifySendRecordVO.setSendDate(ObjectUtils.isEmpty(dataMap.get("send_datetime")) ? DateUtils.toDate(dataMap.get("created_datetime")) : DateUtils.toDate(dataMap.get("send_datetime")));
        notifySendRecordVO.setStatus(OrderStatusEnum.parse((String) dataMap.get("status")));
        if (ObjectUtils.isNotEmpty(dataMap.get("error_code"))) {
            NotifyErrorSolutionVO notifyErrorSolution = notifyErrorSolutionLocalCache.get((String) dataMap.get("error_code"));
            notifySendRecordVO.setErrorCode(notifyErrorSolution.getErrorCode());
            notifySendRecordVO.setErrorName(notifyErrorSolution.getErrorName());
            notifySendRecordVO.setErrorMsg((String) dataMap.get("error_msg"));
        }
        return notifySendRecordVO;
    }

    private NotifyOrderVO convertToNotifyOrderVO(Map<String, Object> dataMap, Map<String, String> spiTitleCache) {
        NotifyOrderVO notifyOrderVO = new NotifyOrderVO();
        notifyOrderVO.setCustomerNo((String) dataMap.get("customer_no"));
        notifyOrderVO.setNotifyMerchantNo((String) dataMap.get("notify_merchant_no"));
        notifyOrderVO.setAppId((String) dataMap.get("app_id"));
        notifyOrderVO.setOrderId((String) dataMap.get("order_id"));
        notifyOrderVO.setOrderDate(DateUtils.toDate(dataMap.get("order_datetime")));
        notifyOrderVO.setNotificationId((String) dataMap.get("notification_id"));
        notifyOrderVO.setStatus(OrderStatusEnum.parse((String) dataMap.get("status")));
        notifyOrderVO.setUrl((String) dataMap.get("url"));
        notifyOrderVO.setNotifyRule((String) dataMap.get("notify_rule"));
        if (ObjectUtils.isNotEmpty(dataMap.get("error_code"))) {
            NotifyErrorSolutionVO notifyErrorSolution = notifyErrorSolutionLocalCache.get((String) dataMap.get("error_code"));
            notifyOrderVO.setLatestErrorCode(notifyErrorSolution.getErrorCode());
            notifyOrderVO.setLatestErrorName(notifyErrorSolution.getErrorName());
            notifyOrderVO.setLatestErrorMsg(StringUtils.isEmpty((String) dataMap.get("error_msg")) ? notifyOrderVO.getLatestErrorCode() : (String) dataMap.get("error_msg"));
        }
        notifyOrderVO.setNotifyRule((String) dataMap.get("notify_rule"));
        notifyOrderVO.setRealNotifyMerchantNo((String) dataMap.get("real_notify_merchant_no"));
        notifyOrderVO.setRealNotifyRule((String) dataMap.get("real_notify_rule"));
        notifyOrderVO.setGuid((String) dataMap.get("guid"));
        String spiName = (String) dataMap.get("spi_name");

        if (StringUtils.isNotEmpty(spiName)) {
            notifyOrderVO.setSpiName(spiName);
            String spiTitle = StringUtils.defaultIfEmpty(spiTitleCache.get(spiName), spiService.findSpiTitle(spiName));
            notifyOrderVO.setSpiTitle(spiTitle);
            spiTitleCache.put(spiName, spiTitle);
        }

        //最新失败时间
        String orderId = (String) dataMap.get("order_id");
        Date orderDate = DateUtils.toDate(dataMap.get("order_datetime"));
        notifyOrderVO.setLatestFailDate(queryLatestFailDate(orderId, orderDate));

        //失败次数和总次数
        Integer failTimes = querySendTimes(notifyOrderVO.getOrderId(), OrderStatusEnum.FAILED.getValue());
        Integer notifyTimes = querySendTimes(notifyOrderVO.getOrderId(), null);
        notifyOrderVO.setFailTimes(failTimes);
        notifyOrderVO.setNotifyTimes(notifyTimes);
        return notifyOrderVO;
    }

    private Date queryLatestFailDate(String orderId, Date orderDate) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderId", orderId);
        paramMap.put("orderDate", orderDate);
        List<Map<String, Object>> list = queryService.query("listNotifyRecord", paramMap);
        if (CollectionUtils.isNotEmpty(list) && MapUtils.isNotEmpty(list.get(0))) {
            String notifyRecordId = (String) list.get(0).get("id");
            paramMap.put("notifyRecordId", notifyRecordId);
            paramMap.put("status", OrderStatusEnum.FAILED.getValue());
            Map<String, Object> latestSendResult = queryService.queryUnique("listLatestSendResult", paramMap, true);
            if (MapUtils.isNotEmpty(latestSendResult)) {
                return DateUtils.toDate(latestSendResult.get("send_datetime"));
            }
        }
        return null;
    }

    private Integer querySendTimes(String orderId, String status) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderId", orderId);
        paramMap.put("status", status);
        Map<String, Object> times = notifySendRecordQueryService.queryUnique("querySendTimes", paramMap, true);
        if (MapUtils.isNotEmpty(times)) {
            return Integer.valueOf(times.get("times").toString());
        }
        return 0;
    }

    private NotifyRecordVO convertToNotifyRecordVO(Map<String, Object> dataMap) {
        NotifyRecordVO notifyRecordVO = new NotifyRecordVO();
        notifyRecordVO.setId((String) dataMap.get("id"));
        notifyRecordVO.setOrderDate(DateUtils.toDate(dataMap.get("order_datetime")));
        notifyRecordVO.setNotificationId((String) dataMap.get("notification_id"));
        notifyRecordVO.setStatus(OrderStatusEnum.parse((String) dataMap.get("status")));
        notifyRecordVO.setUrl((String) dataMap.get("url"));
        notifyRecordVO.setSendDate(DateUtils.toDate(dataMap.get("send_datetime")));
        if (ObjectUtils.isNotEmpty(dataMap.get("error_code"))) {
            NotifyErrorSolutionVO notifyErrorSolution = notifyErrorSolutionLocalCache.get((String) dataMap.get("error_code"));
            notifyRecordVO.setErrorCode(notifyErrorSolution.getErrorCode());
            notifyRecordVO.setLatestErrorName(notifyErrorSolution.getErrorName());
            notifyRecordVO.setErrorMsg(StringUtils.isEmpty((String) dataMap.get("error_msg")) ? notifyRecordVO.getErrorCode() : (String) dataMap.get("error_msg"));
        }
        notifyRecordVO.setContent((String) dataMap.get("content"));
        notifyRecordVO.setBackend((String) dataMap.get("backend"));
        notifyRecordVO.setOperator((String) dataMap.get("operator"));
        notifyRecordVO.setOperCause((String) dataMap.get("oper_cause"));
        notifyRecordVO.setOperSource((String) dataMap.get("oper_source"));

        if (StringUtils.isEmpty(notifyRecordVO.getBackend()) && OrderStatusEnum.FAILED.equals(notifyRecordVO.getStatus())) {
            notifyRecordVO.setLatestFailDate(notifyRecordVO.getSendDate());
            notifyRecordVO.setLatestFailMsg(notifyRecordVO.getErrorMsg());
        }

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("notifyRecordId", notifyRecordVO.getId());
        paramMap.put("status", OrderStatusEnum.FAILED.getValue());
        Map<String, Object> latestSendResult = queryService.queryUnique("listLatestSendResult", paramMap, true);
        if (MapUtils.isNotEmpty(latestSendResult)) {
            notifyRecordVO.setLatestFailMsg((String) latestSendResult.get("error_msg"));
            if (StringUtils.isNotEmpty(notifyRecordVO.getLatestFailMsg())) {
                notifyRecordVO.setLatestFailDate(DateUtils.toDate(latestSendResult.get("send_datetime")));
            }
        }
        return notifyRecordVO;
    }

    @Override
    public void resend(ResendVO resendVO) {
        ShiroRealm.ShiroUser shiroUser = ShiroUtils.getShiroUser();
        NotifyOrderVO notifyOrderVO = findNotifyOrder(resendVO.getOrderId(), resendVO.getOrderDate());
        PortalExceptionEnum.NOTIFY_ORDER_NOT_EXIST.assertIsTrue(!Objects.isNull(notifyOrderVO));
        ReNotifyRequestDTO reNotifyRequestDTO = new ReNotifyRequestDTO();
        reNotifyRequestDTO.setUrl(resendVO.getUrl());
        if (StringUtils.isEmpty(resendVO.getUrl())) {
            reNotifyRequestDTO.setUrl(notifyOrderVO.getUrl());
        }
        reNotifyRequestDTO.setOrderDate(resendVO.getOrderDate());
        reNotifyRequestDTO.setOrderId(resendVO.getOrderId());
        reNotifyRequestDTO.setOperator(shiroUser.getUserId());
        reNotifyRequestDTO.setOperCause(resendVO.getOperCause());
        reNotifyRequestDTO.setOperSource(YOP_PROTAL);
        getDigitalNotifyFacade().reNotify(reNotifyRequestDTO);

    }

    @Override
    public void batchResend(List<ResendVO> resendVOList) {
        ShiroRealm.ShiroUser shiroUser = ShiroUtils.getShiroUser();
        BatchRenotifyRequestDTO batchRenotifyRequestDTO = new BatchRenotifyRequestDTO();
        List<ReNotifyRequestDTO> reNotifyRequestDTOS = new ArrayList<>(resendVOList.size());
        resendVOList.forEach(resendVO -> {
            ReNotifyRequestDTO reNotifyRequestDTO = new ReNotifyRequestDTO();
            reNotifyRequestDTO.setOrderId(resendVO.getOrderId());
            reNotifyRequestDTO.setOrderDate(resendVO.getOrderDate());
            reNotifyRequestDTO.setOperator(shiroUser.getUserId());
            reNotifyRequestDTO.setOperCause(resendVO.getOperCause());
            reNotifyRequestDTO.setOperSource(YOP_PROTAL);
            reNotifyRequestDTOS.add(reNotifyRequestDTO);
        });
        batchRenotifyRequestDTO.setReNotifyRequestDTOList(reNotifyRequestDTOS);
        getDigitalNotifyFacade().batchRenotify(batchRenotifyRequestDTO);
    }

    @Override
    public void queryAndResend(NotifyOrderQueryParam notifyOrderQueryParam) {
        PortalExceptionEnum.TIME_INTERVAL_TOO_LONG.assertIsTrue(notifyOrderQueryParam.getNotifyEndDate().getTime() - notifyOrderQueryParam.getNotifyStartDate().getTime() <= MAX_TIME, 7);
        Long pageSize = (Long) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_QUERY_RESEND_PAGE_SIZE);
        Long intervalMillis = (Long) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_QUERY_RESEND_INTERVAL_MILLIS);
        PageQueryParam pageQueryParam = new PageQueryParam();
        pageQueryParam.setPageSize(pageSize.intValue());
        executorService.execute(() -> {
            log.info("begin notifier resend, param: {}", notifyOrderQueryParam);
            while (true) {
                List<NotifyOrderVO> notifyOrderVOS = notifyOrderQueryService.listNotifyOrder(notifyOrderQueryParam, pageQueryParam).getItems();
                if (CollectionUtils.isEmpty(notifyOrderVOS)) {
                    break;
                }
                List<ResendVO> resendVOList = new ArrayList<>(notifyOrderVOS.size());
                notifyOrderVOS.forEach(notifyOrderVO -> {
                    ResendVO resendVO = new ResendVO();
                    resendVO.setOrderId(notifyOrderVO.getOrderId());
                    resendVO.setOrderDate(notifyOrderVO.getOrderDate());
                    resendVO.setOperCause(notifyOrderQueryParam.getOperCause());
                    resendVOList.add(resendVO);
                });
                this.batchResend(resendVOList);
                if (intervalMillis > 0) {
                    try {
                        Thread.sleep(intervalMillis);
                    } catch (InterruptedException e) {
                        log.error("", e);
                    }
                }
                if (notifyOrderVOS.size() < pageSize.intValue()) {
                    break;
                }
                pageQueryParam.setPageNo(pageQueryParam.getPageNo() + 1);
            }
            log.info("end notifier resend");
        });
    }

    private NotifyOrderVO findNotifyOrder(String orderId, Date orderDate) {
        Map<String, Object> param = new HashMap<>();
        param.put("orderId", orderId);
        param.put("orderDate", orderDate);
        Map map = queryService.queryUnique("findNotifyOrder", param, false);
        if (null == map) {
            return null;
        }

        return convertToNotifyOrderVO(map, new HashMap<>());
    }

    @Override
    public String sync(SyncVO syncVO) {
        NotifyOrderVO notifyOrderVO = findNotifyOrder(syncVO.getOrderId(), syncVO.getOrderDate());
        PortalExceptionEnum.NOTIFY_ORDER_NOT_EXIST.assertIsTrue(!Objects.isNull(notifyOrderVO));
        if (!(OrderStatusEnum.PENDING.equals(notifyOrderVO.getStatus()) || OrderStatusEnum.SENDING.equals(notifyOrderVO.getStatus()))) {
            return notifyOrderVO.getStatus().getValue();
        }
        List<NotifyRecordVO> notifyRecordVOList = listNotifyRecord(NotifyRecordQueryParam.builder().orderId(syncVO.getOrderId()).orderDate(syncVO.getOrderDate()).status(OrderStatusEnum.SENDING).build(), null);
        if (CollectionUtils.isEmpty(notifyRecordVOList)) {
            return notifyOrderVO.getStatus().getValue();
        }
        List<NotificationIdentityDTO> notificationIdentityDTOS = new ArrayList<>(notifyRecordVOList.size());
        for (NotifyRecordVO notifyRecordVO : notifyRecordVOList) {
            NotificationIdentityDTO notificationIdentityDTO = new NotificationIdentityDTO();
            notificationIdentityDTO.setMerchantNo(getNotifyCustomerNo(notifyOrderVO.getNotifyMerchantNo(), notifyOrderVO.getCustomerNo(), notifyOrderVO.getAppId()));
            notificationIdentityDTO.setMerchantOrder(notifyRecordVO.getNotificationId());
            notificationIdentityDTO.setRuleCode(getNotifyRule(getNotifyAppId(notifyOrderVO.getAppId(), notificationIdentityDTO.getMerchantNo()), notifyOrderVO.getNotifyRule()));
            notificationIdentityDTOS.add(notificationIdentityDTO);
        }
        List<NotificationStatusDTO> notificationStatusDTOS = getNotifyFacade().batchQueryNotificationStatus(notificationIdentityDTOS);
        return doSync(notifyOrderVO, notificationStatusDTOS);
    }

    private String doSync(NotifyOrderVO notifyOrderVO, List<NotificationStatusDTO> notificationStatusDTOList) {
        // 正常情况下notificationStatusDTOList不可能为空
        if (CollectionUtils.isEmpty(notificationStatusDTOList)) {
            log.error("can not get response from system named notifier-struggle");
            return notifyOrderVO.getStatus().getValue();
        }
        StatusMappingEnum orderStatus = StatusMappingEnum.FAILED;
        NotifierOrderUpdateEvent notifierOrderUpdateEvent = new NotifierOrderUpdateEvent();
        notifierOrderUpdateEvent.setOrderId(notifyOrderVO.getOrderId());
        notifierOrderUpdateEvent.setOrderDate(notifyOrderVO.getOrderDate());
        List<NotifierOrderUpdateEvent.NotifyRecord> notifyRecords = new ArrayList<>(notificationStatusDTOList.size());
        for (NotificationStatusDTO notificationStatusDTO : notificationStatusDTOList) {
            NotifierOrderUpdateEvent.NotifyRecord notifyRecord = new NotifierOrderUpdateEvent.NotifyRecord();
            notifyRecord.setNotificationId(notificationStatusDTO.getMerchantOrder());
            StatusMappingEnum statusMappingEnum = StatusMappingEnum.parseWithStruggleStatus(notificationStatusDTO.getStatus().name());
            if (statusMappingEnum.getPriority() > orderStatus.getPriority()) {
                orderStatus = statusMappingEnum;
            }
            if (!StatusMappingEnum.SENDING.equals(statusMappingEnum)) {
                notifyRecord.setErrorCode(notificationStatusDTO.getErrorCode());
                notifyRecord.setErrorMsg(notificationStatusDTO.getErrorMsg());
            }
            notifyRecord.setStatus(OrderStatusEnum.parse(statusMappingEnum.getDeveloperStatus()));
            notifyRecords.add(notifyRecord);
        }
        notifierOrderUpdateEvent.setStatus(OrderStatusEnum.parse(orderStatus.getDeveloperStatus()));
        notifierOrderUpdateEvent.setRecords(notifyRecords);
        if (rabbitTemplate.isPresent()) {
            rabbitTemplate.get().convertAndSend(NOTIFY_ORDER_STATUS_UPDATE_TOPIC, JSON_MAPPER.toJson(notifierOrderUpdateEvent));
        }
        return orderStatus.getDeveloperStatus();
    }

    private String getNotifyCustomerNo(String notifyMerchantNo, String orderCostumerNo, String orderAppKey) {
        if (StringUtils.isNotEmpty(notifyMerchantNo)) {
            return notifyMerchantNo;
        }
        if (StringUtils.isNotEmpty(orderCostumerNo)) {
            return orderCostumerNo;
        }
        return orderAppKey;
    }

    private String getNotifyAppId(String orderAppId, String customerNo) {
        String appId = null;
        if (StringUtils.isNotEmpty(orderAppId)) {
            appId = orderAppId;
        } else {
            AppFacade appFacade = RemoteServiceFactory.getService(AppFacade.class);
            AppDTO appDTO = appFacade.findByCustomerNo(customerNo);
            if (null != appDTO) {
                appId = appDTO.getAppId();
            }
        }
        AppAliasFacade appAliasFacade = RemoteServiceFactory.getService(AppAliasFacade.class);
        AppAliasDTO appAliasDTO = appAliasFacade.find(appId);
        if (Objects.isNull(appAliasDTO)) {
            return appId;
        }
        return StringUtils.defaultIfEmpty(appAliasDTO.getAppId(), appId);
    }

    private String getNotifyRule(String appId, String orderNotifyRule) {
        NotifyConfigDTO notifyConfig = getNotifyConfigFacade().find(appId);
        String notifyRule = null;
        if (notifyConfig != null) {
            notifyRule = notifyConfig.getNotifyRule();
        }
        if (StringUtils.isEmpty(notifyRule)) {
            notifyRule = orderNotifyRule;
        }
        if (StringUtils.isEmpty(notifyRule)) {
            notifyRule = DEFAULT_NOTIFY_RULE_CODE;
        }
        return notifyRule;
    }

    private NotifyConfigFacade getNotifyConfigFacade() {
        return RemoteServiceFactory.getService(NotifyConfigFacade.class);
    }

    private DigitalNotifyFacade getDigitalNotifyFacade() {
        return RemoteServiceFactory.getService(DigitalNotifyFacade.class);
    }

    private NotifyFacade getNotifyFacade() {
        return RemoteServiceFactory.getService(NotifyFacade.class);
    }

    enum StatusMappingEnum {

        SUCCESS("SUCCESS", "succeed", Integer.MAX_VALUE, "成功"),
        SENDING("SENDING", "pending,retrying", 2, "发送中"),
        FAILED("FAILED", "failed", Integer.MIN_VALUE, "失败");

        private static Map<String, StatusMappingEnum> valueMap = Maps.newHashMap();
        private static Map<String, StatusMappingEnum> struggleMap = Maps.newHashMap();

        static {
            for (StatusMappingEnum item : StatusMappingEnum.values()) {
                valueMap.put(item.name(), item);
                String[] struggleStatuses = StringUtils.split(item.struggleStatus, ",");
                for (String struggleStatus : struggleStatuses) {
                    struggleMap.put(struggleStatus, item);
                }
            }
        }

        private String developerStatus;
        private String struggleStatus;
        private int priority;
        private String displayName;

        StatusMappingEnum(String developerStatus, String struggleStatus, int priority, String displayName) {
            this.developerStatus = developerStatus;
            this.struggleStatus = struggleStatus;
            this.priority = priority;
            this.displayName = displayName;
        }

        public static StatusMappingEnum parse(String developerStatus) {
            return valueMap.get(developerStatus);
        }

        public static StatusMappingEnum parseWithStruggleStatus(String struggleStatus) {
            return struggleMap.get(struggleStatus);
        }

        public String getDeveloperStatus() {
            return developerStatus;
        }

        public String getStruggleStatus() {
            return struggleStatus;
        }

        public int getPriority() {
            return priority;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

}
