package com.yeepay.g3.app.yop.portal.dto;

import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateStrategyEnum;
import com.yeepay.g3.yop.frame.exception.authentication.AuthenticationFailedException;
import org.junit.jupiter.api.Test;

import static org.junit.Assert.assertEquals;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/9/15 16:50
 */
public class ResponseMessageTest {

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonDefaultMapper();

    @Test
    void Void() {
        ResponseMessage msg = new ResponseMessage();
        assertEquals("{\"status\":\"success\",\"code\":105500}", JSON_MAPPER.toJson(msg));
    }

    @Test
    void message() {
        ResponseMessage msg = new ResponseMessage("成功啦");
        assertEquals("{\"status\":\"success\",\"code\":105500,\"message\":\"成功啦\"}", JSON_MAPPER.toJson(msg));
    }

    @Test
    void throwing() {
        Throwable e = new RuntimeException("出问题了");
        ResponseMessage msg = new ResponseMessage(e);
        assertEquals("{\"status\":\"error\",\"code\":105500,\"message\":\"出问题了\"}", JSON_MAPPER.toJson(msg));
    }

    @Test
    void yeepayBizException() {
        Throwable e = new AuthenticationFailedException(AuthenticateStrategyEnum.YOP_OAUTH2);
        ResponseMessage msg = new ResponseMessage(e);
        assertEquals("{\"status\":\"error\",\"code\":105500}", JSON_MAPPER.toJson(msg));
    }

}