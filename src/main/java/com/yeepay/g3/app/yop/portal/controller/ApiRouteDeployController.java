/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.ApiRouteQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.vo.ApiRouteVO;
import com.yeepay.g3.app.yop.portal.vo.page.ApiRouteDeployRecordPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ApiRouteDeployRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.ApiRouteListItem;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.sys.dto.OperationInfo;
import com.yeepay.g3.facade.yop.sys.dto.route.ApiRouteDeployRequest;
import com.yeepay.g3.facade.yop.sys.dto.route.ApiRouteEntiretyDTO;
import com.yeepay.g3.facade.yop.sys.dto.route.ApiRouteRollbackRequest;
import com.yeepay.g3.facade.yop.sys.facade.ApiRouteDeployFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/11/10 18:58
 */
@Controller
@RequestMapping("/rest/api/route")
public class ApiRouteDeployController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiRouteDeployController.class);

    @Autowired
    private ApiRouteQueryService apiRouteQueryService;

    private ApiRouteDeployFacade apiRouteDeployFacade = RemoteServiceFactory.getService(ApiRouteDeployFacade.class);

    @Operation(summary = "发布路由", description = "", tags = {"route-deploy"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK")})
    @RequestMapping(value = "/deploy",
            consumes = {"application/x-www-form-urlencoded"},
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage postRestApiRoutePublish(@RequestParam(value = "apiId") String apiId, @RequestParam(value = "cause") String cause) {
        ApiRouteDeployRequest deployRequest = new ApiRouteDeployRequest();
        deployRequest.setApiId(apiId);
        OperationInfo operationInfo = new OperationInfo();
        operationInfo.setOperator(ShiroUtils.getOperatorCode());
        operationInfo.setCause(cause);
        deployRequest.setOperationInfo(operationInfo);
        apiRouteDeployFacade.deploy(deployRequest);
        return new ResponseMessage();
    }


    @Operation(summary = "回滚路由", description = "", tags = {"route-deploy"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK")})
    @RequestMapping(value = "/rollback",
            consumes = {"application/x-www-form-urlencoded"},
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage<Void> postRestApiRouteRollback(@RequestParam(value = "deployId") Long deployId, @RequestParam(value = "cause") String cause) {
        ApiRouteRollbackRequest rollbackRequest = new ApiRouteRollbackRequest();
        rollbackRequest.setDeployId(deployId);
        OperationInfo operationInfo = new OperationInfo();
        operationInfo.setOperator(ShiroUtils.getOperatorCode());
        operationInfo.setCause(cause);
        rollbackRequest.setOperationInfo(operationInfo);
        apiRouteDeployFacade.rollback(rollbackRequest);
        return new ResponseMessage();
    }


    @Operation(summary = "路由发布记录详情", description = "buzuo", tags = {"route-deploy"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK", content = @Content(array = @ArraySchema(schema = @Schema(implementation = ApiRouteListItem.class))))})
    @RequestMapping(value = "/deploy-record/detail",
            produces = {"application/json"},
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage<List<ApiRouteVO>> getRestApiRouteDeployRecordDetail(@NotNull @Parameter(in = ParameterIn.QUERY, description = "记录标号", required = true, schema = @Schema()) @Valid @RequestParam(value = "recordId", required = true) Long recordId) {
        ApiRouteEntiretyDTO apiRouteEntiretyDTO = apiRouteDeployFacade.findRecordDetail(recordId);
        List<ApiRouteVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(apiRouteEntiretyDTO.getApiRoutes())) {
            apiRouteEntiretyDTO.getApiRoutes().stream().forEach(apiRouteDTO -> result.add(ApiRouteVO.convert(apiRouteDTO)));
        }
        return new ResponseMessage("result", result);
    }


    @Operation(summary = "路由发布记录列表", description = "", tags = {"route-deploy"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK", content = @Content(array = @ArraySchema(schema = @Schema(implementation = PageQueryResult.class))))})
    @RequestMapping(value = "/deploy-record/list",
            produces = {"application/json"},
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage<PageQueryResult<ApiRouteDeployRecordPageItem>> getRestApiRouteDeployRecordList(@Parameter(in = ParameterIn.QUERY, description = "操作类型", schema = @Schema()) @Valid @RequestParam(value = "opType", required = false) String opType,
                                                                                                          @Parameter(in = ParameterIn.QUERY, description = "操作时间-头", schema = @Schema()) @Valid @RequestParam(value = "createdStartDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date createdStartDate,
                                                                                                          @Parameter(in = ParameterIn.QUERY, description = "操作时间-尾", schema = @Schema()) @Valid @RequestParam(value = "createdEndDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date createdEndDate,
                                                                                                          @Parameter(in = ParameterIn.QUERY, description = "页码", schema = @Schema()) @Valid @RequestParam(value = "_pageNo", required = false) Integer _pageNo,
                                                                                                          @Parameter(in = ParameterIn.QUERY, description = "页面大小", schema = @Schema()) @Valid @RequestParam(value = "_pageSize", required = false) Integer _pageSize,
                                                                                                          @Parameter(in = ParameterIn.QUERY, description = "操作人", schema = @Schema()) @Valid @RequestParam(value = "operator", required = false) String operator,
                                                                                                          @RequestParam(value = "apiId") String apiId) {
        ApiRouteDeployRecordQueryParam queryParam = new ApiRouteDeployRecordQueryParam()
                .withCreatedEndDate(createdEndDate)
                .withCreatedStartDate(createdStartDate)
                .withOperator(operator)
                .withOpType(opType)
                .withPageNo(_pageNo)
                .withPageSize(_pageSize)
                .withApiId(apiId);
        return new ResponseMessage("page", apiRouteQueryService.pageQueryForDeploy(queryParam));
    }
}
