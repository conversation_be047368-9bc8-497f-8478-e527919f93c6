package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.SecurityDefVO;

import java.io.IOException;
import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/9 17:27
 */
public interface SecurityDefQueryService {

    /**
     * 查询所有的安全定义
     *
     * @return 安全定义列表
     * @throws IOException io异常
     */
    List<SecurityDefVO> list() throws IOException;
}
