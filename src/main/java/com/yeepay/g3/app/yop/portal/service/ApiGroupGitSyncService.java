package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.dto.ApiGroupGitSyncContext;

/**
 * title: Api分组git同步service<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-12 18:58
 */
public interface ApiGroupGitSyncService {


    /**
     * 保存同步上下文
     *
     * @param context context
     * @return context
     */
    ApiGroupGitSyncContext saveSyncContext(ApiGroupGitSyncContext context);

    /**
     * 查询同步上下文
     *
     * @param apiGroup  api分组
     * @param requestId 请求id
     * @return 文件内容
     */
    ApiGroupGitSyncContext findSyncRequest(String apiGroup, String requestId);

    /**
     * 删除同步请求
     *
     * @param apiGroup  api分组
     * @param requestId 请求id
     */
    void deleteSyncRequest(String apiGroup, String requestId);

    /**
     * 刷新所有
     */
    void refreshAll();

}
