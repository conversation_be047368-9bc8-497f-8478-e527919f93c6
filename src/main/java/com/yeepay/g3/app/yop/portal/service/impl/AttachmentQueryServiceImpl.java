package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.AttachmentQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.page.AttachmentPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.doc.dto.AttachmentDTO;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

import static com.yeepay.g3.app.yop.portal.utils.Constants.ATTACHMENT_ACCESS_PATH;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018-12-21 18:13
 */
@Component
public class AttachmentQueryServiceImpl implements AttachmentQueryService {

    @Resource(name = "attachmentQueryService")
    private QueryService queryService;

    private final PageItemConverter<AttachmentDTO> converter = new AttachmentPageItemConverter();

    @Override
    public PageQueryResult<AttachmentDTO> pageQuery(AttachmentPageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> bizParams = getBizParams(param);
        queryParam.setParams(bizParams);
        QueryResult queryResult = queryService.query("pageList", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), converter);
    }

    private Map<String, Object> getBizParams(AttachmentPageQueryParam param){
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("fileType", param.getFileType());
        bizParams.put("fileName", param.getFileName());
        bizParams.put("fileId", param.getFileId());
        return bizParams;
    }

    class AttachmentPageItemConverter extends BasePageItemConverter<AttachmentDTO> {
        @Override
        public AttachmentDTO convert(Map<String, Object> params) {
            AttachmentDTO item = new AttachmentDTO();
            item.setId((Long) params.get("id"));
            item.setFileId((String) params.get("file_id"));
            item.setFileName((String) params.get("file_name"));
            item.setFileType((String) params.get("file_type"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }
}
