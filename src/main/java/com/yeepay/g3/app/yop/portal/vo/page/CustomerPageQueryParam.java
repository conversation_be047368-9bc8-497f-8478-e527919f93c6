/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.CustomerStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.CustomerTypeEnum;

import javax.validation.constraints.Size;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/7/19 下午5:43
 */
public class CustomerPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    @Size(max = 32)
    private String customerNo;

    @Size(max = 128)
    private String name;

    private String providerCode;

    private CustomerTypeEnum type;

    private CustomerStatusEnum status;

    private String tenantCode;

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProviderCode() {
        return providerCode;
    }

    public void setProviderCode(String providerCode) {
        this.providerCode = providerCode;
    }

    public CustomerTypeEnum getType() {
        return type;
    }

    public void setType(CustomerTypeEnum type) {
        this.type = type;
    }

    public CustomerStatusEnum getStatus() {
        return status;
    }

    public void setStatus(CustomerStatusEnum status) {
        this.status = status;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }
}
