package com.yeepay.g3.app.yop.portal.service.impl;

import com.fasterxml.jackson.databind.JavaType;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.TestCaseQueryService;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.RegTestCaseVO;
import com.yeepay.g3.app.yop.portal.vo.RegTestExecHisVO;
import com.yeepay.g3.app.yop.portal.vo.RegressionTestControllerVO;
import com.yeepay.g3.facade.yop.sys.dto.FormRequestParamDTO;
import com.yeepay.g3.facade.yop.sys.dto.RegressionAssertionDTO;

import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.query.QueryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class TestCaseQueryServiceImpl implements TestCaseQueryService {

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();

    private static final JavaType TEST_CASE_FORM_PARAM_JAVA_TYPE = JSON_MAPPER.contructCollectionType(List.class, FormRequestParamDTO.class);

    private static final JavaType TEST_CASE_ASSERT_PARAM_JAVA_TYPE = JSON_MAPPER.contructCollectionType(List.class, RegressionAssertionDTO.class);

    @Resource(name = "testCaseQueryService")
    private QueryService queryService;

    @Override
    public RegTestCaseVO findTestCaseById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("test case id can not be null");
        }
        Map<String, Object> param = Maps.newHashMap();
        param.put("id", id);
        Map<String, Object> map = queryService.queryUnique("findTestCaseById", param, false);
        return convertToTestCaseVO(map);
    }

    @Override
    public RegTestExecHisVO findExecHisByHisId(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("test case exec his id can not be null");
        }
        Map<String, Object> param = Maps.newHashMap();
        param.put("id", id);
        Map<String, Object> map = queryService.queryUnique("findExecHisByHisId", param, false);
        return convertTestExecHisVO(map);
    }

    @Override
    public Map<String, Integer> statRegTestCountByApiUris(String[] apiUris) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("apiUris", apiUris);
        List<Map<String, Object>> list = queryService.query("statRegTestCountByApiUris", param);
        return listToMap(list);
    }

    @Override
    public List<RegressionTestControllerVO.ExecHisStatResult> execHisStatByHistoryId(Long historyId) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("historyId", historyId);
        List<Map<String, Object>> list = queryService.query("execHisStatByHistoryId", param);
        return convertHisStatResult(list);
    }

    private Map<String, Integer> listToMap(List<Map<String, Object>> list) {
        Map<String, Integer> res = Maps.newHashMap();
        for (Map<String, Object> map : list) {
            if (map.get("api_uri") == null || map.get("total_count") == null) {
                continue;
            }
            res.put(map.get("api_uri").toString(), Integer.parseInt(map.get("total_count").toString()));
        }
        return res;
    }

    private RegTestCaseVO convertToTestCaseVO(Map<String, Object> params) {
        RegTestCaseVO vo = new RegTestCaseVO();
        if (params == null || params.isEmpty()) {
            return vo;
        }
        vo.setId((Long) params.get("id"));
        vo.setApiUri((String) params.get("api_uri"));
        vo.setTitle((String) params.get("title"));
        vo.setRegressive((Boolean) params.get("regressive"));
        vo.setAppKey((String) params.get("app_key"));
        vo.setSecurity((String) params.get("security"));
        List<FormRequestParamDTO> formRequestParam = JSON_MAPPER.fromJson((String) params.get("form_request_param"), TEST_CASE_FORM_PARAM_JAVA_TYPE);
        vo.setFormRequestParam(formRequestParam);
        vo.setJsonRequestParam((String) params.get("json_request_param"));
        List<RegressionAssertionDTO> assertionList = JSON_MAPPER.fromJson((String) params.get("assertion_list"), TEST_CASE_ASSERT_PARAM_JAVA_TYPE);
        vo.setAssertionList(assertionList);
        vo.setTokenId(params.get("token_id") == null ? null : (Long) params.get("token_id"));
        vo.setCreatedDateTime((Date) params.get("created_datetime"));
        vo.setLastModifiedDateTime((Date) params.get("last_modified_datetime"));
        return vo;
    }

    private RegTestExecHisVO convertTestExecHisVO(Map<String, Object> params) {
        RegTestExecHisVO vo = new RegTestExecHisVO();
        if (params == null || params.isEmpty()) {
            return vo;
        }
        vo.setId((Long) params.get("id"));
        vo.setStatus((String) params.get("status"));
        vo.setOperator((String) params.get("operator"));
        vo.setEnvironment((String) params.get("environment"));
        vo.setStartExecuteTime((Date) params.get("start_execute_time"));
        vo.setFinishExecuteTime((Date) params.get("finish_execute_time"));
        vo.setCreatedDateTime((Date) params.get("created_datetime"));
        vo.setLastModifiedDateTime((Date) params.get("last_modified_datetime"));
        return vo;
    }

    private List<RegressionTestControllerVO.ExecHisStatResult> convertHisStatResult(List<Map<String, Object>> list) {
        List<RegressionTestControllerVO.ExecHisStatResult> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return resultList;
        }
        for (Map<String, Object> map : list) {
            RegressionTestControllerVO.ExecHisStatResult execHisStatResult = new RegressionTestControllerVO.ExecHisStatResult();
            execHisStatResult.setApiUri((String) map.get("api_uri"));
            execHisStatResult.setSuccessCount(Integer.parseInt(map.get("success_count").toString()));
            execHisStatResult.setFailureCount(Integer.parseInt(map.get("failure_count").toString()));
            resultList.add(execHisStatResult);
        }
        return resultList;
    }
}
