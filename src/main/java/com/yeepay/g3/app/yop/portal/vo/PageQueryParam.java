/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/9 11:59 上午
 */
@Builder
@Data
public class PageQueryParam implements Serializable {
    private static final long serialVersionUID = -1L;
    @Builder.Default
    private int pageNo = 1;
    @Builder.Default
    private int pageSize = 20;

    @Tolerate
    public PageQueryParam() {

    }
}
