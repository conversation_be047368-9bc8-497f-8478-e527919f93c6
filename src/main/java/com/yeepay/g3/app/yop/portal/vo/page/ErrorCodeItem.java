package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.ErrorCodeTypeEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午2:51
 */
public class ErrorCodeItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private ErrorCodeTypeEnum type;

    private String apiGroupCode;

    private String apiUri;

    private String errorCode;

    private String subErrorCode;

    private String subErrorMsg;

    private Date createdDateTime;

    private Date lastModifiedDateTime;

    private String solution;

    private String spSolution;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ErrorCodeTypeEnum getType() {
        return type;
    }

    public void setType(ErrorCodeTypeEnum type) {
        this.type = type;
    }

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getSubErrorCode() {
        return subErrorCode;
    }

    public void setSubErrorCode(String subErrorCode) {
        this.subErrorCode = subErrorCode;
    }

    public String getSubErrorMsg() {
        return subErrorMsg;
    }

    public void setSubErrorMsg(String subErrorMsg) {
        this.subErrorMsg = subErrorMsg;
    }

    public Date getCreatedDateTime() {
        return createdDateTime;
    }

    public void setCreatedDateTime(Date createdDateTime) {
        this.createdDateTime = createdDateTime;
    }

    public Date getLastModifiedDateTime() {
        return lastModifiedDateTime;
    }

    public void setLastModifiedDateTime(Date lastModifiedDateTime) {
        this.lastModifiedDateTime = lastModifiedDateTime;
    }

    public String getSolution() {
        return solution;
    }

    public void setSolution(String solution) {
        this.solution = solution;
    }

    public String getSpSolution() {
        return spSolution;
    }

    public void setSpSolution(String spSolution) {
        this.spSolution = spSolution;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
