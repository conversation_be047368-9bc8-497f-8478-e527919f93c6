/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.service.SceneQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.vo.SceneParamVO;
import com.yeepay.g3.app.yop.portal.vo.SceneVO;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.yeepay.g3.app.yop.portal.utils.Constants.NON_EXISTS_SP_CODE;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/7 上午10:41
 */
@Component
public class SceneQueryServiceImpl implements SceneQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SceneQueryServiceImpl.class);

    @Resource(name = "sceneQueryService")
    private QueryService queryService;

    @Override
    public List<SceneVO> list(SceneParamVO sceneParamVO) {
        List queryResult = queryService.query("list", getBizMap(sceneParamVO));
        return convert(queryResult);
    }

    private Map<String, Object> getBizMap(SceneParamVO sceneParamVO) {
        Map<String, Object> bizMap = new HashMap<>();
        bizMap.put("productCode", sceneParamVO.getProductCode());
        if (CollectionUtils.isNotEmpty(sceneParamVO.getIds())) {
            bizMap.put("ids", sceneParamVO.getIds());
        }
        if (ShiroUtils.isSpOperator()) {
            final Set<String> spCodes = ShiroUtils.getShiroUser().getSpScopes();
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(spCodes)) {
                bizMap.put("spCodes", Collections.singletonList(NON_EXISTS_SP_CODE));
            } else {
                bizMap.put("spCodes", spCodes);
            }
        }
        return bizMap;
    }

    private List<SceneVO> convert(List queryResult) {
        if (Collections3.isEmpty(queryResult)) {
            return Collections.emptyList();
        }
        List<SceneVO> sceneVOS = new ArrayList<>(queryResult.size());
        for (Object result : queryResult) {
            Map resultMap = (Map) result;
            SceneVO sceneVO = new SceneVO();
            sceneVO.setId((Long) resultMap.get("id"));
            sceneVO.setName((String) resultMap.get("scene_name"));
            sceneVO.setProductCode((String) resultMap.get("product_code"));
            sceneVO.setSeq((Integer) resultMap.get("seq"));
            sceneVOS.add(sceneVO);
        }
        return sceneVOS;
    }
}
