package com.yeepay.g3.app.yop.portal.utils;

import java.nio.charset.Charset;

/**
 * title: Constants <br>
 * description: <br>
 * Copyright: Copyright (c)2017<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * @author: hao.wang-3
 * @version: 1.0.0
 * @since: 2017/9/19 下午10:31
 */
public interface Constants {
    String MODIFY_PWD = "MODIFY_PWD";
    String RECOVER_PWD = "RECOVER_PWD";
    String VALIDATE_CODE_SUCCESS_TIMESTAMP = "_VALIDATE_CODE_TIMESTAMP";
    String UPDATE_PWD_VALIDATE_CODE_SUCCESS_TIMESTAMP = MODIFY_PWD + VALIDATE_CODE_SUCCESS_TIMESTAMP;
    String RECOVER_PWD_VALIDATE_CODE_SUCCESS_TIMESTAMP = RECOVER_PWD + VALIDATE_CODE_SUCCESS_TIMESTAMP;

    String UPLOAD_CERT_FILE_EXTENTION = ".cer";

    String API_GROUP_WITH_API_LIST = "api_group_with_api_list";
    String API_GROUP_WITHOUT_API_LIST = "api_group_without_api_list";

    String CEPH_BUCKET_NAME = "bucketName";
    String CEPH_SUB_BUCKET_NAME = "subBucketName";
    String CEPH_BUCKET_ACL = "bucketAcl";
    String CEPH_BUCKET_DEFAULT_ACL = "PublicRead";
    String CEPH_TOKEN = "token";
    String CEPH_ACCESS_KEY = "accessKey";
    String CEPH_YCS_URL = "ycsUrl";
    String CEPH_STATIC_URL = "staticUrl";
    String CEPH_BATCH_POST_ADDRESS = "batchPostAddress";
    String CEPH_NGINX_ACCESS_PREFIX = "nginxProxyPrefix";
    String ATTACHMENT_SUPPORTED_TYPES = "supportedTypes";
    String ATTACHMENT_MAX_FILE_SIZE = "maxFileSize";
    String ATTACHMENT_ACCESS_PATH = "/attachments/access";

    /**
     * mock sp code
     */
    String NON_EXISTS_SP_CODE = "non-exists-sp";
    String NON_EXISTS_OPERATOR_CODE = "non-exists-operator";
    String YOP_SYS_PARAMS_CONFIG_SEPERATER = ",";

    String YOP_GIT_SYNC_ACTIVE_MODE = "ACTIVE";

    String YOP_GIT_SYNC_PASSIVE_MODE = "PASSIVE";

    Charset DEFAULT_CHAR_SET = Charset.forName("UTF-8");

    /**
     * 压缩配置key
     */

    String COMPRESS_SWITCH = "shouldCompress";
    /*单位B*/
    String COMPRESS_THRESHOLD = "compressThreshold";
    String COMPRESS_SUPPORTED_TYPES = "supportedTypes";
    String COMPRESS_SUPPORTED_TYPES_SEPERATOR = ",";
    String COMPRESS_SUPPORTED_TYPES_ALIAS_SEPERATOR = "|";
    String COMPRESS_CMD_PREFIX = "cmd_";
    String COMPRESS_SRC_FILE_VARIABLE = "$srcFile";
    String COMPRESS_TARGET_FILE_VARIABLE = "$targetFile";
    String COMPRESS_WORK_DIR = "/apps/tmp/attachments";

    String PATH_SEPARATOR = "/";

    String YOP_MBR_DOC_ACCESS_MBR_PREFIX_CFG_KEY = "mbr_prefix";
    String YOP_MBR_DOC_ACCESS_PREVIEW_PREFIX_CFG_KEY = "preview_prefix";
    String YOP_MBR_DOC_PRODUCTS_INDEX_SUFFIX_FORMAT = "/docs/products/%s";
    String YOP_MBR_DOC_OPEN_INDEX_SUFFIX_FORMAT = "/docs/open/%s";
    String YOP_MBR_DOC_PLATFORM_INDEX_SUFFIX_FORMAT = "/docs/platform";
    String YOP_MBR_DOC_SOLUTION_INDEX_SUFFIX_FORMAT = "/docs/solutions/%s";

    String ALL_SP_INFO_KEY = "sp";
}
