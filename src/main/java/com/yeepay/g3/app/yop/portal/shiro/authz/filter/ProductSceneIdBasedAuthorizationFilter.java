package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;

/**
 * title: 基于场景id的权限控制<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-27 20:58
 */
@Component
public class ProductSceneIdBasedAuthorizationFilter extends CacheAbstractAuthorizationFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductSceneIdBasedAuthorizationFilter.class);

    @Override
    protected String[] getSpCodes(String[] params) {
        String[] spCodes;
        if (params == null || params.length < 1) {
            spCodes = null;
        } else {
            spCodes = new String[params.length];
            try {
                for (int i = 0; i < params.length; i++) {
                    spCodes[i] = productSceneIdLocalCache.get(Long.valueOf(params[i]));
                }
            } catch (ExecutionException e) {
                LOGGER.info("get spCode by product-scene-id wrong, params are :{},exception is :{}", params, e);
            }
            LOGGER.debug("get spCodes by product-scene-id success, params:{}, result:{}", params, spCodes);
        }
        return spCodes;
    }

    @Override
    public String shiroName() {
        return "product_scene_id_based";
    }
}
