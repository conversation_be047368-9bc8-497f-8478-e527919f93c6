#!/bin/bash

JAVA_RUN="java"
JAVA_OPTS="-jar $JAVA_OPTS"

JAVA_OPTS="$JAVA_OPTS -Dappname=yop-portal"
JAVA_OPTS="$JAVA_OPTS -Xms2048m -Xmx2048m -XX:MetaspaceSize=128m -XX:MaxMetaspaceSize=256m"
JAVA_OPTS="$JAVA_OPTS -XX:MaxDirectMemorySize=512M"
JAVA_OPTS="$JAVA_OPTS -XX:ParallelGCThreads=2"
JAVA_OPTS="$JAVA_OPTS -XX:ConcGCThreads=1"

JAVA_OPTS="$JAVA_OPTS -server -Xss228k"
JAVA_OPTS="$JAVA_OPTS -Ddbconfigpath=/apps/commoncfg/runtimecfg"
JAVA_OPTS="$JAVA_OPTS -Dloader.path=/apps/commoncfg"
JAVA_OPTS="$JAVA_OPTS -Dsun.net.inetaddr.ttl=-1"
JAVA_OPTS="$JAVA_OPTS -XX:+DisableExplicitGC"
JAVA_OPTS="$JAVA_OPTS -Xshare:off -Dhostname=yce-jenkins"
JAVA_OPTS="$JAVA_OPTS -Djava.awt.headless=true"
JAVA_OPTS="$JAVA_OPTS -Djava.net.preferIPv4Stack=true"
JAVA_OPTS="$JAVA_OPTS -Djute.maxbuffer=41943040"
#JAVA_OPTS="$JAVA_OPTS -Dfile.encoding='UTF-8'"
JAVA_OPTS="$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"

JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
JAVA_OPTS="$JAVA_OPTS -XX:InitialCodeCacheSize=64M -XX:CodeCacheExpansionSize=1M -XX:CodeCacheMinimumFreeSpace=1M -XX:ReservedCodeCacheSize=200M -XX:MinMetaspaceExpansion=1M -XX:MaxMetaspaceExpansion=8M -XX:CompressedClassSpaceSize=256M"
JAVA_OPTS="$JAVA_OPTS -XX:+UseLargePages -XX:LargePageSizeInBytes=16m"
JAVA_OPTS="$JAVA_OPTS -XX:-OmitStackTraceInFastThrow"
JAVA_OPTS="$JAVA_OPTS -XX:+UseFastAccessorMethods"
JAVA_OPTS="$JAVA_OPTS -Dlog4j2.formatMsgNoLookups=true"

JAVA_OPTS="$JAVA_OPTS -Dsun.net.http.retryPost=false"

JAVA_OPTS="$JAVA_OPTS -verbose:gc -Xloggc:/apps/log/gc.log"
#JAVA_OPTS="$JAVA_OPTS -XX:OnOutOfMemoryError='kill -9 %p'"
JAVA_OPTS="$JAVA_OPTS -XX:ErrorFile=/apps/log/gc_error_crash_%p.log"
JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="$JAVA_OPTS -XX:HeapDumpPath=/apps/log/"
JAVA_OPTS="$JAVA_OPTS -XX:NumberOfGCLogFiles=20"
JAVA_OPTS="$JAVA_OPTS -XX:GCLogFileSize=30m"
JAVA_OPTS="$JAVA_OPTS -XX:+UseGCLogFileRotation"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintVMOptions"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCDetails"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCTimeStamps"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintHeapAtGC"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCDateStamps"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintTenuringDistribution"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintAdaptiveSizePolicy"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCApplicationStoppedTime"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCApplicationConcurrentTime"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintConcurrentLocks"
JAVA_OPTS="$JAVA_OPTS -XX:+TraceClassUnloading"

# JMX
pubip=""
if [ "-$pubip" == "-" ]
then
{
  pubip=""
}
fi
if [ "-$pubip" != "-" ]
then
{
  JAVA_OPTS="$JAVA_OPTS -Djava.rmi.server.hostname=$pubip"
}
fi
JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote"
JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.port=6969"
JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.ssl=false"
JAVA_OPTS="$JAVA_OPTS -Dcom.sun.management.jmxremote.authenticate=false"
JAVA_OPTS="$JAVA_OPTS -Ddubbo.application.environment=$DUBBO_APPLICATION_ENVIRONMENT"

echo "Using JAVA_OPTS:$JAVA_OPTS"
echo "Using JAVA_ARGS:$JAVA_ARGS"

java $JAVA_OPTS -Dfile.encoding='UTF-8' -XX:OnOutOfMemoryError="kill -9 %p" /apps/app.jar $JAVA_ARGS