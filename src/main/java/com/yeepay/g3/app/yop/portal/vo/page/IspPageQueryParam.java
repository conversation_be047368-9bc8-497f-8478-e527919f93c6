/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/9/25 上午10:50
 */
public class IspPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private String spCode;

    private String status;

    private String tenantCode;

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public static final class Builder {
        private Integer pageNo;
        private String spCode;
        private Integer pageSize;
        private String status;
        private String tenantCode;

        private Builder() {
        }

        public static Builder anIspPageQueryParam() {
            return new Builder();
        }

        public Builder withPageNo(Integer pageNo) {
            this.pageNo = pageNo;
            return this;
        }

        public Builder withSpCode(String spCode) {
            this.spCode = spCode;
            return this;
        }

        public Builder withPageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder withStatus(String status) {
            this.status = status;
            return this;
        }

        public Builder withTenantCode(String tenantCode) {
            this.tenantCode = tenantCode;
            return this;
        }

        public IspPageQueryParam build() {
            IspPageQueryParam ispPageQueryParam = new IspPageQueryParam();
            ispPageQueryParam.setPageNo(pageNo);
            ispPageQueryParam.setSpCode(spCode);
            ispPageQueryParam.setPageSize(pageSize);
            ispPageQueryParam.setStatus(status);
            ispPageQueryParam.setTenantCode(tenantCode);
            return ispPageQueryParam;
        }
    }
}
