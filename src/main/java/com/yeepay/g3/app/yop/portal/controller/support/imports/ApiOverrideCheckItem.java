package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.app.yop.portal.vo.ApiManageVO;

import java.io.Serializable;

/**
 * title: api覆盖检查项<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-19 11:24
 */
public class ApiOverrideCheckItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private ApiManageVO api;

    private ApiManageVO srcApi;

    public ApiManageVO getApi() {
        return api;
    }

    public void setApi(ApiManageVO api) {
        this.api = api;
    }

    public ApiOverrideCheckItem withApi(ApiManageVO api) {
        this.api = api;
        return this;
    }

    public ApiManageVO getSrcApi() {
        return srcApi;
    }

    public void setSrcApi(ApiManageVO srcApi) {
        this.srcApi = srcApi;
    }

    public ApiOverrideCheckItem withSrcApi(ApiManageVO srcApi) {
        this.srcApi = srcApi;
        return this;
    }

}
