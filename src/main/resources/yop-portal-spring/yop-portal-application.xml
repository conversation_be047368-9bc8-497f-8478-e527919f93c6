<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/aop  http://www.springframework.org/schema/aop/spring-aop-2.5.xsd"
       default-autowire="byName">

    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <bean id="mapper" class="org.dozer.DozerBeanMapper">
        <!--<property name="mappingFiles" value="classpath:dozer/dozer-mapping.xml"/>-->
    </bean>

    <bean id="springContextUtils" class="com.yeepay.g3.app.yop.portal.shiro.utils.SpringContextUtil"/>

    <bean id="ehcacheManager" class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean">
        <property name="configLocation" value="classpath:yop-portal-spring/ehcache.xml"/>
    </bean>

    <import resource="classpath:/yop-portal-spring/yop-portal-datasource.xml"/>
    <import resource="classpath:/yop-portal-spring/yop-portal-query.xml"/>

    <beans profile="rabbitMQ">
        <import resource="classpath:/yop-portal-spring/yop-portal-rabbit.xml"/>
    </beans>
    <beans profile="rocketMQ">
        <import resource="classpath:/yop-portal-spring/yop-portal-rocket.xml"/>
    </beans>
</beans>
