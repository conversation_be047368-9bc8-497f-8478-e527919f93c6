package com.yeepay.g3.app.yop.portal.utils;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.frame.yop.ca.utils.Digests;
import com.yeepay.g3.frame.yop.ca.utils.Encodes;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;

import java.util.regex.Pattern;

/**
 * title: 校验工具类<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2017<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/4/18 20:17
 */
public class ValidateUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ValidateUtils.class);

    private static final String PATCHCA = "PATCHCA";

    // 密码匹配有问题，空格不好使
    private static final Pattern PASSWORD_PATTERN = Pattern.compile("(?!^\\d+$)(?!^[a-zA-Z]+$)(?!^[_#@]+$).{6,16}");
    private static final Pattern NET_SEGMENT_PATTERN = Pattern.compile("^(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])\\.(\\d{1,2}|1\\d\\d|2[0-4]\\d|25[0-5])/([1-9]|[12][0-9]|3[01])$");

    /**
     * 校验密码是否规范
     * 6-16个字符
     * 只能包含字母、数字以及标点符号（除空格）
     * 字母、数字和标点符号至少包含两种
     *
     * @param password 密码
     * @return true:是,false:否
     */
    public static boolean isPassword(String password) {
        return PASSWORD_PATTERN.matcher(password).matches();
    }

    /**
     * 校验密码
     *
     * @param inputPassword 输入的密码
     * @param password      密码
     * @param saltStr       盐
     * @return true/密码一致，false/密码不一致
     */
    public static boolean checkPassword(String inputPassword, String password, String saltStr) {
        byte[] salt = null;
        int iterations = 1;
        if (StringUtils.isNotEmpty(saltStr)) {
            salt = Encodes.decodeHex(saltStr);
            iterations = 1000;
        }
        String pwd = Encodes.encodeHex(Digests.digest(inputPassword, "SHA-1", salt, iterations));
        return StringUtils.equals(pwd, password);
    }

    /**
     * 校验图片验证码
     *
     * @param captcha 图片验证码
     */
    public static boolean checkCaptcha(String captcha) {
        if (ConfigUtils.isProductionMode()) {
            Session session = SecurityUtils.getSubject().getSession();
            String captchaInSession = (String) session.removeAttribute(PATCHCA);
            LOGGER.debug("captchaInSession:{}, captchaUserInput:{}", captchaInSession, captcha);
            return StringUtils.isNotBlank(captchaInSession) && StringUtils.equals(captchaInSession, captcha);
        }
        return true;
    }

    /**
     * 是否是网段
     *
     * @param netSegment 网段
     * @return true:是,false:否
     */
    public static boolean isNetSegment(String netSegment) {
        return NET_SEGMENT_PATTERN.matcher(netSegment).matches();
    }

}
