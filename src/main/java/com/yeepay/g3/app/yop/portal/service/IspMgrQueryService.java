package com.yeepay.g3.app.yop.portal.service;


import com.yeepay.g3.app.yop.portal.vo.SpCodeVO;
import com.yeepay.g3.app.yop.portal.vo.page.IspInfoPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.IspPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.perm.dto.IspInfoDTO;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: IspMgrQueryService
 * @Description:
 * @date 2018年5月11日 上午10:26:16
 */
public interface IspMgrQueryService {

    List<SpCodeVO> queryAllSpCode();

    List<SpCodeVO> queryAllSpCodeForSp(Set<String> spCodes);

    PageQueryResult<IspInfoPageItem> pageList(IspPageQueryParam param);

    PageQueryResult<IspInfoPageItem> pageListForSp(IspPageQueryParam param, Set<String> spCodes);

    /**
     * 根据服务提供方code查租户code
     *
     * @param spCodes
     * @return
     */
    List<String> findTenantCodesBySpCodes(Set<String> spCodes);

    IspInfoDTO findBySpCode(String spCode);
}
