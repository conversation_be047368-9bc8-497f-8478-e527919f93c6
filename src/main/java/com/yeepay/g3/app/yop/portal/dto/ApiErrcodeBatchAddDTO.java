package com.yeepay.g3.app.yop.portal.dto;

import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 14:10
 */
public class ApiErrcodeBatchAddDTO extends BaseDTO {

    private static final long serialVersionUID = -1L;

    @NotNull
    private String apiId;

    @NotEmpty
    private List<Long> errcodeIds;

    public String getApiId() {
        return apiId;
    }

    public void setApiId(String apiId) {
        this.apiId = apiId;
    }

    public List<Long> getErrcodeIds() {
        return errcodeIds;
    }

    public void setErrcodeIds(List<Long> errcodeIds) {
        this.errcodeIds = errcodeIds;
    }
}
