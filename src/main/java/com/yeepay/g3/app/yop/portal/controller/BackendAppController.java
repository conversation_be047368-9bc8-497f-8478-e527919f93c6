/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.fasterxml.jackson.databind.JsonNode;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.exception.YopPortalRateLimiterException;
import com.yeepay.g3.app.yop.portal.service.ApiQueryService;
import com.yeepay.g3.app.yop.portal.service.BackendAppQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.validation.group.BackendAppCommon;
import com.yeepay.g3.app.yop.portal.validation.group.BackendAppCreate;
import com.yeepay.g3.app.yop.portal.validation.group.BackendAppEdit;
import com.yeepay.g3.app.yop.portal.validation.group.BackendAppRebuild;
import com.yeepay.g3.app.yop.portal.vo.BackendAppRebuildVO;
import com.yeepay.g3.app.yop.portal.vo.BackendAppVO;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.page.BackendAppPageQueryParam;
import com.yeepay.g3.boot.autoconfigure.ratelimiter.annotations.GuavaRateLimiter;
import com.yeepay.g3.facade.yop.sys.dto.BackendAppDTO;
import com.yeepay.g3.facade.yop.sys.dto.ClassLoaderRebuildRequest;
import com.yeepay.g3.facade.yop.sys.enums.ClassLoadModeEnum;
import com.yeepay.g3.facade.yop.sys.enums.DeployModeEnum;
import com.yeepay.g3.facade.yop.sys.enums.RpcModeEnum;
import com.yeepay.g3.facade.yop.sys.facade.BackendAppFacade;
import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/9/25 10:10
 */
@Controller
@RequestMapping("/rest/backend-app")
public class BackendAppController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BackendAppController.class);

    private BackendAppFacade backendAppFacade = RemoteServiceFactory.getService(BackendAppFacade.class);

    @Autowired
    private BackendAppQueryService backendAppQueryService;

    @Autowired
    private ApiQueryService apiQueryService;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@RequestParam(value = "spCode", required = false) String spCode,
                                @RequestParam(value = "backendCode", required = false) String backendCode,
                                @RequestParam(value = "backendName", required = false) String backendName,
                                @RequestParam(value = "deployMode", required = false) String deployMode,
                                @RequestParam(value = "classLoadMode", required = false) String classLoadMode,
                                @RequestParam(value = "rpcMode", required = false) String rpcMode,
                                @RequestParam(value = "_pageNo", required = false) Integer _pageNo,
                                @RequestParam(value = "_pageSize", required = false) Integer _pageSize) {
        BackendAppPageQueryParam param = new BackendAppPageQueryParam();
        param.setSpCode(spCode);
        param.setBackendCode(backendCode);
        param.setBackendName(backendName);
        param.setDeployMode(DeployModeEnum.parse(deployMode));
        param.setClassLoadMode(ClassLoadModeEnum.parse(classLoadMode));
        param.setRpcMode(RpcModeEnum.parse(rpcMode));
        param.setPageNo(_pageNo);
        param.setPageSize(_pageSize);
        if (ShiroUtils.isPlatformOperator()) {
            return new ResponseMessage("result", backendAppQueryService.pageList(param));
        } else {
            return new ResponseMessage("result", backendAppQueryService.pageListForSp(param));
        }
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody @Validated({BackendAppCommon.class, BackendAppCreate.class}) BackendAppVO backendAppVO) {
        BackendAppDTO backendAppDTO = new BackendAppDTO();
        BeanUtils.copyProperties(backendAppVO, backendAppDTO);
        backendAppFacade.create(backendAppDTO);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody @Validated({BackendAppCommon.class, BackendAppEdit.class}) BackendAppVO backendAppVO) {
        BackendAppDTO backendAppDTO = new BackendAppDTO();
        BeanUtils.copyProperties(backendAppVO, backendAppDTO);
        backendAppFacade.update(backendAppDTO);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam("backendCode") String backendCode) {
        return new ResponseMessage("result", backendAppFacade.findByBackendCode(backendCode));
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam("backendCode") String backendCode) {
        backendAppFacade.delete(backendCode);
        return new ResponseMessage();
    }

    @GuavaRateLimiter(key = "'yop-portal:class:rebuild-lock' + #backendAppRebuildVO.backendCode", permitsPerSecond = 1.0 / 60, exception = YopPortalRateLimiterException.class)
    @ResponseBody
    @RequestMapping(value = "/class-loader/rebuild", method = RequestMethod.POST)
    public ResponseMessage classLoaderRebuild(@RequestBody @Validated({BackendAppCommon.class, BackendAppRebuild.class}) BackendAppRebuildVO backendAppRebuildVO) {
        LOGGER.info("rebuild class loader, request:{}", backendAppRebuildVO);
        ClassLoaderRebuildRequest rebuildRequest = new ClassLoaderRebuildRequest();
        BeanUtils.copyProperties(backendAppRebuildVO, rebuildRequest);
        backendAppFacade.rebuildClassLoader(rebuildRequest);
        return new ResponseMessage();
    }

    @RequestMapping(value = "/export-apis", method = RequestMethod.GET)
    public void exportApis(@RequestParam(value = "backendCode") String backendCode, HttpServletResponse response) {
        OutputStream os = null;
        JsonNode jsonNode = apiQueryService.listApiServletConfig(backendCode);
        if (jsonNode == null) {
            throw new YeepayRuntimeException("api为空");
        }
        try {
            String fileName = backendCode + ".json";
            os = response.getOutputStream();
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
            response.setContentType("application/octet-stream; charset=utf-8");
            response.setHeader("Access-Control-Expose-Headers", "*");
            // Fix 中文乱码
            os.write(jsonNode.toString().getBytes(Charset.forName("utf-8")));
            os.flush();
        } catch (IOException e) {
            LOGGER.error("error download api...", e);
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    LOGGER.error("error close stream...", e);
                }
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/rebuild-type/list", method = RequestMethod.GET)
    public ResponseMessage rebuildTypeList() {
        List<CommonsVO> list = new ArrayList<>();
        Map<String, String> rebuildTypeMap = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_REBUILD_TYPE);
        for (Map.Entry<String, String> entry : rebuildTypeMap.entrySet()) {
            list.add(new CommonsVO(entry.getKey(), entry.getValue()));
        }
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/deploy-mode/list", method = RequestMethod.GET)
    public ResponseMessage deployModeList() {
        List<CommonsVO> list = new ArrayList<>();
        DeployModeEnum.getDisplayValueMap().forEach((value, displayName) -> {
            list.add(new CommonsVO(value, displayName));
        });
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/rpc-mode/list", method = RequestMethod.GET)
    public ResponseMessage rpcModeList() {
        List<CommonsVO> list = new ArrayList<>();
        RpcModeEnum.getDisplayValueMap().forEach((value, displayName) -> {
            list.add(new CommonsVO(value, displayName));
        });
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/class-load-mode/list", method = RequestMethod.GET)
    public ResponseMessage classLoadModeList() {
        List<CommonsVO> list = new ArrayList<>();
        ClassLoadModeEnum.getDisplayValueMap().forEach((value, displayName) -> {
            list.add(new CommonsVO(value, displayName));
        });
        return new ResponseMessage("result", list);
    }

}
