package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.dto.SpiImportContext;
import com.yeepay.g3.app.yop.portal.service.SpiImportCacheService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * title: Spi导入缓存服务<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-31 17:26
 */
@Component
public class SpiImportCacheServiceImpl implements SpiImportCacheService {


    @Override
    @CachePut(value = "yp:si", key = "#requestId")
    public SpiImportContext storeContext(String requestId, SpiImportContext context) {
        return context;
    }

    @Override
    @Cacheable(value = "yp:si", key = "#requestId")
    public SpiImportContext loadContext(String requestId) {
        //do nothing
        return null;
    }

    @Override
    @CacheEvict(value = "yp:si", key = "#requestId")
    public void evictContext(String requestId) {

    }
}
