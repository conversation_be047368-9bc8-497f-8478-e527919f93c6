package com.yeepay.g3.yop.frame.chain.interceptor.json;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.yeepay.g3.facade.yop.api.enums.ApiParamDataTypeEnum;
import com.yeepay.g3.yop.frame.chain.ChainExecutionControl;
import com.yeepay.g3.yop.frame.chain.interceptor.AbstractInterceptor;
import com.yeepay.g3.yop.frame.chain.interceptor.annotations.ContentTypeRoutable;
import com.yeepay.g3.yop.frame.context.AuthenticateContext;
import com.yeepay.g3.yop.frame.context.Context;
import com.yeepay.g3.yop.frame.context.ContextUtils;
import com.yeepay.g3.yop.frame.context.impl.JsonPayloadContext;
import com.yeepay.g3.yop.frame.definition.JsonParamDefinition;
import com.yeepay.g3.yop.frame.exception.InternalException;
import com.yeepay.g3.yop.frame.internalvariable.InternalVariableGetter;
import com.yeepay.g3.yop.frame.response.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.List;

@ContentTypeRoutable(MediaType.APPLICATION_JSON_VALUE)
@Component("internalVariableInterceptor")
public class InternalVariableInterceptor extends AbstractInterceptor<AuthenticateContext, JsonPayloadContext> {

    @Autowired
    private InternalVariableGetter internalVariableGetter;

    @Override
    public boolean isEnabled(Context context) {
        if (context.getApiContext().isApiV2()) {
            return false;
        }
        return true;
    }

    @Override
    public Response onIntercept(Context<AuthenticateContext, JsonPayloadContext> context, ChainExecutionControl control) throws InternalException {
        JsonNode root = context.getParamContext().getParamContainer();
        JsonParamDefinition rootDefinition = ContextUtils.getApiDefinition(context).getJsonParamDefinitionsDetail().getRootDefinition();

        populateDefaultValue(root, null, rootDefinition, context);
        return control.invokeNext();
    }

    /**
     * 填充默认值
     *
     * @param data
     * @param schema
     */
    private void populateDefaultValue(JsonNode data, JsonNode parent, JsonParamDefinition schema, Context context) {
        ApiParamDataTypeEnum dataType = schema.getParamDataType();
        if (dataType.isSimpleType()) {
            String defaultValue = schema.getDefaultValue();
            //没配置默认值
            if (StringUtils.isBlank(defaultValue)) {
                return;
            }
            if (Boolean.TRUE.equals(schema.getInternal()) || data == null || StringUtils.isBlank(data.asText())) {
                ((ObjectNode) parent).put(schema.getParamName(), internalVariableGetter.get(context, defaultValue));
            }
            return;
        }

        List<JsonParamDefinition> children = schema.getChildren();
        if (data == null || children == null || children.isEmpty()) {
            return;
        }

        if (dataType == ApiParamDataTypeEnum.ARRAY) {
            for (int i = 0; i < data.size(); i++) {
                populateDefaultValue(data.get(i), null, schema.getChild(), context);
            }
        }

        //对象类型的参数不能赋默认值
        if (dataType == ApiParamDataTypeEnum.OBJECT) {
            for (JsonParamDefinition child : children) {
                populateDefaultValue(data.get(child.getParamName()), data, child, context);
            }
        }
    }
}
