package com.yeepay.g3.app.yop.portal.controller;


import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.biz.ApiGroupGitSyncBiz;
import com.yeepay.g3.app.yop.portal.controller.support.imports.*;
import com.yeepay.g3.app.yop.portal.controller.support.sync.ApiGroupGitSyncResultDTO;
import com.yeepay.g3.app.yop.portal.controller.support.sync.GitSyncStatics;
import com.yeepay.g3.app.yop.portal.dto.ApiGroupGitSyncCommitDTO;
import com.yeepay.g3.app.yop.portal.dto.ApiGroupGitSyncContext;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.git.GitBranch;
import com.yeepay.g3.app.yop.portal.git.GitClient;
import com.yeepay.g3.app.yop.portal.git.GitFile;
import com.yeepay.g3.app.yop.portal.git.GitPushEvent;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.swagger.SwaggerAnalysisResult;
import com.yeepay.g3.app.yop.portal.utils.swagger.SwaggerAnalyzer;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.dto.imports.ApiImportCheckItem;
import com.yeepay.g3.facade.yop.sys.enums.GitSyncOperationEnum;
import com.yeepay.g3.facade.yop.sys.enums.SwaggerDataFormatEnum;
import com.yeepay.g3.facade.yop.sys.facade.ApiGroupGitSyncInfoMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.ApiMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.ModelMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.SpiMgrFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.yeepay.g3.app.yop.portal.utils.Constants.YOP_GIT_SYNC_ACTIVE_MODE;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-11 18:53
 */
@Controller
@RequestMapping("/rest/api-group/git-sync")
public class ApiGroupGitSyncController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiGroupGitSyncController.class);

    private ApiGroupGitSyncInfoMgrFacade gitSyncInfoMgrFacade = RemoteServiceFactory.getService(ApiGroupGitSyncInfoMgrFacade.class);

    private ApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(ApiMgrFacade.class);

    private SpiMgrFacade spiMgrFacade = RemoteServiceFactory.getService(SpiMgrFacade.class);

    private ModelMgrFacade modelMgrFacade = RemoteServiceFactory.getService(ModelMgrFacade.class);

    @Autowired
    private ApiGroupGitSyncBiz gitSyncBiz;

    @Autowired
    private GitClient gitClient;

    @RequestMapping(value = "config", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage queryConfig(@RequestParam("apiGroup") String apiGroup) {
        try {
            CheckUtils.notEmpty(apiGroup, "apiGroup");
            ApiGroupGitSyncInfoDTO result = gitSyncInfoMgrFacade.find(apiGroup);
            if (result == null) {
                return new ResponseMessage();
            } else {
                return new ResponseMessage("result", gitSyncInfoMgrFacade.find(apiGroup));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when get config, apiGroup:" + apiGroup, ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "config", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage updateConfig(ApiGroupGitSyncInfoDTO info) {
        try {
            CheckUtils.notEmpty(info, "body");
            CheckUtils.notEmpty(info.getApiGroup(), "apiGroup");
            CheckUtils.notNull(info.getSyncOpen(), "syncOpen");
            String syncMode = (String) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_GIT_SYNC_MODE);
            if (StringUtils.equals(syncMode, YOP_GIT_SYNC_ACTIVE_MODE)) {
                CheckUtils.notEmpty(info.getGitRepository(), "gitRepository");
                CheckUtils.notEmpty(info.getGitBranch(), "gitBranch");
                CheckUtils.notEmpty(info.getFilePath(), "filePath");
                String commitId = gitClient.getLatestCommit(info.getGitRepository(), info.getGitBranch(), info.getFilePath());
                if (commitId == null) {
                    throw new YeepayRuntimeException("file not exist.");
                }
            }
            if (info.getVersion() == null) {
                gitSyncInfoMgrFacade.add(info);
            } else {
                //更新时无法更新currentCommitId和latestCommitId
                info.setCurrentCommitId(null);
                info.setLatestCommitId(null);
                gitSyncInfoMgrFacade.update(info);
            }
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when update config", ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "diff", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage showDiff(@RequestParam("apiGroup") String apiGroup,
                                    @RequestParam(value = "currentCommitId", required = false) String currentCommitId,
                                    @RequestParam("latestCommitId") String latestCommitId) {
        try {
            CheckUtils.notEmpty(apiGroup, "apiGroup");
            CheckUtils.notEmpty(latestCommitId, "latestCommitId");
            ApiGroupGitSyncInfoDTO info = gitSyncInfoMgrFacade.find(apiGroup);
            if (info == null) {
                throw new YeepayRuntimeException("GitSyncInfo not exist.");
            }
            if (BooleanUtils.isNotTrue(info.getSyncOpen())) {
                throw new YeepayRuntimeException("GitSync is closed.");
            }
            return new ResponseMessage("result", gitSyncBiz.diff(info, currentCommitId, latestCommitId));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when show diff", ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "sync", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage doSync(String apiGroup, String requestId) {
        try {
            CheckUtils.notEmpty(apiGroup, "apiGroup");
            CheckUtils.notEmpty(requestId, "requestId");
            ApiGroupGitSyncContext context = gitSyncBiz.findSyncRequest(apiGroup, requestId);
            if (context == null) {
                throw new YeepayRuntimeException("no context cached, apiGroup:{0}, requestId:{1}.", apiGroup, requestId);
            }
            SwaggerDataFormatEnum dataFormat = StringUtils.endsWith(context.getFilePath(), ".json") ?
                    SwaggerDataFormatEnum.JSON : SwaggerDataFormatEnum.YAML;
            SwaggerAnalysisResult swaggerAnalysisResult = SwaggerAnalyzer.analysis(context.getLatestFileContent(), dataFormat);
            List<ImportAnalysisFailedInfo> failedInfos = ImportsUtils.getFailedInfos(swaggerAnalysisResult);
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "swagger解析失败")
                        .put("detail", failedInfos);
            }
            if (MapUtils.isEmpty(swaggerAnalysisResult.getApis())) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "没有有效的API数据");
            }
            fillApiGroup(apiGroup, swaggerAnalysisResult);
            failedInfos = ImportsUtils.checkAnalysisResult(swaggerAnalysisResult);
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "导入数据合法性校验失败")
                        .put("detail", failedInfos);
            }
            ApiDependencyAnalysisResult dependencyAnalysisResult = new ApiDependencyAnalysisResult();
            failedInfos = ImportsUtils.apiDependencyAnalysis(new ApiDependencyAnalysisRequest(swaggerAnalysisResult.getApis(), swaggerAnalysisResult.getCallbacks(),
                    swaggerAnalysisResult.getModels()), dependencyAnalysisResult);
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "依赖分析失败")
                        .put("detail", failedInfos);
            }
            List<ApiImportCheckItem> apiImportCheckItems = new ArrayList<>(dependencyAnalysisResult.getDependencyInfos().size());
            dependencyAnalysisResult.getDependencyInfos().forEach(info -> apiImportCheckItems.add(new ApiImportCheckItem()
                    .withApiName(info.getApiName())
                    .withRequestPath(info.getRequestKey().getPath())
                    .withRequestHttpMethod(info.getRequestKey().getHttpMethod())));
            ApiImportCheckResult checkResult = apiMgrFacade.checkImport(new ApiImportCheckRequest()
                    .withApiGroup(apiGroup)
                    .withApis(apiImportCheckItems)
                    .withSpis(MapUtils.isEmpty(dependencyAnalysisResult.getSpis()) ? null : new ArrayList<>(dependencyAnalysisResult.getSpis().keySet()))
                    .withModels(MapUtils.isEmpty(dependencyAnalysisResult.getModels()) ? null : new ArrayList<>(dependencyAnalysisResult.getModels().keySet())));
            failedInfos = ImportsUtils.getImportCheckFailedInfos(checkResult);
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "导入数据与现有数据存在冲突")
                        .put("detail", failedInfos);
            }
            failedInfos = ImportsUtils.checkOverrideConsistence(dependencyAnalysisResult, checkResult, swaggerAnalysisResult.isNativeSwagger());
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "导入数据与现有数据存在冲突")
                        .put("detail", failedInfos);
            }
            ApiGroupGitSyncResultDTO result = new ApiGroupGitSyncResultDTO();
            result.setApiSyncResult(syncApis(dependencyAnalysisResult, checkResult));
            result.setSpiSyncResult(syncSpis(dependencyAnalysisResult, checkResult));
            result.setModelSyncResult(syncModels(dependencyAnalysisResult, checkResult));
            gitSyncInfoMgrFacade.updateCurrentCommitId(context.getApiGroup(), context.getLatestCommitId());
            gitSyncBiz.deleteSyncRequest(apiGroup, requestId);
            return new ResponseMessage("result", result);
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when do sync", ex);
            return new ResponseMessage(ex);
        }
    }


    private void fillApiGroup(String apiGroup, SwaggerAnalysisResult swaggerAnalysisResult) {
        if (MapUtils.isNotEmpty(swaggerAnalysisResult.getApis())) {
            swaggerAnalysisResult.getApis().values().forEach(api -> api.getBasic().setApiGroup(apiGroup));
        }
        if (MapUtils.isNotEmpty(swaggerAnalysisResult.getCallbacks())) {
            swaggerAnalysisResult.getCallbacks().values().forEach(spi -> spi.getBasic().setApiGroup(apiGroup));
        }
        if (MapUtils.isNotEmpty(swaggerAnalysisResult.getModels())) {
            swaggerAnalysisResult.getModels().values().forEach(model -> model.setApiGroup(apiGroup));
        }
    }


    private GitSyncStatics syncApis(ApiDependencyAnalysisResult dependencyAnalysisResult, ApiImportCheckResult checkResult) {
        GitSyncStatics apiSyncStatics = new GitSyncStatics();
        apiSyncStatics.setTotal(dependencyAnalysisResult.getApis().size());
        ApiBatchSyncRequest apiBatchSyncRequest = new ApiBatchSyncRequest();
        Map<GitSyncOperationEnum, List<ApiDTO>> apis = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(checkResult.getApisToCreate())) {
            checkResult.getApisToCreate().forEach(requestKey -> {
                ApiDTO apiToCreate = dependencyAnalysisResult.getApis().get(requestKey);
                apis.computeIfAbsent(GitSyncOperationEnum.CREATE, gitSyncOperationEnum -> new ArrayList<>()).add(apiToCreate);
            });
        }
        if (MapUtils.isNotEmpty(checkResult.getApisToOverride())) {
            checkResult.getApisToOverride().forEach(((requestKey, existApi) -> {
                ApiDTO apiToOverride = dependencyAnalysisResult.getApis().get(requestKey);
                if (ImportCompareUtils.isEqualApi(apiToOverride, existApi)) {
                    apiSyncStatics.addIgnoredItem(requestKey.toString());
                } else {
                    apis.computeIfAbsent(GitSyncOperationEnum.OVERRIDE, gitSyncOperationEnum -> new ArrayList<>()).add(apiToOverride);
                }
            }));
        }
        if (apis.size() == 0) {
            return apiSyncStatics;
        }
        apiBatchSyncRequest.setApis(apis);
        apiBatchSyncRequest.setOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause("portal同步"));
        ApiBatchSyncResponse response = apiMgrFacade.gitSync(apiBatchSyncRequest);
        if (CollectionUtils.isNotEmpty(response.getSuccess())) {
            response.getSuccess().forEach(requestKey -> apiSyncStatics.addSuccessItem(requestKey.toString()));
        }
        if (CollectionUtils.isNotEmpty(response.getFailed())) {
            apiSyncStatics.setFailed(response.getFailed());
        }
        return apiSyncStatics;
    }

    private GitSyncStatics syncSpis(ApiDependencyAnalysisResult dependencyAnalysisResult, ApiImportCheckResult checkResult) {
        if (MapUtils.isEmpty(dependencyAnalysisResult.getSpis())) {
            return null;
        }

        GitSyncStatics spiSyncStatics = new GitSyncStatics();
        spiSyncStatics.setTotal(dependencyAnalysisResult.getSpis().size());
        spiSyncStatics.setUnused(dependencyAnalysisResult.getUnusedSpis());
        if (dependencyAnalysisResult.getUnusedSpis() != null
                && (dependencyAnalysisResult.getUnusedSpis().size() == dependencyAnalysisResult.getSpis().size())) {
            return spiSyncStatics;
        }

        SpiBatchSyncRequest spiBatchSyncRequest = new SpiBatchSyncRequest();
        Map<GitSyncOperationEnum, List<SpiDTO>> spis = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(checkResult.getSpisToCreate())) {
            checkResult.getSpisToCreate().forEach(spiName -> {
                SpiDTO spiToCreate = dependencyAnalysisResult.getSpis().get(spiName);
                spis.computeIfAbsent(GitSyncOperationEnum.CREATE, gitSyncOperationEnum -> new ArrayList<>()).add(spiToCreate);
            });
        }
        if (MapUtils.isNotEmpty(checkResult.getSpisToOverride())) {
            checkResult.getSpisToOverride().forEach(((spiName, existSpi) -> {
                SpiDTO spiToOverride = dependencyAnalysisResult.getSpis().get(spiName);
                if (ImportCompareUtils.isEqualSpi(spiToOverride, existSpi)) {
                    spiSyncStatics.addIgnoredItem(spiName);
                } else {
                    spis.computeIfAbsent(GitSyncOperationEnum.OVERRIDE, gitSyncOperationEnum -> new ArrayList<>()).add(spiToOverride);
                }
            }));
        }
        if (spis.size() == 0) {
            return spiSyncStatics;
        }
        spiBatchSyncRequest.setSpis(spis);
        spiBatchSyncRequest.setOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause("portal同步"));
        SpiBatchSyncResponse response = spiMgrFacade.gitSync(spiBatchSyncRequest);
        if (CollectionUtils.isNotEmpty(response.getSuccess())) {
            response.getSuccess().forEach(spiSyncStatics::addSuccessItem);
        }
        if (CollectionUtils.isNotEmpty(response.getFailed())) {
            spiSyncStatics.setFailed(response.getFailed());
        }
        return spiSyncStatics;
    }

    private GitSyncStatics syncModels(ApiDependencyAnalysisResult dependencyAnalysisResult, ApiImportCheckResult checkResult) {
        if (MapUtils.isEmpty(dependencyAnalysisResult.getModels())) {
            return null;
        }

        GitSyncStatics modelSyncStatics = new GitSyncStatics();
        modelSyncStatics.setTotal(dependencyAnalysisResult.getModels().size());
        modelSyncStatics.setUnused(dependencyAnalysisResult.getUnusedModels());
        if (dependencyAnalysisResult.getUnusedModels() != null
                && (dependencyAnalysisResult.getUnusedModels().size() == dependencyAnalysisResult.getModels().size())) {
            return modelSyncStatics;
        }
        ModelBatchSyncRequest modelBatchSyncRequest = new ModelBatchSyncRequest();
        Map<GitSyncOperationEnum, List<ModelDTO>> models = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(checkResult.getModelsToCreate())) {
            checkResult.getModelsToCreate().forEach(modelName -> {
                ModelDTO modelToCreate = dependencyAnalysisResult.getModels().get(modelName);
                models.computeIfAbsent(GitSyncOperationEnum.CREATE, gitSyncOperationEnum -> new ArrayList<>()).add(modelToCreate);
            });
        }
        if (MapUtils.isNotEmpty(checkResult.getModelsToOverride())) {
            checkResult.getModelsToOverride().forEach(((modelName, existModel) -> {
                ModelDTO modelToOverride = dependencyAnalysisResult.getModels().get(modelName);
                if (ImportCompareUtils.isEqualModel(modelToOverride, existModel)) {
                    modelSyncStatics.addIgnoredItem(modelName);
                } else {
                    models.computeIfAbsent(GitSyncOperationEnum.OVERRIDE, gitSyncOperationEnum -> new ArrayList<>()).add(modelToOverride);
                }
            }));
        }
        if (models.size() == 0) {
            return modelSyncStatics;
        }
        modelBatchSyncRequest.setModels(models);
        modelBatchSyncRequest.setOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause("portal同步"));
        ModelBatchSyncResponse response = modelMgrFacade.gitSync(modelBatchSyncRequest);
        if (CollectionUtils.isNotEmpty(response.getSuccess())) {
            response.getSuccess().forEach(modelSyncStatics::addSuccessItem);
        }
        if (CollectionUtils.isNotEmpty(response.getFailed())) {
            modelSyncStatics.setFailed(response.getFailed());
        }
        return modelSyncStatics;
    }

    @RequestMapping(value = "latest-commit", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getLatestCommit(@RequestParam(value = "apiGroup") String apiGroup,
                                           @RequestParam(value = "repository", required = false) String repository,
                                           @RequestParam(value = "branch", required = false) String branch,
                                           @RequestParam(value = "filePath", required = false) String filePath) {
        try {
            CheckUtils.notEmpty(apiGroup, "apiGroup");
            if (StringUtils.isNotEmpty(repository)) {
                CheckUtils.notEmpty(branch, "branch");
                CheckUtils.notEmpty(filePath, "filePath");
                String latestCommitId = gitClient.getLatestCommit(repository, branch, filePath);
                if (StringUtils.isEmpty(latestCommitId)) {
                    throw new YeepayRuntimeException("file not exist");
                }
                ApiGroupGitSyncInfoDTO info = gitSyncInfoMgrFacade.find(apiGroup);
                if (info != null && !StringUtils.equals(latestCommitId, info.getLatestCommitId())) {
                    gitSyncInfoMgrFacade.updateLatestCommitId(apiGroup, latestCommitId);
                }
                ApiGroupGitSyncInfoDTO result = new ApiGroupGitSyncInfoDTO();
                result.setCurrentCommitId(info == null ? null : info.getCurrentCommitId());
                result.setLatestCommitId(latestCommitId);
                return new ResponseMessage("result", result);
            } else {
                ApiGroupGitSyncInfoDTO info = gitSyncInfoMgrFacade.find(apiGroup);
                if (info == null) {
                    throw new YeepayRuntimeException("GitSyncInfo not exist.");
                }
                String latestCommitId = gitClient.getLatestCommit(info.getGitRepository(), info.getGitBranch(), info.getFilePath());
                if (StringUtils.isEmpty(latestCommitId)) {
                    throw new YeepayRuntimeException("file not exist");
                }
                if (!latestCommitId.equals(info.getLatestCommitId())) {
                    gitSyncInfoMgrFacade.updateLatestCommitId(apiGroup, latestCommitId);
                }
                ApiGroupGitSyncInfoDTO result = new ApiGroupGitSyncInfoDTO();
                result.setCurrentCommitId(info.getCurrentCommitId());
                result.setLatestCommitId(latestCommitId);
                return new ResponseMessage("result", result);
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when get Latest commit", ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "load-branch", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getBranch(@RequestParam("repository") String repository) {
        try {
            CheckUtils.notEmpty(repository, "repository");
            List<GitBranch> branches = gitClient.getBranches(repository);
            if (CheckUtils.isEmpty(branches)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "no branches exist");
            } else {
                List<String> result = branches.stream().map(GitBranch::getName).collect(Collectors.toList());
                return new ResponseMessage("result", result);
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when load branch", ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "refresh", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage refreshAll() {
        try {
            gitSyncBiz.refreshAll();
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when refresh all", ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "git-event/push")
    @ResponseBody
    public ResponseMessage eventTrigger(@RequestBody GitPushEvent pushEvent, @RequestParam("apiGroup") String apiGroup) {
        try {
            LOGGER.info("GitPushEvent received, apiGroup:{}, gitRepository:{}", apiGroup, pushEvent.getProject().getPathWithNamespace());
            ApiGroupGitSyncInfoDTO info = gitSyncInfoMgrFacade.find(apiGroup);
            if (info == null) {
                throw new YeepayRuntimeException("GitSyncInfo not exist.");
            }
            String latestCommitId = gitClient.getLatestCommit(info.getGitRepository(), info.getGitBranch(), info.getFilePath());
            if (StringUtils.isEmpty(latestCommitId)) {
                throw new YeepayRuntimeException("file not exist");
            }
            if (!latestCommitId.equals(info.getLatestCommitId())) {
                gitSyncInfoMgrFacade.updateLatestCommitId(apiGroup, latestCommitId);
            }
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Unexpected exception occurred when handled GitPushEvent.", ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "commit-to-production", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage commitToProduction(@RequestParam("apiGroup") String apiGroup, @RequestParam("commitId") String commitId) {
        try {
            CheckUtils.notEmpty(apiGroup, "apiGroup");
            CheckUtils.notEmpty(commitId, "commitId");
            ApiGroupGitSyncInfoDTO syncInfo = gitSyncInfoMgrFacade.find(apiGroup);
            if (syncInfo == null) {
                throw new YeepayRuntimeException("GitSyncInfo not exist.");
            }
            if (BooleanUtils.isNotTrue(syncInfo.getSyncOpen())) {
                throw new YeepayRuntimeException("GitSync is closed.");
            }
            GitFile file = gitClient.getFile(syncInfo.getGitRepository(), syncInfo.getFilePath(), commitId);
            if (file == null) {
                throw new YeepayRuntimeException("file not exist, commitId:{0}", commitId);
            }
            gitSyncBiz.commitToProduction(new ApiGroupGitSyncCommitDTO().withApiGroup(apiGroup)
                    .withGitRepository(syncInfo.getGitRepository())
                    .withGitBranch(syncInfo.getGitBranch())
                    .withFilePath(syncInfo.getFilePath())
                    .withCommitId(commitId)
                    .withContent(file.getContent()));
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Unexpected exception occurred when commit to production.", ex);
            return new ResponseMessage(ex);
        }
    }


    @RequestMapping(value = "handle-commit", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage handleCommit(@RequestBody ApiGroupGitSyncCommitDTO commit) {
        try {
            CheckUtils.notNull(commit, "commit");
            CheckUtils.notEmpty(commit.getApiGroup(), "commit.apiGroup");
            CheckUtils.notEmpty(commit.getGitRepository(), "commit.gitRepository");
            CheckUtils.notEmpty(commit.getGitBranch(), "commit.gitBranch");
            CheckUtils.notEmpty(commit.getFilePath(), "commit.filePath");
            CheckUtils.notEmpty(commit.getCommitId(), "commit.commitId");
            CheckUtils.notEmpty(commit.getContent(), "commit.content");
            gitSyncBiz.handleCommit(commit);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Unexpected exception occurred when handle commit.", ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "mode", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage getSyncMode() {
        return new ResponseMessage("result", ConfigUtils.getSysConfigParam(ConfigEnum.YOP_GIT_SYNC_MODE));
    }

}
