/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.app.yop.portal.enums.ApiVersionEnum;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Tolerate;

/**
 * title: spi关联页面查询api的参数类<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/4 11:40 上午
 */
@Data
@Builder
public class SpiApiQueryParam extends BasePageQueryParam {
    private static final long serialVersionUID = -1L;

    /**
     * spi名称
     */
    private String spiName;

    /**
     * api名称
     */
    private String apiName;

    /**
     * api uri
     */
    private String apiUri;

    /**
     * api版本
     */
    private ApiVersionEnum apiVersion;

    @Tolerate
    public SpiApiQueryParam() {

    }

}
