/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.regression.sdk.model.transform;

import com.yeepay.g3.app.yop.portal.regression.sdk.model.ParamTypeEnum;
import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestRequest;
import com.yeepay.g3.core.yop.sdk.sample.http.Headers;
import com.yeepay.g3.core.yop.sdk.sample.http.HttpMethodName;
import com.yeepay.g3.core.yop.sdk.sample.internal.DefaultRequest;
import com.yeepay.g3.core.yop.sdk.sample.internal.Request;
import com.yeepay.g3.core.yop.sdk.sample.internal.RestartableInputStream;
import com.yeepay.g3.core.yop.sdk.sample.model.transform.RequestMarshaller;

import java.util.UUID;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/19 下午4:14
 */
public class RegressionTestMarshaller implements RequestMarshaller<RegressionTestRequest> {

    @Override
    public Request marshall(RegressionTestRequest in) {
        Request<RegressionTestRequest> request = new DefaultRequest<>(in, in.getServiceName());
        request.setResourcePath(in.getResourcePath());
        request.setHttpMethod(HttpMethodName.POST);//只支持POST请求
        if (!request.getHeaders().containsKey(Headers.YOP_REQUEST_ID)) {
            String requestId = UUID.randomUUID().toString();
            request.addHeader(Headers.YOP_REQUEST_ID, requestId);
        }

        if (ParamTypeEnum.JSON.equals(in.getParamTypeEnum())) {
            request.addHeader(Headers.CONTENT_TYPE, "application/json");
            request.setContent(RestartableInputStream.wrap(in.getJsonParams().getBytes()));
        } else {
            request.addHeader(Headers.CONTENT_TYPE, "multipart/form-data");
            request.setParameters(in.getFormParams());
        }

        return request;
    }

    private static class CacheInstanceHolder {
        public static RegressionTestMarshaller INSTANCE = new RegressionTestMarshaller();

        private CacheInstanceHolder() {
        }
    }

    public static RegressionTestMarshaller getInstance() {
        return RegressionTestMarshaller.CacheInstanceHolder.INSTANCE;
    }
}
