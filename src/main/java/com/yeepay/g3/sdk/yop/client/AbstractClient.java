package com.yeepay.g3.sdk.yop.client;

import com.alibaba.csp.sentinel.Entry;
import com.alibaba.csp.sentinel.Tracer;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yeepay.g3.sdk.yop.YopServiceException;
import com.yeepay.g3.sdk.yop.client.apache.YopServerResponseInterceptor;
import com.yeepay.g3.sdk.yop.client.metric.YopFailureItem;
import com.yeepay.g3.sdk.yop.client.metric.YopStatus;
import com.yeepay.g3.sdk.yop.client.metric.event.host.YopHostFailEvent;
import com.yeepay.g3.sdk.yop.client.metric.event.host.YopHostRequestEvent;
import com.yeepay.g3.sdk.yop.client.metric.event.host.YopHostSuccessEvent;
import com.yeepay.g3.sdk.yop.client.router.GateWayRouter;
import com.yeepay.g3.sdk.yop.client.router.ServerRootSpace;
import com.yeepay.g3.sdk.yop.client.router.SimpleGateWayRouter;
import com.yeepay.g3.sdk.yop.config.AppSdkConfig;
import com.yeepay.g3.sdk.yop.config.AppSdkConfigProvider;
import com.yeepay.g3.sdk.yop.config.AppSdkConfigProviderRegistry;
import com.yeepay.g3.sdk.yop.config.support.BackUpAppSdkConfigManager;
import com.yeepay.g3.sdk.yop.encrypt.AESEncrypter;
import com.yeepay.g3.sdk.yop.encrypt.CertTypeEnum;
import com.yeepay.g3.sdk.yop.encrypt.DigestAlgEnum;
import com.yeepay.g3.sdk.yop.encrypt.RSA;
import com.yeepay.g3.sdk.yop.error.YopError;
import com.yeepay.g3.sdk.yop.exception.*;
import com.yeepay.g3.sdk.yop.http.Headers;
import com.yeepay.g3.sdk.yop.http.HttpMethodName;
import com.yeepay.g3.sdk.yop.http.HttpUtils;
import com.yeepay.g3.sdk.yop.http.YopHttpResponse;
import com.yeepay.g3.sdk.yop.internal.RestartableInputStream;
import com.yeepay.g3.sdk.yop.invoke.model.UriResource;
import com.yeepay.g3.sdk.yop.model.DownloadInputStream;
import com.yeepay.g3.sdk.yop.model.YopErrorResponse;
import com.yeepay.g3.sdk.yop.sentinel.YopDegradeRuleHelper;
import com.yeepay.g3.sdk.yop.sentinel.YopSph;
import com.yeepay.g3.sdk.yop.unmarshaller.JacksonJsonMarshaller;
import com.yeepay.g3.sdk.yop.utils.CharacterConstants;
import com.yeepay.g3.sdk.yop.utils.FileUtils;
import com.yeepay.g3.sdk.yop.utils.InternalConfig;
import com.yeepay.g3.sdk.yop.utils.checksum.CRC64;
import com.yeepay.g3.sdk.yop.utils.checksum.CRC64Utils;
import com.yeepay.g3.sdk.yop.utils.io.MarkableFileInputStream;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.Header;
import org.apache.http.HeaderIterator;
import org.apache.http.HttpHost;
import org.apache.http.HttpStatus;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.NTCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.client.utils.HttpClientUtils;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.InputStreamEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContextBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.PublicKey;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.zip.CheckedInputStream;

import static com.yeepay.g3.sdk.yop.client.YopConstants.API_URI_PREFIX;
import static com.yeepay.g3.sdk.yop.utils.CharacterConstants.COLON;
import static com.yeepay.g3.sdk.yop.utils.CharacterConstants.EMPTY;

public class AbstractClient {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractClient.class);

    private static final String CONTENT_TYPE_JSON = "application/json";
    private static final String CONTENT_TYPE_STREAM = "application/octet-stream";

    private static final int EXT_READ_BUFFER_SIZE = 64 * 1024;

    private static final GateWayRouter GATE_WAY_ROUTER;

    private static CloseableHttpClient httpClient;

    private static org.apache.http.client.config.RequestConfig.Builder requestConfigBuilder;
    private static CredentialsProvider credentialsProvider;
    private static HttpHost proxyHttpHost;

    protected static final String SESSION_ID = getUUID();

    private static final YopError FILE_CHECK_ERROR;

    private static final YopError FILE_UPLOAD_ERROR;


    static {
        initApacheHttpClient();
        FILE_CHECK_ERROR = new YopError();
        FILE_CHECK_ERROR.setCode("40044");
        FILE_CHECK_ERROR.setMessage("业务处理失败");
        FILE_CHECK_ERROR.setSubCode("isv.scene.filestore.put.crc-failed");
        FILE_CHECK_ERROR.setSubMessage("文件上传crc校验失败");

        FILE_UPLOAD_ERROR = new YopError();
        FILE_UPLOAD_ERROR.setCode("40044");
        FILE_UPLOAD_ERROR.setMessage("业务处理失败");
        FILE_UPLOAD_ERROR.setSubCode("isv.scene.filestore.put.failed");
        FILE_UPLOAD_ERROR.setSubMessage("文件上传失败");

        AppSdkConfigProvider sdkConfigProvider = AppSdkConfigProviderRegistry.getProvider();
        AppSdkConfig appSdkConfig = sdkConfigProvider.getDefaultConfig() == null ? BackUpAppSdkConfigManager.getBackUpConfig()
                : sdkConfigProvider.getDefaultConfig();
        ServerRootSpace serverRootSpace;
        try {
            serverRootSpace = new ServerRootSpace(
                    StringUtils.defaultIfBlank(appSdkConfig.getServerRoot(), YopConstants.DEFAULT_SERVER_ROOT),
                    StringUtils.defaultIfBlank(appSdkConfig.getYosServerRoot(), YopConstants.DEFAULT_YOS_SERVER_ROOT),
                    StringUtils.defaultIfBlank(appSdkConfig.getSandboxServerRoot(), YopConstants.DEFAULT_SANDBOX_SERVER_ROOT),
                    appSdkConfig.getPreferredServerRoots(), appSdkConfig.getPreferredYosServerRoots());
        } catch (Exception e) {
            throw new YopClientException("server root illegal");
        }
        GATE_WAY_ROUTER = new SimpleGateWayRouter(serverRootSpace);

        // 熔断配置
        List<String> serverRoots = Lists.newArrayList(serverRootSpace.getYosServerRoot(), serverRootSpace.getSandboxServerRoot());
        if (CollectionUtils.isNotEmpty(serverRootSpace.getPreferredEndPoint())) {
            serverRoots.addAll(serverRootSpace.getPreferredEndPoint());
        }
        if (CollectionUtils.isNotEmpty(serverRootSpace.getPreferredYosEndPoint())) {
            serverRoots.addAll(serverRootSpace.getPreferredYosEndPoint());
        }
        YopDegradeRuleHelper.initDegradeRule(serverRoots, InternalConfig.getCircuitBreakerConfig());
    }

    // 创建包含connection pool与超时设置的client
    public static void initApacheHttpClient() {
        RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(InternalConfig.READ_TIMEOUT)
                .setConnectTimeout(InternalConfig.CONNECT_TIMEOUT)
                .build();

        httpClient = HttpClientBuilder.create()
                .setMaxConnTotal(InternalConfig.MAX_CONN_TOTAL)
                .setMaxConnPerRoute(InternalConfig.MAX_CONN_PER_ROUTE)
                .setSSLSocketFactory(InternalConfig.TRUST_ALL_CERTS ? getTrustedAllSSLConnectionSocketFactory() : null)
                .setDefaultRequestConfig(requestConfig)
                .evictExpiredConnections()
                .evictIdleConnections(3, TimeUnit.SECONDS)
                .disableAutomaticRetries()
                .setKeepAliveStrategy(new YopConnectionKeepAliveStrategy())
                .addInterceptorLast(YopServerResponseInterceptor.INSTANCE)
                .build();

        requestConfigBuilder = RequestConfig.custom();
        requestConfigBuilder.setConnectTimeout(InternalConfig.CONNECT_TIMEOUT);
        requestConfigBuilder.setSocketTimeout(InternalConfig.READ_TIMEOUT);
        requestConfigBuilder.setStaleConnectionCheckEnabled(true);
        /*if (InternalConfig.getLocalAddress() != null) {
            requestConfigBuilder.setLocalAddress(config.getLocalAddress());
        }*/

        if (InternalConfig.proxy != null) {
            String proxyHost = InternalConfig.proxy.getHost();
            int proxyPort = InternalConfig.proxy.getPort();
            String scheme = InternalConfig.proxy.getScheme();
            if (proxyHost != null && proxyPort > 0) {
                proxyHttpHost = new HttpHost(proxyHost, proxyPort, scheme);
                requestConfigBuilder.setProxy(proxyHttpHost);
                credentialsProvider = new BasicCredentialsProvider();
                String proxyUsername = InternalConfig.proxy.getUsername();
                String proxyPassword = InternalConfig.proxy.getPassword();
                String proxyDomain = InternalConfig.proxy.getDomain();
                String proxyWorkstation = InternalConfig.proxy.getWorkstation();
                if (proxyUsername != null && proxyPassword != null) {
                    credentialsProvider.setCredentials(new AuthScope(proxyHost, proxyPort),
                            new NTCredentials(proxyUsername, proxyPassword,
                                    proxyWorkstation, proxyDomain));
                }
            }
        }
    }

    public static void destroyApacheHttpClient() {
        try {
            httpClient.close();
        } catch (IOException e) {
            LOGGER.error("httpclient close fail", e);
        }
    }

    private static SSLConnectionSocketFactory getTrustedAllSSLConnectionSocketFactory() {
        LOGGER.warn("[yop-sdk]已设置信任所有证书。仅供内测使用，请勿在生产环境配置。");
        SSLConnectionSocketFactory sslConnectionSocketFactory = null;
        try {
            SSLContextBuilder builder = new SSLContextBuilder();
            builder.loadTrustMaterial(null, new TrustStrategy() {
                @Override
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            });
            sslConnectionSocketFactory = new SSLConnectionSocketFactory(builder.build());
        } catch (Exception e) {
            LOGGER.error("error when get trust-all-certs request factory,will return normal request factory instead", e);
        }
        return sslConnectionSocketFactory;
    }

    /**
     * 构建普通formHttp请求
     *
     * @param request    yop请求
     * @param contentUrl 请求地址
     * @param httpMethod http方法
     * @return http请求
     * @throws IOException io异常
     */
    protected static HttpUriRequest buildFormHttpRequest(YopRequest request, String contentUrl, HttpMethodName httpMethod) {
        RequestBuilder requestBuilder;
        if (HttpMethodName.POST == httpMethod) {
            requestBuilder = RequestBuilder.post();
        } else if (HttpMethodName.GET == httpMethod) {
            requestBuilder = RequestBuilder.get();
        } else if (HttpMethodName.DELETE == httpMethod) {
            requestBuilder = RequestBuilder.delete();
        } else if (HttpMethodName.PUT == httpMethod) {
            requestBuilder = RequestBuilder.put();
        } else {
            throw new YopClientException("unsupported http method");
        }
        requestBuilder.setUri(contentUrl);
        for (Map.Entry<String, String> entry : request.getHeaders().entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }
        try {
            for (Map.Entry<String, Collection<String>> entry : request.getParams().asMap().entrySet()) {
                String paramKey = entry.getKey();
                for (String value : entry.getValue()) {
                    requestBuilder.addParameter(paramKey, URLEncoder.encode(value, YopConstants.ENCODING));
                }
            }
        } catch (IOException ex) {
            throw new YopClientException("unable to create http request.", ex);
        }
        return requestBuilder.build();
    }

    /**
     * 构建普通jsonHttp请求
     *
     * @param request    yop请求
     * @param contentUrl 请求地址
     * @param httpMethod http方法
     * @return http请求
     * @throws IOException io异常
     */
    protected static HttpUriRequest buildJsonHttpRequest(YopRequest request, String contentUrl, HttpMethodName httpMethod, String jsonString) {
        RequestBuilder requestBuilder = RequestBuilder.post();
        requestBuilder.setUri(contentUrl);
        for (Map.Entry<String, String> entry : request.getHeaders().entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }
        StringEntity entity = new StringEntity(jsonString, YopConstants.ENCODING);
        entity.setContentEncoding(YopConstants.ENCODING);
        entity.setContentType(ContentType.APPLICATION_JSON.getMimeType());
        requestBuilder.setEntity(entity);
        return requestBuilder.build();
    }

    protected static Pair<HttpUriRequest, List<CheckedInputStream>> buildHttpRequest(YopRequest request, String contentUrl,
                                                                                          YopRequestType requestType, HttpMethodName methodName) {
        if (YopRequestType.MULTI_FILE_UPLOAD.equals(requestType)) {
            return buildMultiFormRequest(request, contentUrl);
        }
        HttpUriRequest httpUriRequest;
        if (null != request.getJsonParam()) {
            httpUriRequest = buildJsonHttpRequest(request, contentUrl, methodName, request.getJsonParam());
        } else {
            httpUriRequest = buildFormHttpRequest(request, contentUrl, methodName);
        }
        return new ImmutablePair<HttpUriRequest, List<CheckedInputStream>>(httpUriRequest, null);
    }

    /**
     * 构建multiFormRequest
     *
     * @param request    yop请求
     * @param contentUrl 请求地址
     * @return key为http请求，value为checkInputStream列表
     * @throws IOException io异常
     */
    protected static Pair<HttpUriRequest, List<CheckedInputStream>> buildMultiFormRequest(YopRequest request, String contentUrl) {
        RequestBuilder requestBuilder = RequestBuilder.post().setUri(contentUrl);
        for (Map.Entry<String, String> entry : request.getHeaders().entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }
        TreeMap<String, CheckedInputStream> checkedInputStreams = null;
        try {
            if (!request.hasFiles()) {
                for (Map.Entry<String, Collection<String>> entry : request.getParams().asMap().entrySet()) {
                    String paramKey = entry.getKey();
                    for (String value : entry.getValue()) {
                        requestBuilder.addParameter(paramKey, URLEncoder.encode(value, YopConstants.ENCODING));
                    }
                }
            } else {
                checkedInputStreams = Maps.newTreeMap();
                MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create().setCharset(Charset.forName(YopConstants.ENCODING));
                for (Map.Entry<String, Object> entry : request.getMultipartFiles().entrySet()) {
                    String paramName = entry.getKey();
                    final Object paramValue = entry.getValue();
                    if (paramValue instanceof InputStream) {
                        resetStreamIfNecessary((InputStream) paramValue);
                    }
                    Pair<String, CheckedInputStream> checkedInputStreamPair = wrapToCheckInputStream(paramValue);
                    multipartEntityBuilder.addBinaryBody(paramName, checkedInputStreamPair.getRight(), ContentType.DEFAULT_BINARY, checkedInputStreamPair.getLeft());
                    checkedInputStreams.put(paramName, checkedInputStreamPair.getRight());
                }
                for (Map.Entry<String, String> entry : request.getParams().entries()) {
                    multipartEntityBuilder.addTextBody(entry.getKey(), URLEncoder.encode(entry.getValue(), YopConstants.ENCODING));
                }
                requestBuilder.setEntity(multipartEntityBuilder.build());
            }
        } catch (IOException ex) {
            throw new YopClientException("unable to create http request.", ex);
        }
        HttpUriRequest httpPost = requestBuilder.build();
        List<CheckedInputStream> inputStreamList = checkedInputStreams == null ? null : new ArrayList<CheckedInputStream>(checkedInputStreams.values());
        return new ImmutablePair<HttpUriRequest, List<CheckedInputStream>>(httpPost, inputStreamList);
    }

    private static void resetStreamIfNecessary(InputStream content) {
        if (content instanceof RestartableInputStream) {
            ((RestartableInputStream) content).restart();
        }
    }

    /**
     * 构建buildMultiPartUploadRequest
     *
     * @param request    yop请求
     * @param contentUrl 请求地址
     * @param file       文件或流
     * @param partSize   每块大小
     * @return key为http请求，value为checkInputStream
     * @throws IOException io异常
     */
    protected static Pair<HttpUriRequest, CheckedInputStream> buildMultiPartUploadRequest(YopRequest request, String contentUrl, Object file, long partSize) {
        RequestBuilder requestBuilder = RequestBuilder.put().setUri(contentUrl);
        for (Map.Entry<String, String> entry : request.getHeaders().entrySet()) {
            requestBuilder.addHeader(entry.getKey(), entry.getValue());
        }
        CheckedInputStream checkedInputStream = null;
        try {
            Pair<String, CheckedInputStream> checkedInputStreamPair = wrapToCheckInputStream(file);
            checkedInputStream = checkedInputStreamPair.getRight();
            InputStreamEntity reqEntity = new InputStreamEntity(checkedInputStream, partSize);
            reqEntity.setContentType(ContentType.DEFAULT_BINARY.getMimeType());
            requestBuilder.setEntity(reqEntity);
        } catch (IOException ex) {
            throw new YopClientException("unable to create http request.", ex);
        }
        HttpUriRequest httpPost = requestBuilder.build();
        return new ImmutablePair<HttpUriRequest, CheckedInputStream>(httpPost, checkedInputStream);
    }

    /**
     * 包装为checkInputStream
     *
     * @param file 文件或者流
     * @return key为文件名称，value为流
     * @throws IOException io异常
     */
    private static Pair<String, CheckedInputStream> wrapToCheckInputStream(Object file) throws IOException {
        if (file instanceof String) {
            File paramFile = new File((String) file);
            CheckedInputStream inputStream = new CheckedInputStream(new FileInputStream(paramFile), new CRC64());
            return new ImmutablePair<String, CheckedInputStream>(paramFile.getName(), inputStream);
        }
        if (file instanceof File) {
            CheckedInputStream inputStream = new CheckedInputStream(new FileInputStream((File) file), new CRC64());
            return new ImmutablePair<String, CheckedInputStream>(((File) file).getName(), inputStream);
        }
        if (file instanceof FileInputStream) {
            return getCheckedInputStreamPair((FileInputStream) file);
        }
        if (file instanceof InputStream) {
            return getCheckedInputStreamPair((InputStream) file);
        }
        throw new YopClientException("不支持的上传文件类型");
    }

    private static Pair<String, CheckedInputStream> getCheckedInputStreamPair(FileInputStream fileInputStream) throws IOException {
        MarkableFileInputStream in = new MarkableFileInputStream(fileInputStream);
        in.mark(0);
        //解析文件扩展名的时候会读取流的前64*1024个字节,需要reset文件流
        String fileName = FileUtils.getFileName(in);
        in.reset();
        CheckedInputStream inputStream = new CheckedInputStream(in, new CRC64());
        return new ImmutablePair<String, CheckedInputStream>(fileName, inputStream);
    }

    private static Pair<String, CheckedInputStream> getCheckedInputStreamPair(InputStream inputStream) throws IOException {
        //解析文件扩展名的时候会读取流的前64*1024个字节
        byte[] extReadBuffer = new byte[EXT_READ_BUFFER_SIZE];
        int totalRead = 0;
        int lastRead = inputStream.read(extReadBuffer);
        while (lastRead != -1) {
            totalRead += lastRead;
            if (totalRead == EXT_READ_BUFFER_SIZE) {
                break;
            }
            lastRead = inputStream.read(extReadBuffer, totalRead, EXT_READ_BUFFER_SIZE - totalRead);
        }
        ByteArrayInputStream extReadIn = new ByteArrayInputStream(extReadBuffer, 0, totalRead);
        String fileName = FileUtils.getFileName(extReadIn);
        extReadIn.reset();
        SequenceInputStream sequenceInputStream = new SequenceInputStream(extReadIn, inputStream);
        return new ImmutablePair<String, CheckedInputStream>(fileName, new CheckedInputStream(sequenceInputStream,
                new CRC64()));
    }

    protected static YopResponse fetchContentByApacheHttpClient(HttpUriRequest request) throws IOException {
        return fetchContentByApacheHttpClient(getApiUri(request.getURI()), request, ResponseConfig.NONE_OPERATION_CONFIG);
    }

    private static String getApiUri(URI uri) {
        final String path = uri.getPath();
        for (String apiUriPrefix : API_URI_PREFIX) {
            final int apiUriIdx = path.indexOf(apiUriPrefix);
            if (apiUriIdx > 0) {
                return StringUtils.substring(path, apiUriIdx);
            }
        }
        throw new YopClientException("Illegal ApiUri, value:" + uri);
    }

    protected static YopResponse fetchContentByApacheHttpClient(String apiUri, HttpUriRequest request, ResponseConfig responseConfig) throws IOException {
        return fetchContentByApacheHttpClient(apiUri, request, responseConfig, 0);
    }

    protected static YopResponse fetchContentByApacheHttpClient(String apiUri, HttpUriRequest request, ResponseConfig responseConfig, int retryCount) throws IOException {
        HttpContext httpContext = createHttpContext();
        CloseableHttpResponse remoteResponse = null;
        Throwable ex = null;
        final long reqStartTime = System.currentTimeMillis();
        try {
            remoteResponse = getHttpClient().execute(request, httpContext);
            return parseResponse(remoteResponse, responseConfig);
        } catch (YopClientException | YopHttpException e) {
            ex = e;
            throw e;
        } catch (Throwable e) {
            String requestId = getRequestId(request);
            ex = e;
            throw new YopHttpException("unable to execute request, requestId:" + requestId, e);
        } finally {
            reportHostEvent(apiUri, request, remoteResponse, ex, reqStartTime, retryCount);
            if (null != ex || (null != remoteResponse && isJsonResponse(remoteResponse))) {
                HttpClientUtils.closeQuietly(remoteResponse);
            }
        }
    }

    private static void reportHostEvent(String apiUri, HttpUriRequest request, CloseableHttpResponse httpResponse,
                                        Throwable originEx, long reqStartTime, int retryCount) {
        try {
            long elapsedTime = System.currentTimeMillis() - reqStartTime;
            final String appKey = getAppKey(request);
            boolean isEx = null != originEx,
                    isClientEx = originEx instanceof YopClientException,
                    isServiceEx = originEx instanceof YopServiceException,
                    isHttpEx = originEx instanceof YopHttpException,
                    isUnexpectedEx = isEx && !(isClientEx || isHttpEx),
                    isHostEx = isHttpEx || isUnexpectedEx,
                    needReport = !isEx || isServiceEx || isHostEx;
            if (needReport) {
                if (isHostEx) {
                    final YopHostFailEvent failEvent = new YopHostFailEvent();
                    setBasic(failEvent, appKey, apiUri, request, httpResponse, elapsedTime, retryCount);
                    failEvent.setStatus(YopStatus.FAIL);
                    failEvent.setData(new YopFailureItem(originEx));
                    ClientReporter.reportHostRequest(failEvent);
                } else {
                    final YopHostSuccessEvent successEvent = new YopHostSuccessEvent();
                    setBasic(successEvent, appKey, apiUri, request, httpResponse, elapsedTime, retryCount);
                    successEvent.setStatus(YopStatus.SUCCESS);
                    successEvent.setData("");
                    ClientReporter.reportHostRequest(successEvent);
                }
            }
        } catch (Exception e) {
            LOGGER.warn("ReportError, ex:" + e);
        }
    }

    private static void setBasic(YopHostRequestEvent<?> event, String appKey, String apiUri,
                                 HttpUriRequest request, CloseableHttpResponse httpResponse,
                                 long elapsedTime, int retryCount) {
        event.setAppKey(appKey);
        event.setServerResource(apiUri);
        event.setServerHost(HttpUtils.generateHostHeader(request.getURI()));
        String serverIp = "";
        if (null != httpResponse) {
            final Header serverIpHeader = httpResponse.getFirstHeader(Headers.YOP_SERVER_IP);
            if (null != serverIpHeader && StringUtils.isNotBlank(serverIpHeader.getValue())) {
                serverIp = serverIpHeader.getValue();
            }
        }
        event.setServerIp(StringUtils.defaultString(serverIp, ""));
        event.setElapsedMillis(elapsedTime);
        event.setRetry(retryCount > 0);
    }

    private static String getRequestId(HttpUriRequest request) {
        return request.getFirstHeader(Headers.YOP_REQUEST_ID).getValue();
    }

    private static String getAppKey(HttpUriRequest request) {
        return request.getFirstHeader(Headers.YOP_APP_KEY).getValue();
    }

    protected static YopResponse parseResponse(CloseableHttpResponse httpResponse, ResponseConfig responseConfig) throws IOException {
        return parseResponse(YopRequestType.WEB, httpResponse, responseConfig);
    }

    protected static YopResponse parseResponse(YopRequestType requestType, CloseableHttpResponse httpResponse, ResponseConfig responseConfig) throws IOException {
        YopHttpResponse response = new YopHttpResponse(httpResponse);
        Header yopViaHeader = httpResponse.getFirstHeader(Headers.YOP_VIA);
        if (yopViaHeader != null && StringUtils.equals(yopViaHeader.getValue(), YopConstants.SANDBOX_GATEWAY_VIA)) {
            LOGGER.info("response from sandbox-gateway");
        }
        int statusCode = response.getStatusCode();
        if (statusCode / 100 == HttpStatus.SC_OK / 100 && statusCode != HttpStatus.SC_NO_CONTENT) {
            //not a error
            YopResponse result = new YopResponse();
            handleHeaders(result, httpResponse);
            result.setState("SUCCESS");
            result.setRequestId(response.getHeader(Headers.YOP_REQUEST_ID));
            if (response.getContent() != null) {
                if (isJsonResponse(httpResponse)) {
                    String content = IOUtils.toString(response.getContent(), YopConstants.ENCODING);
                    if (responseConfig != ResponseConfig.NONE_OPERATION_CONFIG) {
                        verifySignature(content, response.getHeader(Headers.YOP_SIGN), responseConfig.getYopPublicKey());
                        content = decryptResponse(content, responseConfig);
                    }
                    JacksonJsonMarshaller.load(content, result);
                    if (result.getStringResult() != null) {
                        result.setResult(JacksonJsonMarshaller.unmarshal(result.getStringResult(), Object.class));
                    }
                } else if (YopRequestType.FILE_DOWNLOAD.equals(requestType) || isDownloadResponse(httpResponse)) {
                    result.setResult(new DownloadInputStream(response.getContent(), httpResponse));
                } else {
                    throw new YopHttpException("Response Error, contentType:" + httpResponse.getEntity().getContentType());
                }
            }
            return result;
        } else if (statusCode >= HttpStatus.SC_INTERNAL_SERVER_ERROR && statusCode != HttpStatus.SC_BAD_GATEWAY) {
            if (response.getContent() != null) {
                String content = IOUtils.toString(response.getContent(), YopConstants.ENCODING);
                if (responseConfig != ResponseConfig.NONE_OPERATION_CONFIG) {
                    verifySignature(content, response.getHeader(Headers.YOP_SIGN), responseConfig.getYopPublicKey());
                    content = decryptResponse(content, responseConfig);
                }
                YopResponse result = new YopResponse();
                handleHeaders(result, httpResponse);
                result.setState("FAILURE");
                YopErrorResponse errorResponse = JacksonJsonMarshaller.unmarshal(content, YopErrorResponse.class);
                result.setRequestId(errorResponse.getRequestId());
                result.setError(YopError.Builder.anYopError()
                        .withCode(errorResponse.getCode())
                        .withSubCode(errorResponse.getSubCode())
                        .withMessage(errorResponse.getMessage())
                        .withSubMessage(errorResponse.getSubMessage())
                        .withDocUrl(errorResponse.getDocUrl())
                        .build());
                return result;
            } else {
                throw new YopHttpException("ResponseError, Empty Content, httpStatusCode:" + response.getStatusCode());
            }
        } else if (statusCode == HttpStatus.SC_BAD_GATEWAY || statusCode == HttpStatus.SC_NOT_FOUND) {
            throw new YopHttpException("Response Error, statusCode:" + statusCode);
        }
        final YopServiceException invokeEx = new YopServiceException("ReqParam Illegal, Bad Request, statusCode:" + statusCode);
        invokeEx.setErrorType(YopServiceException.ErrorType.Client);
        throw invokeEx;
    }

    private static String decryptResponse(String content, ResponseConfig response) {
        //只有需要解密而且网关确实返回密文的情况下才解密（某些情况下即使请求加密，网关也无法正常对结果加密）
        if (BooleanUtils.isTrue(response.isNeedDecrypt()) && !StringUtils.startsWith(content, CharacterConstants.LEFT_BRACE)) {
            return AESEncrypter.decrypt(content, response.getDecryptKey());
        }
        return content;
    }

    private static void verifySignature(String content, String signature, PublicKey yopPublicKey) {
        if (StringUtils.isEmpty(signature)) {
            return;
        }
        //本版本sdk摘要算法只有sha256
        content = content.replaceAll("[ \t\n]", EMPTY);
        if (!RSA.verifySign(content, signature, yopPublicKey, DigestAlgEnum.SHA256)) {
            throw new VerifySignFailedException("response sign verify failure");
        }
    }

    private static boolean isJsonResponse(CloseableHttpResponse response) {
        return null != response.getEntity() && StringUtils.startsWith(response.getEntity().getContentType().getValue(), CONTENT_TYPE_JSON);
    }

    private static boolean isDownloadResponse(CloseableHttpResponse response) {
        return null != response.getEntity() && StringUtils.startsWith(response.getEntity().getContentType().getValue(), CONTENT_TYPE_STREAM);
    }

    /**
     * 填充header
     *
     * @param yopResponse 业务response
     * @param response    httpResponse
     */
    private static void handleHeaders(YopResponse yopResponse, CloseableHttpResponse response) {
        HeaderIterator headerIterator = response.headerIterator();
        while (headerIterator.hasNext()) {
            Header header = headerIterator.nextHeader();
            if (StringUtils.startsWith(header.getName(), Headers.YOP_PREFIX)) {
                yopResponse.addHeader(header.getName(), header.getValue());
            }
            if (StringUtils.equals(header.getName(), Headers.ETAG)) {
                yopResponse.addHeader(header.getName(), header.getValue());
            }
        }
    }

    protected static void checkFileIntegrity(YopResponse response, String crc64) {
        if (response.isSuccess()) {
            String responseCrc64 = response.getHeaders().get(Headers.YOP_HASH_CRC64ECMA);
            if (null == responseCrc64 || StringUtils.equals(responseCrc64, crc64)) {
                return;
            }
            response.setState("FAILURE");
            response.setError(getFileCheckError());
        }
    }

    public static YopError getFileCheckError() {
        return FILE_CHECK_ERROR;
    }

    public static YopError getFileUploadError() {
        return FILE_UPLOAD_ERROR;
    }

    /**
     * Creates HttpClient Context object based on the internal request.
     *
     * @return HttpClient Context object.
     */
    private static HttpClientContext createHttpContext() {
        HttpClientContext context = HttpClientContext.create();
        context.setRequestConfig(requestConfigBuilder.build());
        if (credentialsProvider != null) {
            context.setCredentialsProvider(credentialsProvider);
        }
        /*if (config.isProxyPreemptiveAuthenticationEnabled()) {
            AuthCache authCache = new BasicAuthCache();
            authCache.put(proxyHttpHost, new BasicScheme());
            context.setAuthCache(authCache);
        }*/
        return context;
    }

    public static CloseableHttpClient getHttpClient() {
        return httpClient;
    }

    protected static String richRequest(String methodOrUri, YopRequest request) {
        return routeRequest(methodOrUri, request) + methodOrUri;
    }

    protected static String richRequest(String serverRoot, String apiUri) {
        return serverRoot + apiUri;
    }

    protected static String routeRequest(String methodOrUri, YopRequest request) {
        return GATE_WAY_ROUTER.route(methodOrUri, request, Collections.emptyList()).getResource().toString();
    }

    protected static UriResource routeRequest(String methodOrUri, YopRequest request, List<URI> excludeServerRoots) {
        return GATE_WAY_ROUTER.route(methodOrUri, request, excludeServerRoots);
    }

    protected static String getUUID() {
        // 与高版本统一
        return UUID.randomUUID().toString();
    }

    protected static YopResponse handleRequest(String apiUri, YopRequest request, HttpMethodName method, YopRequestType requestType) throws IOException {
        return handleRequestWithRetry(apiUri, request, method, requestType, YopSecurityType.RSA2048);
    }

    protected static YopResponse handleRequestWithRetry(String apiUri, YopRequest request, HttpMethodName method,
                                                        YopRequestType requestType, YopSecurityType securityType) throws IOException {
        final long start = System.currentTimeMillis();
        List<URI> excludeServerRoots = Lists.newArrayList();
        UriResource lastServerRoot = null;
        Throwable currentEx;
        int retryCount = 0;
        boolean needRetry;
        do {
            try {
                lastServerRoot = routeRequest(apiUri, request, excludeServerRoots);
                final YopResponse result = handleRequestWithDegrade(lastServerRoot, apiUri,
                        request, method, requestType, securityType, retryCount);
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("Success ServerRoot, {}, elapsed:{}, retryCount:{}", lastServerRoot,
                            System.currentTimeMillis() - start, retryCount);
                }
                return result;
            } catch (Throwable throwable) {
                currentEx = throwable;
                // 路由异常，客户端配置问题
                if (null == lastServerRoot || null == lastServerRoot.getResource()) {
                    throw new YopClientException("Config Error, No ServerRoot Found");
                }

                // 客户端异常、业务异常，直接抛给上层
                if (throwable instanceof YopClientException) {
                    throw (YopClientException) throwable;
                }

                // 可重试异常
                if (throwable instanceof YopHostException) {
                    needRetry = true;
                    excludeServerRoots.add(lastServerRoot.getResource());
                    // 熔断异常
                    if (!(throwable instanceof YopBlockException)) {
                        retryCount++;
                    }
                } else {
                    needRetry = false;
                }

                if (LOGGER.isDebugEnabled()) {
                    LOGGER.debug("Fail ServerRoot, {}, exDetail:{}, elapsed:{}, needRetry:{}", lastServerRoot,
                            ExceptionUtils.getMessage(currentEx), System.currentTimeMillis() - start, needRetry);
                }
            }
        } while (needRetry);

        // 非预期异常处理
        throw handleUnExpectedError(currentEx);
    }

    private static YopResponse handleRequestWithDegrade(UriResource lastServerRoot, String apiUri, YopRequest request,
                                                        HttpMethodName method, YopRequestType requestType, YopSecurityType securityType,
                                                        int retryCount) {

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Trying ServerRoot, value:{}", lastServerRoot);
        }
        Entry entry = null;
        Throwable degradeError = null;
        try {
            final String resource = lastServerRoot.computeResourceKey();
            YopDegradeRuleHelper.addDegradeRule(resource, InternalConfig.getCircuitBreakerConfig());
            entry = YopSph.getInstance().entry(resource);
            return doHandleRequest(lastServerRoot.getResource().toString(), apiUri, request, method,
                    requestType, securityType, retryCount);
        } catch (YopClientException clientError) {//客户端异常&业务异常
            throw clientError;
        } catch (YopHostException | YopUnknownException serverError) {//域名异常&未知异常
            degradeError = serverError;
            throw serverError;
        } catch (Exception ex) {//熔断异常&其他未知异常
            if (BlockException.isBlockException(ex)) {
                throw new YopHostBlockException("ServerRoot Blocked, ex:", ex);
            } else {
                degradeError = ex;
                throw handleUnExpectedError(ex);
            }
        } finally {
            if (null != entry) {
                if (null != degradeError) {
                    Tracer.trace(degradeError);
                }
                entry.exit();
            }
        }
    }

    private static YopResponse doHandleRequest(String serverRoot, String apiUri, YopRequest request,
                                               HttpMethodName method, YopRequestType requestType, YopSecurityType securityType,
                                               int retryCount) {
        final long start = System.currentTimeMillis();
        try {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Sending ServerRoot, value:{}", serverRoot);
            }
            String contentUrl = richRequest(serverRoot, apiUri);
            final Pair<HttpUriRequest, List<CheckedInputStream>> httpRequest = buildHttpRequest(request, contentUrl, requestType, method);

            final ResponseConfig responseConfig = YopSecurityType.RSA2048.equals(securityType) ? new ResponseConfig()
                    .withNeedEncrypt(request.isNeedEncrypt())
                    .withEncryptKey(request.getEncryptKey())
                    .withYopPublicKey(InternalConfig.getYopPublicKey(CertTypeEnum.RSA2048)) : ResponseConfig.NONE_OPERATION_CONFIG;
            YopResponse response = fetchContentByApacheHttpClient(apiUri, httpRequest.getLeft(), responseConfig, retryCount);
            handleResult(response);
            if (httpRequest.getRight() != null) {
                checkFileIntegrity(response, CRC64Utils.getCRC64(httpRequest.getRight()));
            }
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Success ServerRoot, {}, elapsed:{}", serverRoot, System.currentTimeMillis() - start);
            }
            return response;
        } catch (YopClientException clientError) {//客户端异常&业务异常
            throw clientError;
        } catch (YopHttpException serverEx) {// 调用YOP异常
            final AnalyzeException analyzedEx = AnalyzeException.analyze(serverEx);
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug("Fail ServerRoot, {}, exDetail:{}", serverRoot, analyzedEx.getExDetail());
            }
            if (analyzedEx.isNeedRetry()) {
                //发生可重试异常且还有重试次数
                if (retryCount < InternalConfig.getMaxRetryCount()) {
                    throw new YopHostException("Need Change Host, ex:", serverEx);
                } else {
                    throw new YopUnknownException("last retry failure, ", serverEx);
                }
            }
            if (analyzedEx.isServerError()) {
                throw handleUnExpectedError(serverEx);
            }
            throw new YopClientException("Client Error, ex:", serverEx);
        } catch (Exception ex) {//未知异常
            throw handleUnExpectedError(ex);
        }
    }

    private static RuntimeException handleUnExpectedError(Throwable ex) {
        if (ex instanceof YopUnknownException) {
            return (YopUnknownException) ex;
        }
        return new YopUnknownException("UnExpected Error, ", ex);
    }

    protected static void handleResult(YopResponse response) {
        String stringResult = response.getStringResult();
        if (StringUtils.isNotBlank(stringResult)) {
            response.setResult(JacksonJsonMarshaller.unmarshal(stringResult, Object.class));
        }
    }

    private static class AnalyzeException {

        private boolean needRetry;
        private boolean serverError = true;

        private String exDetail;

        public boolean isNeedRetry() {
            return needRetry;
        }

        public void setNeedRetry(boolean needRetry) {
            this.needRetry = needRetry;
        }

        public boolean isServerError() {
            return serverError;
        }

        public void setServerError(boolean serverError) {
            this.serverError = serverError;
        }

        public String getExDetail() {
            return exDetail;
        }

        public static AnalyzeException analyze(Throwable e) {
            final AnalyzeException result = new AnalyzeException();
            final Throwable[] allExceptions = ExceptionUtils.getThrowables(e);

            if (allExceptions.length == 1) {
                result.exDetail = e.getClass().getCanonicalName() + COLON + StringUtils.defaultString(e.getMessage());
                return result;
            }

            // 当笔重试 (域名异常)
            final List<String> exceptionDetails = Lists.newArrayList();
            for (int i = 0; i < allExceptions.length; i++) {
                Throwable rootCause = allExceptions[i];
                final String exType = rootCause.getClass().getCanonicalName(),
                        exTypeAndMsg = exType + COLON + StringUtils.defaultString(rootCause.getMessage());
                exceptionDetails.add(exType);
                exceptionDetails.add(exTypeAndMsg);
                if (InternalConfig.getRetryExceptions().contains(exType) ||
                        InternalConfig.getRetryExceptions().contains(exTypeAndMsg)) {
                    result.exDetail = exTypeAndMsg;
                    result.setNeedRetry(true);
                    return result;
                }
            }

            Throwable lastCause = allExceptions[allExceptions.length -1];
            result.exDetail = lastCause.getClass().getCanonicalName() + COLON + StringUtils.defaultString(lastCause.getMessage());

            // 不重试，不计入短路
            if (CollectionUtils.containsAny(InternalConfig.getCircuitBreakerConfig().getExcludeExceptions(), exceptionDetails)) {
                result.setServerError(false);
                return result;
            }

            // 其他异常，计入短路
            return result;
        }
    }

    public enum YopRequestType {
        WEB,
        FILE_DOWNLOAD,
        MULTI_FILE_UPLOAD,
    }

    public enum YopSecurityType {
        AES,
        RSA2048
    }

}
