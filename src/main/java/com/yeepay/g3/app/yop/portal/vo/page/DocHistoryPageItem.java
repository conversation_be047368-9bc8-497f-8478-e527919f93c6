package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.doc.dto.v2.DocContentBlock;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocPageOperTypeEnum;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-29 15:24
 */
public class DocHistoryPageItem implements Serializable {
    private static final long serialVersionUID = -1L;

    private Long id;
    private String docNo;
    private String pageNo;
    private String parentPageNo;
    private Long pageId;
    private Long pagePid;
    private String title;
    private DocPageOperTypeEnum operType;
    private List<DocContentBlock> blocks;
    private String operator;
    private Long version;
    private Date createdDate;
    private String docTitle;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    public String getPageNo() {
        return pageNo;
    }

    public void setPageNo(String pageNo) {
        this.pageNo = pageNo;
    }

    public Long getPageId() {
        return pageId;
    }

    public DocHistoryPageItem setPageId(Long pageId) {
        this.pageId = pageId;
        return this;
    }

    public Long getPagePid() {
        return pagePid;
    }

    public DocHistoryPageItem setPagePid(Long pagePid) {
        this.pagePid = pagePid;
        return this;
    }

    public String getParentPageNo() {
        return parentPageNo;
    }

    public void setParentPageNo(String parentPageNo) {
        this.parentPageNo = parentPageNo;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public DocPageOperTypeEnum getOperType() {
        return operType;
    }

    public void setOperType(DocPageOperTypeEnum operType) {
        this.operType = operType;
    }

    public List<DocContentBlock> getBlocks() {
        return blocks;
    }

    public void setBlocks(List<DocContentBlock> blocks) {
        this.blocks = blocks;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getDocTitle() {
        return docTitle;
    }

    public void setDocTitle(String docTitle) {
        this.docTitle = docTitle;
    }
}
