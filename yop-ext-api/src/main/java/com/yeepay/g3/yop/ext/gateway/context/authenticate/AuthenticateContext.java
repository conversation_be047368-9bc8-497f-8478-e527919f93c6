package com.yeepay.g3.yop.ext.gateway.context.authenticate;

import com.yeepay.g3.facade.yop.sys.dto.SecurityReqDTO;
import com.yeepay.g3.yop.ext.gateway.context.encrypt.EncryptContext;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateProtocolVersion;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateStrategyEnum;

/**
 * title: 认证上下文<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 16/11/28 上午10:52
 */
public interface AuthenticateContext {

    /**
     * 认证策略
     *
     * @return 认证策略
     */
    AuthenticateStrategyEnum getAuthenticateStrategy();

    /**
     * 认证策略（协议）版本
     *
     * @return 协议版本
     */
    AuthenticateProtocolVersion getAuthenticateProtocolVersion();

    /**
     * 根据appKey,protocolPrefix,protocolContent初始化自身
     * appKey可能不会传递，视认证策略而定，有的策略需要从protocolContent中还原出appKey
     *
     * @param protocolPrefix  协议前缀
     * @param protocolContent 协议内容
     */
    void initialize(String protocolPrefix, String protocolContent);

    /**
     * 初始化加密上下文
     *
     * @param encryptType 加密类型
     * @param cipherKey   加密密钥
     */
    void initEncryptContext(String encryptType, String cipherKey);

    /**
     * 获取加密上下文
     *
     * @return 加密上下文
     */
    EncryptContext getEncryptContext();

    /**
     * 设置当前匹配的安全需求
     *
     * @param securityReq 安全需求
     */
    void setSecurityReq(SecurityReqDTO securityReq);

    /**
     * 获取当匹配的安全需求
     *
     * @return 安全需求
     */
    SecurityReqDTO getSecurityReq();

    String getPlatformSerialNo();

    void setPlatformSerialNo(String platformSerialNo);
}
