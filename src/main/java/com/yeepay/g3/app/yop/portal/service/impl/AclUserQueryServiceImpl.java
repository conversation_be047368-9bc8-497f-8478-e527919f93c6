/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.AclUserQueryService;
import com.yeepay.g3.app.yop.portal.utils.PageUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.page.AclUserPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.AclUserPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/10/25 16:31
 */
@Component
public class AclUserQueryServiceImpl implements AclUserQueryService {

    @Resource(name = "aclUserQueryService")
    private QueryService queryService;

    private PageItemConverter<AclUserPageItem> pageItemConverter = new AclUserPageItemConverter();

    @Override
    public PageQueryResult<AclUserPageItem> pageList(AclUserPageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> bizParams = getBizParams(param);
        queryParam.setParams(bizParams);
        QueryResult queryResult = queryService.query("pageList", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
    }

    @Override
    public PageQueryResult<AclUserPageItem> pageListForSp(AclUserPageQueryParam param, Set<String> spCodes) {
        if (Collections3.isEmpty(spCodes)) {
            return PageUtils.getEmptyPage(param.getPageNo());
        }
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> bizParams = getBizParams(param);
        bizParams.put("spCodes", spCodes);
        queryParam.setParams(bizParams);
        QueryResult queryResult = queryService.query("pageListForSp", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
    }

    @Override
    public PageQueryResult<AclUserPageItem> pageListForRole(AclUserPageQueryParam param, String roleCode) {
        if (StringUtils.isEmpty(roleCode)) {
            return PageUtils.getEmptyPage(param.getPageNo());
        }
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> bizParams = getBizParams(param);
        bizParams.put("roleCode", roleCode);
        queryParam.setParams(bizParams);
        QueryResult queryResult = queryService.query("pageListForRole", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
    }

    @Override
    public List<String> pageCommonListForRole(String roleCode) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("roleCode", roleCode);
        List<Map> maps = queryService.query("pageCommonListForRole", bizParams);
        List<String> operatorCodes = new ArrayList<>();
        for (Map map : maps) {
            operatorCodes.add((String) map.get("operator_code"));
        }
        return operatorCodes;
    }

    private Map<String, Object> getBizParams(AclUserPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("name", param.getName());
        bizParams.put("position", param.getPosition());
        bizParams.put("status", param.getStatus());
        return bizParams;
    }

    class AclUserPageItemConverter extends BasePageItemConverter<AclUserPageItem> {

        @Override
        public AclUserPageItem convert(Map<String, Object> params) {
            AclUserPageItem item = new AclUserPageItem();
            item.setId((Long) params.get("id"));
            item.setOptCode((String) params.get("operator_code"));
            item.setOptName((String) params.get("operator_name"));
            item.setEmail((String) params.get("operator_email"));
            item.setPosition((String) params.get("position"));
            item.setStatus((String) params.get("status"));
            item.setType((String) params.get("type"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }

}
