/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ProductQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.ProductSimpleVO;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.ProductPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ProductPageQueryParam;
import com.yeepay.g3.facade.yop.sys.enums.ProductStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.ProductTypeEnum;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.yeepay.g3.app.yop.portal.utils.Constants.NON_EXISTS_SP_CODE;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/5 下午4:06
 */
@Component
public class ProductQueryServiceImpl implements ProductQueryService {

    @Resource(name = "productQueryService")
    private QueryService queryService;

    private final PageItemConverter<ProductPageItem> pageItemConverter = new ProductPageItemConverter();

    @Override
    public PageQueryResult<ProductPageItem> pageQuery(ProductPageQueryParam param) {
        Map<String, Object> bizParams = getBizParams(param);
        List<Map> list;
        if (StringUtils.isNotBlank(param.getApiUri())) {
            list = queryService.query("pageListByApiUri", bizParams);
        } else {
            list = queryService.query("pageList", bizParams);
        }
        PageQueryResult<ProductPageItem> result = new PageQueryResult<>();
        List<ProductPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public List<ProductSimpleVO> spProducts() {
        Map<String, Object> bizParams = Maps.newHashMap();
        if (ShiroUtils.isSpOperator()) {
            final Set<String> spCodes = ShiroUtils.getShiroUser().getSpScopes();
            if (CollectionUtils.isEmpty(spCodes)) {
                bizParams.put("spCodes", Collections.singletonList(NON_EXISTS_SP_CODE));
            } else {
                bizParams.put("spCodes", spCodes);
            }
        }
        List<Map> list = queryService.query("simplelist", bizParams);
        return simpleConvert(list);
    }

    @Override
    public List<ProductSimpleVO> allNormalProducts() {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("status", ProductStatusEnum.NORMAL.name());
        List<Map> list = queryService.query("simplelist", bizParams);
        return simpleConvert(list);
    }

    private List<ProductSimpleVO> simpleConvert(List<Map> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().map(product -> {
                final ProductSimpleVO item = new ProductSimpleVO();
                item.setProductCode((String) product.get("product_code"));
                item.setProductName((String) product.get("product_name"));
                item.setProductType((String) product.get("product_type"));
                item.setSpCode((String) product.get("sp_code"));
                item.setDesc((String) product.get("product_desc"));
                return item;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private Map<String, Object> getBizParams(ProductPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("code", param.getCode());
        bizParams.put("name", param.getName());
        bizParams.put("type", param.getType());
        bizParams.put("apiUri", param.getApiUri());
        bizParams.put("spCode", param.getSpCode());
        if (ShiroUtils.isSpOperator()) {
            final Set<String> spCodes = ShiroUtils.getShiroUser().getSpScopes();
            if (CollectionUtils.isEmpty(spCodes)) {
                bizParams.put("spCodes", Collections.singletonList(NON_EXISTS_SP_CODE));
            } else {
                bizParams.put("spCodes", spCodes);
            }
        }
        bizParams.put("status", param.getStatus());
        bizParams.put("createdStartDate", param.getCreatedStartDate());
        bizParams.put("createdEndDate", param.getCreatedEndDate());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class ProductPageItemConverter extends BasePageItemConverter<ProductPageItem> {

        @Override
        public ProductPageItem convert(Map<String, Object> params) {
            ProductPageItem item = new ProductPageItem();
            item.setSpCode((String) params.get("sp_code"));
            item.setSpName((String) params.get("sp_name"));
            item.setCode((String) params.get("product_code"));
            item.setName((String) params.get("product_name"));
            item.setType(ProductTypeEnum.parse((String) params.get("product_type")));
            item.setStatus(ProductStatusEnum.parse((String) params.get("status")));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }
}
