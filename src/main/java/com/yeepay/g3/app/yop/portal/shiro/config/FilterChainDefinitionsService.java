package com.yeepay.g3.app.yop.portal.shiro.config;

import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/7/25 下午3:12
 */
public interface FilterChainDefinitionsService {

    /**
     * 读取配置资源
     */
    Map<String, String> obtainPermission();

    /**
     * 初始化框架权限资源配置
     */
    void intiPermission();

    /**
     * 重新加载框架权限资源配置 (强制线程同步)
     */
    void updatePermission();

}
