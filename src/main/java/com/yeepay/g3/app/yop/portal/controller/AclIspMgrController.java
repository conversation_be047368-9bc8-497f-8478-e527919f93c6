/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.cache.SpLocalCache;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.facade.yop.perm.dto.IspInfoDTO;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.facade.yop.perm.facade.IspMgrFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

import static com.yeepay.g3.app.yop.portal.utils.Constants.ALL_SP_INFO_KEY;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/10/26 19:45
 */
@Controller
@RequestMapping("/rest/acl/isp")
public class AclIspMgrController {

    public static final String PLATFORM_SP_CODE = "PLATFORM";

    @Autowired
    private SpLocalCache spLocalCache;

    private IspMgrFacade ispMgrFacade = RemoteServiceFactory.getService(IspMgrFacade.class);

    @ResponseBody
    @RequestMapping(value = "/has-right-but-unassigned-to-user", method = RequestMethod.GET)
    public ResponseMessage querySps(@RequestParam String optCode, @RequestParam OperatorTypeEnum optType) {
        Map<String, IspInfoDTO> spMap = spLocalCache.get(ALL_SP_INFO_KEY);
        List<IspInfoDTO> spInfoDTOS = new ArrayList<>();
        List<String> spCodesOfOpt = ispMgrFacade.findSpCodeByOperatorCode(optCode);
        Set<String> spCodes;
        if (ShiroUtils.getShiroUser().isPlatformOperator()) {
            spCodes = spMap.keySet();
        } else {
            spCodes = ShiroUtils.getShiroUser().getSpScopes();
        }
        spCodes.removeAll(spCodesOfOpt);
        filterSpCodes(optType, spCodes);
        for (String spCode : spCodes) {
            spInfoDTOS.add(spMap.get(spCode));
        }
        return new ResponseMessage("result", spInfoDTOS);
    }

    @ResponseBody
    @RequestMapping(value = "/has-right", method = RequestMethod.GET)
    public ResponseMessage querySps(@RequestParam OperatorTypeEnum optType) {
        Map<String, IspInfoDTO> spMap = spLocalCache.get(ALL_SP_INFO_KEY);
        List<IspInfoDTO> spInfoDTOS = new ArrayList<>();
        if (ShiroUtils.getShiroUser().isPlatformOperator()) {
            spInfoDTOS = new ArrayList<IspInfoDTO>(((HashMap) spMap).values());
        } else {
            Set<String> spCodes = ShiroUtils.getShiroUser().getSpScopes();
            filterSpCodes(optType, spCodes);
            for (String spCode : spCodes) {
                spInfoDTOS.add(spMap.get(spCode));
            }
        }
        return new ResponseMessage("result", spInfoDTOS);
    }

    private void filterSpCodes(OperatorTypeEnum optType, Set<String> spCodes) {
        if (OperatorTypeEnum.SP_BASED.equals(optType)) {
            Iterator<String> spCodesIt = spCodes.iterator();
            while (spCodesIt.hasNext()) {
                String spCode = spCodesIt.next();
                if (PLATFORM_SP_CODE.equals(spCode)) {
                    spCodesIt.remove();
                }
            }
        }
    }


}
