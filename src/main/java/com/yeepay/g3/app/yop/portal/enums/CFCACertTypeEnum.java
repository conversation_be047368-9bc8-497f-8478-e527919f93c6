/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/3/26 11:41 上午
 */
public enum CFCACertTypeEnum {
    NORMAL("1", "普通");
    private static final Map<String, CFCACertTypeEnum> VALUE_MAP;

    static {
        VALUE_MAP = new HashMap<>();
        for (CFCACertTypeEnum cfcaCertTypeEnum : CFCACertTypeEnum.values()) {
            VALUE_MAP.put(cfcaCertTypeEnum.getValue(), cfcaCertTypeEnum);
        }
    }

    private String value;
    private String decs;

    CFCACertTypeEnum(String value, String decs) {
        this.value = value;
        this.decs = decs;
    }

    public static CFCACertTypeEnum parse(String value) {
        return VALUE_MAP.get(value);
    }

    public String getValue() {
        return value;
    }

    public String getDecs() {
        return decs;
    }
}
