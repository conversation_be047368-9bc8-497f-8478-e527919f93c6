package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.dto.ApiImportContext;
import com.yeepay.g3.app.yop.portal.service.ApiImportCacheService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * title: Api导入缓存服务<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-31 17:26
 */
@Component
public class ApiImportCacheServiceImpl implements ApiImportCacheService {


    @Override
    @CachePut(value = "yp:ai", key = "#requestId")
    public ApiImportContext storeContext(String requestId, ApiImportContext context) {
        return context;
    }

    @Override
    @Cacheable(value = "yp:ai", key = "#requestId")
    public ApiImportContext loadContext(String requestId) {
        //do nothing
        return null;
    }

    @Override
    @CacheEvict(value = "yp:ai", key = "#requestId")
    public void evictContext(String requestId) {

    }
}
