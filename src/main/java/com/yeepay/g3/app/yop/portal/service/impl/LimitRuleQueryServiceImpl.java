/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.LimitRuleQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.page.LimitRulePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.LimitRulePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/5/13 上午10:57
 */
@Component
public class LimitRuleQueryServiceImpl implements LimitRuleQueryService {

    @Resource(name = "limitRuleQueryService")
    private QueryService queryService;

    private final PageItemConverter<LimitRulePageItem> pageItemConverter = new LimitRulePageItemConverter();

    @Override
    public PageQueryResult<LimitRulePageItem> pageQuery(LimitRulePageQueryParam param) {
        Map<String, Object> bizParams = getBizParams(param);
        List<Map> list = queryService.query("pageList", bizParams);
        PageQueryResult<LimitRulePageItem> result = new PageQueryResult<>();
        List<LimitRulePageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    private Map<String, Object> getBizParams(LimitRulePageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("customerNo", param.getCustomerNo());
        bizParams.put("appId", param.getAppId());
        bizParams.put("requestPath", param.getRequestPath());
        bizParams.put("status", param.getStatus());
        bizParams.put("type", param.getType());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class LimitRulePageItemConverter extends BasePageItemConverter<LimitRulePageItem> {

        @Override
        public LimitRulePageItem convert(Map<String, Object> params) {
            LimitRulePageItem item = new LimitRulePageItem();
            item.setId((Long) params.get("id"));
            item.setCustomerNo((String) params.get("customer_no"));
            item.setAppId((String) params.get("app_id"));
            item.setRequestMethod((String) params.get("request_method"));
            item.setRequestPath((String) params.get("request_path"));
            item.setStatus((String) params.get("status"));
            item.setVersion(((BigInteger) params.get("version")).longValue());
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }
}
