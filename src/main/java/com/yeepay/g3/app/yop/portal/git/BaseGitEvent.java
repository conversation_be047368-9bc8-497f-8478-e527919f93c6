package com.yeepay.g3.app.yop.portal.git;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * title:git事件基类 <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-18 12:03
 */
public abstract class BaseGitEvent implements Serializable {
    private static final long serialVersionUID = -1L;

    @JsonProperty("object_kind")
    private String objectKind;

    public String getObjectKind() {
        return objectKind;
    }

    public void setObjectKind(String objectKind) {
        this.objectKind = objectKind;
    }
}
