package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.IspMgrQueryService;
import com.yeepay.g3.app.yop.portal.utils.MapUtils;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.app.yop.portal.validation.group.IspCreate;
import com.yeepay.g3.app.yop.portal.vo.IspInfoVO;
import com.yeepay.g3.app.yop.portal.vo.TypeVO;
import com.yeepay.g3.app.yop.portal.vo.page.IspPageQueryParam;
import com.yeepay.g3.core.yop.utils.bean.BeanConvertUtils;
import com.yeepay.g3.facade.yop.perm.dto.IspInfoDTO;
import com.yeepay.g3.facade.yop.perm.enums.IspStatusEnum;
import com.yeepay.g3.facade.yop.perm.facade.IspMgrFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: SpMgrController
 * @Description:
 * @date 2018年5月11日 上午11:25:01
 */
@Controller
@RequestMapping("/rest/isp")
public class IspMgrController {

    private static final Logger LOGGER = LoggerFactory.getLogger(IspMgrController.class);

    @Autowired
    private IspMgrQueryService IspMgrQueryService;

    private IspMgrFacade ispMgrFacade = RemoteServiceFactory.getService(IspMgrFacade.class);

    @ResponseBody
    @RequestMapping(value = "/commons/sp-codes", method = RequestMethod.GET)
    public ResponseMessage queryAllSpCode() {
        try {
            if (ShiroUtils.isPlatformOperator()) {
                return new ResponseMessage("result", IspMgrQueryService.queryAllSpCode());
            } else {
                return new ResponseMessage("result", IspMgrQueryService.queryAllSpCodeForSp(ShiroUtils.getShiroUser().getSpScopes()));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when query all spCode", ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@RequestParam(value = "spCode", required = false) String spCode,
                                @RequestParam(value = "status", required = false) String status,
                                @RequestParam(value = "tenantCode", required = false) String tenantCode,
                                @RequestParam(value = "_pageNo", required = false) Integer _pageNo,
                                @RequestParam(value = "_pageSize", required = false) Integer _pageSize) {
        IspPageQueryParam param = IspPageQueryParam.Builder.anIspPageQueryParam()
                .withSpCode(spCode)
                .withStatus(status)
                .withPageNo(_pageNo)
                .withPageSize(_pageSize)
                .withTenantCode(tenantCode)
                .build();
        if (ShiroUtils.isPlatformOperator()) {
            return new ResponseMessage("result", IspMgrQueryService.pageList(param));
        } else {
            return new ResponseMessage("result", IspMgrQueryService.pageListForSp(param, ShiroUtils.getShiroUser().getSpScopes()));
        }
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam(value = "spCode") String spCode) {
        IspInfoDTO ispInfoDTO = ispMgrFacade.findBySpCode(spCode);
        return new ResponseMessage("result", ispInfoDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody @Validated(IspCreate.class) IspInfoVO ispInfoVO) {
        IspInfoDTO ispInfoDTO = BeanConvertUtils.convert(ispInfoVO, IspInfoDTO.class);
        //目前新建的服务方，租户code都是YEEPAY
        ispInfoDTO.setTenantCode(ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CUSTOMER_TENANT_DEFAULT, String.class));
        ispMgrFacade.create(ispInfoDTO, ispInfoVO.getManagerCodes());
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public ResponseMessage edit(@RequestBody IspInfoVO ispInfoVO) {
        IspInfoDTO ispInfoDTO = BeanConvertUtils.convert(ispInfoVO, IspInfoDTO.class);
        ispMgrFacade.update(ispInfoDTO);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam String spCode) {
        ispMgrFacade.logicDelete(spCode);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/status-enum", method = RequestMethod.GET)
    public ResponseMessage queryRoleType() {
        Map<String, String> typeMap = IspStatusEnum.getDisplayValueMap();
        typeMap.remove("FROZEN");
        List<TypeVO> roleTypes = MapUtils.mapToTypeVOs(typeMap);
        return new ResponseMessage("result", roleTypes);
    }
}
