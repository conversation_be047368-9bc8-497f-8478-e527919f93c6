package com.yeepay.g3.app.yop.portal.exception;

import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;

/**
 * title:Spi依赖缺失异常 <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-15 15:13
 */
public class SpiDependencyMissException extends YeepayRuntimeException {

    private static final long serialVersionUID = -1L;

    private String spiName;

    public SpiDependencyMissException(String spiName) {
        super("spi dependency miss, spiName:{0}.", spiName);
        this.spiName = spiName;
    }

    public String getSpiName() {
        return spiName;
    }
}
