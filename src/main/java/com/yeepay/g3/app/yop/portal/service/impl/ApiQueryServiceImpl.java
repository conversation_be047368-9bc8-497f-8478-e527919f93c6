package com.yeepay.g3.app.yop.portal.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.dto.ApiSimpleInfoDTO;
import com.yeepay.g3.app.yop.portal.enums.ApiParamFormatEnum;
import com.yeepay.g3.app.yop.portal.service.ApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.service.ApiQueryService;
import com.yeepay.g3.app.yop.portal.service.ApiRequestService;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.page.ApiDefinePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ApiDefinePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.facade.yop.api.enums.ApiStatusEnum;
import com.yeepay.g3.facade.yop.api.enums.ApiTypeEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/16 14:13
 */
@Component
public class ApiQueryServiceImpl implements ApiQueryService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiQueryServiceImpl.class);
    private static final String OLD_API = "OLD_API";

    @Resource(name = "apiQueryService")
    private QueryService queryService;

    @Autowired
    private ApiRequestService apiRequestService;

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    private final PageItemConverter<ApiDefinePageItem> pageItemConverter = new ApiDefinePageItemConverter();

    private final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();

    @Override
    public SimpleApiVO findByApiId(String apiId) {
        Map<String, Object> param = new HashMap<>();
        param.put("apiId", apiId);
        List<Map> list = queryService.query("findByApiId", param);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        SimpleApiVO simpleApiVO = new SimpleApiVO();
        simpleApiVO.setApiId(apiId);
        simpleApiVO.setApiUri((String) list.get(0).get("path"));
        simpleApiVO.setApiName((String) list.get(0).get("name"));
        simpleApiVO.setApiTitle((String) list.get(0).get("title"));
        simpleApiVO.setHttpMethod((String) list.get(0).get("http_method"));
        if (OLD_API.equals(simpleApiVO.getApiTitle())) {
            param.put("path", simpleApiVO.getApiUri());
            List<Map> old = queryService.query("findOldApi", param);
            simpleApiVO.setApiTitle((String) old.get(0).get("api_title"));
            simpleApiVO.setHttpMethod((String) old.get(0).get("http_method"));
        }
        return simpleApiVO;
    }

    @Override
    public PageQueryResult<ApiDefinePageItem> pageQuery(ApiDefinePageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        QueryResult queryResult = queryService.query("list", queryParam);
        final PageQueryResult<ApiDefinePageItem> result = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        result.setItems(fillApiIdV2(result.getItems()));
        return result;
    }

    private List<ApiDefinePageItem> fillApiIdV2(List<ApiDefinePageItem> apis) {
        if (CollectionUtils.isNotEmpty(apis)) {
            final List<String> oldApiUris = apis.stream().map(ApiDefinePageItem::getApiUri).collect(Collectors.toList());
            final Map<String, String> apiIdMap = apiRequestService.findApiIdsForOldApi(oldApiUris);
            if (MapUtils.isNotEmpty(apiIdMap)) {
                apis.forEach(api -> api.setApiIdV2(apiIdMap.get(api.getApiUri())));
            }
        }
        return apis;
    }

    @Override
    public PageQueryResult<ApiDefinePageItem> pageQueryForSp(ApiDefinePageQueryParam param, String operatorCode) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> bizParams = getBizParams(param);
        bizParams.put("operatorCode", operatorCode);
        queryParam.setParams(bizParams);
        QueryResult queryResult = queryService.query("listForSp", queryParam);
        final PageQueryResult<ApiDefinePageItem> result = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        result.setItems(fillApiIdV2(result.getItems()));
        return result;
    }

    @Override
    public List<ApiSimpleInfoDTO> queryApiForByGroupCode(String groupCode) {
        Map<String, Object> map = new HashMap<String, Object>() {
            {
                put("apiGroupCode", groupCode);
            }
        };
        List restult = queryService.query("apiList", map);
        if (CollectionUtils.isEmpty(restult)) {
            return new ArrayList<>();
        } else {
            return convert(restult);
        }
    }

    @Override
    public List<ApiParamSensitiveVO> listApiParam(ApiParamSensitiveParamVO param) {
        QueryParam queryParam = new QueryParam();
        queryParam.setParams(com.yeepay.g3.app.yop.portal.utils.MapUtils.objectToMap(param));
        List<Map<String, Object>> list = queryService.queryList("listApiParam", queryParam);
        return convertToApiParamSensitiveVO(list);
    }

    @Override
    public List<ApiParamSensitiveVO> listApiReturnParam(ApiParamSensitiveParamVO param) {
        QueryParam queryParam = new QueryParam();
        queryParam.setParams(com.yeepay.g3.app.yop.portal.utils.MapUtils.objectToMap(param));
        List<Map<String, Object>> list = queryService.queryList("listApiReturnParam", queryParam);
        return convertToApiParamSensitiveVO(list);
    }

    @Override
    public JsonNode listApiServletConfig(String BackendCode) {
        QueryParam queryParam = new QueryParam();
        Map<String, String> param = new HashMap();
        param.put("backendCode", BackendCode);
        queryParam.setParams(param);
        List<Map<String, Object>> list = queryService.queryList("bakcendApiServletConfig", queryParam);
        return convertToApiJson(list);
    }

    @Override
    public List<TypeVO> listCallback(String apiId) {
        Map<String, Object> params = new HashMap<>();
        params.put("apiId", apiId);
        List<Map> list = queryService.query("listCallback", params);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }
        List<TypeVO> result = new ArrayList<>();
        list.forEach(map -> {
            TypeVO typeVO = new TypeVO();
            typeVO.setName((String) map.get("title"));
            typeVO.setCode((String) map.get("name"));
            result.add(typeVO);
        });
        return result;
    }

    private JsonNode convertToApiJson(List<Map<String, Object>> list) {
        if (Collections3.isEmpty(list)) {
            return null;
        }

        Map<String, ApiServletConfigVO> apis = new HashMap<>();
        for (Map<String, Object> map : list) {
            String url = (String) map.get("url");
            try {
                if (apis.get(url) == null) {
                    ApiServletConfigVO apiServletConfigVO = new ApiServletConfigVO();
                    apiServletConfigVO.setEndClass((String) map.get("end_class"));
                    apiServletConfigVO.setEndMethod((String) map.get("end_method"));
                    List<Map<String, String>> params = new ArrayList<>();
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put((String) map.get("end_param_name"), (String) map.get("end_param_name"));
                    params.add(Integer.parseInt("" + map.get("end_param_index")), paramMap);
                    if (StringUtils.equals((String) map.get("param_type"), "FORM")) {
                        apiServletConfigVO.setParams(params);
                    } else {
                        apiServletConfigVO.setJson(params);
                    }
                    apis.put(url, apiServletConfigVO);
                } else {
                    ApiServletConfigVO apiServletConfigVO = apis.get(url);
                    List<Map<String, String>> params;
                    if (StringUtils.equals((String) map.get("param_type"), "FORM")) {
                        params = apiServletConfigVO.getParams();
                    } else {
                        params = apiServletConfigVO.getJson();
                    }
                    int index = Integer.parseInt("" + map.get("end_param_index"));
                    Map<String, String> paramMap;
                    if (CollectionUtils.isEmpty(params) || params.size() <= index) {
                        paramMap = new HashMap<>();
                        paramMap.put((String) map.get("end_param_name"), (String) map.get("end_param_name"));
                        params.add(Integer.parseInt("" + map.get("end_param_index")), paramMap);
                    } else {
                        paramMap = params.get(index);
                        paramMap.put((String) map.get("end_param_name"), (String) map.get("end_param_name"));
                    }
                }
            } catch (Exception e) {
                LOGGER.error("error when convertToApiJson,api is {},error:", url, e);
            }
        }
        JsonNode jsonNode = objectMapper.convertValue(apis, JsonNode.class);
        return jsonNode;
    }

    private List<ApiParamSensitiveVO> convertToApiParamSensitiveVO(List<Map<String, Object>> list) {
        if (Collections3.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<ApiParamSensitiveVO> result = new ArrayList<>(list.size());
        final Map<Long, ApiParamSensitiveVO> voMap = new HashMap<>();
        for (Map<String, Object> map : list) {
            ApiParamSensitiveVO vo = new ApiParamSensitiveVO();
            vo.setId(Long.parseLong("" + map.get("ID")));
            if (map.get("PARENT_ID") != null) {
                vo.setParentId(Long.parseLong("" + map.get("PARENT_ID")));
            }
            vo.setParamName(StringUtils.trimToEmpty((String) map.get("PARAM_NAME")));
            vo.setSensitive("1".equals("" + map.get("SENSITIVE")));
            vo.setFormat(ApiParamFormatEnum.parse((String) map.get("PARAM_DATA_FORMAT")));
            voMap.put(vo.getId(), vo);
            result.add(vo);
        }
        result.forEach(vo -> {
            vo.setParamName(formatParamName(vo, voMap));
        });
        return result;
    }

    private String formatParamName(ApiParamSensitiveVO vo, final Map<Long, ApiParamSensitiveVO> voMap) {
        String paramName = vo.getParamName();
        Long parentId = vo.getParentId();
        if (parentId == null || parentId == -1L) {
            return paramName;
        }
        ApiParamSensitiveVO parent = voMap.get(parentId);
        if (null != parent && parent.getParentId() != null && parent.getParentId() != -1L) {
            paramName = formatParamName(parent, voMap) + "." + paramName;
        }
        return paramName;
    }


    @SuppressWarnings("unchecked")
    @Override
    public List<ApiDefinePageItem> list(String[] apiGroups) {
        Map<String, Object> bizParams = Maps.newHashMap();
        if (apiGroups != null && apiGroups.length > 0) {
            bizParams.put("apiGroupCodes", apiGroups);
        }
        return pageItemConverter.convert(queryService.query("list", bizParams));
    }

    @Override
    public List<ApiDefinePageItem> listApis(String[] apiGroups) {
        Map<String, Object> params = new HashMap<>();
        params.put("groups", apiGroups);
        List<Map> list = queryService.query("listBothV1AndV2", params);
        if (CollectionUtils.isEmpty(list)) {
            Collections.emptyList();
        }
        List<ApiDefinePageItem> result = new ArrayList<>(list.size());
        list.forEach(map -> {
            ApiDefinePageItem apiDefinePageItem = new ApiDefinePageItem();
            apiDefinePageItem.setApiTitle((String) map.get("title"));
            apiDefinePageItem.setApiIdV2((String) map.get("api_id"));
            apiDefinePageItem.setApiUri((String) map.get("path"));
            result.add(apiDefinePageItem);
        });
        return result;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<ApiDefinePageItem> listForSp(String[] apiGroups) {
        List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
        if (Collections3.isEmpty(apiGroupCodes)) {
            return Collections.emptyList();
        }
        if (apiGroups != null) {
            apiGroupCodes.retainAll(Arrays.asList(apiGroups));
        }
        Map<String, Object> bizParams = Maps.newHashMap();
        if (Collections3.isNotEmpty(apiGroupCodes)) {
            bizParams.put("apiGroupCodes", apiGroupCodes);
        }
        final List<ApiDefinePageItem> result = pageItemConverter.convert(queryService.query("listForSp", bizParams));
        return fillApiIdV2(result);
    }

    private Map<String, Object> getBizParams(ApiDefinePageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiTitle", param.getApiTitle());
        bizParams.put("apiUri", param.getApiUri());
        bizParams.put("apiType", param.getApiType());
        bizParams.put("apiGroupCode", param.getApiGroupCode());
        if (CollectionUtils.isNotEmpty(param.getApiGroupCodes())) {
            bizParams.put("apiGroupCodes", param.getApiGroupCodes());
        }
        bizParams.put("status", param.getStatus());
//        bizParams.put("securityReq", param.getSecurityReq());
        return bizParams;
    }

    class ApiDefinePageItemConverter extends BasePageItemConverter<ApiDefinePageItem> {

        @Override
        public ApiDefinePageItem convert(Map<String, Object> params) {
            ApiDefinePageItem item = new ApiDefinePageItem();
            item.setApiId((Long) params.get("ID"));
            item.setApiUri((String) params.get("API_URI"));
            item.setApiTitle((String) params.get("API_TITLE"));
            item.setApiType((String) params.get("API_TYPE"));
            item.setApiTypeDesc(item.getApiType() == null ? null : ApiTypeEnum.parse(item.getApiType()).getDisplayName());
            item.setApiGroupCode((String) params.get("API_GROUP"));
            item.setApiGroupTitle((String) params.get("API_GROUP_NAME"));
            item.setCreatedDate((Date) params.get("CREATED_DATETIME"));
            item.setLastModifiedDate((Date) params.get("LAST_MODIFIED_DATETIME"));
            item.setStatus((String) params.get("STATUS"));
            item.setVersion((Long) params.get("VERSION"));
            item.setStatusDesc(item.getStatus() == null ? null : ApiStatusEnum.valueOf(item.getStatus()).getDesc());
            String tagsString = (String) params.get("TAGS");
            item.setTags(StringUtils.split(tagsString, ","));
            return item;
        }
    }

    private List<ApiSimpleInfoDTO> convert(List results) {
        List<ApiSimpleInfoDTO> apis = new ArrayList<>(results.size());
        for (Object result : results) {
            Map resultMap = (Map) result;
            ApiSimpleInfoDTO apiSimpleInfo = new ApiSimpleInfoDTO();
            apiSimpleInfo.setApiUri((String) resultMap.get("api_uri"));
            apiSimpleInfo.setDescription((String) resultMap.get("api_title"));
            apis.add(apiSimpleInfo);
        }
        return apis;
    }
}
