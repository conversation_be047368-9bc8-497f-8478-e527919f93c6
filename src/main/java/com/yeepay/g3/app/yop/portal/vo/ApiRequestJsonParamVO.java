package com.yeepay.g3.app.yop.portal.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.ArrayList;
import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 11:32
 */
public class ApiRequestJsonParamVO extends AbstractApiRequestParamVO {

    private static final long serialVersionUID = -1963395706717793278L;

    private String dtoClassName;

    private List<ApiRequestJsonParamVO> children;

    public String getDtoClassName() {
        return dtoClassName;
    }

    public void setDtoClassName(String dtoClassName) {
        this.dtoClassName = dtoClassName;
    }

    public List<ApiRequestJsonParamVO> getChildren() {
        return children;
    }

    public void setChildren(List<ApiRequestJsonParamVO> children) {
        this.children = children;
    }

    public void addChild(ApiRequestJsonParamVO child) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
