/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.regression.sdk.model;

import com.yeepay.g3.core.yop.sdk.sample.http.HttpMethodName;
import com.yeepay.g3.core.yop.sdk.sample.model.BaseRequest;

import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/19 下午4:31
 */
public class RegressionTestRequest extends BaseRequest {

    private static final long serialVersionUID = -1L;

    private String serviceName;

    /**
     * apiUri
     */
    private String resourcePath;

    private HttpMethodName HttpMethod;

    private ParamTypeEnum paramTypeEnum;

    /**
     * 业务参数
     */
    private Map<String, List<String>> formParams;

    private String jsonParams;

    private String signAlg;

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getResourcePath() {
        return resourcePath;
    }

    public void setResourcePath(String resourcePath) {
        this.resourcePath = resourcePath;
    }

    public HttpMethodName getHttpMethod() {
        return HttpMethod;
    }

    public void setHttpMethod(HttpMethodName httpMethod) {
        HttpMethod = httpMethod;
    }

    public ParamTypeEnum getParamTypeEnum() {
        return paramTypeEnum;
    }

    public void setParamTypeEnum(ParamTypeEnum paramTypeEnum) {
        this.paramTypeEnum = paramTypeEnum;
    }

    public Map<String, List<String>> getFormParams() {
        return formParams;
    }

    public void setFormParams(Map<String, List<String>> formParams) {
        this.formParams = formParams;
    }

    public String getJsonParams() {
        return jsonParams;
    }

    public void setJsonParams(String jsonParams) {
        this.jsonParams = jsonParams;
    }

    public String getSignAlg() {
        return signAlg;
    }

    public void setSignAlg(String signAlg) {
        this.signAlg = signAlg;
    }

    @Override
    public String getOperationId() {
        return "";
    }
}
