package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.StatisticInvokeTimeApiGroupItem;
import com.yeepay.g3.app.yop.portal.vo.page.StatisticInvokeTimesApiGroupQueryParam;

/**
 * @Title: StatisticInvokeTimesApiGroupQueryService
 * @Description: 
 * <AUTHOR>
 * @date 2018年5月10日 下午2:43:02
 * @version V1.0
 */
public interface StatisticInvokeTimesApiGroupQueryService {

    PageQueryResult<StatisticInvokeTimeApiGroupItem> pageQuery(StatisticInvokeTimesApiGroupQueryParam param);
    
}
