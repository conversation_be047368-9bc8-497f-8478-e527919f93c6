/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.blacklist;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/6/15 9:37 下午
 */
public class HandleStrategyFactory {
    private static Map<String, HandleStrategy> map = new HashMap<>();

    public static void register(HandleStrategy handleStrategy) {
        map.put(handleStrategy.name(), handleStrategy);
    }

    public static HandleStrategy get(String strategyName) {
        return map.get(strategyName);
    }
}
