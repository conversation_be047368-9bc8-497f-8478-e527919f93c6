package com.yeepay.g3.app.yop.portal.shiro.authc.filter;

import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.app.yop.portal.shiro.authc.OAuth2Token;
import com.yeepay.g3.app.yop.portal.shiro.utils.ShiroFilterUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.subject.support.DefaultSubjectContext;
import org.apache.shiro.web.filter.authc.AuthenticatingFilter;
import org.apache.shiro.web.servlet.ShiroHttpServletRequest;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * title: 基于 OAuth2 Token 的授权拦截器<br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/5/31 下午2:51
 */
@Component
public class OAuth2TokenAuthenticationFilter extends AuthenticatingFilter implements CustomShiroFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(OAuth2TokenAuthenticationFilter.class);

    private static final String AUTHORIZATION_HEADER = "authorization";
    private static final String AUTHORIZATION_SEPARATOR = "Bearer ";

    @Override
    public boolean onPreHandle(ServletRequest request, ServletResponse response, Object mappedValue) throws Exception {
        request.setAttribute(DefaultSubjectContext.SESSION_CREATION_ENABLED, Boolean.FALSE);
        return super.onPreHandle(request, response, mappedValue);
    }

    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) {
        String authorizationHeader = getAuthzHeader(request);
        if (StringUtils.isEmpty(authorizationHeader)) {
            // Create an empty authentication token since there is no
            // Authorization header.
            return new OAuth2Token("");
        }

        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Attempting to execute login with headers [{}]", authorizationHeader);
        }

        String accessToken = getPrincipalsAndCredentials(authorizationHeader);
        return new OAuth2Token(accessToken);
    }

    protected String getAuthzHeader(ServletRequest request) {
        return ((ShiroHttpServletRequest) request).getHeader(AUTHORIZATION_HEADER);
    }

    protected String getPrincipalsAndCredentials(String authorizationHeader) {
        if (authorizationHeader == null) {
            return null;
        }
        String[] authTokens = authorizationHeader.split(" ");
        if (null == authTokens || authTokens.length < 2) {
            return null;
        }
        return getPrincipalsAndCredentials(authTokens[0], authTokens[1]);
    }

    protected String getPrincipalsAndCredentials(String scheme, String encoded) {
        return encoded;
    }

    @Override
    protected boolean onLoginSuccess(AuthenticationToken token, Subject subject, ServletRequest request, ServletResponse response) throws Exception {
        // TODO 单纯为了初始化授权信息。。。
        subject.isPermitted("*");
        return super.onLoginSuccess(token, subject, request, response);
    }

    @Override
    protected boolean onLoginFailure(AuthenticationToken token, AuthenticationException e, ServletRequest request, ServletResponse response) {
        HttpServletResponse res = WebUtils.toHttp(response);
        try {
            res.setContentType("application/json");
            res.setCharacterEncoding("UTF-8");
            LOGGER.error("login fail, detail:{}", e.getMessage());
            if (ShiroFilterUtils.isAjax(request)) {
                res.sendError(HttpServletResponse.SC_UNAUTHORIZED);
            } else {
                PrintWriter out = response.getWriter();
                out.println("{\"status\":\"error\",\"message\":\"请输入正确的令牌\",\"detail\":\"" + e.getMessage() + "\"}");
                out.flush();
                out.close();
            }
        } catch (IOException e1) {
            LOGGER.error(" oauth onLoginFailure exception", e1);
        }

        return super.onLoginFailure(token, e, request, res);
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        return executeLogin(request, response);
    }

    @Override
    public String shiroName() {
        return "oauth2";
    }
}
