package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.doc.enums.v2.DocStatusEnum;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocVisableEnum;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-29 10:16
 */
@Data
public class DocPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    @Size(min = 1, max = 20)
    private String productCode;

    @Size(min = 1, max = 32)
    private String docNo;

    @Size(min = 1, max = 64)
    private String title;

    private DocVisableEnum visible;

    private DocStatusEnum status;

    private List<String> docNos;

    /**
     * 根据sp过滤
     */
    private String spCode;
}
