/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yeepay.g3.facade.yop.perm.dto.AclRoleAuthDTO;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/10/26 20:18
 */
public class AclIspRoleAuthVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private String spCode;

    private String spName;

    private Boolean modity;

    @JsonProperty("roles")
    private List<AclRoleAuthDTO> aclRoleAuthDTOS;

    public String getSpCode() {
        return this.spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getSpName() {
        return this.spName;
    }

    public void setSpName(String spName) {
        this.spName = spName;
    }

    public Boolean getModity() {
        return this.modity;
    }

    public void setModity(Boolean modity) {
        this.modity = modity;
    }

    public List<AclRoleAuthDTO> getAclRoleAuthDTOS() {
        return aclRoleAuthDTOS;
    }

    public void setAclRoleAuthDTOS(List<AclRoleAuthDTO> aclRoleAuthDTOS) {
        this.aclRoleAuthDTOS = aclRoleAuthDTOS;
    }
}
