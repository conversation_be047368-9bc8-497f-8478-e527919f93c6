package com.yeepay.g3.app.yop.portal.controller.support.imports;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * title: Spi导入结果<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 18:41
 */
public class SpisImportResult implements Serializable {

    private static final long serialVersionUID = -1L;

    private Integer total;

    private Integer success;

    private Integer failed;

    private List<SpiImportFailedInfo> failedDetails;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getSuccess() {
        return success;
    }

    public void setSuccess(Integer success) {
        this.success = success;
    }

    public void increaseSuccess() {
        if (this.success == null) {
            this.success = 1;
        } else {
            this.success = this.success + 1;
        }
    }

    public Integer getFailed() {
        return failed;
    }

    public void setFailed(Integer failed) {
        this.failed = failed;
    }

    public void increaseFailed() {
        if (this.failed == null) {
            this.failed = 1;
        } else {
            this.failed = this.failed + 1;
        }
    }

    public List<SpiImportFailedInfo> getFailedDetails() {
        return failedDetails;
    }

    public void setFailedDetails(List<SpiImportFailedInfo> failedDetails) {
        this.failedDetails = failedDetails;
    }

    public void addFailedDetail(SpiImportFailedInfo failedInfo) {
        increaseFailed();
        if (this.failedDetails == null) {
            this.failedDetails = new ArrayList<>();
        }
        this.failedDetails.add(failedInfo);
    }
}
