package com.yeepay.g3.app.yop.portal.git;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * title: Push 事件<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-18 12:02
 */
public class GitPushEvent extends BaseGitEvent {

    private static final long serialVersionUID = -1L;

    private String before;

    private String after;

    private String ref;

    @JsonProperty("checkout_sha")
    private String checkOutSha;

    @JsonProperty("user_id")
    private String usedId;

    @JsonProperty("user_name")
    private String userName;

    @JsonProperty("user_username")
    private String userUserName;

    @JsonProperty("user_email")
    private String userEmail;

    @JsonProperty("user_avatar")
    private String userAvatar;

    @JsonProperty("project_id")
    private Integer projectId;

    private GitProject project;

    private GitRepository repository;

    private List<GitCommit> commits;

    @JsonProperty("total_commits_count")
    private Integer totalCommitsCount;

    public String getBefore() {
        return before;
    }

    public void setBefore(String before) {
        this.before = before;
    }

    public String getAfter() {
        return after;
    }

    public void setAfter(String after) {
        this.after = after;
    }

    public String getRef() {
        return ref;
    }

    public void setRef(String ref) {
        this.ref = ref;
    }

    public String getCheckOutSha() {
        return checkOutSha;
    }

    public void setCheckOutSha(String checkOutSha) {
        this.checkOutSha = checkOutSha;
    }

    public String getUsedId() {
        return usedId;
    }

    public void setUsedId(String usedId) {
        this.usedId = usedId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserUserName() {
        return userUserName;
    }

    public void setUserUserName(String userUserName) {
        this.userUserName = userUserName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public Integer getProjectId() {
        return projectId;
    }

    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    public GitProject getProject() {
        return project;
    }

    public void setProject(GitProject project) {
        this.project = project;
    }

    public GitRepository getRepository() {
        return repository;
    }

    public void setRepository(GitRepository repository) {
        this.repository = repository;
    }

    public List<GitCommit> getCommits() {
        return commits;
    }

    public void setCommits(List<GitCommit> commits) {
        this.commits = commits;
    }

    public Integer getTotalCommitsCount() {
        return totalCommitsCount;
    }

    public void setTotalCommitsCount(Integer totalCommitsCount) {
        this.totalCommitsCount = totalCommitsCount;
    }
}
