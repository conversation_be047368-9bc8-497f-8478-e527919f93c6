/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.exception;

import com.yeepay.g3.boot.web.exception.BaseException;
import com.yeepay.g3.boot.web.exception.enums.IResponseEnum;

/**
 * title: 最新版异常类<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/7 11:08 上午
 */
public class YopNPortalException extends BaseException {
    // TODO 后续改代码请逐渐使用新版的异常处理方式

    public YopNPortalException(IResponseEnum responseEnum) {
        super(responseEnum);
    }

    public YopNPortalException(String code, String message) {
        super(code, message);
    }

    public YopNPortalException(IResponseEnum responseEnum, Object[] args, String message) {
        super(responseEnum, args, message);
    }

    public YopNPortalException(IResponseEnum responseEnum, Object[] args, String message, Throwable cause) {
        super(responseEnum, args, message, cause);
    }

}
