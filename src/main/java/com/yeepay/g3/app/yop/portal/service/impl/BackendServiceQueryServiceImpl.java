package com.yeepay.g3.app.yop.portal.service.impl;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.BackendServiceQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.BackendServiceVO;
import com.yeepay.g3.app.yop.portal.vo.page.BackendServicePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.BackendServicePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * title: BackendServiceQueryServiceImpl<br/>
 * description: 后端服务查询实现<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:46
 */
@Component
public class BackendServiceQueryServiceImpl implements BackendServiceQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BackendServiceQueryServiceImpl.class);

    @Resource(name = "backendServiceQueryService")
    private QueryService queryService;

    private final ObjectMapper oldObjectMapper = JsonMapper.nonDefaultObjectMapper();

    private final ObjectMapper objectMapper = JsonMapper.nonEmptyObjectMapper();

    {
        oldObjectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL, JsonTypeInfo.As.PROPERTY);
    }


    private PageItemConverter<BackendServicePageItem> pageItemConverter = new BackendServiceQueryServiceImpl.BackendServiceItemConverter();

    @Override
    public PageQueryResult<BackendServicePageItem> pageList(BackendServicePageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        QueryResult queryResult = queryService.query("pageList", queryParam);
        PageQueryResult<BackendServicePageItem> pageQueryResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        return pageQueryResult;
    }

    @Override
    public PageQueryResult<BackendServicePageItem> pageListForSp(BackendServicePageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> map = getBizParams(param);
        queryParam.setParams(map);
        QueryResult queryResult = queryService.query("pageListForSp", queryParam);
        PageQueryResult<BackendServicePageItem> pageQueryResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        return pageQueryResult;
    }

    @Override
    public List<BackendServiceVO> simpleList() {
        Map<String, Object> bizParams = Maps.newHashMap();
        if (ShiroUtils.isSpOperator()) {
            bizParams.put("spCodes", ShiroUtils.getShiroUser().getSpScopes());
        }
        List<Map<String, Object>> list = queryService.query("pageListForSp", bizParams);
        return convertBackendServiceVO(list);
    }

    @Override
    public Map<String, Object> simpleDetail(Long id) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("id", id);
        List<Map<String, Object>> list = queryService.query("simpleDetail", bizParams);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }

        return null;
    }

    @Override
    public boolean exist(String name) {
        Map<String, Object> param = new HashMap<>();
        param.put("name", name);
        List<Map<String, Object>> list = queryService.query("exist", param);
        return CollectionUtils.isNotEmpty(list);
    }

    private List<BackendServiceVO> convertBackendServiceVO(List<Map<String, Object>> list) {
        List<BackendServiceVO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return resultList;
        }
        for (Map<String, Object> map : list) {
            BackendServiceVO backendServiceVO = new BackendServiceVO();
            backendServiceVO.setName((String) map.get("name"));
            backendServiceVO.setType((String) map.get("type"));
            Object obj = map.get("properties");
            try {
                Map<String, Object> properties = getConfig(obj.toString());
                backendServiceVO.setBasePath((String) properties.get("basePath"));
                backendServiceVO.setConnectionTimeout(((Number) properties.get("connectionTimeout")).longValue());
                backendServiceVO.setReadTimeout(((Number) properties.get("readTimeout")).longValue());
            } catch (Throwable e) {
                LOGGER.error("get backend service conf error ", e);
            }
            resultList.add(backendServiceVO);
        }
        return resultList;
    }

    private Map<String, Object> getConfig(String value) {
        if (value.contains("@class")) {
            try {
                return oldObjectMapper.readValue(value, Map.class);
            } catch (IOException ex) {
                throw new YeepayRuntimeException("fail to read backend service config:" + value + " to map", ex);
            }
        }
        try {
            return objectMapper.readValue(value, Map.class);
        } catch (IOException ex) {
            throw new YeepayRuntimeException("fail to read backend service config:" + value + " to map", ex);
        }
    }

    private Map<String, Object> getBizParams(BackendServicePageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("name", param.getName());
        bizParams.put("type", param.getType());
        bizParams.put("spCode", param.getSpCode());
        return bizParams;
    }

    class BackendServiceItemConverter extends BasePageItemConverter<BackendServicePageItem> {

        @Override
        public BackendServicePageItem convert(Map<String, Object> params) {
            BackendServicePageItem item = new BackendServicePageItem();
            item.setId((Long) params.get("id"));
            item.setVersion((Long) params.get("version"));
            item.setName((String) params.get("name"));
            item.setType((String) params.get("type"));
            item.setSpCode((String) params.get("sp_code"));
            item.setSpCodeName((String) params.get("sp_name"));
            item.setCreatedDateTime((Date) params.get("created_datetime"));
            item.setLastModifiedDateTime((Date) params.get("last_modified_datetime"));
            return item;
        }
    }
}
