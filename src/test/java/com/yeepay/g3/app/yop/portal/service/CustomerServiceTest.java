/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/8/27 1:14 下午
 */
public class CustomerServiceTest extends SpringBootTests {

    private static String yeepayCustomerNo = "10040039448";
    private static String yopCustomerNo = "1595815987915711";

    @Autowired
    private CustomerService customerService;

    @Test
    public void testGetAdminEmail() {
        List<String> yeepayAdminEmails = customerService.getAdminEmail(yeepayCustomerNo);
        Assert.assertTrue(yeepayAdminEmails.size() == 1);
        List<String> yopAdminEmails = customerService.getAdminEmail(yopCustomerNo);
        Assert.assertTrue(yopAdminEmails.size() > 1);
    }
}
