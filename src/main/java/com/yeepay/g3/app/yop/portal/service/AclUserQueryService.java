package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.AclUserPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.AclUserPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.List;
import java.util.Set;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午5:11
 */
public interface AclUserQueryService {

    PageQueryResult<AclUserPageItem> pageList(AclUserPageQueryParam param);

    PageQueryResult<AclUserPageItem> pageListForSp(AclUserPageQueryParam param, Set<String> spCodes);

    PageQueryResult<AclUserPageItem> pageListForRole(AclUserPageQueryParam param, String roleCode);

    List<String> pageCommonListForRole(String roleCode);

}
