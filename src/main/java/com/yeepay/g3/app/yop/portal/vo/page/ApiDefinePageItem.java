package com.yeepay.g3.app.yop.portal.vo.page;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/16 14:31
 */
public class ApiDefinePageItem implements Serializable {

    private static final long serialVersionUID = 6147246495653729812L;

    private Long apiId;

    private String apiIdV2;

    private String apiTitle;

    private String apiUri;

    private String apiType;

    private String apiTypeDesc;

    private String apiGroupCode;

    private String apiGroupTitle;

    private List<String> apiSecurities;

    private List<String> apiGroupSecurities;

    private String status;

    private String statusDesc;

    private String[] tags;

    private Long version;

    private Date createdDate;

    private Date lastModifiedDate;

    public Long getApiId() {
        return apiId;
    }

    public void setApiId(Long apiId) {
        this.apiId = apiId;
    }

    public ApiDefinePageItem withApiId(Long apiId) {
        this.apiId = apiId;
        return this;
    }

    public String getApiIdV2() {
        return apiIdV2;
    }

    public void setApiIdV2(String apiIdV2) {
        this.apiIdV2 = apiIdV2;
    }

    public ApiDefinePageItem withApiIdV2(String apiIdV2) {
        this.apiIdV2 = apiIdV2;
        return this;
    }

    public String getApiTitle() {
        return apiTitle;
    }

    public void setApiTitle(String apiTitle) {
        this.apiTitle = apiTitle;
    }

    public ApiDefinePageItem withApiTitle(String apiTitle) {
        this.apiTitle = apiTitle;
        return this;
    }

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public ApiDefinePageItem withApiUri(String apiUri) {
        this.apiUri = apiUri;
        return this;
    }

    public String getApiType() {
        return apiType;
    }

    public void setApiType(String apiType) {
        this.apiType = apiType;
    }

    public String getApiTypeDesc() {
        return apiTypeDesc;
    }

    public void setApiTypeDesc(String apiTypeDesc) {
        this.apiTypeDesc = apiTypeDesc;
    }

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public ApiDefinePageItem withApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
        return this;
    }

    public String getApiGroupTitle() {
        return apiGroupTitle;
    }

    public void setApiGroupTitle(String apiGroupTitle) {
        this.apiGroupTitle = apiGroupTitle;
    }

    public ApiDefinePageItem withApiGroupTitle(String apiGroupTitle) {
        this.apiGroupTitle = apiGroupTitle;
        return this;
    }

    public List<String> getApiSecurities() {
        return apiSecurities;
    }

    public void setApiSecurities(List<String> apiSecurities) {
        this.apiSecurities = apiSecurities;
    }

    public ApiDefinePageItem addApiSecurity(String apiSecurity) {
        if (apiSecurities == null) {
            apiSecurities = new ArrayList<>();
        }
        apiSecurities.add(apiSecurity);
        return this;
    }

    public List<String> getApiGroupSecurities() {
        return apiGroupSecurities;
    }

    public void setApiGroupSecurities(List<String> apiGroupSecurities) {
        this.apiGroupSecurities = apiGroupSecurities;
    }

    public ApiDefinePageItem addApiGroupSecurity(String apiGroupSecurity) {
        if (apiGroupSecurities == null) {
            apiGroupSecurities = new ArrayList<>();
        }
        apiGroupSecurities.add(apiGroupSecurity);
        return this;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public ApiDefinePageItem withStatus(String status) {
        this.status = status;
        return this;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public ApiDefinePageItem withCreateDate(Date createdDate) {
        this.createdDate = createdDate;
        return this;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public ApiDefinePageItem withLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
        return this;
    }

    public String[] getTags() {
        return tags;
    }

    public void setTags(String[] tags) {
        this.tags = tags;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
