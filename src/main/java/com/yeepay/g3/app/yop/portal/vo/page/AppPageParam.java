/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.AppStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.AppTypeEnum;

import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/12 下午7:50
 */
public class AppPageParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private String appId;

    private String name;

    private String subjectNo;

    private String customerNo;

    private AppTypeEnum type;

    private AppStatusEnum status;

    private Date createdStartDate;

    private Date createdEndDate;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSubjectNo() {
        return subjectNo;
    }

    public void setSubjectNo(String subjectNo) {
        this.subjectNo = subjectNo;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public AppTypeEnum getType() {
        return type;
    }

    public void setType(AppTypeEnum type) {
        this.type = type;
    }

    public AppStatusEnum getStatus() {
        return status;
    }

    public void setStatus(AppStatusEnum status) {
        this.status = status;
    }

    public Date getCreatedStartDate() {
        return createdStartDate;
    }

    public void setCreatedStartDate(Date createdStartDate) {
        this.createdStartDate = createdStartDate;
    }

    public Date getCreatedEndDate() {
        return createdEndDate;
    }

    public void setCreatedEndDate(Date createdEndDate) {
        this.createdEndDate = createdEndDate;
    }
}
