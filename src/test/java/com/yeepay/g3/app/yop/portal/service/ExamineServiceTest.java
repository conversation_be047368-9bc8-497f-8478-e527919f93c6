/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import com.yeepay.g3.app.yop.portal.vo.ApiExamineVO;
import com.yeepay.g3.app.yop.portal.vo.ProductApiExamineVO;
import com.yeepay.g3.app.yop.portal.vo.ProductExamineVO;
import lombok.Setter;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/5/18 12:08 下午
 */
public class ExamineServiceTest extends SpringBootTests {
    @Setter(onMethod_ = @Autowired)
    private ExamineService examineService;

    @Test
    public void testProductApi() {
        examineService.productApi(buildNormalRequest(), "test.wu");
    }

    private ProductApiExamineVO buildNormalRequest() {
        ProductApiExamineVO result = new ProductApiExamineVO();
        ApiExamineVO apiExamineVO = new ApiExamineVO();
        apiExamineVO.setApiId("testApiId");
        apiExamineVO.setApiDescription("testApiDescription");
        apiExamineVO.setApiTitle("testTitle");
        apiExamineVO.setApiUri("/rest/v1.0/test/ttt");
        apiExamineVO.setRelateCallback(true);
        apiExamineVO.setRelateCallback(false);
        List<ApiExamineVO> apis = new ArrayList<>();
        apis.add(apiExamineVO);
        result.setApis(apis);
        ProductExamineVO existed = new ProductExamineVO();
        existed.setProductCode("testProduct");
        List<ProductExamineVO> existedProducts = new ArrayList<>();
        existedProducts.add(existed);
        result.setProducts(existedProducts);
        return result;
    }
}
