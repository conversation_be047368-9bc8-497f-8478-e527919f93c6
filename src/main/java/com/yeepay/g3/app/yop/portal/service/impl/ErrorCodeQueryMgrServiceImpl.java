package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ErrorCodeQueryMgrService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.page.ErrorCodeItem;
import com.yeepay.g3.app.yop.portal.vo.page.ErrorCodePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.sys.enums.ErrorCodeTypeEnum;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午3:52
 */

@Component
public class ErrorCodeQueryMgrServiceImpl implements ErrorCodeQueryMgrService {

    @Resource(name = "errorCodeMgrQueryService")
    private QueryService queryService;

    private final PageItemConverter <ErrorCodeItem> pageItemConverter = new ErrorCodePageQueryParamConverter();

    @Override
    public PageQueryResult <ErrorCodeItem> queryErrorCodes(ErrorCodePageQueryParam errorCodePageQueryParam) {
        QueryParam param = PageQueryUtils.getBaseQueryParam(errorCodePageQueryParam);
        param.setParams(getParams(errorCodePageQueryParam));
        QueryResult queryResult = queryService.query("list", param);
        return PageQueryUtils.convertResult(queryResult, errorCodePageQueryParam.getPageNo(), pageItemConverter);
    }

    class ErrorCodePageQueryParamConverter extends BasePageItemConverter <ErrorCodeItem> {

        @Override
        public ErrorCodeItem convert(Map <String, Object> params) {
            ErrorCodeItem item = new ErrorCodeItem();
            item.setId((Long) params.get("id"));
            item.setType(params.get("type") == null ? null : ErrorCodeTypeEnum.parse((String) params.get("type")));
            item.setApiGroupCode((String) params.get("api_group_code"));
            item.setApiUri((String) params.get("api_uri"));
            item.setErrorCode((String) params.get("error_code"));
            item.setSubErrorCode((String) params.get("sub_error_code"));
            item.setSubErrorMsg((String) params.get("sub_error_msg"));
            item.setCreatedDateTime((Date) params.get("created_datetime"));
            item.setLastModifiedDateTime((Date) params.get("last_modified_datetime"));
            item.setSolution((String) params.get("inner_solution"));
            item.setSpSolution((String) params.get("outer_solution"));
            return item;
        }
    }

    private Map <String, Object> getParams(ErrorCodePageQueryParam param) {
        Map <String, Object> params = Maps.newHashMap();
        params.put("type", param.getType());
        params.put("apiGroupCode", param.getApiGroupCode());
        params.put("apiGroupCodes", param.getApiGroupCodes());
        params.put("apiUri", param.getApiUri());
        params.put("errorCode", param.getErrorCode());
        params.put("subErrorCode", param.getSubErrorCode());
        params.put("pageNo", param.getPageNo());
        params.put("pageSize", param.getPageSize());
        return params;
    }
}
