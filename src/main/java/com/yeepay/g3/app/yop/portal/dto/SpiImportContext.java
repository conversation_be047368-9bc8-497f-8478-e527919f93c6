package com.yeepay.g3.app.yop.portal.dto;

import com.yeepay.g3.app.yop.portal.controller.support.imports.SpiImportItem;
import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title: Spi导入上下文<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-31 17:09
 */
public class SpiImportContext implements Serializable {

    private static final long serialVersionUID = -1L;

    private String apiGroup;

    private List<SpiImportItem> spisToCreate;

    private List<SpiImportItem> spisToOverride;

    private Map<String, List<ApiRequestKey>> callbackApiRelations;

    private Map<String, ModelDTO> models;

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    public SpiImportContext withApiGroup(String apiGroup){
        this.apiGroup = apiGroup;
        return this;
    }

    public List<SpiImportItem> getSpisToCreate() {
        return spisToCreate;
    }

    public void setSpisToCreate(List<SpiImportItem> spisToCreate) {
        this.spisToCreate = spisToCreate;
    }

    public SpiImportContext withSpisToCreate(List<SpiImportItem> spisToCreate) {
        this.spisToCreate = spisToCreate;
        return this;
    }

    public SpiImportContext addSpisToCreateItem(SpiImportItem item) {
        if (this.spisToCreate == null) {
            this.spisToCreate = new ArrayList<>();
        }
        this.spisToCreate.add(item);
        return this;
    }

    public List<SpiImportItem> getSpisToOverride() {
        return spisToOverride;
    }

    public void setSpisToOverride(List<SpiImportItem> spisToOverride) {
        this.spisToOverride = spisToOverride;
    }

    public SpiImportContext addSpisToOverrideItem(SpiImportItem item) {
        if (this.spisToOverride == null) {
            this.spisToOverride = new ArrayList<>();
        }
        this.spisToOverride.add(item);
        return this;
    }

    public Map<String, List<ApiRequestKey>> getCallbackApiRelations() {
        return callbackApiRelations;
    }

    public void setCallbackApiRelations(Map<String, List<ApiRequestKey>> callbackApiRelations) {
        this.callbackApiRelations = callbackApiRelations;
    }

    public SpiImportContext withCallbackApiRelations(Map<String, List<ApiRequestKey>> callbackApiRelations) {
        this.callbackApiRelations = callbackApiRelations;
        return this;
    }

    public Map<String, ModelDTO> getModels() {
        return models;
    }

    public void setModels(Map<String, ModelDTO> models) {
        this.models = models;
    }

    public SpiImportContext withModels(Map<String, ModelDTO> models) {
        this.models = models;
        return this;
    }
}
