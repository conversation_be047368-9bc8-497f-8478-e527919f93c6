/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.CustomerChangeQueryService;
import com.yeepay.g3.app.yop.portal.utils.PageUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.page.CustomerChangePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.CustomerChangePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.facade.yop.sys.enums.CustomerOperateTypeEnum;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/7/19 下午7:02
 */
@Component
public class CustomerChangeQueryServiceImpl implements CustomerChangeQueryService {

    @Resource(name = "customerChangeQueryService")
    private QueryService queryService;

    private final PageItemConverter<CustomerChangePageItem> pageItemConverter = new CustomerChangePageItemConverter();

    @Override
    public PageQueryResult<CustomerChangePageItem> pageQuery(CustomerChangePageQueryParam param) {
        Map<String, Object> bizParams = getBizParams(param);
        List<Map> list = queryService.query("pageList", bizParams);
        PageQueryResult<CustomerChangePageItem> result = new PageQueryResult<>();
        List<CustomerChangePageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public PageQueryResult<CustomerChangePageItem> pageQueryForSp(CustomerChangePageQueryParam param, List<String> providerCodes) {
        if (Collections3.isEmpty(providerCodes)) {
            return PageUtils.getEmptyPage(param.getPageNo());
        }
        Map<String, Object> bizParams = getBizParams(param);
        bizParams.put("providerCodes", providerCodes);
        List<Map> list = queryService.query("pageListForSp", bizParams);
        PageQueryResult<CustomerChangePageItem> result = new PageQueryResult<>();
        List<CustomerChangePageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    private Map<String, Object> getBizParams(CustomerChangePageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("customerNo", param.getCustomerNo());
        bizParams.put("type", param.getType());
        bizParams.put("operator", param.getOperator());
        bizParams.put("createdStartDate", param.getCreatedStartDate());
        bizParams.put("createdEndDate", param.getCreatedEndDate());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class CustomerChangePageItemConverter extends BasePageItemConverter<CustomerChangePageItem> {

        @Override
        public CustomerChangePageItem convert(Map<String, Object> params) {
            CustomerChangePageItem item = new CustomerChangePageItem();
            item.setCustomerNo((String) params.get("customer_no"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setType(CustomerOperateTypeEnum.parse((String) params.get("oper_type")));
            item.setOperator((String) params.get("operator"));
            item.setCause((String) params.get("oper_cause"));
            item.setDetail((String) params.get("oper_detail"));
            item.setProviderCode((String) params.get("provider_code"));
            return item;
        }
    }
}
