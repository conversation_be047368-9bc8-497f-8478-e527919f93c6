package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.app.yop.portal.shiro.utils.Constants;
import com.yeepay.g3.app.yop.portal.shiro.utils.RequestUtils;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.core.yop.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authz.AuthorizationFilter;
import org.apache.shiro.web.util.WebUtils;

import javax.servlet.Filter;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

import static org.apache.shiro.util.StringUtils.split;

/**
 * title: 基于 Sp 编码的鉴权拦截器<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/6/11 下午1:39
 */
public abstract class AbstractAuthorizationFilter extends AuthorizationFilter implements CustomShiroFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractAuthorizationFilter.class);

    private static final char PARAMNAME_PERMS_SEPARATOR = '#';

    protected static final JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();

    private static final String FILTERED_TAGS = ".FILTERED";

    private static final String TENANT_CODE = "tenantCode:";

    @Override
    protected void postHandle(ServletRequest request, ServletResponse response) throws Exception {
        super.postHandle(request, response);

        request.setAttribute(FILTERED_TAGS, true);
    }

    @Override
    protected boolean isEnabled(ServletRequest request, ServletResponse response) throws ServletException, IOException {
        // 异步请求
        Boolean afterFiltered = (Boolean) (request.getAttribute(FILTERED_TAGS));
        if (BooleanUtils.isTrue(afterFiltered)) {
            return false;
        }

        Object skip = request.getAttribute(Constants._PORTAL_SKIP_FILTER);
        if (skip != null && (Boolean) skip == true) {
            return false;
        }
        return ShiroUtils.isSpOperator();
    }

    //是否拥有权限
    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) throws Exception {
        Subject subject = getSubject(request, response);
        String requestURI = getPathWithinApplication(request);

        //condition_rule
        MapConfig config = (MapConfig) mappedValue;

        boolean isPermitted = true;
        String[] perms = config.getPerms();
        if (perms != null && perms.length > 0) {
            String[] params = getParams(request, response, config);
            String[] spCodes = getSpCodes(params);
            String[] tenantCodes = getTenantCodes(params);
            List<String> permList = new ArrayList<>();
            if (null != spCodes && spCodes.length > 0) {
                request.setAttribute(Constants._PORTAL_SP_CODE, spCodes[0]);
                for (String spCode : spCodes) {
                    for (String perm : perms) {
                        permList.add(StringUtils.upperCase(spCode) + ":" + perm);
                    }
                }
            }
            boolean spCodeIsPermitted = true;
            if (CollectionUtils.isEmpty(permList) || !subject.isPermittedAll(permList.toArray(new String[]{}))) {
                spCodeIsPermitted = false;
            }
            List<String> tenantList = new ArrayList<>();
            if (null != tenantCodes && tenantCodes.length > 0) {
                for (String tenantCode : tenantCodes) {
                    tenantList.add(TENANT_CODE + tenantCode);
                }
            }
            boolean tenantCodeIsPermitted = true;
            if (CollectionUtils.isEmpty(tenantList) || !subject.isPermittedAll(tenantList.toArray(new String[]{}))) {
                tenantCodeIsPermitted = false;
            }
            isPermitted = spCodeIsPermitted || tenantCodeIsPermitted;
        } else {
            LOGGER.warn("not config perms for authz filter named:{} for URI:{}.", this.getClass().getSimpleName(), requestURI);
            isPermitted = false;
        }
        return isPermitted;
    }

    //获取用户所属spCodes
    protected abstract String[] getSpCodes(String[] params);

    //获取用户所属tenantCodes
    protected String[] getTenantCodes(String[] params) {
        return new String[0];
    }

    protected String[] getParams(ServletRequest request, ServletResponse response, MapConfig config) {
        String[] params;
        if (StringUtils.isBlank(config.getParamName())) {
            Subject subject = getSubject(request, response);
            params = ((ShiroRealm.ShiroUser) subject.getPrincipal()).getSpScopes().toArray(new String[]{});
        } else {
            params = RequestUtils.getParamValue(request, config.getParamName());
        }
        LOGGER.debug("requestURI:{}, config:{}, params:{}", ((HttpServletRequest) request).getRequestURI(), config, params);
        return params;
    }

    @Override
    public Filter processPathConfig(String path, String config) {
        // format: paramName#perms1,perms2
        MapConfig mapConfig = new MapConfig();
        String[] values = split(config, PARAMNAME_PERMS_SEPARATOR);
        mapConfig.setParamName(values[0]);
        if (config != null) {
            mapConfig.setPerms(split(values[1]));
        }
        this.appliedPaths.put(path, mapConfig);
        return this;
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {

        Subject subject = getSubject(request, response);
        // If the subject isn't identified, redirect to login URL
        if (subject.getPrincipal() == null) {
            saveRequestAndRedirectToLogin(request, response);
        } else {
            // If subject is known but not authorized, redirect to the unauthorized URL if there is one
            // If no unauthorized URL is specified, just return an unauthorized HTTP status code
            String unauthorizedUrl = getUnauthorizedUrl();
            //SHIRO-142 - ensure that redirect _or_ error code occurs - both cannot happen due to response commit:
            if (org.apache.shiro.util.StringUtils.hasText(unauthorizedUrl)) {
                WebUtils.issueRedirect(request, response, unauthorizedUrl);
            } else {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                PrintWriter out = response.getWriter();
                out.println("{\"status\":\"error\",\"message\":\"您没有此操作的权限\"}");
                out.flush();
                out.close();
            }
        }
        return false;
    }

}
