package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.dto.spi.SpiApiModel;
import com.yeepay.g3.app.yop.portal.enums.ApiVersionEnum;
import com.yeepay.g3.app.yop.portal.exception.YopPortalException;
import com.yeepay.g3.app.yop.portal.service.SpiQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.SpiVO;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.facade.yop.sys.enums.SpiTypeEnum;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;

/**
 * title: SpiQueryServiceImpl<br/>
 * description: spi查询实现<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:46
 */
@Component
public class SpiQueryServiceImpl implements SpiQueryService {

    // 迁移的旧版api在表tbl_api中的api分组为"OLD_API",通过此标识判断api版本
    private static final String OLD_API_TAG = "OLD_API";

    @Resource(name = "spiQueryService")
    private QueryService queryService;

    private PageItemConverter<SpiPageItem> pageItemConverter = new SpiItemConverter();

    private PageItemConverter<SpiChangeRecordPageItem> changeRecordPageItemConverter = new SpiChangeRecordItemConverter();


    @Override
    public PageQueryResult<SpiPageItem> pageList(SpiPageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        QueryResult queryResult = queryService.query("pageList", queryParam);
        PageQueryResult<SpiPageItem> pageQueryResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        return pageQueryResult;
    }

    @Override
    public PageQueryResult<SpiPageItem> pageListForSp(SpiPageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> map = getBizParams(param);
        queryParam.setParams(map);
        QueryResult queryResult = queryService.query("pageListForSp", queryParam);
        PageQueryResult<SpiPageItem> pageQueryResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        return pageQueryResult;
    }

    @Override
    public List<SpiVO.SpiListVO> simpleList(String apiGroup) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiGroup", apiGroup);
        bizParams.put("spiType", SpiTypeEnum.CALLBACK.getValue());
        List<Map<String, Object>> list = queryService.query("pageList", bizParams);
        return convertSpiListVO(list);
    }

    @Override
    public List<SpiVO.SpiListVO> simpleList(List<String> spiNames) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("spiNames", spiNames);
        bizParams.put("spiType", SpiTypeEnum.CALLBACK.getValue());
        List<Map<String, Object>> list = queryService.query("pageList", bizParams);
        return convertSpiListVO(list);
    }

    @Override
    public PageQueryResult<SpiChangeRecordPageItem> changeRecordPageList(SpiChangeRecordPageQueryParam param) {
        Map<String, Object> paramMap = getBizParamsForChangeRecord(param);
        List<Map> pageList = queryService.query("changeRecordPageList", paramMap);
        PageQueryResult<SpiChangeRecordPageItem> result = new PageQueryResult<>();
        List<SpiChangeRecordPageItem> items = new ArrayList<>(CollectionUtils.size(pageList));
        if (CollectionUtils.isNotEmpty(pageList)) {
            pageList.forEach(map -> items.add(changeRecordPageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public PageQueryResult<SpiChangeRecordPageItem> changeRecordPageListForSp(SpiChangeRecordPageQueryParam param) {
        Map<String, Object> paramMap = getBizParamsForChangeRecord(param);
        List<Map> list = queryService.query("changeRecordPageListForSp", paramMap);
        PageQueryResult<SpiChangeRecordPageItem> result = new PageQueryResult<>();
        List<SpiChangeRecordPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(changeRecordPageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public List<SpiVO.SpiListVO> apiCallbacks(String apiId) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiId", apiId);
        List<Map<String, Object>> list = queryService.query("apiCallbackList", bizParams);
        return convertSpiListVO(list);
    }

    @Override
    public List<SpiApiModel> listApiContacted(SpiApiQueryParam param) {
        if (StringUtils.isEmpty(param.getSpiName())) {
            throw new YopPortalException("spiName is required");
        }
        Map<String, Object> params = genSpiApiQueryParam(param);
        List<Map<String, Object>> list = queryService.query("listApi", params);
        return convertToSpiApiModelList(list, param);
    }

    private Map<String, Object> genSpiApiQueryParam(SpiApiQueryParam spiApiQueryParam) {
        Map<String, Object> params = Maps.newHashMap();
        if (Objects.isNull(spiApiQueryParam)) {
            return params;
        }
        params.put("spiName", spiApiQueryParam.getSpiName());
        params.put("apiUri", spiApiQueryParam.getApiUri());
        params.put("apiName", spiApiQueryParam.getApiName());
        Integer pageNo = spiApiQueryParam.getPageNo() == null ? 1 : spiApiQueryParam.getPageNo();
        Integer pageSize = spiApiQueryParam.getPageSize() == null ? 10 : spiApiQueryParam.getPageSize();
        params.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        params.put("_maxSize", pageSize);
        return params;
    }

    private List<SpiApiModel> convertToSpiApiModelList(List<Map<String, Object>> list, SpiApiQueryParam spiApiQueryParam) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<SpiApiModel> result = new ArrayList<>(list.size());
        for (Map<String, Object> map : list) {
            SpiApiModel spiApiModel = new SpiApiModel();
            spiApiModel.setUri((String) map.get("path"));
            spiApiModel.setApiId((String) map.get("api_id"));
            if (OLD_API_TAG.equals(map.get("api_group"))) {
                spiApiModel.setVersion(ApiVersionEnum.V1.name());
                spiApiModel.setTitle((String) map.get("old_api_title"));
            } else {
                spiApiModel.setVersion(ApiVersionEnum.V2.name());
                spiApiModel.setTitle((String) map.get("title"));
            }
            if (Objects.isNull(spiApiQueryParam.getApiVersion()) || spiApiModel.getVersion().equals(spiApiQueryParam.getApiVersion().name())) {
                result.add(spiApiModel);
            }
        }
        return result;
    }

    private List<SpiVO.SpiListVO> convertSpiListVO(List<Map<String, Object>> list) {
        List<SpiVO.SpiListVO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return resultList;
        }
        for (Map<String, Object> map : list) {
            SpiVO.SpiListVO spiListVO = new SpiVO.SpiListVO();
            spiListVO.setName((String) map.get("name"));
            spiListVO.setTitle((String) map.get("title"));
            resultList.add(spiListVO);
        }
        return resultList;
    }

    private Map<String, Object> getBizParams(SpiPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("name", param.getName());
        bizParams.put("apiGroup", param.getApiGroup());
        if (CollectionUtils.isNotEmpty(param.getApiGroupCodes())) {
            bizParams.put("apiGroupCodes", param.getApiGroupCodes());
        }
        bizParams.put("title", param.getTitle());
        bizParams.put("spiType", param.getSpiType());
        return bizParams;
    }

    private Map<String, Object> getBizParamsForChangeRecord(SpiChangeRecordPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("name", param.getName());
        bizParams.put("opType", param.getOpType());
        bizParams.put("operatedStartDate", param.getOperatedStartDate());
        bizParams.put("operatedEndDate", param.getOperatedEndDate());
        bizParams.put("apiGroup", param.getApiGroup());
        bizParams.put("operator", param.getOperator());
        if (CollectionUtils.isNotEmpty(param.getApiGroupCodes())) {
            bizParams.put("apiGroupCodes", param.getApiGroupCodes());
        }
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class SpiItemConverter extends BasePageItemConverter<SpiPageItem> {

        @Override
        public SpiPageItem convert(Map<String, Object> params) {
            SpiPageItem item = new SpiPageItem();
            item.setId((Long) params.get("id"));
            item.setVersion((Long) params.get("version"));
            item.setName((String) params.get("name"));
            item.setStatus((String) params.get("status"));
            item.setTitle((String) params.get("title"));
            item.setApiGroup((String) params.get("api_group"));
            item.setSpiType((String) params.get("spi_type"));
            item.setDescription((String) params.get("description"));
            item.setCreatedDateTime((Date) params.get("created_datetime"));
            item.setLastModifiedDateTime((Date) params.get("last_modified_datetime"));
            return item;
        }
    }

    // 有的数据库驱动查询出来的时间类型为LocalDateTime，故在此处简单转换一下
    private Date toDate(Object date) {
        if (null == date) {
            return null;
        }
        if (date instanceof Date) {
            return (Date) date;
        }
        LocalDateTime localDateTime = (LocalDateTime) date;
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        return Date.from(zdt.toInstant());
    }

    class SpiChangeRecordItemConverter extends BasePageItemConverter<SpiChangeRecordPageItem> {

        @Override
        public SpiChangeRecordPageItem convert(Map<String, Object> params) {
            SpiChangeRecordPageItem item = new SpiChangeRecordPageItem();
            item.setId((Long) params.get("id"));
            item.setName((String) params.get("spi_name"));
            item.setCreatedDateTime(((Date) params.get("created_datetime")));
            item.setApiGroup((String) params.get("api_group"));
            item.setOperator((String) params.get("operator"));
            item.setOpType((String) params.get("op_type"));
            item.setCause((String) params.get("cause"));
            return item;
        }
    }
}
