/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.ApiRouteDeployRecordPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ApiRouteDeployRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.ApiRouteListItem;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/11/5 17:44
 */
public interface ApiRouteQueryService {

    List<ApiRouteListItem> queryForApiList(String apiId);

    PageQueryResult<ApiRouteDeployRecordPageItem> pageQueryForDeploy(ApiRouteDeployRecordQueryParam param);

}
