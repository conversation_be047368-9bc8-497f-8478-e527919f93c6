package com.yeepay.g3.app.yop.portal.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 10:42
 */
public class ApiRequestParamsVO implements Serializable {

    private static final long serialVersionUID = -5143300246210241355L;

    private List<ApiRequestFormParamVO> form;

    private List<ApiRequestJsonParamVO> json;

    public List<ApiRequestFormParamVO> getForm() {
        return form;
    }

    public void setForm(List<ApiRequestFormParamVO> form) {
        this.form = form;
    }

    public List<ApiRequestJsonParamVO> getJson() {
        return json;
    }

    public void setJson(List<ApiRequestJsonParamVO> json) {
        this.json = json;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
