package com.yeepay.g3.app.yop.portal;

import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.AclButtonVO;
import com.yeepay.g3.app.yop.portal.vo.AclMenuRefVO;
import com.yeepay.g3.facade.yop.perm.enums.ResourceStatusEnum;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2016<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/7/23 14:59
 */
public class AclResourceControllerTest {

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();

    @Test
    public void putMenuRefJson() {
        AclMenuRefVO invoke = new AclMenuRefVO();
        invoke.setStatus(ResourceStatusEnum.ENABLE);
        invoke.setResourceName("调用记录");
        invoke.setUrl("/invoke-log");

        AclMenuRefVO invokeLog = new AclMenuRefVO();
        invokeLog.setStatus(ResourceStatusEnum.ENABLE);
        invokeLog.setResourceName("接口调用记录");
        invokeLog.setUrl("/invoke-log/list");

        AclMenuRefVO invokeStatistic = new AclMenuRefVO();
        invokeStatistic.setStatus(ResourceStatusEnum.ENABLE);
        invokeStatistic.setResourceName("接口调用统计");
        invokeStatistic.setUrl("/statistic/invoke-times/api-group");

        List<AclMenuRefVO> children = new ArrayList<>();
        children.add(invokeLog);
        children.add(invokeStatistic);
        invoke.setChildren(children);

        JsonMapper jsonMapper = new JsonMapper();
        List<AclMenuRefVO> menuRefVOS = new ArrayList<>();
        menuRefVOS.add(invoke);
        String json = jsonMapper.toJson(menuRefVOS);
        AclMenuRefVO aclMenuRefVO = jsonMapper.fromJson(json,AclMenuRefVO.class);
        assertNotNull(aclMenuRefVO);
    }

    @Test
    public void putButtonJson(){
        List<AclButtonVO> buttonVOS = new ArrayList<>();
        AclButtonVO invokeLog = new AclButtonVO();
        invokeLog.setStatus(ResourceStatusEnum.ENABLE);
        invokeLog.setUrl("/rest/invoke-log/list");

        AclButtonVO invokeDetail = new AclButtonVO();
        invokeDetail.setStatus(ResourceStatusEnum.ENABLE);
        invokeDetail.setUrl("/rest/invoke-log/detail");

        buttonVOS.add(invokeLog);
        buttonVOS.add(invokeDetail);

        String json = JSON_MAPPER.toJson(buttonVOS);
        List<AclButtonVO> aclButtonVO = JSON_MAPPER.fromJson(json,List.class);
        assertNotNull(aclButtonVO);
    }

}
