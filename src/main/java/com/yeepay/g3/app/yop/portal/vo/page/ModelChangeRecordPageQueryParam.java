package com.yeepay.g3.app.yop.portal.vo.page;

import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * title: ModelChangeRecordPageQueryParam<br/>
 * description: model变更记录分页查询参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/25 下午5:33
 */
public class ModelChangeRecordPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private String name;

    private String opType;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String operatedStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private String operatedEndDate;

    private String apiGroup;

    private String operator;

    private List<String> apiGroupCodes;


    public String getName() {
        return name;
    }

    public ModelChangeRecordPageQueryParam setName(String name) {
        this.name = name;
        return this;
    }

    public String getOpType() {
        return opType;
    }

    public ModelChangeRecordPageQueryParam setOpType(String opType) {
        this.opType = opType;
        return this;
    }

    public String getOperatedStartDate() {
        return operatedStartDate;
    }

    public ModelChangeRecordPageQueryParam setOperatedStartDate(String operatedStartDate) {
        this.operatedStartDate = operatedStartDate;
        return this;
    }

    public String getOperatedEndDate() {
        return operatedEndDate;
    }

    public ModelChangeRecordPageQueryParam setOperatedEndDate(String operatedEndDate) {
        this.operatedEndDate = operatedEndDate;
        return this;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public ModelChangeRecordPageQueryParam setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getOperator() {
        return operator;
    }

    public ModelChangeRecordPageQueryParam setOperator(String operator) {
        this.operator = operator;
        return this;
    }

    public List<String> getApiGroupCodes() {
        return apiGroupCodes;
    }

    public ModelChangeRecordPageQueryParam setApiGroupCodes(List<String> apiGroupCodes) {
        this.apiGroupCodes = apiGroupCodes;
        return this;
    }

    @Override
    public String toString() {
        return "SpiChangeRecordPageQueryParam{" +
                "name='" + name + '\'' +
                ", opType='" + opType + '\'' +
                ", operatedStartDate=" + operatedStartDate +
                ", operatedEndDate=" + operatedEndDate +
                ", apiGroup='" + apiGroup + '\'' +
                ", operator='" + operator + '\'' +
                ", apiGroupCodes=" + apiGroupCodes +
                '}';
    }
}
