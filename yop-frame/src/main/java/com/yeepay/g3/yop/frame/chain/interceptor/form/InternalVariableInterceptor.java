
package com.yeepay.g3.yop.frame.chain.interceptor.form;

import com.yeepay.g3.yop.frame.chain.ChainExecutionControl;
import com.yeepay.g3.yop.frame.chain.interceptor.AbstractInterceptor;
import com.yeepay.g3.yop.frame.chain.interceptor.annotations.ContentTypeRoutable;
import com.yeepay.g3.yop.frame.context.Context;
import com.yeepay.g3.yop.frame.context.ContextUtils;
import com.yeepay.g3.yop.frame.definition.ApiParamDefinition;
import com.yeepay.g3.yop.frame.exception.InternalException;
import com.yeepay.g3.yop.frame.internalvariable.InternalVariableGetter;
import com.yeepay.g3.yop.frame.response.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 内部参数／默认值拦截器
 *
 * <AUTHOR>
 * @version 1.0
 */

@ContentTypeRoutable(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
@Component("internalVariableInterceptor")
public class InternalVariableInterceptor extends AbstractInterceptor {

    @Autowired
    private InternalVariableGetter internalVariableGetter;

    @Override
    public boolean isEnabled(Context context) {
        if (context.getApiContext().isApiV2()) {
            return false;
        }
        return true;
    }

    @Override
    public Response onIntercept(Context context, ChainExecutionControl control) throws InternalException {
        Map<String, String[]> params = context.getRawRequest().getParameterMap();
        List<ApiParamDefinition> paramDefinitions = ContextUtils.getApiDefinition(context).getFormParamDefinitionsDetail().getParamDefinitions();
        for (ApiParamDefinition paramDefinition : paramDefinitions) {
            String name = paramDefinition.getParamName();
            // 内部参数
            if (Boolean.TRUE.equals(paramDefinition.getInternal())) {
                params.remove(name);
            }

            // 默认值
            String dv = paramDefinition.getDefaultValue();
            if (StringUtils.isBlank(dv) || params.get(name) != null) {
                continue;
            }
            params.put(name, new String[]{internalVariableGetter.get(context, dv)});
        }
        return control.invokeNext();
    }
}
