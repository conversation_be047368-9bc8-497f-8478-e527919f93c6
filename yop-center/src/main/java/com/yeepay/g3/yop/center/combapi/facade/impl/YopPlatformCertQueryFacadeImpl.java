/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.center.combapi.facade.impl;

import com.google.common.collect.Lists;
import com.yeepay.g3.encryptor.yop.platform.PlatformCert;
import com.yeepay.g3.encryptor.yop.platform.PlatformCertContext;
import com.yeepay.g3.encryptor.yop.platform.PlatformCertSelector;
import com.yeepay.g3.encryptor.yop.utils.CertUtils;
import com.yeepay.g3.facade.yop.sys.enums.CertTypeEnum;
import com.yeepay.g3.frame.yop.ca.utils.Sm4Utils;
import com.yeepay.g3.yop.center.combapi.facade.YopPlatformCertQueryFacade;
import com.yeepay.g3.yop.center.dto.*;
import com.yeepay.g3.yop.frame.cache.CertRefreshableLocalCache;
import com.yeepay.g3.yop.frame.definition.AppSecretKeys;
import com.yeepay.g3.yop.frame.definition.authenticate.SecretKey;
import com.yeepay.g3.yop.frame.exception.authentication.EncryptionKeyUnavailableException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/2/8 5:51 下午
 */
@Component
public class YopPlatformCertQueryFacadeImpl implements YopPlatformCertQueryFacade {

    @Autowired
    private PlatformCertSelector platformCertSelector;

    @Autowired
    private CertRefreshableLocalCache certRefreshableLocalCache;

    @Override
    public YopPlatformCerts find(YopPlatformCertQueryParam param) {
        YopPlatformCerts yopPlatformCerts = new YopPlatformCerts();
        String appId = param.getAppId();
        List<PlatformCert> platformCerts = getPlatformCert(param);
        AppSecretKeys appSecretKeys = certRefreshableLocalCache.get(appId);
        List<SecretKey> encryptKeys = appSecretKeys.getEncryptKeys(CertTypeEnum.SM4);
        if (CollectionUtils.isEmpty(encryptKeys)) {
            throw new EncryptionKeyUnavailableException();
        }
        SecretKey secretKey = encryptKeys.get(0);
        List<YopPlatformCert> certs = platformCerts.stream()
                .map(platformCert -> convert(secretKey, platformCert))
                .collect(Collectors.toList());
        yopPlatformCerts.setData(certs);
        return yopPlatformCerts;
    }

    @Override
    public YopPlatformPlainCerts findPlainCert(YopPlatformCertQueryParam param) {
        YopPlatformPlainCerts platformPlainCerts = new YopPlatformPlainCerts();
        List<PlatformCert> platformCerts = getPlatformCert(param);
        List<YopPlatformPlainCert> certs = platformCerts.stream()
                .map(platformCert -> convertToYopPlatformPlainCert(platformCert))
                .collect(Collectors.toList());
        platformPlainCerts.setData(certs);
        return platformPlainCerts;
    }

    private List<PlatformCert> getPlatformCert(YopPlatformCertQueryParam param) {
        String appId = param.getAppId();
        String serialNo = param.getSerialNo();
        List<PlatformCert> platformCerts;
        if (StringUtils.isNotEmpty(serialNo)) {
            PlatformCert platformCert = platformCertSelector.selectOne(serialNo);
            if (null == platformCert) {
                platformCerts = Collections.emptyList();
            } else {
                platformCerts = Lists.newArrayList(platformCert);
            }
        } else {
            PlatformCertContext platformCertContext = new PlatformCertContext();
            platformCertContext.setAppId(appId);
            platformCertContext.setCertType(param.getCertType());
            platformCerts = platformCertSelector.selectBuffer(platformCertContext);
        }

        return platformCerts;
    }

    private YopPlatformPlainCert convertToYopPlatformPlainCert(PlatformCert platformCert) {
        YopPlatformPlainCert platformPlainCert = new YopPlatformPlainCert();
        platformPlainCert.setSerialNo(CertUtils.parseToHex(platformCert.getSerialNo()));
        platformPlainCert.setEffectiveDate(platformCert.getEffectiveDate());
        platformPlainCert.setExpireDate(platformCert.getExpiredDate());
        platformPlainCert.setCert(platformCert.getConfig().get("pubKey"));
        return platformPlainCert;
    }

    private YopPlatformCert convert(SecretKey secretKey, PlatformCert platformCert) {
        YopPlatformCert yopPlatformCert = new YopPlatformCert();
        yopPlatformCert.setSerialNo(CertUtils.parseToDecimal(platformCert.getSerialNo()));
        yopPlatformCert.setEffectiveDate(platformCert.getEffectiveDate());
        yopPlatformCert.setExpireDate(platformCert.getExpiredDate());
        EncryptCertificate encryptCertificate = new EncryptCertificate();
        yopPlatformCert.setEncryptCertificate(encryptCertificate);
        encryptCertificate.setAlgorithm("AEAD_SM4_GCM");
        Map<String, String> config = platformCert.getConfig();
        String pubKey = config.get("pubKey");
        String encryptPubKey = Sm4Utils.encrypt_GCM_NoPadding(secretKey.getKey().toString(), pubKey);
        encryptCertificate.setCipherText(encryptPubKey);
        return yopPlatformCert;
    }
}
