package com.yeepay.g3.app.yop.portal.vo;

import java.io.Serializable;

public class RegTestAtokVO implements Serializable {

    private static final long serialVersionUID = -1;

    private Long tokenId;
    private String grantType;
    private String username;
    private String tokenName;
    private String scope;
    private String appKey;
    private String apiGroup;

    public Long getTokenId() {
        return tokenId;
    }

    public void setTokenId(Long tokenId) {
        this.tokenId = tokenId;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getTokenName() {
        return tokenName;
    }

    public void setTokenName(String tokenName) {
        this.tokenName = tokenName;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    @Override
    public String toString() {
        return "RegTestAtokVO{" +
                "tokenId=" + tokenId +
                ", grantType='" + grantType + '\'' +
                ", username='" + username + '\'' +
                ", tokenName='" + tokenName + '\'' +
                ", scope='" + scope + '\'' +
                ", appKey='" + appKey + '\'' +
                ", apiGroup='" + apiGroup + '\'' +
                '}';
    }
}
