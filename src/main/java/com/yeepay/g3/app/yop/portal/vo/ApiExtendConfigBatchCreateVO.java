package com.yeepay.g3.app.yop.portal.vo;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/10 10:21
 */
public class ApiExtendConfigBatchCreateVO implements Serializable {

    private static final long serialVersionUID = -1;

    private List<String> apiIds;
    private String errorCodeLocation;
    private String messageLocation;

    private String requestSuccessValue;

    private String requestSuccessType;

    public String getErrorCodeLocation() {
        return errorCodeLocation;
    }

    public void setErrorCodeLocation(String errorCodeLocation) {
        this.errorCodeLocation = errorCodeLocation;
    }

    public List<String> getApiIds() {
        return apiIds;
    }

    public void setApiIds(List<String> apiIds) {
        this.apiIds = apiIds;
    }

    public String getMessageLocation() {
        return messageLocation;
    }

    public void setMessageLocation(String messageLocation) {
        this.messageLocation = messageLocation;
    }

    public String getRequestSuccessValue() {
        return requestSuccessValue;
    }

    public void setRequestSuccessValue(String requestSuccessValue) {
        this.requestSuccessValue = requestSuccessValue;
    }

    public String getRequestSuccessType() {
        return requestSuccessType;
    }

    public void setRequestSuccessType(String requestSuccessType) {
        this.requestSuccessType = requestSuccessType;
    }
}
