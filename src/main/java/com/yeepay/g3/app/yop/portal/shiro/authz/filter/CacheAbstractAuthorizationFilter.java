/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeepay.g3.app.yop.portal.service.*;
import com.yeepay.g3.app.yop.portal.shiro.utils.SpringContextUtil;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.YosStoreConfigVO;
import com.yeepay.g3.facade.yop.api.dto.ApiDefineDTO;
import com.yeepay.g3.facade.yop.api.facade.OldApiMgrFacade;
import com.yeepay.g3.facade.yop.doc.dto.v2.DocDTO;
import com.yeepay.g3.facade.yop.doc.facade.DocMgrFacade;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.dto.route.ApiRouteDTO;
import com.yeepay.g3.facade.yop.sys.dto.route.ApiRouteEntiretyDTO;
import com.yeepay.g3.facade.yop.sys.facade.*;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 19/1/8 14:40
 */
public abstract class CacheAbstractAuthorizationFilter extends AbstractAuthorizationFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(CacheAbstractAuthorizationFilter.class);

    // routeId -> sp
    protected LoadingCache<Long, String> routeLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(104, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by routeId:{}.", key);
                    String spCode;
                    try {
                        ApiRouteMgrFacade apiRouteMgrFacade = RemoteServiceFactory.getService(ApiRouteMgrFacade.class);
                        ApiRouteDTO apiRouteDTO = apiRouteMgrFacade.findById(key);
                        spCode = null != apiRouteDTO ? apiIdLocalCache.get(apiRouteDTO.getApiId()) : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by routeId failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });
    // routeDeployId -> sp
    protected LoadingCache<Long, String> routeDeployLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(105, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by routeDeployId:{}.", key);
                    String spCode;
                    try {
                        ApiRouteDeployFacade apiRouteDeployFacade = RemoteServiceFactory.getService(ApiRouteDeployFacade.class);
                        ApiRouteEntiretyDTO apiRouteEntiretyDTO = apiRouteDeployFacade.findRecordDetail(key);
                        spCode = null != apiRouteEntiretyDTO ? apiIdLocalCache.get(apiRouteEntiretyDTO.getApiId()) : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by routeDeployId failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // apiGroup -> sp
    protected LoadingCache<String, String> apiGroupLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(101, TimeUnit.SECONDS)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) throws Exception {
                    LOGGER.info("load spCode by apiGroup:{}.", key);
                    String spCode;
                    try {
                        ApiGroupFacade apiGroupFacade = RemoteServiceFactory.getService(ApiGroupFacade.class);
                        ApiGroupDTO apiGroupDTO = apiGroupFacade.findByApiGroupCode(key);
                        spCode = null != apiGroupDTO ? apiGroupDTO.getSpCode() : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by apiGroup failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // fileStore -> sp
    protected LoadingCache<Long, String> fileStoreLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(91, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by backendApp:{}.", key);
                    String spCode;
                    try {
                        YosStoreConfigQueryService yosStoreConfigQueryService1 = (YosStoreConfigQueryService) SpringContextUtil.getBean("yosStoreConfigQueryServiceImpl");
                        YosStoreConfigVO yosStoreConfigVO = yosStoreConfigQueryService1.findById(key);
                        spCode = null != yosStoreConfigVO ? apiGroupLocalCache.get(yosStoreConfigVO.getApiGroupCode()) : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by backendApp failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });
    // apiId -> sp
    protected LoadingCache<Long, String> apiLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(103, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by apiId:{}.", key);
                    String spCode;
                    try {
                        OldApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(OldApiMgrFacade.class);
                        ApiDefineDTO user = apiMgrFacade.findApiById(key);
                        spCode = null != user ? apiGroupLocalCache.get(user.getApiGroup()) : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by api failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // backendApp -> sp
    protected LoadingCache<String, String> backendAppLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(107, TimeUnit.SECONDS)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) throws Exception {
                    LOGGER.info("load spCode by backendApp:{}.", key);
                    String spCode;
                    try {
                        BackendAppFacade backendAppFacade = RemoteServiceFactory.getService(BackendAppFacade.class);
                        BackendAppDTO backendAppDTO = backendAppFacade.findByBackendCode(key);
                        spCode = null != backendAppDTO ? backendAppDTO.getSpCode() : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by backendApp failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // modelId -> sp
    protected LoadingCache<Long, String> modelLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(109, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by modelId:{}.", key);
                    String spCode;
                    try {
                        ModelMgrFacade modelMgrFacade = RemoteServiceFactory.getService(ModelMgrFacade.class);
                        ModelDTO modelDTO = modelMgrFacade.find(key);
                        spCode = null != modelDTO ? apiGroupLocalCache.get(modelDTO.getApiGroup()) : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by modelId failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // spiId -> sp
    protected LoadingCache<Long, String> spiLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(113, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by spiId:{}.", key);
                    String spCode;
                    try {
                        SpiMgrFacade spiMgrFacade = RemoteServiceFactory.getService(SpiMgrFacade.class);
                        SpiDTO spiDTO = spiMgrFacade.find(key);
                        spCode = null != spiDTO ? apiGroupLocalCache.get(spiDTO.getBasic().getApiGroup()) : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by spiId failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // modelChangeId -> sp
    protected LoadingCache<Long, String> modelChangeLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(119, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by model change record Id:{}.", key);
                    String spCode;
                    try {
                        ModelMgrFacade modelMgrFacade = RemoteServiceFactory.getService(ModelMgrFacade.class);
                        ModelChangeRecordDTO modelChangeRecordDTO = modelMgrFacade.findChangeRecord(key);
                        spCode = null != modelChangeRecordDTO ? apiGroupLocalCache.get(modelChangeRecordDTO.getApiGroup()) : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by model change record Id failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // spiChangeId -> sp
    protected LoadingCache<Long, String> spiChangeLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(149, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by spi change record Id:{}.", key);
                    String spCode;
                    try {
                        SpiMgrFacade spiMgrFacade = RemoteServiceFactory.getService(SpiMgrFacade.class);
                        SpiChangeRecordDTO spiChangeRecordDTO = spiMgrFacade.findChangeRecord(key);
                        spCode = null != spiChangeRecordDTO ? apiGroupLocalCache.get(spiChangeRecordDTO.getApiGroup()) : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by spi change record Id failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // productCode -> spCode
    protected LoadingCache<String, String> productCodeLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(143, TimeUnit.SECONDS)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) throws Exception {
                    LOGGER.info("load spCode by product-code:{}.", key);
                    String spCode;
                    try {
                        ProductFacade productFacade = RemoteServiceFactory.getService(ProductFacade.class);
                        final ProductDTO product = productFacade.find(key);
                        spCode = null != product ? product.getSpCode() : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by product-code(" + key + ") failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // product-authz-id -> spCode
    protected LoadingCache<Long, String> productAuthzIdLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(141, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by product-authz-id{}.", key);
                    String spCode;
                    try {
                        ProductAuthzFacade productAuthzFacade = RemoteServiceFactory.getService(ProductAuthzFacade.class);
                        final ProductAuthzDTO productAuthz = productAuthzFacade.find(key);
                        spCode = (null != productAuthz && StringUtils.isNotBlank(productAuthz.getProductCode())) ? productCodeLocalCache.get(productAuthz.getProductCode()) : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by product-authz-id(" + key + ") failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // product-scene-id -> spCode
    protected LoadingCache<Long, String> productSceneIdLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(139, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by product-scene-id{}.", key);
                    String spCode;
                    try {
                        SceneFacade sceneFacade = RemoteServiceFactory.getService(SceneFacade.class);
                        final SceneDTO scene = sceneFacade.find(key);
                        spCode = (null != scene && StringUtils.isNotBlank(scene.getProductCode())) ? productCodeLocalCache.get(scene.getProductCode()) : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by product-scene-id(" + key + ") failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });
    // 新版apiPublishId -> spCode
    protected LoadingCache<Long, String> apiPublishIdLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(138, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by apiDeployId:{}.", key);
                    String spCode;
                    try {
                        ApiPublishFacade apiPublishFacade = RemoteServiceFactory.getService(ApiPublishFacade.class);
                        ApiDTO apiDTO = apiPublishFacade.findRecordDetail(key);
                        if (null != apiDTO && StringUtils.isNotEmpty(apiDTO.getBasic().getApiGroup())) {
                            spCode = apiGroupLocalCache.get(apiDTO.getBasic().getApiGroup());
                        } else {
                            spCode = "";
                        }
                    } catch (Exception e) {
                        LOGGER.error("load spCode by apiDeployId(" + key + ") failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // 新版apiId -> spCode
    protected LoadingCache<String, String> apiIdLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(137, TimeUnit.SECONDS)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) throws Exception {
                    LOGGER.info("load spCode by apiId:{}.", key);
                    String spCode;
                    try {
                        ApiManageQueryService apiManageQueryService = (ApiManageQueryService) SpringContextUtil.getBean("apiManageQueryServiceImpl");
                        final Map<String, Object> map = apiManageQueryService.simpleApiDetail(key, null);
                        if (null != map && map.containsKey("api_group")) {
                            spCode = apiGroupLocalCache.get((String) map.get("api_group"));
                        } else {
                            spCode = "";
                        }
                    } catch (Exception e) {
                        LOGGER.error("load spCode by apiId(" + key + ") failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // backendServiceId -> spCode
    protected LoadingCache<Long, String> backendServiceIdLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(131, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, String>() {
                @Override
                public String load(Long key) throws Exception {
                    LOGGER.info("load spCode by backendServiceId:{}.", key);
                    String spCode;
                    try {
                        BackendServiceQueryService backendServiceQueryService = (BackendServiceQueryService) SpringContextUtil.getBean("backendServiceQueryServiceImpl");
                        final Map<String, Object> map = backendServiceQueryService.simpleDetail(key);
                        if (null != map && map.containsKey("api_group")) {
                            spCode = apiGroupLocalCache.get((String) map.get("api_group"));
                        } else {
                            spCode = "";
                        }
                    } catch (Exception e) {
                        LOGGER.error("load spCode by backendServiceId(" + key + ") failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // docId -> spCodeList
    protected LoadingCache<Long, List<String>> docIdLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(127, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, List<String>>() {
                @Override
                public List<String> load(Long key) throws Exception {
                    LOGGER.info("load spCodes by docId:{}.", key);
                    try {
                        DocMgrFacade docMgrFacade = RemoteServiceFactory.getService(DocMgrFacade.class);
                        final DocDTO doc = docMgrFacade.findDocById(key, null);
                        if (null != doc && CollectionUtils.isNotEmpty(doc.getSpCodes())) {
                            return doc.getSpCodes();
                        }
                    } catch (Exception e) {
                        LOGGER.error("load spCodes by docId failed.", e);
                    }
                    return null;
                }
            });

    // docPublishId -> spCodeList
    protected LoadingCache<Long, List<String>> docPublishIdLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(127, TimeUnit.SECONDS)
            .build(new CacheLoader<Long, List<String>>() {
                @Override
                public List<String> load(Long key) throws Exception {
                    LOGGER.info("load spCodes by docPublishId:{}.", key);
                    try {
                        DocQueryService docQueryService = (DocQueryService) SpringContextUtil.getBean("docQueryServiceImpl");
                        final String docNo = docQueryService.findDocByPublishId(key);
                        if (StringUtils.isNotBlank(docNo)) {
                            return docNoLocalCache.get(docNo);
                        }
                    } catch (Exception e) {
                        LOGGER.error("load spCodes by docPublishId failed.", e);
                    }
                    return null;
                }
            });

    // docNo -> spCodes
    protected LoadingCache<String, List<String>> docNoLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(123, TimeUnit.SECONDS)
            .build(new CacheLoader<String, List<String>>() {
                @Override
                public List<String> load(String key) throws Exception {
                    LOGGER.info("load spCodes by docNo:{}.", key);
                    try {
                        DocMgrFacade docMgrFacade = RemoteServiceFactory.getService(DocMgrFacade.class);
                        final DocDTO doc = docMgrFacade.findDoc(key, null);
                        if (null != doc && CollectionUtils.isNotEmpty(doc.getSpCodes())) {
                            return doc.getSpCodes();
                        }
                    } catch (Exception e) {
                        LOGGER.error("load spCodes by docNo failed.", e);
                    }
                    return null;
                }
            });

    //apiUri -> spCode
    protected LoadingCache<String, String> oldApiUriLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(151, TimeUnit.SECONDS)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) throws Exception {
                    LOGGER.info("load spCode by apiUri:{}.", key);
                    String spCode;
                    try {
                        OldApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(OldApiMgrFacade.class);
                        final ApiDefineDTO api = apiMgrFacade.findApiByUri(key);
                        spCode = null != api && StringUtils.isNotBlank(api.getApiGroup())
                                ? apiGroupLocalCache.get(api.getApiGroup()) : "";
                    } catch (Exception e) {
                        LOGGER.error("load spCode by apiUri failed.", e);
                        spCode = null;
                    }
                    return spCode;
                }
            });

    // providerCode -> tenantCode
    protected LoadingCache<String, String> providerCodeLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) throws Exception {
                    LOGGER.info("load tenantCode by provider-code:{}.", key);
                    Map<String, String> map = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_CUSTOMER_PROVIDER_CODES_TENANT_CODES);
                    String tenantCode = map.get(key);
                    return tenantCode;
                }
            });

    // customerNo -> tenantCode
    protected LoadingCache<String, String> customerNoLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(155, TimeUnit.SECONDS)
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(String key) throws Exception {
                    LOGGER.info("load tenantCode by customerNo:{}.", key);
                    String tenantCode;
                    try {
                        CustomerQueryService customerQueryService = (CustomerQueryService) SpringContextUtil.getBean("customerQueryServiceImpl");
                        final Map<String, Object> map = customerQueryService.findByCustomerNo(key);
                        if (null != map && map.containsKey("provider_code")) {
                            tenantCode = providerCodeLocalCache.get((String) map.get("provider_code"));
                        } else {
                            tenantCode = "";
                        }
                    } catch (Exception e) {
                        LOGGER.error("load tenantCode by customerNo(" + key + ") failed.", e);
                        tenantCode = null;
                    }
                    return tenantCode;
                }
            });

}
