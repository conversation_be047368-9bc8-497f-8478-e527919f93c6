/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.converter;

import com.yeepay.g3.app.yop.portal.vo.CustomSolutionVO;
import com.yeepay.g3.facade.yop.sys.dto.CustomSolutionDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * title: 解决方案转换<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/10
 */
@Mapper
public interface CustomSolutionConverter {

    CustomSolutionConverter INSTANCE = Mappers.getMapper(CustomSolutionConverter.class);

    CustomSolutionDTO toDTO(CustomSolutionVO vo);

    CustomSolutionVO toVO(CustomSolutionDTO dto);
}
