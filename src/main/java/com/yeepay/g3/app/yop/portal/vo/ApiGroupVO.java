package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.ApiGroupCommon;
import com.yeepay.g3.app.yop.portal.validation.group.ApiGroupCreate;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR> @version 1.0.0
 * @since 18/7/4 14:41
 */
public class ApiGroupVO implements Serializable {

    private static final long serialVersionUID = -1;

    @NotNull(message = "{apiGroup.apiGroupCode}", groups = ApiGroupCommon.class)
    private String apiGroupCode;

    @NotNull(message = "{apiGroup.apiGroupName}", groups = ApiGroupCreate.class)
    private String apiGroupName;

    @NotNull(message = "{apiGroup.spCode}", groups = ApiGroupCreate.class)
    private String spCode;

    private String description;

    private Long version;

    private Date createdDate;

    private Date lastModifiedDate;

    public ApiGroupVO(String apiGroupCode, String apiGroupName) {
        this.apiGroupCode = apiGroupCode;
        this.apiGroupName = apiGroupName;
    }

    public ApiGroupVO() {
    }

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public String getApiGroupName() {
        return apiGroupName;
    }

    public void setApiGroupName(String apiGroupName) {
        this.apiGroupName = apiGroupName;
    }

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
