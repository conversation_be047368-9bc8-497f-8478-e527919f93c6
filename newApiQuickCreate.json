{"createdByExitedInterface": true, "appName": "yop-sp-hessian", "className": "com.yeepay.yop.sp.facade.TestParamParseFacade", "method": "pojoParam(com.yeepay.yop.sp.dto.WrapperDTO)", "basic": {"name": "pojoParam", "apiType": "COMMON", "apiGroup": "test-wdc", "title": "pojoParam", "classic": "", "description": "\n", "optionsRule": [{"name": "IDEMPOTENT", "config": {"supported": false, "useAllParams": true, "params": [], "hasBizError": false, "bizErrorCode": ""}}, {"name": "SANDBOX", "config": {"supported": false}}]}, "request": {"httpMethod": "POST", "path": "/rest/v1.0/test-wdc/pojo-param", "requestBody": {"description": "", "contents": {"application/x-www-form-urlencoded": {"schema": "{\"type\":\"object\",\"properties\":{\"bigDecimalNum\":{\"type\":\"number\",\"format\":null},\"longNum\":{\"type\":\"integer\",\"format\":\"int64\"},\"intNum\":{\"type\":\"integer\",\"format\":\"int32\"},\"doubleAmount\":{\"type\":\"number\",\"format\":\"double\"}}}", "examples": []}}}, "encrypt": false, "requestModel": {}}, "response": {"httpCode": 200, "contentType": "application/json", "encrypt": false, "content": {"schema": "{\"$ref\":\"#/components/schemas/WrapperDTO\"}", "examples": []}, "responseModel": {"nestModel": [{"modelRef": "WrapperDTO", "nestModel": [], "schema": "{\"type\":\"object\",\"properties\":{\"bigDecimalNum\":{\"exclusiveMaximum\":true,\"type\":\"number\",\"exclusiveMinimum\":true},\"longNum\":{\"exclusiveMaximum\":true,\"format\":\"int64\",\"type\":\"integer\",\"exclusiveMinimum\":true},\"strList\":{\"type\":\"array\",\"items\":{\"exclusiveMaximum\":true,\"type\":\"string\",\"exclusiveMinimum\":true}},\"intNum\":{\"exclusiveMaximum\":true,\"format\":\"int32\",\"type\":\"integer\",\"exclusiveMinimum\":true},\"doubleAmount\":{\"exclusiveMaximum\":true,\"format\":\"double\",\"type\":\"number\",\"exclusiveMinimum\":true}}}"}], "modelRef": "WrapperDTO", "schema": "{\"type\":\"object\",\"properties\":{\"bigDecimalNum\":{\"exclusiveMaximum\":true,\"type\":\"number\",\"exclusiveMinimum\":true},\"longNum\":{\"exclusiveMaximum\":true,\"format\":\"int64\",\"type\":\"integer\",\"exclusiveMinimum\":true},\"strList\":{\"type\":\"array\",\"items\":{\"exclusiveMaximum\":true,\"type\":\"string\",\"exclusiveMinimum\":true}},\"intNum\":{\"exclusiveMaximum\":true,\"format\":\"int32\",\"type\":\"integer\",\"exclusiveMinimum\":true},\"doubleAmount\":{\"exclusiveMaximum\":true,\"format\":\"double\",\"type\":\"number\",\"exclusiveMinimum\":true}}}"}}, "callbacks": [], "sensitiveVariables": []}