/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.LimitRulePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.LimitRulePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/5/13 上午10:53
 */
public interface LimitRuleQueryService {

    PageQueryResult<LimitRulePageItem> pageQuery(LimitRulePageQueryParam param);

}
