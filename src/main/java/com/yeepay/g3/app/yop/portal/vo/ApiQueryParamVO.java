/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.app.yop.portal.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/11/1
 */
@Data
public class ApiQueryParamVO implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 关键词，根据apiUri、apiTitle模糊查询
     */
    private String keyword;
}
