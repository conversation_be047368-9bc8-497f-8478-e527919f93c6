package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ApiQueryService;
import com.yeepay.g3.app.yop.portal.service.InvokeLogQueryService;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.ApiParamSensitiveParamVO;
import com.yeepay.g3.app.yop.portal.vo.ApiParamSensitiveVO;
import com.yeepay.g3.app.yop.portal.vo.InvokeLogRequestParamVO;
import com.yeepay.g3.app.yop.portal.vo.InvokeLogVO;
import com.yeepay.g3.app.yop.portal.vo.page.InvokeLogPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.InvokeLogPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class InvokeLogQueryServiceImpl implements InvokeLogQueryService {

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonDefaultMapper();

    private static final Logger LOGGER = LoggerFactory.getLogger(InvokeLogQueryServiceImpl.class);

    public final static String APPLICATION_JSON_VALUE = "application/json";

    @Resource(name = "apiManageQueryService")
    private QueryService apiManageQueryService;

    @Resource(name = "invokeLogQueryService")
    private QueryService queryService;

    @Autowired
    private ApiQueryService apiQueryService;

    private final PageItemConverter<InvokeLogPageItem> pageItemConverter = new InvokeLogPageItemConverter();

    @Override
    public PageQueryResult<InvokeLogPageItem> pageQuery(InvokeLogPageQueryParam param) {
        Map<String, Object> paramMap = getBizParams(param);
        List<Map> list = queryService.query("list", paramMap);
        PageQueryResult<InvokeLogPageItem> result = new PageQueryResult<>();
        List<InvokeLogPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        List<String> apiUris = new ArrayList<>(items.size());
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> {
                InvokeLogPageItem item = pageItemConverter.convert(map);
                apiUris.add(item.getApiUri());
                items.add(item);
            });
        }
        Map<String, String> map = getTitleByApiUri(apiUris);
        items.forEach(item -> {
            String apiTitle = map.get(item.getApiUri());
            item.setApiTitle(apiTitle);
        });

        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    private Map<String, String> getTitleByApiUri(List<String> apiUris) {
        if (CollectionUtils.isNotEmpty(apiUris)) {
            final List<Map<String, Object>> relateApiInfo = apiManageQueryService.query("getTitleByApiUri",
                    Collections.singletonMap("apiUris", apiUris));
            if (CollectionUtils.isNotEmpty(relateApiInfo)) {
                Map<String, String> map = Maps.newHashMapWithExpectedSize(relateApiInfo.size());
                relateApiInfo.forEach(apiInfo -> {
                    String apiUri = (String) apiInfo.get("path"), apiTitle = (String) apiInfo.get("api_title");
                    map.put(apiUri, apiTitle);
                });
                return map;
            }
        }
        return Collections.emptyMap();
    }

    @Override
    public InvokeLogVO detailQuery(InvokeLogRequestParamVO queryParam) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("requestDatetime", queryParam.getRequestDatetime());
        param.put("id", queryParam.getId());
        Map map = queryService.queryUnique("detail", param, false, false);
        Map extendMap = queryService.queryUnique("extendDetail", param, false, false);
        if (map == null || map.isEmpty()) {
            return null;
        }
        return convert(map, extendMap);
    }

    private Map<String, Object> getBizParams(InvokeLogPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiUri", param.getApiUri());
        bizParams.put("appKey", param.getAppKey());
        bizParams.put("apiGroupCode", param.getApiGroupCode());
        bizParams.put("requestStartDate", param.getRequestStartDate());
        Boolean partition = (Boolean) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_INVOKE_LOG_TABLE_PARTITION);
        if (Boolean.TRUE.equals(partition)) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(param.getRequestStartDate());
            bizParams.put("partition", calendar.get(Calendar.DAY_OF_MONTH) - 1);
        }
        bizParams.put("requestEndDate", param.getRequestEndDate());
        bizParams.put("requestId", param.getRequestId());
        bizParams.put("guid", param.getGuid());
        bizParams.put("requestIp", param.getRequestIp());
        bizParams.put("dataCenter", param.getDataCenter());
        bizParams.put("securityStrategy", param.getSecurityStrategy());
        bizParams.put("bizOrderNo", param.getBizOrderNo());
        bizParams.put("status", param.getStatus());
        bizParams.put("errorCode", param.getErrorCode());
        bizParams.put("subErrorCode", param.getSubErrorCode());
        bizParams.put("minBackendLatency", param.getMinBackendLatency());
        bizParams.put("maxBackendLatency", param.getMaxBackendLatency());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    private InvokeLogVO convert(Map<String, Object> map, Map<String, Object> extendMap) {
        InvokeLogVO invokeLogVO = new InvokeLogVO();
        invokeLogVO.setRequestId((String) map.get("request_id"));
        invokeLogVO.setGuid((String) map.get("guid"));
        invokeLogVO.setAppKey((String) map.get("app_key"));
        invokeLogVO.setApiUri((String) map.get("api_uri"));
        Map<String, String> apiMap = getTitleByApiUri(Collections.singletonList(invokeLogVO.getApiUri()));
        invokeLogVO.setApiTitle(apiMap.get(invokeLogVO.getApiUri()));
        invokeLogVO.setApiGroupCode((String) map.get("api_group"));
        invokeLogVO.setCustomerNo((String) map.get("customer_no"));
        invokeLogVO.setRequestIp((String) map.get("request_ip"));
        invokeLogVO.setDataCenter((String) map.get("data_center"));
        invokeLogVO.setStatus((String) map.get("status"));
        invokeLogVO.setErrorCode((String) map.get("error_code"));
        invokeLogVO.setSubErrorCode((String) map.get("sub_error_code"));
        invokeLogVO.setSecurityStrategy(map.get("security_strategy") == null ? null : (String) map.get("security_strategy"));
        invokeLogVO.setBizOrderCode((String) map.get("biz_order_code"));
        if (StringUtils.isNotBlank(invokeLogVO.getBizOrderCode())) {
            invokeLogVO.setBizOrderValue((String) map.get("biz_order_value"));
        }
        invokeLogVO.setHttpMethod((String) map.get("request_method"));
        invokeLogVO.setRequestDatetime((Date) map.get("request_datetime"));
        invokeLogVO.setResponseDatetime((Date) map.get("response_datetime"));
        invokeLogVO.setTotalLatency((Integer) map.get("total_latency"));
        invokeLogVO.setBackendLatency((Integer) map.get("backend_latency"));
        if(null != extendMap && !extendMap.isEmpty()){
            invokeLogVO.setRequestHeader((String) extendMap.get("request_header"));
            if (StringUtils.isNotBlank(invokeLogVO.getRequestHeader())) {
                Map<String, String> header = JSONUtils.jsonToMap(invokeLogVO.getRequestHeader(), String.class, String.class);
                String contentType = header.get("content-type");
                if (StringUtils.startsWith(contentType, APPLICATION_JSON_VALUE)) {
                    invokeLogVO.setApplicationType(InvokeLogVO.APPLICATION_JSON_TYPE);
                } else {
                    invokeLogVO.setApplicationType(InvokeLogVO.APPLICATION_FORM_TYPE);
                }
                invokeLogVO.setContentType(contentType);
                if (StringUtils.isNotBlank(contentType)) {
                    int i = contentType.lastIndexOf("charset");
                    if (i < 0) {
                        invokeLogVO.setContentCharset("UTF-8");
                    } else {
                        invokeLogVO.setContentCharset(contentType.substring(i + 8).toUpperCase());
                    }
                }
                String authorization = header.get("authorization");
                if (StringUtils.isNotBlank(authorization) && authorization.startsWith("Bearer")) {
                    header.put("authorization", "Bearer ********");
                }
                invokeLogVO.setRequestHeader(JSONUtils.toJsonString(header));
            }
            invokeLogVO.setRequestBody((String) extendMap.get("request_body"));
            invokeLogVO.setResponseHeader((String) extendMap.get("response_header"));
            invokeLogVO.setResponseBody((String) extendMap.get("response_body"));
            invokeLogVO.setStackTrace((String) extendMap.get("stack_trace"));
        }

        sensitive(invokeLogVO);
        return invokeLogVO;
    }

    private void sensitive(InvokeLogVO invokeLogVO) {
        try {
            sensitiveRequestBody(invokeLogVO);
        } catch (Exception e) {
            LOGGER.error("sensitive request body error!", e);
        }
        try {
            sensitiveResponseBody(invokeLogVO);
        } catch (Exception e) {
            LOGGER.error("sensitive response body error!", e);
        }
    }

    private void sensitiveRequestBody(InvokeLogVO invokeLogVO) {
        if (StringUtils.isBlank(invokeLogVO.getRequestBody())) {
            return;
        }
        Map<String, Boolean> map = (Map<String, Boolean>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_INVOKE_LOG_REQUEST_BODY_MASK);
        Boolean config = map.get(invokeLogVO.getApiUri());
        if (null == config) {
            config = map.get(invokeLogVO.getApiGroupCode());
        }
        if (null == config) {
            config = map.get("*");
        }
        if (!Boolean.TRUE.equals(config)) {
            String requestBody = (String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_INVOKE_LOG_DEFAULT_REQUEST_BODY);
            invokeLogVO.setRequestBody(requestBody);
            return;
        }
        ApiParamSensitiveParamVO apiParamSensitiveParamVO = new ApiParamSensitiveParamVO();
        apiParamSensitiveParamVO.setApiUri(invokeLogVO.getApiUri());
        apiParamSensitiveParamVO.setParamType(invokeLogVO.getApplicationType());
        List<ApiParamSensitiveVO> params = apiQueryService.listApiParam(apiParamSensitiveParamVO);

        String requestBody = invokeLogVO.getRequestBody();
        if (InvokeLogVO.APPLICATION_JSON_TYPE.equals(invokeLogVO.getApplicationType())) {
            requestBody = sensitiveJson(requestBody, params);
        } else {
            requestBody = sensitiveForm(requestBody, params);
        }
        invokeLogVO.setRequestBody(requestBody);
    }

    private void sensitiveResponseBody(InvokeLogVO invokeLogVO) {
        if (StringUtils.isBlank(invokeLogVO.getResponseBody())) {
            return;
        }
        ApiParamSensitiveParamVO apiParamSensitiveParamVO = new ApiParamSensitiveParamVO();
        apiParamSensitiveParamVO.setApiUri(invokeLogVO.getApiUri());
        apiParamSensitiveParamVO.setParamType(invokeLogVO.getApplicationType());
        List<ApiParamSensitiveVO> params = apiQueryService.listApiReturnParam(apiParamSensitiveParamVO);
        String responseBody = invokeLogVO.getResponseBody();
        if (responseBody.startsWith("{")) {
            responseBody = sensitiveJson(responseBody, params);
        }
        invokeLogVO.setResponseBody(responseBody);
    }

    private String sensitiveJson(String body, List<ApiParamSensitiveVO> params) {
        if (Collections3.isEmpty(params)) {
            return body;
        }
        Map<String, Object> requestBody = JSON_MAPPER.fromJson(body, Map.class);
        for (ApiParamSensitiveVO param : params) {
            if (Boolean.TRUE.equals(param.getSensitive()) && param.getFormat() != null) {
                try {
                    Object value = PropertyUtils.getProperty(requestBody, param.getParamName());
                    if (param.getFormat() != null && value instanceof String) {
                        PropertyUtils.setProperty(requestBody, param.getParamName(), param.getFormat().sensitive(value.toString()));
                    }
                } catch (Exception e) {
                    LOGGER.error("sensitive property error!", e);
                }
            }
        }
        return JSONUtils.toJsonString(requestBody);
    }

    private String sensitiveForm(String body, List<ApiParamSensitiveVO> params) {
        if (Collections3.isEmpty(params)) {
            return body;
        }
        Map<String, Object> requestBody = JSON_MAPPER.fromJson(body, Map.class);
        for (ApiParamSensitiveVO param : params) {
            if (Boolean.TRUE.equals(param.getSensitive()) && param.getFormat() != null) {
                try {
                    Object paramValue = PropertyUtils.getProperty(requestBody, param.getParamName());
                    if (paramValue instanceof List) {
                        List list = (List) paramValue;
                        if (null != list && list.size() == 1) {
                            Object value = list.get(0);
                            if (param.getFormat() != null && value instanceof String) {
                                PropertyUtils.setProperty(requestBody, param.getParamName(), new ArrayList() {{
                                    add(param.getFormat().sensitive(value.toString()));
                                }});
                            }
                        }
                    } else {
                        Object value = paramValue;
                        if (param.getFormat() != null && value instanceof String) {
                            PropertyUtils.setProperty(requestBody, param.getParamName(), new ArrayList() {{
                                add(param.getFormat().sensitive(value.toString()));
                            }});
                        }
                    }
                } catch (Exception e) {
                    LOGGER.error("sensitive property error!", e);
                }
            }
        }
        return JSONUtils.toJsonString(requestBody);
    }

    class InvokeLogPageItemConverter extends BasePageItemConverter<InvokeLogPageItem> {

        @Override
        public InvokeLogPageItem convert(Map<String, Object> params) {
            InvokeLogPageItem item = new InvokeLogPageItem();
            item.setId((Long) params.get("id"));
            item.setGuid((String) params.get("guid"));
            item.setRequestId((String) params.get("request_id"));
            item.setAppKey((String) params.get("app_key"));
            item.setApiUri((String) params.get("api_uri"));
            item.setRequestIp((String) params.get("request_ip"));
            item.setDataCenter((String) params.get("data_center"));
            item.setStatus((String) params.get("status"));
            item.setErrorCode((String) params.get("error_code"));
            item.setSubErrorCode((String) params.get("sub_error_code"));
            item.setSecurityStrategy(params.get("security_strategy") == null ? null : ((String) params.get("security_strategy")).replace("-", "_"));
            item.setRequestDatetime((Date) params.get("request_datetime"));
            item.setResponseDatetime((Date) params.get("response_datetime"));
            return item;
        }
    }

}
