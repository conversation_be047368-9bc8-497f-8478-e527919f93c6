package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * title: <br>
 * description: 负责打开登录页面(GET请求)和登录出错页面(POST请求)<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017/4/12 下午2:51
 */
@Controller
@RequestMapping(value = "/signin")
public class SigninController {

    @ResponseBody
    @RequestMapping(value = {"/**", "/status"}, method = RequestMethod.GET)
    public ResponseMessage signin() {
        Subject subject = SecurityUtils.getSubject();
        ResponseMessage responseMessage = new ResponseMessage("status", subject.isAuthenticated());
        if (subject.isAuthenticated()) {
            ShiroRealm.ShiroUser shiroUser = (ShiroRealm.ShiroUser) subject.getPrincipal();
            responseMessage.put("userId", shiroUser.getUserId());
            responseMessage.put("userName", shiroUser.getUsername());
            responseMessage.put("userType", shiroUser.getUserType());
            responseMessage.put("accessToken", shiroUser.getAccessToken());
        }
        return responseMessage;
    }

    @ResponseBody
    @RequestMapping(value = "/**", method = RequestMethod.POST)
    public ResponseMessage fail() {
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            return new ResponseMessage("已经登录");
        }
        return new ResponseMessage(ResponseMessage.Status.ERROR, "登录失败");
    }

}
