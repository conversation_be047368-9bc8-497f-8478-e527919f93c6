package com.yeepay.g3.app.yop.portal.vo.page;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/16 11:47
 */
public class ApiDefinePageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -7328424552775647120L;

    private String apiUri;

    private String apiTitle;

    private String apiType;

    private String apiGroupCode;

    private List<String> apiGroupCodes;

    private String status;

    private String securityReq;

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public String getApiTitle() {
        return apiTitle;
    }

    public void setApiTitle(String apiTitle) {
        this.apiTitle = apiTitle;
    }

    public String getApiType() {
        return apiType;
    }

    public void setApiType(String apiType) {
        this.apiType = apiType;
    }

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public List<String> getApiGroupCodes() {
        return apiGroupCodes;
    }

    public void setApiGroupCodes(List<String> apiGroupCodes) {
        this.apiGroupCodes = apiGroupCodes;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSecurityReq() {
        return securityReq;
    }

    public void setSecurityReq(String securityReq) {
        this.securityReq = securityReq;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }


    public static final class Builder {
        private Integer pageNo;
        private Integer pageSize;
        private String apiUri;
        private String apiTitle;
        private String apiType;
        private String apiGroupCode;
        private List<String> apiGroupCodes;
        private String status;
        private String securityReq;

        private Builder() {
        }

        public static Builder anApiDefinePageQueryParam() {
            return new Builder();
        }

        public Builder withPageNo(Integer pageNo) {
            this.pageNo = pageNo;
            return this;
        }

        public Builder withPageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder withApiUri(String apiUri) {
            this.apiUri = apiUri;
            return this;
        }

        public Builder withApiTitle(String apiTitle) {
            this.apiTitle = apiTitle;
            return this;
        }

        public Builder withApiType(String apiType) {
            this.apiType = apiType;
            return this;
        }

        public Builder withApiGroupCode(String apiGroupCode) {
            this.apiGroupCode = apiGroupCode;
            return this;
        }

        public Builder withApiGroupCodes(List<String> apiGroupCodes) {
            this.apiGroupCodes = apiGroupCodes;
            return this;
        }

        public Builder withStatus(String status) {
            this.status = status;
            return this;
        }

        public Builder withSecurityReq(String securityReq) {
            this.securityReq = securityReq;
            return this;
        }

        public ApiDefinePageQueryParam build() {
            ApiDefinePageQueryParam apiDefinePageQueryParam = new ApiDefinePageQueryParam();
            apiDefinePageQueryParam.setPageNo(pageNo);
            apiDefinePageQueryParam.setPageSize(pageSize);
            apiDefinePageQueryParam.setApiUri(apiUri);
            apiDefinePageQueryParam.setApiTitle(apiTitle);
            apiDefinePageQueryParam.setApiType(apiType);
            apiDefinePageQueryParam.setApiGroupCode(apiGroupCode);
            apiDefinePageQueryParam.setApiGroupCodes(apiGroupCodes);
            apiDefinePageQueryParam.setStatus(status);
            apiDefinePageQueryParam.setSecurityReq(securityReq);
            return apiDefinePageQueryParam;
        }
    }
}
