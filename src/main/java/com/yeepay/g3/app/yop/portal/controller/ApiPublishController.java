package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.ApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.service.ApiManageQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.page.ApiPublishRecordPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ApiPublishRecordPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.sys.dto.ApiRollbackRequest;
import com.yeepay.g3.facade.yop.sys.dto.OperationInfo;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiPublishRequest;
import com.yeepay.g3.facade.yop.sys.enums.ApiOperationTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.ApiPublishFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * title: Api发布Controller<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-02-13 09:44
 */
@Controller
@RequestMapping("/rest/api/manage")
public class ApiPublishController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiPublishController.class);

    private ApiPublishFacade apiPublishFacade = RemoteServiceFactory.getService(ApiPublishFacade.class);

    @Autowired
    private ApiManageQueryService queryService;

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    @RequestMapping(value = "publish", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage deploy(@RequestParam("apiId") String apiId,
                                  @RequestParam(value = "cause", required = false) String cause) {

        try {
            ApiPublishRequest request = new ApiPublishRequest();
            request.setOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(cause));
            request.setApiId(apiId);
            apiPublishFacade.publish(request);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when publish api, apiId:" + apiId + " .", ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "rollback", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage offline(@RequestParam("id") Long id,
                                   @RequestParam(value = "cause", required = false) String cause) {

        try {
            ApiRollbackRequest request = new ApiRollbackRequest();
            request.setPublishRecordId(id);
            request.setOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(cause == null ? "无" : cause));
            apiPublishFacade.rollback(request);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when rollback api, record id:" + id + " .", ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "publish-record/list", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage listDeployRecord(@RequestParam(value = "apiGroup", required = false) String apiGroup,
                                            @RequestParam(value = "path", required = false) String path,
                                            @RequestParam(value = "method", required = false) String method,
                                            @RequestParam(value = "opType", required = false) ApiOperationTypeEnum opType,
                                            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date startDate,
                                            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date endDate,
                                            @RequestParam(value = "_pageNo", required = false) Integer pageNo) {
        ApiPublishRecordPageQueryParam param = null;
        try {
            param = new ApiPublishRecordPageQueryParam()
                    .withApiGroup(apiGroup)
                    .withPath(path)
                    .withMethod(method)
                    .withOpType(opType)
                    .withStartDate(startDate)
                    .withEndDate(endDate);
            param.setPageNo(pageNo);
            param.setPageSize(getDeployRecordPageSize());
            PageQueryResult<ApiPublishRecordPageItem> result;
            if (ShiroUtils.isPlatformOperator()) {
                result = queryService.publishRecordList(param);
            } else {
                List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
                if (CollectionUtils.isEmpty(apiGroupCodes)) {
                    result = new PageQueryResult<>();
                } else if (StringUtils.isEmpty(param.getApiGroup())) {
                    param.setApiGroupCodes(apiGroupCodes);
                    result = queryService.publishRecordListForSp(param);
                } else if (apiGroupCodes.contains(param.getApiGroup())) {
                    result = queryService.publishRecordList(param);
                } else {
                    result = new PageQueryResult<>();
                }
            }
            return new ResponseMessage("page", result);
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list publish record, queryParam:" + param, ex);
            return new ResponseMessage(ex);
        }
    }

    @RequestMapping(value = "publish-record/detail", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage detailDeployRecord(@RequestParam(value = "id") Long id) {
        try {

            return new ResponseMessage("page", apiPublishFacade.findRecordDetail(id));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when detail deployRecord, id:" + id, ex);
            return new ResponseMessage(ex);
        }
    }

    private int getDeployRecordPageSize() {
        Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
        Long pageSize = pageSizeMap.get("api-publish-record");
        return pageSize == null ? 10 : pageSize.intValue();
    }


}
