/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 19/1/8 15:27
 */
@Component
public class FileStoreBasedAuthorizationFilter extends CacheAbstractAuthorizationFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileStoreBasedAuthorizationFilter.class);

    @Override
    protected String[] getSpCodes(String[] params) {
        String[] spCodes;
        if (params == null || params.length < 1) {
            spCodes = null;
        } else {
            spCodes = new String[params.length];
            try {
                for (int i = 0; i < params.length; i++) {
                    spCodes[i] = fileStoreLocalCache.get(Long.valueOf(params[i]));
                }
            } catch (ExecutionException e) {
                LOGGER.info("get spCode of fileStore wrong, params are :{},exception is :{}", params, e);
            }
            LOGGER.debug("spCodes by fileStore:{}", spCodes);
        }
        return spCodes;
    }

    @Override
    public String shiroName() {
        return "fileStore_based";
    }
}
