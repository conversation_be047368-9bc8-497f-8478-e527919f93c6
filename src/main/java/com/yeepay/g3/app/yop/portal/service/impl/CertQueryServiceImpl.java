/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.CertQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.page.CertPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.CertPageParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/14 上午10:34
 */
@Component
public class CertQueryServiceImpl implements CertQueryService {

    @Resource(name = "certQueryService")
    private QueryService queryService;

    private final PageItemConverter<CertPageItem> pageItemConverter = new AppPageItemConverter();

    @Override
    public PageQueryResult<CertPageItem> pageQuery(CertPageParam param) {
        List<Map> queryResult = queryService.query("list", getBizParams(param));
        PageQueryResult<CertPageItem> result = new PageQueryResult();
        List<CertPageItem> items = new ArrayList<>(CollectionUtils.size(queryResult));
        if (CollectionUtils.isNotEmpty(queryResult)) {
            queryResult.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    private Map<String, Object> getBizParams(CertPageParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("customerNo", param.getCustomerNo());
        bizParams.put("appId", param.getAppId());
        bizParams.put("appName", param.getAppName());
        bizParams.put("type", param.getType());
        bizParams.put("status", param.getStatus());
        bizParams.put("createdStartDate", param.getCreatedStartDate());
        bizParams.put("createdEndDate", param.getCreatedEndDate());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class AppPageItemConverter extends BasePageItemConverter<CertPageItem> {

        @Override
        public CertPageItem convert(Map<String, Object> params) {
            CertPageItem item = new CertPageItem();
            item.setId((Long) params.get("id"));
            item.setCustomerNo((String) params.get("customer_no"));
            item.setAppId((String) params.get("app_id"));
            item.setAppName((String) params.get("app_name"));
            item.setAppType((String) params.get("app_type"));
            item.setAlias((String) params.get("cert_alias"));
            String digestStr = (String) params.get("digest");
            Map<String,String> digest = new HashMap<>(3);
            if(StringUtils.isNotEmpty(digestStr)){
                digest = JsonMapper.toMap(digestStr);
                digest.remove("SHA1");
            }else {
                digest.put("SHA256", (String) params.get("sign_sha256"));
            }
            item.setDigest(digest);
            item.setType((String) params.get("cert_type"));
            item.setUsage((String) params.get("cert_usage"));
            item.setSource((String) params.get("cert_source"));
            Date effectiveDate = (Date) params.get("effective_date");
            Date expiredDate = (Date) params.get("expired_date");
            item.setEffectiveDate(null == effectiveDate ? null : DateFormatUtils.format(effectiveDate, "yyyy-MM-dd"));
            item.setExpiredDate(null == expiredDate ? null : DateFormatUtils.format(expiredDate, "yyyy-MM-dd"));
            item.setStatus((String) params.get("status"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }

}
