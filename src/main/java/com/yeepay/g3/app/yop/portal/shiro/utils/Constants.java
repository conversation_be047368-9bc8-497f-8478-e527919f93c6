/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.utils;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-29 14:59
 */
public class Constants {

    /**
     * 是否跳过拦截器
     */
    public static final String _PORTAL_SKIP_FILTER = "_PORTAL_SKIP_FILTER";

    /**
     * 当前操作所属spCode
     */
    public static final String _PORTAL_SP_CODE = "_PORTAL_SP_CODE";

    /**
     * 审核单号
     */
    public static final String _AUDIT_RECORD_CODE = "x-yop-record-code";

    /**
     * 当前操作人
     */
    public static final String _OPERATOR = "x-yop-operator";

}
