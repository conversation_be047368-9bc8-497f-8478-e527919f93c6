package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.utils.validate.SchemaTypeFormatUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * title: schema通用服务<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-13 17:18
 */
@Controller
@RequestMapping("/rest/schema/commons")
public class SchemaCommonController {

    @ResponseBody
    @RequestMapping("/type-format")
    public ResponseMessage getTypeFormat() {
        return new ResponseMessage("result", SchemaTypeFormatUtils.getSchemaTypeFormatMapping());
    }

}
