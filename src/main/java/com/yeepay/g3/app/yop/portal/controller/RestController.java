package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;

/**
 * title: 登录控制器<br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/1/30 下午2:07
 */
@Controller
@RequestMapping(value = "/rest")
public class RestController {

    private static final Logger LOGGER = LoggerFactory.getLogger(RestController.class);

    @RequestMapping(value = "/**")
    @ResponseBody
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseMessage all(HttpServletRequest request) {
        LOGGER.warn("找不到数据接口:{}.", request.getRequestURI());
        return new ResponseMessage("找不到数据接口");
    }

}
