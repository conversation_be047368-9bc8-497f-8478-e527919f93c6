package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.dto.SpiImportContext;

/**
 * title: Spi导入缓存服务<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-31 17:07
 */
public interface SpiImportCacheService {

    SpiImportContext storeContext(String requestId, SpiImportContext context);

    SpiImportContext loadContext(String requestId);

    void evictContext(String requestId);

}
