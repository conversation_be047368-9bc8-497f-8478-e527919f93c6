/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.blacklist;

import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/6/15 9:47 下午
 */
@Component
public class DiscardHandleStrategy implements HandleStrategy {
    private final String name = "DISCARD";

    @Override
    public void handle(String appId, String url) {
        return;
    }

    @Override
    public String name() {
        return name;
    }

    @PostConstruct
    public void register() {
        HandleStrategyFactory.register(this);
    }
}
