/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.ProductCreate;
import com.yeepay.g3.app.yop.portal.validation.group.ProductDelete;
import com.yeepay.g3.app.yop.portal.validation.group.ProductEdit;
import com.yeepay.g3.facade.yop.sys.enums.ProductTypeEnum;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/5 下午4:54
 */
public class ProductVO implements Serializable {

    private static final long serialVersionUID = -1L;

    @NotNull(groups = {ProductCreate.class})
    @Size(min = 1, max = 32, groups = {ProductCreate.class})
    private String spCode;

    @NotNull(groups = {ProductCreate.class, ProductEdit.class, ProductDelete.class})
    @Size(min = 1, max = 32, groups = {ProductCreate.class, ProductEdit.class, ProductDelete.class})
    private String code;

    @NotNull(groups = {ProductCreate.class})
    @Size(min = 1, max = 64, groups = {ProductCreate.class, ProductEdit.class})
    private String name;

    @NotNull(groups = {ProductCreate.class})
    private ProductTypeEnum type;

    @Size(max = 512, groups = {ProductCreate.class, ProductEdit.class})
    private String desc;

    @Size(max = 300, groups = {ProductCreate.class, ProductEdit.class, ProductDelete.class})
    private String cause;
    private MultipartFile icon;

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ProductTypeEnum getType() {
        return type;
    }

    public void setType(ProductTypeEnum type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCause() {
        return cause;
    }

    public void setCause(String cause) {
        this.cause = cause;
    }

    public MultipartFile getIcon() {
        return icon;
    }

    public void setIcon(MultipartFile icon) {
        this.icon = icon;
    }
}
