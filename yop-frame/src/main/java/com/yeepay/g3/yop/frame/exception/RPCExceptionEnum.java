package com.yeepay.g3.yop.frame.exception;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/1/30 下午8:33
 */
public enum RPCExceptionEnum {

    UNKNOWN_EXCEPTION("unknown-exception"),
    NETWORK_EXCEPTION("network-exception"),
    TIMEOUT_EXCEPTION("timeout-exception"),
    BIZ_EXCEPTION("biz-exception"),
    FORBIDDEN_EXCEPTION("forbidden"),
    SERIALIZATION_EXCEPTION("serialization"),
    NO_PROVIDER("no-provider"),
    BIND_ARGS_FAILURE("bind-args-failed");

    private String code;

    RPCExceptionEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
