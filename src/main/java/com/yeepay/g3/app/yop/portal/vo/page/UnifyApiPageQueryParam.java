package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.app.yop.portal.enums.ApiVersionEnum;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 14:21
 */
public class UnifyApiPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private ApiVersionEnum apiVersion = ApiVersionEnum.V2;

    private String apiGroupCode;

    private String apiUri;

    private String apiTitle;

    private String apiStatus;

    private String apiType;

    private JoinCodeEnum joinCode;

    private Object joinValue;


    public ApiVersionEnum getApiVersion() {
        return apiVersion;
    }

    public void setApiVersion(ApiVersionEnum apiVersion) {
        this.apiVersion = apiVersion;
    }

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public String getApiTitle() {
        return apiTitle;
    }

    public void setApiTitle(String apiTitle) {
        this.apiTitle = apiTitle;
    }

    public String getApiStatus() {
        return apiStatus;
    }

    public void setApiStatus(String apiStatus) {
        this.apiStatus = apiStatus;
    }

    public String getApiType() {
        return apiType;
    }

    public void setApiType(String apiType) {
        this.apiType = apiType;
    }

    public JoinCodeEnum getJoinCode() {
        return joinCode;
    }

    public void setJoinCode(JoinCodeEnum joinCode) {
        this.joinCode = joinCode;
    }

    public Object getJoinValue() {
        return joinValue;
    }

    public void setJoinValue(Object joinValue) {
        this.joinValue = joinValue;
    }

    public enum JoinCodeEnum {
        PRODUCT,
        ERRCODE,
        SPINAME,
        ALL
    }
}
