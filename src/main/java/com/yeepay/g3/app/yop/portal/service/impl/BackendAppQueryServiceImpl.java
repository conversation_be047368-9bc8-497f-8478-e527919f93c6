/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.service.ApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.service.BackendAppQueryService;
import com.yeepay.g3.app.yop.portal.utils.MapUtils;
import com.yeepay.g3.app.yop.portal.utils.PageUtils;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.page.BackendAppPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.BackendAppPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/9/25 上午11:51
 */
@Component
public class BackendAppQueryServiceImpl implements BackendAppQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(BackendAppQueryServiceImpl.class);

    @Resource(name = "backendAppQueryService")
    private QueryService queryService;

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    private PageItemConverter<BackendAppPageItem> pageItemConverter = new BackendAppItemConverter();

    @Override
    public PageQueryResult<BackendAppPageItem> pageList(BackendAppPageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(MapUtils.objectToMap(param));
        QueryResult queryResult = queryService.query("pageList", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
    }

    @Override
    public PageQueryResult<BackendAppPageItem> pageListForSp(BackendAppPageQueryParam param) {
        Set<String> spCodes = ShiroUtils.getShiroUser().getSpScopes();
        if (Collections3.isEmpty(spCodes)) {
            return PageUtils.getEmptyPage(param.getPageNo());
        }
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> map = MapUtils.objectToMap(param);
        map.put("spCodes", spCodes);
        queryParam.setParams(map);
        QueryResult queryResult = queryService.query("pageListForSp", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
    }

    class BackendAppItemConverter extends BasePageItemConverter<BackendAppPageItem> {

        @Override
        public BackendAppPageItem convert(Map<String, Object> params) {
            BackendAppPageItem item = new BackendAppPageItem();
            item.setSpCode((String) params.get("sp_code"));
            item.setBackendCode((String) params.get("backend_code"));
            item.setBackendName((String) params.get("backend_name"));
            item.setDeployMode((String) params.get("deploy_mode"));
            item.setClassLoadMode((String) params.get("class_load_mode"));
            item.setRpcMode((String) params.get("rpc_mode"));
            item.setDescription((String) params.get("description"));
            item.setVersion((Long) params.get("version"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }

}
