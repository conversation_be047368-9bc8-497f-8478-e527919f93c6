package com.yeepay.g3.yop.frame.protocol.response;

import com.google.common.collect.Maps;
import com.yeepay.g3.yop.frame.utils.YopConstants;
import com.yeepay.g3.facade.yop.sys.dto.ErrorResRewriteDTO;
import com.yeepay.g3.facade.yop.sys.dto.ErrorResRewriteRuleDTO;
import com.yeepay.g3.facade.yop.sys.enums.ErrorResRewriteTypeEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.frame.CharacterConstants;
import com.yeepay.g3.yop.frame.cache.ErrorResRewriteRefreshableLocalCache;
import com.yeepay.g3.yop.frame.chain.interceptor.annotations.ExceptionHandler;
import com.yeepay.g3.yop.frame.chain.interceptor.annotations.ResponseTypeAdaptive;
import com.yeepay.g3.yop.frame.chain.interceptor.annotations.SDKVersionAdaptive;
import com.yeepay.g3.yop.frame.context.*;
import com.yeepay.g3.yop.frame.exception.InternalException;
import com.yeepay.g3.yop.frame.http.Headers;
import com.yeepay.g3.yop.frame.marshaller.JacksonJsonMarshaller;
import com.yeepay.g3.yop.frame.protocol.ProtocolVersion;
import com.yeepay.g3.yop.frame.protocol.response.yop.YopMarshaller;
import com.yeepay.g3.yop.frame.protocol.response.yop.YopMarshallerRegistry;
import com.yeepay.g3.yop.frame.protocol.response.yop.v300rc1.exceptions.StatusEnum;
import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.frame.response.ResponseType;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * title: Yop结果序列化器<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/10/13 11:17
 */
@Component
public class YopResponseMarshaller extends BaseResponseMarshaller implements YopMarshallerRegistry {

    private static final Logger LOGGER = LoggerFactory.getLogger(YopResponseMarshaller.class);

    private Map<String, YopMarshaller> registry = Maps.newHashMap();

    @Resource
    private ErrorResRewriteRefreshableLocalCache errorResRewriteLocalCache;

    @Override
    public void marshal(Context context, Response response, HttpServletResponse res) {
        if (MapUtils.isNotEmpty(response.getHeaders())) {
            response.getHeaders().forEach(res::setHeader);
        }
        res.setCharacterEncoding(YopConstants.ENCODING);
        res.setHeader(YopConstants.ACCESS_CONTROL_ALLOW_ORIGIN, CharacterConstants.ASTERISK);
        res.setHeader(YopConstants.ACCESS_CONTROL_ALLOW_METHODS, CharacterConstants.ASTERISK);
        res.setHeader(Headers.YOP_REQUEST_ID, context.getRequestId());
        if (response.getException() != null) {
            errorResRewrite(context, response);
        }

        getMarshaller(context, response).marshal(context, response, res);
    }

    @Override
    public void register(YopMarshaller yopMarshaller) {
        SDKVersionAdaptive sdkVersionAdaptive = AnnotationUtils.findAnnotation(yopMarshaller.getClass(), SDKVersionAdaptive.class);
        ResponseTypeAdaptive responseTypeAdaptive = AnnotationUtils.findAnnotation(yopMarshaller.getClass(), ResponseTypeAdaptive.class);
        if (sdkVersionAdaptive == null || responseTypeAdaptive == null) {
            throw new IllegalArgumentException(yopMarshaller.getClass().getCanonicalName() + " must be annotated by SDKVersionAdaptive and ResponseTypeAdaptive");
        }

        SDKVersion[] sdkVersions = SDKVersion.between(sdkVersionAdaptive.min(), sdkVersionAdaptive.max());
        for (SDKVersion sdkVersion : sdkVersions) {
            ResponseType[] responseTypes = responseTypeAdaptive.value();
            for (ResponseType responseType : responseTypes) {
                if (responseType != ResponseType.EXCEPTION) {
                    registry.put(generateKey(sdkVersion, responseType, null), yopMarshaller);
                    continue;
                }
                ExceptionHandler exceptionHandler = AnnotationUtils.findAnnotation(yopMarshaller.getClass(), ExceptionHandler.class);
                if (exceptionHandler == null) {
                    throw new IllegalArgumentException(yopMarshaller.getClass().getCanonicalName() + " is annotated to by ResponseTypeAdaptive(Exception),but no ExceptionHandler specified.");
                }
                for (Class exception : exceptionHandler.value()) {
                    registry.put(generateKey(sdkVersion, responseType, exception), yopMarshaller);
                }
            }
        }
    }


    @Override
    public YopMarshaller getMarshaller(Context context, Response response) {
        SDKVersion sdkVersion = context.getSDKVersion();
        ResponseType type = response.getResponseType();
        Class exceptionType = null;
        if (type.isException()) {
            exceptionType = response.getException().getClass();
        }

        String originalKey = generateKey(sdkVersion, type, exceptionType);
        YopMarshaller target = registry.get(originalKey);
        if (target != null) {
            return target;
        }

        LOGGER.warn("there is no response marshaller found for sdk-version:{},response-type:{},exception-type:{},for better performance please specify one",
                sdkVersion, type, exceptionType == null ? null : exceptionType.getSimpleName());
        /*如果根据异常没有直接找到，则寻找其父异常类的处理*/
        if (type.isException()) {
            exceptionType = exceptionType.getSuperclass();
            while (InternalException.class.isAssignableFrom(exceptionType)) {
                target = registry.get(generateKey(sdkVersion, type, exceptionType));
                if (target != null) {
                    registry.put(originalKey, target);
                    return target;
                }
                exceptionType = exceptionType.getSuperclass();
            }
        }

        throw new RuntimeException(String.format("there is no response marshaller found for sdk-version:%s,response-type:%s,exception-type:%s!",
                sdkVersion, type, exceptionType == null ? null : exceptionType.getSimpleName()));
    }

    private String generateKey(SDKVersion sdkVersion, ResponseType responseType, Class exceptionType) {
        String key = sdkVersion + CharacterConstants.HASH + responseType;
        if (responseType != ResponseType.EXCEPTION) {
            return key;
        }
        return key + CharacterConstants.HASH + exceptionType.getSimpleName();
    }

    private void errorResRewrite(Context context, Response response) {
        InternalException exception = response.getException();
        if (StatusEnum.BUSINESS_ERROR.equals(exception.getStatus())) {
            return;
        }

        String subError = exception.getCode();
        Map<String, Boolean> subErrorCodeSwitchMap = (Map<String, Boolean>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_SUB_ERROR_NEED_ERROR_RES_REWRITE);
        if (subErrorCodeSwitchMap.get(subError) == null || !subErrorCodeSwitchMap.get(subError)) {
            return;
        }

        ApiContext apiContext = context.getApiContext();
        String apiUri = apiContext.getApiUri();
        String apiGroup = apiContext.getApiGroup();

        Map<String, Boolean> apiGroupSwitchMap = (Map<String, Boolean>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CENTER_API_GROUP_NEED_ERROR_RES_REWRITE);
        if (apiGroupSwitchMap.get(apiGroup) == null || !apiGroupSwitchMap.get(apiGroup)) {
            return;
        }

        ErrorResRewriteRuleDTO errorResRewriteRuleDTO = new ErrorResRewriteRuleDTO();
        errorResRewriteRuleDTO.setAppKey(ContextUtils.getAppId(context));
        errorResRewriteRuleDTO.setErrorCode(exception.getStatus().getCode());
        errorResRewriteRuleDTO.setSubErrorCode(subError);
        errorResRewriteRuleDTO.setApiGroupCode(apiGroup);
        errorResRewriteRuleDTO.setApiUri(apiUri);
        ErrorResRewriteDTO errorResRewriteDTO = errorResRewriteLocalCache.get(errorResRewriteRuleDTO);
        if (!errorResRewriteDTO.isExist()) {
            LOGGER.info("errorResRewrite is not find ,apigroup is :{}", apiGroup);
            return;
        }

        if (ErrorResRewriteTypeEnum.TEXT.equals(errorResRewriteDTO.getType())) {
            response.setResponseType(ResponseType.POJO);
            response.setException(null);
            try {
                response.setValue(JacksonJsonMarshaller.unmarshal(errorResRewriteDTO.getTemplate(), Object.class));
            } catch (IOException e) {
                LOGGER.info("structure of template is error ", e);
            }
        }
    }

    @Override
    public ProtocolVersion supportProtocol() {
        return ProtocolVersion.YOP;
    }

    @PostConstruct
    @Override
    public void init() {
        super.init();
    }
}
