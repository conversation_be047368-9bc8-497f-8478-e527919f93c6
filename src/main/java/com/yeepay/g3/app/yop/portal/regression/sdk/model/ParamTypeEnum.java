/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.regression.sdk.model;


import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/19 下午5:07
 */
public enum ParamTypeEnum {

    FORM("FORM", "FORM"),
    JSON("JSON", "JSON");

    private static final Map<String, ParamTypeEnum> VALUE_MAP = new HashMap<>();

    private String value;

    private String displayName;

    static {
        for (ParamTypeEnum item : ParamTypeEnum.values()) {
            VALUE_MAP.put(item.value, item);
        }
    }

    ParamTypeEnum(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static ParamTypeEnum parse(String value) {
        return VALUE_MAP.get(value);
    }

    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

    public static Map<String, ParamTypeEnum> getValueMap() {
        return VALUE_MAP;
    }

}
