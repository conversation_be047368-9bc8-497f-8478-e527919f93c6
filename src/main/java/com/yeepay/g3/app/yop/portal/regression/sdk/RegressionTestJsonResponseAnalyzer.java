package com.yeepay.g3.app.yop.portal.regression.sdk;

import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestResponse;
import com.yeepay.g3.core.yop.sdk.sample.http.HttpResponseAnalyzer;
import com.yeepay.g3.core.yop.sdk.sample.http.HttpResponseHandleContext;
import com.yeepay.g3.core.yop.sdk.sample.model.BaseResponse;
import com.yeepay.g3.core.yop.sdk.sample.utils.JsonUtils;

import java.io.InputStream;
import java.util.Map;

/**
 * title: RegressionTestJsonResponseAnalyzer<br/>
 * description: HTTP body json response handler for YOP responses.<br/>
 * Copyright: Copyright (c) 2017<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/11/22 17:54
 */
public class RegressionTestJsonResponseAnalyzer implements HttpResponseAnalyzer {

    @Override
    public <T extends BaseResponse> boolean analysis(HttpResponseHandleContext httpResponse, T response) throws Exception {
        RegressionTestResponse regressionTestResponse = (RegressionTestResponse) response;
        InputStream content = httpResponse.getResponse().getContent();
        if (content != null) {
            if (regressionTestResponse.getMetadata().getContentLength() > 0
                    || "chunked".equalsIgnoreCase(regressionTestResponse.getMetadata().getTransferEncoding())) {
                JsonUtils.load(content, response);
                Map<String, Object> map = regressionTestResponse.getResult();
                regressionTestResponse.setStringResult(JsonUtils.toJsonString(map));
            }
            content.close();
        }
        return true;
    }
}
