/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.*;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 10:57
 */
public interface ApiErrcodeService {

    List<String> checkRelatedApis(Long errcodeId);

    PageQueryResult<ErrcodeApiPageItem> findRelatedApisPage(ErrcodeApiPageQueryParam pageQueryParam);

    PageQueryResult<ApiErrcodePageItem> findRelatedErrcodePage(ApiErrcodePageQueryParam pageQueryParam);

    PageQueryResult<ApiErrcodePageItem> findUnRelatedErrcodePage(ApiErrcodePageQueryParam pageQueryParam);
}
