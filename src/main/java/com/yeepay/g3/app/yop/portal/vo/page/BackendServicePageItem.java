package com.yeepay.g3.app.yop.portal.vo.page;

import java.io.Serializable;
import java.util.Date;

/**
 * title: BackendServicePageItem<br/>
 * description: 后端服务分页查询返回参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:38
 */
public class BackendServicePageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private Long version;

    private String name;

    private String type;

    private String spCode;

    private String spCodeName;

    private Date createdDateTime;

    private Date lastModifiedDateTime;

    public Long getId() {
        return id;
    }

    public BackendServicePageItem setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getVersion() {
        return version;
    }

    public BackendServicePageItem setVersion(Long version) {
        this.version = version;
        return this;
    }

    public String getName() {
        return name;
    }

    public BackendServicePageItem setName(String name) {
        this.name = name;
        return this;
    }

    public String getType() {
        return type;
    }

    public BackendServicePageItem setType(String type) {
        this.type = type;
        return this;
    }

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getSpCodeName() {
        return spCodeName;
    }

    public void setSpCodeName(String spCodeName) {
        this.spCodeName = spCodeName;
    }

    public Date getCreatedDateTime() {
        return createdDateTime;
    }

    public BackendServicePageItem setCreatedDateTime(Date createdDateTime) {
        this.createdDateTime = createdDateTime;
        return this;
    }

    public Date getLastModifiedDateTime() {
        return lastModifiedDateTime;
    }

    public BackendServicePageItem setLastModifiedDateTime(Date lastModifiedDateTime) {
        this.lastModifiedDateTime = lastModifiedDateTime;
        return this;
    }

    @Override
    public String toString() {
        return "BackendServicePageItem{" +
                "id=" + id +
                ", version=" + version +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", spCode=" + spCode +
                ", createdDateTime=" + createdDateTime +
                ", lastModifiedDateTime=" + lastModifiedDateTime +
                '}';
    }
}
