/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.dto;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * title: 页面负责人更新请求<br>
 * description: 页面负责人更新请求包装类<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-26
 */
public class PageOwnerUpdateRequest extends BaseDTO {

    private static final long serialVersionUID = -1L;

    /**
     * 页面ID
     */
    @NotNull(message = "页面ID不能为空")
    private Long pageId;

    /**
     * 负责人ID列表
     */
    @NotEmpty(message = "负责人ID列表不能为空")
    private List<String> ownerIds;

    public Long getPageId() {
        return pageId;
    }

    public void setPageId(Long pageId) {
        this.pageId = pageId;
    }

    public List<String> getOwnerIds() {
        return ownerIds;
    }

    public void setOwnerIds(List<String> ownerIds) {
        this.ownerIds = ownerIds;
    }
}