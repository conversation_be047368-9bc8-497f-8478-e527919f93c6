/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.service.ServiceEnvObj;
import com.yeepay.g3.app.yop.portal.service.SoaAppService;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.common.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/10/16 11:08 上午
 */
@Component
@Slf4j
public class SoaAppServiceImpl implements SoaAppService {
    private final String QUERY_URI = "/soa-center-hessian/service/provider/info";
    private final String APPLICATION = "application";
    private final String SERVICE = "service";
    private final String METHOD = "method";
    private final String UAT = "uat";
    private final String ENVIRONMENT = "environment";
    private JsonMapper jsonMapper = JsonMapper.nonDefaultMapper();

    @Override
    public boolean supportSandbox(String dataJson) {
        List<ServiceEnvObj> serviceEnvObjs = jsonMapper.fromJson(dataJson, jsonMapper.contructCollectionType(List.class, ServiceEnvObj.class));
        return containsUat(serviceEnvObjs);
    }

    private boolean containsUat(List<ServiceEnvObj> serviceEnvObjs) {
        if (CollectionUtils.isEmpty(serviceEnvObjs)) {
            return false;
        }
        for (ServiceEnvObj serviceEnvObj : serviceEnvObjs) {
            if (CollectionUtils.isEmpty(serviceEnvObj.getProviders())) {
                continue;
            }
            for (Map<String, Object> providerObj : serviceEnvObj.getProviders()) {
                if (UAT.equals(providerObj.get(ENVIRONMENT))) {
                    return true;
                }
            }
        }
        return false;
    }
}
