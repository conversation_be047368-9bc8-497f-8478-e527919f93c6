<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:rabbit="http://www.springframework.org/schema/rabbit"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/rabbit
           http://www.springframework.org/schema/rabbit/spring-rabbit.xsd
           http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="mqPropertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
        <property name="ignoreResourceNotFound" value="true"/>
        <property name="ignoreUnresolvablePlaceholders" value="true"/>
        <property name="locations">
            <list>
                <value>classpath:/runtimecfg/rabbitmq.properties</value>
            </list>
        </property>
    </bean>

    <rabbit:connection-factory id="notifierSenderConnectionFactory" host="${rabbitmq.host}" port="${rabbitmq.port}"
                               username="${rabbitmq.userName}"
                               password="${rabbitmq.password}" virtual-host="${rabbitmq.virtualHost}"
                               thread-factory="mqThreadFactory"
                               channel-cache-size="20" connection-timeout="500"/>

    <bean id="mqThreadFactoryBuilder" class="com.google.common.util.concurrent.ThreadFactoryBuilder">
        <property name="nameFormat" value="mq-connection-factory-%d"/>
    </bean>

    <bean id="mqThreadFactory" factory-bean="mqThreadFactoryBuilder" factory-method="build"/>

    <rabbit:admin connection-factory="notifierSenderConnectionFactory"/>

    <bean id="retryTemplate" class="org.springframework.retry.support.RetryTemplate">
        <property name="retryPolicy">
            <bean class="org.springframework.retry.policy.SimpleRetryPolicy">
                <property name="maxAttempts" value="5"/>
            </bean>
        </property>
        <property name="backOffPolicy">
            <bean class="org.springframework.retry.backoff.ExponentialBackOffPolicy">
                <property name="initialInterval" value="1000"/>
                <property name="maxInterval" value="180000"/>
                <property name="multiplier" value="3"/>
            </bean>
        </property>
    </bean>

    <rabbit:template id="rabbitTemplate" connection-factory="notifierSenderConnectionFactory"
                     retry-template="retryTemplate"
                     message-converter="jackson2JsonMessageConverter"/>

    <bean id="jackson2JsonMessageConverter"
          class="org.springframework.amqp.support.converter.Jackson2JsonMessageConverter"/>

</beans>