/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/10 19:42
 */
@Data
public class ProductDockingCategoryVO extends BaseVO {
    private static final long serialVersionUID = -1L;
    private String categoryCode;
    private String categoryName;
    @JsonProperty("pCategoryCode")
    private String pCategoryCode;
    @JsonProperty("pCategoryName")
    private String pCategoryName;
    private Long pid;
    private Long id;
}
