package com.yeepay.g3.app.yop.portal.utils;

import com.yeepay.g3.app.yop.portal.dto.BaseDTO;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.storage.sdk.StorageClient;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;

import java.io.InputStream;
import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-01-02 18:26
 */
public class CephUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(CephUtil.class);
    private static final String PATH_SEPERATOR = "/";
    private static final CloseableHttpClient HTTP_CLIENT = HttpClientBuilder.create().build();

    public static String upload(CephToken token, String bucket, String fileName, long fileSize, InputStream inputStream) {
        long start = System.currentTimeMillis();
        StorageClient client = null;
        try {
            client = getCephClient(token.getEndPoint(), token.getSecret());
            client.put(bucket, fileName, inputStream);
            return bucket + PATH_SEPERATOR + fileName;
        } catch (Exception ex) {
            LOGGER.warn("error when upload to ceph", ex);
            throw ex;
        } finally {
            LOGGER.info("upload to ceph using time {}ms, fileSize:{}B", System.currentTimeMillis() - start, fileSize);
        }
    }

    public static InputStream get(CephToken token, String bucket, String fileName) {
        StorageClient client;
        try {
            client = getCephClient(token.getEndPoint(), token.getSecret());
            return client.get(bucket, fileName);
        } catch (Exception ex) {
            LOGGER.warn("error when get file from ceph, bucket:" + bucket + ", fileName:" + fileName, ex);
            throw new YeepayRuntimeException(ex);
        }
    }

    private static StorageClient getCephClient(String endPoint, String secret) {
        StorageClient client = new StorageClient(endPoint, secret);
        client.setHttpClient(HTTP_CLIENT);
        return client;
    }

    private static String[] splitStorePath(String storePath) {
        if (storePath.startsWith(PATH_SEPERATOR)) {
            storePath = storePath.substring(1);
        }
        int bucketIndex = storePath.indexOf(PATH_SEPERATOR);
        return new String[]{storePath.substring(0, bucketIndex), storePath.substring(bucketIndex + 1)};
    }

    public static void delete(CephToken token, String storePath) {
        long start = System.currentTimeMillis();
        StorageClient client = null;
        try {
            client = getCephClient(token.getEndPoint(), token.getSecret());
            String[] paths = splitStorePath(storePath);
            client.delete(paths[0], paths[1]);
        } catch (Exception ex) {
            LOGGER.warn("error when delete ceph file", ex);
        } finally {
            LOGGER.info("delete ceph file using time {}ms.", System.currentTimeMillis() - start);
        }
    }

    public static class CephToken extends BaseDTO implements Serializable {

        private static final Long serialVersionUID = -1L;
        private String endPoint;
        private String secret;

        public CephToken(String endPoint, String secret) {
            this.endPoint = endPoint;
            this.secret = secret;
        }

        public String getEndPoint() {
            return endPoint;
        }

        public void setEndPoint(String endPoint) {
            this.endPoint = endPoint;
        }

        public String getSecret() {
            return secret;
        }

        public void setSecret(String secret) {
            this.secret = secret;
        }
    }
}
