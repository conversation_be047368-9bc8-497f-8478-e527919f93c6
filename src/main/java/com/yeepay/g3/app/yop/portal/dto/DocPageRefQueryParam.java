package com.yeepay.g3.app.yop.portal.dto;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-19 17:06
 */
public class DocPageRefQueryParam extends BaseDTO {

    private static final long serialVersionUID = -1L;

    private Long pageId;
    private String keywords;
    private List<String> docNos;

    public String getKeywords() {
        return keywords;
    }

    public DocPageRefQueryParam setKeywords(String keywords) {
        this.keywords = keywords;
        return this;
    }

    public List<String> getDocNos() {
        return docNos;
    }

    public DocPageRefQueryParam setDocNos(List<String> docNos) {
        this.docNos = docNos;
        return this;
    }

    public Long getPageId() {
        return pageId;
    }

    public DocPageRefQueryParam setPageId(Long pageId) {
        this.pageId = pageId;
        return this;
    }
}
