/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * title: 查看自定义解决方案<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/19
 */
@Data
public class CustomSolutionShareVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private String solutionCode;

    private String docUrl;
}
