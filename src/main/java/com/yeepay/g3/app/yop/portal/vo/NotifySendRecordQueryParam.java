/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.monitor.enums.OrderStatusEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.Tolerate;

import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/1
 */
@Builder
@Getter
public class NotifySendRecordQueryParam implements Serializable {
    private static final long serialVersionUID = -1L;
    private String notifyRecordId;
    private Date orderDate;
    private OrderStatusEnum status;
    private String notificationId;
    private String customerNo;
    private String appId;
    private Date notifyStartDate;
    private Date notifyEndDate;
    private String spiName;
    private String notifyRule;
    private String url;
    private String errorCode;

    @Tolerate
    public NotifySendRecordQueryParam() {
    }
}
