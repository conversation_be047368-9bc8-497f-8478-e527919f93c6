package com.yeepay.g3.app.yop.portal.regression.execute.engine;

import com.yeepay.g3.app.yop.portal.regression.AssertEngine;
import com.yeepay.g3.app.yop.portal.regression.ExecutionResult;
import com.yeepay.g3.facade.yop.sys.dto.RegTestCaseFindRespDTO;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/8/5 下午4:12
 */
public interface ExecuteEngine {

    ExecutionResult execute(RegTestCaseFindRespDTO testCaseDTO);

    AssertEngine getAssertEngine();

    String getSecurity();

    boolean supportJson();

}
