package com.yeepay.g3.yop.frame.error;

import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.exception.YeepayBizException;
import com.yeepay.g3.yop.frame.YopBizCodeConsts;

/**
 * <pre>
 *   YOP的异常。
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class YopException extends YeepayBizException {

	private static final long serialVersionUID = -3330314702178895004L;
	private static final String SYSTEM_EXCEPTION = String
			.valueOf(YopBizCodeConsts.SYSTEM_EXCEPTION);

	/**
	 * 不推荐使用
	 */
	private YopException(String errCode) {
		super(errCode);
		if (StringUtils.isBlank(errCode)) {
			errCode = SYSTEM_EXCEPTION;
		}
	}

	public YopException(Throwable e, Object... args) {
		this(SYSTEM_EXCEPTION, e, args);
	}

	public YopException(String errCode, Object... args) {
		super(errCode);
		if (StringUtils.isBlank(errCode)) {
			errCode = SYSTEM_EXCEPTION;
		}
		this.initByErrCode(errCode, null, args);
	}

	public YopException(String errCode, Throwable e, Object... args) {
		super(errCode);
		if (StringUtils.isBlank(errCode)) {
			errCode = SYSTEM_EXCEPTION;
		}
		this.initByErrCode(errCode, e, args);
	}

	private void initByErrCode(String errCode, Throwable e, Object... args) {
		this.message = YopErrorHandler.getMessage(errCode, args);
		if (StringUtils.isBlank(message) && e != null) {
			this.message = e.getMessage();
		}
		if (e != null) {
			this.initCause(e);
		}
	}

}
