package com.yeepay.g3.yop.frame.marshaller;

import com.yeepay.g3.yop.frame.utils.YopConstants;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;

/**
 * <pre>
 *   负责将请求方法返回的响应对应流化为相应格式的内容。<br>
 *   以及将字符串反序列化为相应的对象
 * </pre>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class YopMarshallerUtils {

    /**
     * 序列化为字符串
     *
     * @param object
     * @return
     */
    public static String marshal(Object object) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        JacksonJsonMarshaller.marshal(object, outputStream);
        try {
            return outputStream.toString(YopConstants.ENCODING);
        } catch (UnsupportedEncodingException e) {
            return outputStream.toString();
        }
    }

    /**
     * 负责将请求方法返回的响应对应流化为相应格式的内容
     */
    public static void marshal(Object object, OutputStream outputStream) {
        JacksonJsonMarshaller.marshal(object, outputStream);
    }
}
