/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/14 上午11:12
 */
public enum CertInputTypeEnum {

    TEXT("TEXT", "文本"),
    FILE("FILE", "文件");

    private static final Map<String, CertInputTypeEnum> VALUE_MAP;

    static {
        VALUE_MAP = new HashMap<String, CertInputTypeEnum>();
        for (CertInputTypeEnum authType : CertInputTypeEnum.values()) {
            VALUE_MAP.put(authType.getValue(), authType);
        }
    }

    private String value;

    private String displayName;

    CertInputTypeEnum(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static Map<String, CertInputTypeEnum> getValueMap() {
        return VALUE_MAP;
    }

    public static CertInputTypeEnum parse(String value) {
        return VALUE_MAP.get(value);
    }

    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }
}
