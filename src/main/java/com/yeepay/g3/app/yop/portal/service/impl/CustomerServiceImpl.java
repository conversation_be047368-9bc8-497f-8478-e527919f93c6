/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;


import com.yeepay.g3.app.yop.portal.service.CustomerService;
import com.yeepay.g3.facade.mp.dto.QueryAdminYopRequestDTO;
import com.yeepay.g3.facade.mp.dto.QueryOperatorYopResponseDTO;
import com.yeepay.g3.facade.mp.facade.YopQueryFacade;
import com.yeepay.g3.facade.yop.sys.dto.CustomerDTO;
import com.yeepay.g3.facade.yop.sys.dto.OperatorAuthDTO;
import com.yeepay.g3.facade.yop.sys.dto.OperatorDTO;
import com.yeepay.g3.facade.yop.sys.enums.IdentityTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.OperatorAuthStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.OperatorStatusEnum;
import com.yeepay.g3.facade.yop.sys.facade.CustomerFacade;
import com.yeepay.g3.facade.yop.sys.facade.OperatorFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/8/30 10:58 上午
 */
@Component
public class CustomerServiceImpl implements CustomerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomerServiceImpl.class);

    public static final String YEEPAY_CUSTOMER_PROVIDER_CODE = "YEEPAY";

    private CustomerFacade customerFacade = RemoteServiceFactory.getService(CustomerFacade.class);

    private YopQueryFacade yopQueryFacade = RemoteServiceFactory.getService(YopQueryFacade.class);

    private OperatorFacade operatorFacade = RemoteServiceFactory.getService(OperatorFacade.class);

    @Override
    public List<String> getAdminEmail(String customerNo) {
        List<String> emails = new ArrayList<>();
        CustomerDTO customerDTO = customerFacade.find(customerNo);
        if (YEEPAY_CUSTOMER_PROVIDER_CODE.equals(customerDTO.getProviderCode())) {
            QueryAdminYopRequestDTO queryAdminYopRequestDTO = new QueryAdminYopRequestDTO();
            queryAdminYopRequestDTO.setMerchantNumber(customerNo);
            QueryOperatorYopResponseDTO queryOperatorYopResponseDTO = yopQueryFacade.queryAdminOperator(queryAdminYopRequestDTO);
            String email = queryOperatorYopResponseDTO.getEmail();
            LOGGER.info("query admin email ,customerNo:{},code:{},message:{},email:{}", customerNo, queryOperatorYopResponseDTO.getCode(), queryOperatorYopResponseDTO.getMessage(), email);
            if (StringUtils.isNotBlank(email) && !emails.contains(email)) {
                emails.add(email);
            }
        } else {
            final List<OperatorDTO> operators = operatorFacade.findByCustomerNo(customerNo, true);
            if (CollectionUtils.isEmpty(operators)) {
                return emails;
            }
            for (OperatorDTO operator : operators) {
                if (null != operator && operator.getStatus() != OperatorStatusEnum.FROZEN && CollectionUtils.isNotEmpty(operator.getAuths())) {
                    final Optional<OperatorAuthDTO> foundVerifiedEmail = operator.getAuths().stream().filter(e -> IdentityTypeEnum.EMAIL == e.getIdentityType() && e.getStatus() == OperatorAuthStatusEnum.VERIFIED).findFirst();
                    foundVerifiedEmail.ifPresent(operatorAuthDTO -> emails.add(operatorAuthDTO.getIdentifier()));
                }
            }
        }
        return emails;
    }
}
