package com.yeepay.g3.yop.ext.gateway.context.impl;

import com.yeepay.g3.facade.yop.sys.enums.CertTypeEnum;
import com.yeepay.g3.yop.ext.gateway.context.encrypt.EncryptContext;
import com.yeepay.g3.yop.frame.definition.authenticate.SecretKey;

/**
 * title: 标准加密上下文<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-04-22 16:54
 */
public class StandEncryptContext implements EncryptContext {

    /**
     * 加密类型
     */
    private String encryptType;

    /**
     * 密钥类型
     */
    private CertTypeEnum certType;

    /**
     *
     */
    private SecretKey encryptKey;

    private String cipherKey;

    public StandEncryptContext(String encryptType, String cipherKey) {
        this.encryptType = encryptType;
        this.cipherKey = cipherKey;
    }

    @Override
    public String getEncryptType() {
        return encryptType;
    }

    @Override
    public CertTypeEnum getEncryptCertType() {
        return certType;
    }

    @Override
    public void setEncryptCertType(CertTypeEnum certType) {
        this.certType = certType;
    }

    @Override
    public SecretKey getEncryptKey() {
        return this.encryptKey;
    }

    @Override
    public void setEncryptKey(SecretKey encryptKey) {
        this.encryptKey = encryptKey;
    }

    @Override
    public String getCipherKey() {
        return cipherKey;
    }
}
