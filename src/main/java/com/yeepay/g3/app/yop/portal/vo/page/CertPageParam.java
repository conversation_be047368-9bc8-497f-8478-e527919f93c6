/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.CertStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.CertTypeEnum;

import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/14 上午10:23
 */
public class CertPageParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private String customerNo;

    private String appId;

    private String appName;

    private CertTypeEnum type;

    private CertStatusEnum status;

    private Date createdStartDate;

    private Date createdEndDate;

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public CertTypeEnum getType() {
        return type;
    }

    public void setType(CertTypeEnum type) {
        this.type = type;
    }

    public CertStatusEnum getStatus() {
        return status;
    }

    public void setStatus(CertStatusEnum status) {
        this.status = status;
    }

    public Date getCreatedStartDate() {
        return createdStartDate;
    }

    public void setCreatedStartDate(Date createdStartDate) {
        this.createdStartDate = createdStartDate;
    }

    public Date getCreatedEndDate() {
        return createdEndDate;
    }

    public void setCreatedEndDate(Date createdEndDate) {
        this.createdEndDate = createdEndDate;
    }
}
