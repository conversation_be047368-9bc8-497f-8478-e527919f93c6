package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.sys.dto.api.ApiOption;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiVariable;
import com.yeepay.g3.facade.yop.sys.dto.models.Content;
import com.yeepay.g3.facade.yop.sys.dto.models.Header;
import com.yeepay.g3.facade.yop.sys.dto.models.Parameter;
import com.yeepay.g3.facade.yop.sys.dto.models.RequestBody;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * title: ApiManageVO<br/>
 * description: ApiManageVO<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午7:32
 */
public class ApiManageVO implements Serializable {

    private static final long serialVersionUID = 6318473265309553326L;

    private String apiId;

    /**
     * 是否通过已有接口创建
     */
    private boolean createdByExitedInterface;

    // json参数结构
    Map<String, DefinitionVO> definitions;
    private String appName;
    private String className;

    private Long version;

    private ApiBasicVO basic;

    private ApiRequestVO request;

    private ApiResponseVO response;
    private String method;

    private List<String> callbacks;

    private List<ApiVariable> sensitiveVariables;

    private String sourceRef;

    private List<ApiExampleSceneItemVO> exampleScenes;

    public String getApiId() {
        return apiId;
    }

    public ApiManageVO setApiId(String apiId) {
        this.apiId = apiId;
        return this;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public Long getVersion() {
        return version;
    }

    public ApiManageVO setVersion(Long version) {
        this.version = version;
        return this;
    }

    public ApiBasicVO getBasic() {
        return basic;
    }

    public ApiManageVO setBasic(ApiBasicVO basic) {
        this.basic = basic;
        return this;
    }

    public ApiRequestVO getRequest() {
        return request;
    }

    public ApiManageVO setRequest(ApiRequestVO request) {
        this.request = request;
        return this;
    }

    public ApiResponseVO getResponse() {
        return response;
    }

    public Map<String, DefinitionVO> getDefinitions() {
        return definitions;
    }

    public void setDefinitions(Map<String, DefinitionVO> definitions) {
        this.definitions = definitions;
    }

    public ApiManageVO setResponse(ApiResponseVO response) {
        this.response = response;
        return this;
    }

    public List<String> getCallbacks() {
        return callbacks;
    }

    public ApiManageVO setCallbacks(List<String> callbacks) {
        this.callbacks = callbacks;
        return this;
    }

    public List<ApiVariable> getSensitiveVariables() {
        return sensitiveVariables;
    }

    public void setSensitiveVariables(List<ApiVariable> sensitiveVariables) {
        this.sensitiveVariables = sensitiveVariables;
    }

    public String getSourceRef() {
        return sourceRef;
    }

    public void setSourceRef(String sourceRef) {
        this.sourceRef = sourceRef;
    }

    public boolean isCreatedByExitedInterface() {
        return createdByExitedInterface;
    }

    public void setCreatedByExitedInterface(boolean createdByExitedInterface) {
        this.createdByExitedInterface = createdByExitedInterface;
    }

    public List<ApiExampleSceneItemVO> getExampleScenes() {
        return exampleScenes;
    }

    public void setExampleScenes(List<ApiExampleSceneItemVO> exampleScenes) {
        this.exampleScenes = exampleScenes;
    }

    @Override
    public String toString() {
        return "ApiManageVO{" +
                "apiId='" + apiId + '\'' +
                ", version=" + version +
                ", basic=" + basic +
                ", request=" + request +
                ", response=" + response +
                ", callbacks=" + callbacks +
                ", sensitiveVariables=" + sensitiveVariables +
                '}';
    }

    public static class ApiBasicVO extends BaseVO {

        private static final long serialVersionUID = -1L;

        private String name;

        private String title;

        private String apiType;

        private String apiGroup;

        private String description;

        private String status;

        @Deprecated
        private Map<String, Boolean> options;

        private List<ApiOption> optionsRule;

        public String getName() {
            return name;
        }

        public ApiBasicVO setName(String name) {
            this.name = name;
            return this;
        }

        public String getTitle() {
            return title;
        }

        public ApiBasicVO setTitle(String title) {
            this.title = title;
            return this;
        }

        public String getApiType() {
            return apiType;
        }

        public ApiBasicVO setApiType(String apiType) {
            this.apiType = apiType;
            return this;
        }

        public String getApiGroup() {
            return apiGroup;
        }

        public ApiBasicVO setApiGroup(String apiGroup) {
            this.apiGroup = apiGroup;
            return this;
        }

        public String getDescription() {
            return description;
        }

        public ApiBasicVO setDescription(String description) {
            this.description = description;
            return this;
        }

        public String getStatus() {
            return status;
        }

        public ApiBasicVO setStatus(String status) {
            this.status = status;
            return this;
        }

        public Map<String, Boolean> getOptions() {
            return options;
        }

        public ApiBasicVO setOptions(Map<String, Boolean> options) {
            this.options = options;
            return this;
        }

        public List<ApiOption> getOptionsRule() {
            return optionsRule;
        }

        public ApiBasicVO setOptionsRule(List<ApiOption> optionsRule) {
            this.optionsRule = optionsRule;
            return this;
        }
    }

    public static class ApiRequestVO implements Serializable {

        private static final long serialVersionUID = -1L;

        private String path;

        private String httpMethod;

        private List<Parameter> parameters;

        ServiceModelVO requestModel;

        private RequestBody requestBody;

        private Boolean encrypt;

        public String getPath() {
            return path;
        }

        public ApiRequestVO setPath(String path) {
            this.path = path;
            return this;
        }

        public String getHttpMethod() {
            return httpMethod;
        }

        public ApiRequestVO setHttpMethod(String httpMethod) {
            this.httpMethod = httpMethod;
            return this;
        }

        public List<Parameter> getParameters() {
            return parameters;
        }

        public ApiRequestVO setParameters(List<Parameter> parameters) {
            this.parameters = parameters;
            return this;
        }

        public RequestBody getRequestBody() {
            return requestBody;
        }

        public ApiRequestVO setRequestBody(RequestBody requestBody) {
            this.requestBody = requestBody;
            return this;
        }

        public Boolean getEncrypt() {
            return encrypt;
        }

        public ApiRequestVO setEncrypt(Boolean encrypt) {
            this.encrypt = encrypt;
            return this;
        }

        public ServiceModelVO getRequestModel() {
            return requestModel;
        }

        public void setRequestModel(ServiceModelVO requestModel) {
            this.requestModel = requestModel;
        }

        @Override
        public String toString() {
            return "ApiRequestVO{" +
                    ", path='" + path + '\'' +
                    ", httpMethod='" + httpMethod + '\'' +
                    ", parameters=" + parameters +
                    ", requestBody=" + requestBody +
                    ", encrypt=" + encrypt +
                    '}';
        }
    }

    public static class ApiResponseVO implements Serializable {

        private static final long serialVersionUID = -1L;

        private String httpCode;

        private String contentType;

        private List<Header> headers;

        ServiceModelVO responseModel;

        private Content content;

        private Boolean encrypt;

        public String getHttpCode() {
            return httpCode;
        }

        public ApiResponseVO setHttpCode(String httpCode) {
            this.httpCode = httpCode;
            return this;
        }

        public String getContentType() {
            return contentType;
        }

        public ApiResponseVO setContentType(String contentType) {
            this.contentType = contentType;
            return this;
        }

        public List<Header> getHeaders() {
            return headers;
        }

        public ApiResponseVO setHeaders(List<Header> headers) {
            this.headers = headers;
            return this;
        }

        public Content getContent() {
            return content;
        }

        public ApiResponseVO setContent(Content content) {
            this.content = content;
            return this;
        }

        public Boolean getEncrypt() {
            return encrypt;
        }

        public ApiResponseVO setEncrypt(Boolean encrypt) {
            this.encrypt = encrypt;
            return this;
        }

        public ServiceModelVO getResponseModel() {
            return responseModel;
        }

        public void setResponseModel(ServiceModelVO responseModel) {
            this.responseModel = responseModel;
        }

        @Override
        public String toString() {
            return "ApiResponseVO{" +
                    "httpCode='" + httpCode + '\'' +
                    ", contentType='" + contentType + '\'' +
                    ", headers=" + headers +
                    ", content=" + content +
                    ", encrypt=" + encrypt +
                    '}';
        }
    }

}
