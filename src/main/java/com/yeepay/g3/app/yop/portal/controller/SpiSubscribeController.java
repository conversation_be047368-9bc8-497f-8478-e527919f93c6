/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.blacklist.HandleStrategy;
import com.yeepay.g3.app.yop.portal.blacklist.HandleStrategyFactory;
import com.yeepay.g3.app.yop.portal.cache.AppBlacklistCache;
import com.yeepay.g3.app.yop.portal.cache.key.AppBlacklistCacheKey;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.SpiSubscribeQueryService;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.SpiSubscribePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.SpiSubscribePageQueryParam;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Objects;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/7 17:46
 */
@Controller
@RequestMapping("/rest/spi/subscribe")
public class SpiSubscribeController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SpiSubscribeController.class);

    @Autowired
    private SpiSubscribeQueryService spiSubscribeQueryService;

    @Autowired
    private AppBlacklistCache appBlacklistCache;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(SpiSubscribePageQueryParam param) {
        try {
            PageQueryResult<SpiSubscribePageItem> pageQueryResult = spiSubscribeQueryService.pageList(param);
            return new ResponseMessage("page", pageQueryResult);
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list spi subscribe with param: " + param, ex);
            return new ResponseMessage(ex);
        }
    }

    /**
     * 检查通知url
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/check-url", method = RequestMethod.GET)
    public ResponseMessage checkUrl(@RequestParam("url") String url,
                                    @RequestParam("appId") String appId) {
        AppBlacklistCacheKey appBlacklistCacheKey = new AppBlacklistCacheKey();
        appBlacklistCacheKey.setAppId(appId);
        appBlacklistCacheKey.setUrl(url);
        String blacklistHandleStrategyName = appBlacklistCache.get(appBlacklistCacheKey);
        HandleStrategy blacklistHandleStrategy = HandleStrategyFactory.get(blacklistHandleStrategyName);
        if (!Objects.isNull(blacklistHandleStrategy)) {
            LOGGER.info("appId:{} url:{} in blackList, strategy:{}", appId, url, blacklistHandleStrategy.name());
            try {
                blacklistHandleStrategy.handle(appId, url);
            } catch (Exception ex) {
                return new ResponseMessage(ex);
            }
        }
        return new ResponseMessage<>();
    }
}
