/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.AclLogDetailVO;
import com.yeepay.g3.app.yop.portal.vo.AclResourceVO;
import com.yeepay.g3.app.yop.portal.vo.page.AclLogPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.AclLogPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.Date;
import java.util.List;

/**
 * title:查询操作记录 <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/12/17 下午10:56
 */
public interface AclLogQueryService {

    PageQueryResult<AclLogPageItem> pageQuery(AclLogPageQueryParam param);

    List<AclResourceVO> queryTopReosurce();

    /**
     * 查询资源树
     *
     * @return
     */
    AclResourceVO queryResources();

    AclLogDetailVO findDetail(Long id, Date date);

}
