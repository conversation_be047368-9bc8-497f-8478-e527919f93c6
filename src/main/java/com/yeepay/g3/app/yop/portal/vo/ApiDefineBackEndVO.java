package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yeepay.g3.app.yop.portal.validation.group.ApiCommon;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 17:22
 */
public class ApiDefineBackEndVO implements Serializable {

    private static final long serialVersionUID = -3911146491060510466L;

    @NotEmpty(message = "{api.backend.adapterType}", groups = ApiCommon.class)
    private String adapterType;

    @NotEmpty(message = "{api.backend.protocol}", groups = ApiCommon.class)
    private String protocol;

    private String url;

    @NotEmpty(message = "{api.backend.class}", groups = ApiCommon.class)
    @JsonProperty("class")
    private String clazz;

    @NotEmpty(message = "{api.backend.method}", groups = ApiCommon.class)
    private String method;

    public String getAdapterType() {
        return adapterType;
    }

    public void setAdapterType(String adapterType) {
        this.adapterType = adapterType;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getClazz() {
        return clazz;
    }

    public void setClazz(String clazz) {
        this.clazz = clazz;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
