<?xml version="1.0" encoding="UTF-8"?>
<Configuration packages="com.yeepay.g3.utils"><!--packages参数告诉log4j2还需要额外加载哪个包下的Log4j plugin，其中YeepayMessagePatternConverter即为定制的plugin,负责输出的日志带GUID -->
    <Appenders>
        <FluentAppender name="yop-portal" label="" host="app.logsync.yp" port="24324" mode="remote"> <!-- 如果上容器或者新的有appname构建方式，label项无效 -->
            <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} - %c -%-4r [%t] %-5p %Y %x - %msg%n%throwable"/>
        </FluentAppender>
    </Appenders>
    <Loggers>
        <Logger name="com.yeepay" level="INFO"/>
        <Logger name="net.sf.ehcache" level="INFO"/>
        <Logger name="com.alisoft.xplatform.asf" level="WARN"/>
        <Logger name="com.mbi" level="ERROR"/>
        <Logger name="net.mlw" level="INFO"/>
        <Logger name="java.sql" level="INFO"/>
        <Logger name="com.mchange.v2.async" level="INFO"/>
        <Logger name="org.apache" level="INFO"/>
        <Logger name="org.apache.commons.httpclient" level="ERROR"/>
        <Logger name="com.alibaba.dubbo" level="INFO"/>
        <Logger name="com.yeepay.g3.utils.soa.registry.zookeeper" level="FATAL"/>
        <Logger name="org.springframework" level="INFO"/>
        <Logger name="org.hibernate.validator.internal.util.Version" level="INFO"/>
        <Logger name="org.apache.coyote.http11.Http11NioProtocol" level="INFO"/>
        <Logger name="org.apache.tomcat.util.net.NioSelectorPool" level="INFO"/>
        <Logger name="org.apache.catalina.startup.DigesterFactory" level="INFO"/>
        <Root level="INFO">
            <AppenderRef ref="yop-portal"/>
        </Root>
    </Loggers>
</Configuration>