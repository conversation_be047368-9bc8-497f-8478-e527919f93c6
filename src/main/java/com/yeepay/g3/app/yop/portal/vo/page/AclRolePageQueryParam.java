package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.perm.enums.RoleTypeEnum;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午5:40
 */
public class AclRolePageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private RoleTypeEnum type;

    private String spCode;

    private String roleCode;

    private String roleName;

    public RoleTypeEnum getType() {
        return type;
    }

    public void setType(RoleTypeEnum type) {
        this.type = type;
    }

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }


    public static final class Builder {
        private RoleTypeEnum type;
        private Integer pageNo;
        private String spCode;
        private Integer pageSize;
        private String roleCode;
        private String roleName;

        private Builder() {
        }

        public static Builder anAclRolePageQueryParam() {
            return new Builder();
        }

        public Builder withType(RoleTypeEnum type) {
            this.type = type;
            return this;
        }

        public Builder withPageNo(Integer pageNo) {
            this.pageNo = pageNo;
            return this;
        }

        public Builder withSpCode(String spCode) {
            this.spCode = spCode;
            return this;
        }

        public Builder withPageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public Builder withRoleCode(String roleCode) {
            this.roleCode = roleCode;
            return this;
        }

        public Builder withRoleName(String roleName) {
            this.roleName = roleName;
            return this;
        }

        public AclRolePageQueryParam build() {
            AclRolePageQueryParam aclRolePageQueryParam = new AclRolePageQueryParam();
            aclRolePageQueryParam.setType(type);
            aclRolePageQueryParam.setPageNo(pageNo);
            aclRolePageQueryParam.setSpCode(spCode);
            aclRolePageQueryParam.setPageSize(pageSize);
            aclRolePageQueryParam.setRoleCode(roleCode);
            aclRolePageQueryParam.setRoleName(roleName);
            return aclRolePageQueryParam;
        }
    }
}
