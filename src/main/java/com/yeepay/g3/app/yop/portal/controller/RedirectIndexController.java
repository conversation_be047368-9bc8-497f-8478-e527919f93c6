/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/9/4 18:48
 */
@Controller
@RequestMapping("/")
public class RedirectIndexController {

    private static final Logger LOGGER = LoggerFactory.getLogger(RedirectIndexController.class);

    private static final String INDEX_PAGE = "/rest/project-version";

    @RequestMapping(method = RequestMethod.GET)
    @ResponseBody
    public void index(HttpServletRequest request, HttpServletResponse response) {
        try {
            request.getRequestDispatcher(INDEX_PAGE).forward(request, response);
        } catch (Exception ex) {
            LOGGER.error("index.html redirect resource version error", ex);
        }
    }
}
