/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.PageOwnerVO;

import java.util.List;

/**
 * title: 页面负责人查询服务<br>
 * description: 页面负责人查询服务接口<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-26
 */
public interface PageOwnerQueryService {
    
    /**
     * 获取页面负责人列表
     * 
     * @param pageId 页面ID
     * @return 负责人列表
     */
    List<PageOwnerVO> getPageOwners(Long pageId);
}