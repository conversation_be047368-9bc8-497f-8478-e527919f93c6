/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.exception;

import com.yeepay.g3.boot.web.exception.BaseException;
import com.yeepay.g3.boot.web.exception.assertion.Assert;
import com.yeepay.g3.boot.web.exception.enums.IResponseEnum;
import org.apache.commons.lang3.ArrayUtils;

import java.text.MessageFormat;

/**
 * title: 异常断言接口<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/7 11:06 上午
 */
public interface YopPortalAssert extends IResponseEnum, Assert {

    @Override
    default BaseException newException(Object... args) {
        String msg = this.getMessage();
        if (ArrayUtils.isNotEmpty(args)) {
            msg = MessageFormat.format(this.getMessage(), args);
        }

        return new YopNPortalException(this, args, msg);
    }

    @Override
    default BaseException newException(Throwable t, Object... args) {
        String msg = t.getMessage();
        return new YopNPortalException(this, args, msg, t);
    }
}
