/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.LimitRuleQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.validation.group.LimitRuleCreate;
import com.yeepay.g3.app.yop.portal.validation.group.LimitRuleEdit;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.LimitRuleVO;
import com.yeepay.g3.app.yop.portal.vo.page.LimitRulePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.LimitRulePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;
import com.yeepay.g3.facade.yop.sys.dto.OperationInfo;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;
import com.yeepay.g3.facade.yop.sys.dto.limit.*;
import com.yeepay.g3.facade.yop.sys.enums.LimitStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.LimitTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.ApiMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.LimitRuleFacade;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/5/11 上午10:27
 */
@Controller
@RequestMapping("/rest/limit-rule")
public class LimitRuleController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LimitRuleController.class);

    private LimitRuleFacade limitRuleFacade = RemoteServiceFactory.getService(LimitRuleFacade.class);

    private ApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(ApiMgrFacade.class);

    @Autowired
    private LimitRuleQueryService limitRuleQueryService;

    @ResponseBody
    @RequestMapping(value = "/{limitType}/list", method = RequestMethod.GET)
    public ResponseMessage list(@Validated LimitRulePageQueryParam param,
                                @RequestParam(value = "_pageNo", defaultValue = "1") Integer pageNo,
                                @PathVariable("limitType") String limitType) {
        LimitTypeEnum type = LimitTypeEnum.parse(limitType.toUpperCase());
        Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
        Long appPageSize = pageSizeMap.get(limitType);
        param.setPageSize(appPageSize.intValue());
        param.setPageNo(pageNo);
        param.setType(type);
        return new ResponseMessage("page", limitRuleQueryService.pageQuery(param));
    }

    @ResponseBody
    @RequestMapping(value = "/blacklist", method = RequestMethod.GET)
    public ResponseMessage blacklist(@Validated LimitRulePageQueryParam param) {
        param.setPageSize(1);
        param.setType(LimitTypeEnum.BLACKLIST);
        BlacklistLimitRuleDTO blacklistLimitRule = null;
        PageQueryResult<LimitRulePageItem> result = limitRuleQueryService.pageQuery(param);
        if (CollectionUtils.isNotEmpty(result.getItems())) {
            Long id = result.getItems().get(0).getId();
            blacklistLimitRule = (BlacklistLimitRuleDTO) limitRuleFacade.find(id);
        }
        return new ResponseMessage("result", blacklistLimitRule);
    }

    @ResponseBody
    @RequestMapping(value = "/{limitType}/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody @Validated(LimitRuleCreate.class) LimitRuleVO limitRuleVO,
                                  @PathVariable("limitType") String limitType) {
        LimitTypeEnum type = LimitTypeEnum.parse(limitType.toUpperCase());
        LimitRuleCreateRequest request = new LimitRuleCreateRequest();
        request.setOperationInfo(new OperationInfo()
                .withOperator(ShiroUtils.getOperatorCode())
                .withCause(limitRuleVO.getCause()));
        switch (type) {
            case WHITELIST:
                WhitelistLimitRuleDTO whitelistLimitRule = new WhitelistLimitRuleDTO();
                whitelistLimitRule.setAppId(limitRuleVO.getAppId());
                whitelistLimitRule.setWhitelist(limitRuleVO.getWhitelist());
                whitelistLimitRule.setStatus(LimitStatusEnum.ENABLED);
                request.setLimitRule(whitelistLimitRule);
                break;
            case CONCURRENT:
                ConcurrentLimitRuleDTO concurrentLimitRule = new ConcurrentLimitRuleDTO();
                if (StringUtils.isNotEmpty(limitRuleVO.getAppId())) {
                    concurrentLimitRule.setAppId(limitRuleVO.getAppId());
                }
                if (StringUtils.isNotEmpty(limitRuleVO.getRequestMethod())) {
                    ApiDTO api = apiMgrFacade.findByRequestKey(new ApiRequestKey()
                            .withHttpMethod(limitRuleVO.getRequestMethod())
                            .withPath(limitRuleVO.getRequestPath()));
                    concurrentLimitRule.setApiId(api.getApiId());
                } else {
                    concurrentLimitRule.setRequestPath(limitRuleVO.getRequestPath());
                }
                concurrentLimitRule.setRate(limitRuleVO.getRate());
                concurrentLimitRule.setStatus(LimitStatusEnum.ENABLED);
                request.setLimitRule(concurrentLimitRule);
                break;
            case BLACKLIST:
                BlacklistLimitRuleDTO blacklistLimitRule = new BlacklistLimitRuleDTO();
                blacklistLimitRule.setBlacklist(limitRuleVO.getBlacklist());
                blacklistLimitRule.setStatus(LimitStatusEnum.ENABLED);
                request.setLimitRule(blacklistLimitRule);
                break;
            default:
                throw new YeepayRuntimeException("不支持的类型");
        }
        limitRuleFacade.create(request);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/{limitType}/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam Long id,
                                  @PathVariable("limitType") String limitType) {
        typeCheck(id, limitType);
        return new ResponseMessage("result", limitRuleFacade.find(id));
    }

    @ResponseBody
    @RequestMapping(value = "/{limitType}/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody @Validated(LimitRuleEdit.class) LimitRuleVO limitRuleVO,
                                  @PathVariable("limitType") String limitType) {
        typeCheck(limitRuleVO.getId(), limitType);
        LimitTypeEnum type = LimitTypeEnum.parse(limitType.toUpperCase());
        LimitRuleUpdateRequest request = new LimitRuleUpdateRequest();
        request.setOperationInfo(new OperationInfo()
                .withOperator(ShiroUtils.getOperatorCode())
                .withCause(limitRuleVO.getCause()));
        switch (type) {
            case WHITELIST:
                WhitelistLimitRuleDTO whitelistLimitRule = new WhitelistLimitRuleDTO();
                whitelistLimitRule.setId(limitRuleVO.getId());
                whitelistLimitRule.setWhitelist(limitRuleVO.getWhitelist());
                request.setLimitRule(whitelistLimitRule);
                break;
            case CONCURRENT:
                ConcurrentLimitRuleDTO concurrentLimitRule = new ConcurrentLimitRuleDTO();
                concurrentLimitRule.setId(limitRuleVO.getId());
                concurrentLimitRule.setRate(limitRuleVO.getRate());
                request.setLimitRule(concurrentLimitRule);
                break;
            case BLACKLIST:
                BlacklistLimitRuleDTO blacklistLimitRule = new BlacklistLimitRuleDTO();
                blacklistLimitRule.setId(limitRuleVO.getId());
                blacklistLimitRule.setBlacklist(limitRuleVO.getBlacklist());
                request.setLimitRule(blacklistLimitRule);
                break;
            default:
                throw new YeepayRuntimeException("不支持的类型");
        }
        limitRuleFacade.update(request);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/{type}/disable", method = RequestMethod.POST)
    public ResponseMessage disable(@RequestBody @Validated(LimitRuleEdit.class) LimitRuleVO limitRuleVO,
                                   @PathVariable("type") String type) {
        Long id = limitRuleVO.getId();
        typeCheck(id, type);
        LimitRuleDisableRequest request = new LimitRuleDisableRequest();
        request.setOperationInfo(new OperationInfo()
                .withOperator(ShiroUtils.getOperatorCode())
                .withCause(limitRuleVO.getCause()));
        request.setId(id);
        limitRuleFacade.disable(request);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/{type}/enable", method = RequestMethod.POST)
    public ResponseMessage enabled(@RequestBody @Validated(LimitRuleEdit.class) LimitRuleVO limitRuleVO,
                                   @PathVariable("type") String type) {
        Long id = limitRuleVO.getId();
        typeCheck(id, type);
        LimitRuleEnableRequest request = new LimitRuleEnableRequest();
        request.setOperationInfo(new OperationInfo()
                .withOperator(ShiroUtils.getOperatorCode())
                .withCause(limitRuleVO.getCause()));
        request.setId(id);
        limitRuleFacade.enable(request);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/{type}/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestBody @Validated(LimitRuleEdit.class) LimitRuleVO limitRuleVO,
                                  @PathVariable("type") String type) {
        Long id = limitRuleVO.getId();
        typeCheck(id, type);
        LimitRuleDeleteRequest request = new LimitRuleDeleteRequest();
        request.setOperationInfo(new OperationInfo()
                .withOperator(ShiroUtils.getOperatorCode())
                .withCause(limitRuleVO.getCause()));
        request.setId(id);
        limitRuleFacade.delete(request);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/commons/status", method = RequestMethod.GET)
    public ResponseMessage status() {
        List<CommonsVO> list = new ArrayList<>();
        LimitStatusEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/type", method = RequestMethod.GET)
    public ResponseMessage type() {
        List<CommonsVO> list = new ArrayList<>();
        LimitTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    private void typeCheck(Long id, String type) {
        LimitTypeEnum limitType = LimitTypeEnum.parse(type.toUpperCase());
        LimitRuleDTO dto = limitRuleFacade.find(id);
        if (null == dto) {
            throw new YeepayRuntimeException("熔断规则不存在");
        }
        if (!limitType.equals(dto.getType())) {
            throw new YeepayRuntimeException("熔断规则类型不匹配");
        }
    }

}
