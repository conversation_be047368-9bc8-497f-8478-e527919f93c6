package com.yeepay.g3.app.yop.portal.utils.validate;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.core.yop.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.yop.sys.checker.SchemaTypeFormatChecker;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * title: Schema类型格式工具类<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-13 16:55
 */
public class SchemaTypeFormatUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(SchemaTypeFormatUtils.class);

    private static final ObjectMapper OBJECT_MAPPER = JsonMapper.nonDefaultObjectMapper();

    public static SchemaTypeFormatChecker getSchemaTypeFormatChecker() {
        return new SchemaTypeFormatChecker(getSchemaTypeFormatMapping());
    }

    public static Map<String, List<String>> getSchemaTypeFormatMapping() {
        String typeFormatMappingJson = (String) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_SCHEMA_TYPE_FORMAT);
        try {
            return OBJECT_MAPPER.readValue(typeFormatMappingJson, new TypeReference<Map<String, List<String>>>() {
            });
        } catch (IOException ex) {
            LOGGER.error("get schemaTypeFormatMapping failed.", ex);
            throw new YeepayRuntimeException("get schemaTypeFormatMapping failed.", ex);
        }
    }
}
