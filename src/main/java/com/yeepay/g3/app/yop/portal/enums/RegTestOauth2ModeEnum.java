package com.yeepay.g3.app.yop.portal.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * OAUTH2 授权模式枚举
 */
public enum RegTestOauth2ModeEnum {
    AUTHORIZATION_CODE("AuthorizationCode", "authorization-code", "授权码"),
    IMPLICIT("Implicit", "implicit", "隐藏式"),
    PASSWORD_CREDENTIALS("PasswordCredentials", "password", "密码式"),
    CLIENT_CREDENTIALS("ClientCredentials", "client-credentials", "客户端凭证");

    private static final Map<String, RegTestOauth2ModeEnum> VALUE_MAP;

    static {
        VALUE_MAP = new HashMap<String, RegTestOauth2ModeEnum>();
        for (RegTestOauth2ModeEnum authType : RegTestOauth2ModeEnum.values()) {
            VALUE_MAP.put(authType.getSourceValue(), authType);
        }
    }

    private String sourceValue;

    private String targetValue;

    private String displayName;

    public String getSourceValue() {
        return sourceValue;
    }

    public void setSourceValue(String sourceValue) {
        this.sourceValue = sourceValue;
    }

    public String getTargetValue() {
        return targetValue;
    }

    public void setTargetValue(String targetValue) {
        this.targetValue = targetValue;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    RegTestOauth2ModeEnum(String sourceValue, String targetValue, String displayName) {
        this.sourceValue = sourceValue;
        this.targetValue = targetValue;
        this.displayName = displayName;
    }

    public static Map<String, RegTestOauth2ModeEnum> getValueMap() {
        return VALUE_MAP;
    }

    public static RegTestOauth2ModeEnum parse(String value) {
        return VALUE_MAP.get(value);
    }


}
