package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.doc.dto.v2.DocProductDTO;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocStatusEnum;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocTypeEnum;
import com.yeepay.g3.facade.yop.doc.enums.v2.DocVisableEnum;
import com.yeepay.g3.facade.yop.perm.dto.IspInfoDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-29 10:22
 */
@Data
public class DocPageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;
    private String docNo;
    private String title;
    private boolean apiChanged;
    private String docUrl;
    private String docPreviewUrl;
    private DocTypeEnum type;
    private DocVisableEnum visible;
    private DocStatusEnum status;
    private List<DocProductDTO> products;
    private Boolean display;
    private String docVersion;
    private Long version;
    private Date createdDate;
    private Date lastModifiedDate;
    private boolean owner;
    private List<String> productCodes;
    private List<IspInfoDTO> spInfos;
}
