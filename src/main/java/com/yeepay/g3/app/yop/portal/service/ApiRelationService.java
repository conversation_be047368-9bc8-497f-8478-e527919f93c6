package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.ApiRelationVO;
import com.yeepay.g3.app.yop.portal.vo.SimpleApiVO;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/20 2:08 下午
 */
public interface ApiRelationService {
    /**
     * 创建
     *
     * @param apiRelationVO
     */
    void create(ApiRelationVO apiRelationVO);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 编辑
     *
     * @param apiRelationVO
     */
    void edit(ApiRelationVO apiRelationVO);

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    ApiRelationVO detail(Long id);

    /**
     * 查询api下所有关系
     *
     * @param apiId
     * @return
     */
    List<ApiRelationVO> list(String apiId);

    /**
     * 查询可关联的api
     *
     * @param apiGroup API分组编码
     * @param apiId    下单接口编号，用于排除已被关联的接口
     * @return
     */
    List<SimpleApiVO> apis(String apiGroup, String apiId);
}
