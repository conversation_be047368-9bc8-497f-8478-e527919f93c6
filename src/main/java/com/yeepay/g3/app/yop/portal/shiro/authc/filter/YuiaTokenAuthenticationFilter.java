/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.authc.filter;

import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.app.yop.portal.shiro.authc.OAuth2Token;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.web.filter.authc.AuthenticatingFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/7/27 15:31
 */
@Component
public class YuiaTokenAuthenticationFilter extends AuthenticatingFilter implements CustomShiroFilter {
    private static final Logger LOGGER = LoggerFactory.getLogger(YuiaTokenAuthenticationFilter.class);
    private static final String SSO_LOGIN_URL_PREFIX = "/signin/sso";
    private static final String AUTHORIZATION_HEADER = "authorization";
    private static final String AUTHTOKEN_HEADER = "yuiassotoken";
    private static final String AUTHORIZATION_SEPARATOR = "Bearer ";

    @Override
    public String shiroName() {
        return "yuia";
    }

    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) throws Exception {
        String token = getToken((HttpServletRequest) request);
        return new OAuth2Token(token);
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        if (isLoginRequest(request, response)) {
            if (isSSOLogin(request, response)) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.trace("Login submission detected.  Attempting to execute login.");
                }
                return executeLogin(request, response);
            } else {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.trace("Login page view.");
                }
                //allow them to see the login page ;)
                return true;
            }
        } else {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.trace("Attempting to access a path which requires authentication.  Forwarding to the " +
                        "Authentication url [" + getLoginUrl() + "]");
            }
            saveRequestAndRedirectToLogin(request, response);
            return false;
        }
    }

    /**
     * 获取请求参数中的TOKEN
     */
    public String getToken(HttpServletRequest request) {
        String authToken = request.getParameter(AUTHTOKEN_HEADER);
        if (authToken == null || authToken.trim().length() == 0) {
            authToken = request.getHeader(AUTHTOKEN_HEADER);
        }
        if (authToken == null || authToken.trim().length() == 0) {
            authToken = request.getParameter(AUTHORIZATION_HEADER);
        }
        if (authToken == null || authToken.trim().length() == 0) {
            authToken = request.getHeader(AUTHORIZATION_HEADER);
        }
        if (authToken == null || "null".equals(authToken) || "undefined".equals(authToken)) {
            authToken = "";
        }
        if (authToken.startsWith(AUTHORIZATION_SEPARATOR) && authToken.length() >= 8) {
            authToken = authToken.substring(7);
        }
        return authToken;
    }

    protected boolean isSSOLogin(ServletRequest request, ServletResponse response) {
        return (request instanceof HttpServletRequest) && WebUtils.toHttp(request).getRequestURI().startsWith(SSO_LOGIN_URL_PREFIX);
    }

    @Override
    protected boolean onLoginFailure(AuthenticationToken token, AuthenticationException e, ServletRequest request, ServletResponse response) {
        HttpServletResponse res = (HttpServletResponse) response;
        res.setContentType("application/json");
        res.setCharacterEncoding("UTF-8");
        try {
            PrintWriter out = res.getWriter();
            LOGGER.error("yuia AuthenticationException username:{}, detail:{}", token.getPrincipal(), e.getClass().getSimpleName());
            out.println("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\",\"detail\":\"" + e.getClass().getSimpleName() + "\"}");
            out.flush();
            out.close();
        } catch (IOException e1) {
            LOGGER.error("yuia onLoginFailure exception", e1);
        }
        return super.onLoginFailure(token, e, request, res);
    }
}
