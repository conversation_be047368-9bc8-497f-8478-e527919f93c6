package com.yeepay.g3.app.yop.portal.controller.support.sync;

import java.io.Serializable;

/**
 * title: Api分组git同步对比<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-13 11:04
 */
public class ApiGroupGitSyncDiffDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    private String apiGroup;

    private String requestId;

    private String currentContent;

    private String latestContent;

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    public ApiGroupGitSyncDiffDTO withApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public ApiGroupGitSyncDiffDTO withRequestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    public String getCurrentContent() {
        return currentContent;
    }

    public void setCurrentContent(String currentContent) {
        this.currentContent = currentContent;
    }

    public ApiGroupGitSyncDiffDTO withCurrentContent(String currentContent) {
        this.currentContent = currentContent;
        return this;
    }

    public String getLatestContent() {
        return latestContent;
    }

    public void setLatestContent(String latestContent) {
        this.latestContent = latestContent;
    }

    public ApiGroupGitSyncDiffDTO withLatestContent(String latestContent) {
        this.latestContent = latestContent;
        return this;
    }
}
