package com.yeepay.g3.app.yop.portal.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午1:03
 */
public class AclResourceDisableResonRequestVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private List <Long> ids;

    private String disableReason;

    public List <Long> getIds() {
        return ids;
    }

    public void setIds(List <Long> ids) {
        this.ids = ids;
    }

    public String getDisableReason() {
        return disableReason;
    }

    public void setDisableReason(String disableReason) {
        this.disableReason = disableReason;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
