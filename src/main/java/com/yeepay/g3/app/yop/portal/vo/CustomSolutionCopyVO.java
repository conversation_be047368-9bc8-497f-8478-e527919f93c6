/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.CustomSolutionCopy;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * title: 复制方案VO<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/4/10
 */
@Data
public class CustomSolutionCopyVO extends CustomSolutionVO {

    private static final long serialVersionUID = -1L;

    /**
     * 源方案编码
     */
    @NotNull(groups = {CustomSolutionCopy.class})
    @Size(min = 1, max = 32, groups = {CustomSolutionCopy.class})
    private String sourceSolutionCode;
}
