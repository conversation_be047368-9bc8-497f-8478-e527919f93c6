package com.yeepay.g3.app.yop.portal.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ModelQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.ModelVO;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;
import com.yeepay.g3.facade.yop.sys.facade.ApiMgrFacade;
import com.yeepay.g3.utils.common.StringUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * title: ModelQueryServiceImpl<br/>
 * description: model查询实现<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:46
 */
@Component
public class ModelQueryServiceImpl implements ModelQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModelQueryServiceImpl.class);

    private ApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(ApiMgrFacade.class);

    @Resource(name = "modelQueryService")
    private QueryService queryService;

    private PageItemConverter<ModelPageItem> pageItemConverter = new ModelItemConverter();

    private PageItemConverter<ModelChangeRecordPageItem> changeRecordPageItemConverter = new ModelChangeRecordItemConverter();

    @Override
    public PageQueryResult<ModelPageItem> pageList(ModelPageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        QueryResult queryResult = queryService.query("pageList", queryParam);
        PageQueryResult<ModelPageItem> pageQueryResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        return pageQueryResult;
    }

    @Override
    public PageQueryResult<ModelPageItem> pageListForSp(ModelPageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> map = getBizParams(param);
        queryParam.setParams(map);
        QueryResult queryResult = queryService.query("pageListForSp", queryParam);
        PageQueryResult<ModelPageItem> pageQueryResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        return pageQueryResult;
    }

    @Override
    public String findModelDetailById(Long id) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("id", id);
        Map<String, Object> map = queryService.queryUnique("findModelDetailById", param, false);
        if (map == null || map.isEmpty()) {
            return "";
        }
        return (String) map.get("schema");
    }

    @Override
    public List<ModelVO> simpleList(String apiGroup) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiGroup", apiGroup);
        List<Map<String, Object>> list = queryService.query("pageList", bizParams);
        return convertModelVO(list);
    }

    @Override
    public PageQueryResult<ModelChangeRecordPageItem> changeRecordPageList(ModelChangeRecordPageQueryParam param) {
        Map<String, Object> paramMap = getBizParamsForChangeRecord(param);
        List<Map> pageList = queryService.query("changeRecordPageList", paramMap);
        PageQueryResult<ModelChangeRecordPageItem> result = new PageQueryResult<>();
        List<ModelChangeRecordPageItem> items = new ArrayList<>(CollectionUtils.size(pageList));
        if (CollectionUtils.isNotEmpty(pageList)) {
            pageList.forEach(map -> items.add(changeRecordPageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public PageQueryResult<ModelChangeRecordPageItem> changeRecordPageListForSp(ModelChangeRecordPageQueryParam param) {
        Map<String, Object> paramMap = getBizParamsForChangeRecord(param);
        List<Map> list = queryService.query("changeRecordPageListForSp", paramMap);
        PageQueryResult<ModelChangeRecordPageItem> result = new PageQueryResult<>();
        List<ModelChangeRecordPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(changeRecordPageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public boolean exist(String name, String apGroup) {
        QueryParam queryParam = new QueryParam();
        Map param = new HashMap();
        param.put("name", name);
        param.put("apiGroup", apGroup);
        queryParam.setParams(param);
        List<Map> list = queryService.queryList("existed", queryParam);
        return CollectionUtils.isNotEmpty(list);
    }

    @Override
    public Object codes(String apiId) {
        Object codes = "";
        ApiDTO apiDTO = apiMgrFacade.find(apiId);
        String apiGroup = apiDTO.getBasic().getApiGroup();
        String modelName = "";
        if (apiDTO.getResponse() != null && apiDTO.getResponse().getContent() != null) {
            modelName = parseModelName(apiDTO.getResponse().getContent().getSchema());
        }
        if (StringUtils.isNotBlank(modelName)) {
            Map<String, Object> param = Maps.newHashMap();
            param.put("name", modelName);
            param.put("apiGroup", apiGroup);
            Map<String, Object> map = queryService.queryUnique("findModelDetailByNameAndGroup", param, false);
            codes = map.get("schema");
        }
        return codes;
    }

    private static String parseModelName(String schema) {
        String modelName = "";
        JSONObject job = JSONUtil.parseObj(schema);
        String[] s =  job.get("$ref",String.class).split("/");
        for (String s1 : s) {
            modelName = s1;
        }
        return modelName;
    }


    private List<ModelVO> convertModelVO(List<Map<String, Object>> list) {
        List<ModelVO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return resultList;
        }
        for (Map<String, Object> map : list) {
            ModelVO modelVO = new ModelVO();
            modelVO.setId((Long) map.get("id"));
            modelVO.setName((String) map.get("name"));
            modelVO.setDescription((String) map.get("description"));
            resultList.add(modelVO);
        }
        return resultList;
    }

    private Map<String, Object> getBizParams(ModelPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("name", param.getName());
        bizParams.put("apiGroup", param.getApiGroup());
        if (CollectionUtils.isNotEmpty(param.getApiGroupCodes())) {
            bizParams.put("apiGroupCodes", param.getApiGroupCodes());
        }
        bizParams.put("description", param.getDescription());
        return bizParams;
    }

    private Map<String, Object> getBizParamsForChangeRecord(ModelChangeRecordPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("name", param.getName());
        bizParams.put("opType", param.getOpType());
        bizParams.put("apiGroup", param.getApiGroup());
        bizParams.put("operator", param.getOperator());
        bizParams.put("operatedStartDate", param.getOperatedStartDate());
        bizParams.put("operatedEndDate", param.getOperatedEndDate());
        if (CollectionUtils.isNotEmpty(param.getApiGroupCodes())) {
            bizParams.put("apiGroupCodes", param.getApiGroupCodes());
        }
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class ModelItemConverter extends BasePageItemConverter<ModelPageItem> {

        @Override
        public ModelPageItem convert(Map<String, Object> params) {
            ModelPageItem item = new ModelPageItem();
            item.setId((Long) params.get("id"));
            item.setVersion((Long) params.get("version"));
            item.setName((String) params.get("name"));
            item.setApiGroup(params.get("api_group") == null ? "" : (String) params.get("api_group"));
            item.setDescription((String) params.get("description"));
            item.setCreatedDateTime((Date) params.get("created_datetime"));
            item.setLastModifiedDateTime((Date) params.get("last_modified_datetime"));
            return item;
        }
    }

    class ModelChangeRecordItemConverter extends BasePageItemConverter<ModelChangeRecordPageItem> {

        @Override
        public ModelChangeRecordPageItem convert(Map<String, Object> params) {
            ModelChangeRecordPageItem item = new ModelChangeRecordPageItem();
            item.setId((Long) params.get("id"));
            item.setName((String) params.get("model_name"));
            item.setCreatedDateTime(((Date) params.get("created_datetime")));
            item.setApiGroup((String) params.get("api_group"));
            item.setOperator((String) params.get("operator"));
            item.setOpType((String) params.get("op_type"));
            item.setCause((String) params.get("cause"));
            return item;
        }
    }
}
