/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.utils;

import com.google.common.collect.Maps;
import com.yeepay.g3.boot.web.pojo.response.BaseResponse;

import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/10 11:39 上午
 */
public class SecurityResponse extends BaseResponse {
    private Map<String, Object> data = Maps.newHashMap();

    public SecurityResponse() {
        this("000000", "成功");
    }

    public SecurityResponse(String code, String message) {
        super(code, message);
    }

    public Map<String, Object> getData() {
        return data;
    }

    public void setData(Map<String, Object> data) {
        this.data = data;
    }

    public void put(String key, Object value) {
        this.data.put(key, value);
    }
}
