package com.yeepay.g3.app.yop.portal.service.impl;

import com.yeepay.g3.app.yop.portal.service.ErrorCodeQueryService;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.ErrorCodeVO;
import com.yeepay.g3.app.yop.portal.vo.ErrorCodeVO2;
import com.yeepay.g3.app.yop.portal.vo.NotifyErrorSolutionVO;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: ErrorCodeQueryServiceImpl
 * @Description:
 * @date 2018年5月11日 上午11:06:13
 */
@Component
public class ErrorCodeQueryServiceImpl implements ErrorCodeQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ErrorCodeQueryServiceImpl.class);

    protected JsonMapper jsonMapper = JsonMapper.nonEmptyMapper();

    @Override
    public List<ErrorCodeVO2> listErrorCodes() {
        Map<String, String> map = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_ERROR_CODES);
        List<ErrorCodeVO2> list = new ArrayList<>(map.size());
        for (Map.Entry<String, String> entry : map.entrySet()) {
            ErrorCodeVO2 errorCodeVO2 = new ErrorCodeVO2();
            errorCodeVO2.setErrorCode(entry.getKey());
            errorCodeVO2.setErrorMsg(entry.getValue());
            list.add(errorCodeVO2);
        }
        Collections.sort(list, new Comparator<ErrorCodeVO2>() {
            @Override
            public int compare(ErrorCodeVO2 type1, ErrorCodeVO2 type2) {
                return Integer.parseInt(type1.getErrorCode()) - Integer.parseInt(type2.getErrorCode());
            }
        });
        return list;
    }

    @Override
    public List<NotifyErrorSolutionVO> listNotifyErrorSolution() {
        List<NotifyErrorSolutionVO> list = new ArrayList<>();
        Map<String, String> errorCodeSolutions = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_NOTIFY_ORDER_ERROR_CODE_SOLUTION);
        for (Map.Entry<String, String> entry : errorCodeSolutions.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            Map<String, String> map = jsonMapper.fromJson(value, Map.class);
            NotifyErrorSolutionVO notifyErrorSolutionVO;
            notifyErrorSolutionVO = convertToNotifyErrorSolutionVO(map);
            notifyErrorSolutionVO.setErrorCode(key);
            list.add(notifyErrorSolutionVO);
        }
        return list;
    }

    private NotifyErrorSolutionVO convertToNotifyErrorSolutionVO(Map<String, String> map) {
        NotifyErrorSolutionVO vo = new NotifyErrorSolutionVO();
        vo.setErrorName(map.get("errorName"));
        vo.setInnerSolution(map.get("innerSolution"));
        vo.setOuterSolution(map.get("outerSolution"));
        return vo;
    }
}
