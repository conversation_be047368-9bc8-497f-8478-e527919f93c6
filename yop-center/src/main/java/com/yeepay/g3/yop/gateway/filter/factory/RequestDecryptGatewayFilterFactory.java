/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.filter.factory;

import com.google.common.collect.Maps;
import com.yeepay.g3.yop.ext.gateway.filter.GatewayFilter;
import com.yeepay.g3.yop.gateway.filter.OrderedGatewayFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * title: 请求解密过滤器工厂<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/7 18:47
 */
@Slf4j
public class RequestDecryptGatewayFilterFactory extends AbstractGatewayFilterFactory<AbstractGatewayFilterFactory.NameConfig> {

    private final int order;

    private final Map<String, GatewayFilter> requestDecryptFilters = Maps.newHashMap();

    public RequestDecryptGatewayFilterFactory(int order) {
        super(NameConfig.class);
        this.order = order;
    }

    @Override
    public GatewayFilter apply(NameConfig config) {
        log.info("RequestDecryptFilter, config:{}", config);
        return requestDecryptFilters.get(config.getName());
    }

    @Override
    public List<String> shortcutFieldOrder() {
        return Collections.singletonList(NAME_KEY);
    }

    public RequestDecryptGatewayFilterFactory requestDecryptFilter(String name, GatewayFilter requestDecryptFilter) {
        GatewayFilter gatewayFilter = requestDecryptFilter;
        if (!(requestDecryptFilter instanceof Ordered)) {
            gatewayFilter = new OrderedGatewayFilter(requestDecryptFilter, order);
        }
        this.requestDecryptFilters.put(name, gatewayFilter);
        return this;
    }

}
