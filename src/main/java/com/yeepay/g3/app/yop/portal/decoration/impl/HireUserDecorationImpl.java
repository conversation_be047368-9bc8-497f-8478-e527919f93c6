package com.yeepay.g3.app.yop.portal.decoration.impl;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeepay.g3.app.yop.portal.decoration.HireUserDecoration;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.facade.hbird.dto.UserDTO;
import com.yeepay.g3.facade.hbird.enumtype.HireTypeEnum;
import com.yeepay.g3.facade.hbird.facade.UserMgrFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * title: 员工雇佣状态拦截器<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/5/3 上午10:23
 */
@Component
public class HireUserDecorationImpl implements HireUserDecoration {

    private static final Logger LOGGER = LoggerFactory.getLogger(HireUserDecorationImpl.class);

    private LoadingCache<String, HireTypeEnum> userLocalCache = CacheBuilder.newBuilder()
            .maximumSize(500)
            .expireAfterAccess(5, TimeUnit.MINUTES)
            .build(new CacheLoader<String, HireTypeEnum>() {
                @Override
                public HireTypeEnum load(String key) throws Exception {
                    LOGGER.info("load hireType:{}.", key);
                    HireTypeEnum hireTypeEnum = HireTypeEnum.REGULAR;
                    try {
                        UserMgrFacade userMgrFacade = RemoteServiceFactory.getService(UserMgrFacade.class);
                        UserDTO user = userMgrFacade.getByUserName(key);
                        hireTypeEnum = null != user ? user.getHireType() : HireTypeEnum.UNACCEPT;
                    } catch (Exception e) {
                        LOGGER.error("load hireType failed.", e);
                        hireTypeEnum = HireTypeEnum.INTERN;
                    }
                    return hireTypeEnum;
                }
            });

    @Override
    public boolean checkHireStatus(String userName) {
        Boolean hireStatusSwitch = (Boolean) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_HIRE_STATUS_SWITCH);
        if (!hireStatusSwitch) {
            return true;
        }

        try {
            HireTypeEnum hireType = userLocalCache.get(userName);
            List<String> hireStatusAccept = (ArrayList<String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_HIRE_STATUS_ACCEPT);
            return hireStatusAccept.contains(hireType.getValue());
        } catch (Exception e) {
            LOGGER.warn("checkHireStatus has wrong ", e);
            return false;
        }
    }

}
