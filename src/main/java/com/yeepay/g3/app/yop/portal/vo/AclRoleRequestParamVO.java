package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yeepay.g3.facade.yop.perm.enums.RoleTypeEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午6:13
 */
public class AclRoleRequestParamVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    @JsonProperty(value = "type")
    private RoleTypeEnum roleType;

    @JsonProperty(value = "code")
    private String roleCode;

    @JsonProperty(value = "name")
    private String roleName;

    private String spCode;

    private Long version;

    @JsonProperty(value = "resources")
    private List<Long> resourceIds;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public RoleTypeEnum getRoleType() {
        return roleType;
    }

    public void setRoleType(RoleTypeEnum roleType) {
        this.roleType = roleType;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public List<Long> getResourceIds() {
        return resourceIds;
    }

    public void setResourceIds(List<Long> resourceIds) {
        this.resourceIds = resourceIds;
    }

    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
