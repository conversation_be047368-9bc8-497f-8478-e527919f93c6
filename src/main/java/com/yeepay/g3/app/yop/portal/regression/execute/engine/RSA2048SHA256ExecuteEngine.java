/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.regression.execute.engine;

import com.yeepay.g3.app.yop.portal.regression.YopError;
import com.yeepay.g3.app.yop.portal.regression.sdk.RegressionTestClient;
import com.yeepay.g3.app.yop.portal.regression.sdk.model.ParamTypeEnum;
import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestRequest;
import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestResponse;
import com.yeepay.g3.app.yop.portal.vo.RegressionTestRequestVO;
import com.yeepay.g3.core.yop.sdk.sample.auth.credentials.YopRSACredentials;
import com.yeepay.g3.core.yop.sdk.sample.exception.YopServiceException;
import com.yeepay.g3.core.yop.sdk.sample.http.HttpMethodName;
import com.yeepay.g3.core.yop.sdk.sample.model.RequestConfig;
import com.yeepay.g3.core.yop.sdk.sample.model.YopResponseMetadata;
import com.yeepay.g3.sdk.yop.client.YopRequest;
import com.yeepay.g3.sdk.yop.client.YopResponse;
import com.yeepay.g3.utils.common.json.JSONUtils;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateStrategyEnum;
import com.yeepay.g3.yop.frame.marshaller.YopMarshallerUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/19 下午8:49
 */
@Component
public class RSA2048SHA256ExecuteEngine extends AbstractExecuteEngine {

    @Override
    protected YopResponse doExecute(RegressionTestRequestVO vo) throws Exception {
        if (vo.isJsonRequest()) {
            return doJsonExecute(vo);
        } else {
            return doFormExecute(vo);
        }
    }

    private YopResponse doFormExecute(RegressionTestRequestVO vo) throws Exception {
        YopRequest request = new YopRequest(vo.getAppKey(), vo.getCert());
        vo.getFormParam().asMap().forEach((key, value) -> {
            value.forEach(value0 -> {
                request.addParam((String) key, value0);
            });
        });
        request.setNeedEncrypt(vo.isNeedEncrypt());
        request.setEncryptKey(vo.getEncryptPriKey());
        request.getAppSdkConfig().setServerRoot(vo.getServerRoot());
        return proxyToSdk(vo.getResourcePath(), vo.getSupportMethods(), request, true);
    }

    private YopResponse doJsonExecute(RegressionTestRequestVO vo) throws Exception {
        RegressionTestRequest jsonRequest = new RegressionTestRequest();
        jsonRequest.setHttpMethod(HttpMethodName.POST);
        jsonRequest.setResourcePath(vo.getResourcePath());
        jsonRequest.setSignAlg(vo.getSignAlg());
        jsonRequest.setParamTypeEnum(ParamTypeEnum.JSON);
        RequestConfig requestConfig = RequestConfig.Builder.aRequestConfig()
                .withAppKey(vo.getAppKey())
                .withNeedEncrypt(vo.isNeedEncrypt())
                .withCredentials(new YopRSACredentials(vo.getAppKey(), vo.getCert(), vo.getEncryptPriKey()))
                .build();
        jsonRequest.withRequestConfig(requestConfig);
        jsonRequest.setJsonParams(vo.getJsonParam());
        RegressionTestClient client = new RegressionTestClient(getConfig(vo));
        RegressionTestResponse response = null;
        try {
            response = client.execute(jsonRequest);
        } catch (YopServiceException e) {
            return adaptResponse(response, e);
        }
        return adaptResponse(response);
    }

    private String getConfig(RegressionTestRequestVO vo) {
        Map<String, Object> config = new HashMap<>();
        config.put("app_key", vo.getAppKey());
        config.put("server_root", vo.getServerRoot());
        config.put("yos_server_root", vo.getServerRoot());
        return JSONUtils.toJsonString(config);
    }

    private YopResponse adaptResponse(RegressionTestResponse newResponse) {
        return adaptResponse(newResponse, null);
    }

    public YopResponse adaptResponse(RegressionTestResponse newResponse, YopServiceException e) {
        YopResponse response = new YopResponse();
        if (null == e) {
            response.setState("SUCCESS");
            YopResponseMetadata metadata = newResponse.getMetadata();
            response.setRequestId(metadata.getYopRequestId());
            response.setTs(metadata.getDate().getTime());
            response.setResult(newResponse.getResult());
            response.setStringResult(YopMarshallerUtils.marshal(newResponse.getResult()));
            response.setValidSign(false);
        } else {
            response.setRequestId(e.getRequestId());
            response.setState("FAILURE");
            YopError error = new YopError();
            error.setCode(e.getErrorCode());
            error.setMessage(e.getErrorMessage());
            error.setSubCode(e.getSubErrorCode());
            error.setSubMessage(e.getSubMessage());
            response.setError(error);
            response.setValidSign(false);
        }
        return response;
    }

    @Override
    public String getSecurity() {
        return AuthenticateStrategyEnum.YOP_RSA2048_SHA256.getProtocolPrefix();
    }

    @Override
    public boolean supportJson() {
        return true;
    }
}
