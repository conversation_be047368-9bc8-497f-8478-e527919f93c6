/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.realm;

import com.google.common.collect.Sets;
import com.yeepay.g3.app.yop.portal.shiro.authc.TenantToken;
import com.yeepay.g3.app.yop.portal.shiro.utils.OAuth2TokenUtils;
import com.yeepay.g3.app.yop.portal.shiro.utils.TenantTokenUtils;
import com.yeepay.g3.facade.yop.perm.dto.IspOperatorDTO;
import com.yeepay.g3.facade.yop.perm.enums.OperatorStatusEnum;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.facade.yop.perm.facade.IspMgrFacade;
import com.yeepay.g3.facade.yop.perm.facade.IspOperatorMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.ApiGroupFacade;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authc.*;
import org.apache.shiro.authc.credential.AllowAllCredentialsMatcher;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * title: ShiroTenantRealm<br>
 * description: 租户认证realm<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/7/26 8:20 下午
 */
@Component
@Slf4j
public class ShiroTenantRealm extends BasePermRealm {
    public ShiroTenantRealm() {
        setAuthenticationTokenClass(TenantToken.class);
        setCredentialsMatcher(new AllowAllCredentialsMatcher());
    }

    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        return doGetAuthenticationInfo((TenantToken) token);
    }

    private AuthenticationInfo doGetAuthenticationInfo(TenantToken tenantToken) {
        tenantToken = TenantTokenUtils.verify(tenantToken.getTicket());
        //查找或创建用户
        IspOperatorMgrFacade ispOperatorMgrFacade = RemoteServiceFactory.getService(IspOperatorMgrFacade.class);
        IspOperatorDTO ispOperatorDTO = ispOperatorMgrFacade.findOrCreate(tenantToken.getTenantCode(), tenantToken.getOperatorExternal());
        if (ispOperatorDTO == null) {
            log.warn("user is not exist, userId:{}, tenant:{}.", tenantToken.getOperatorExternal(), tenantToken.getTenantCode());
            throw new UnknownAccountException("不存在该用户");
        }
        //检测本系统的用户状态
        if (ispOperatorDTO.getStatus().equals(OperatorStatusEnum.FROZEN)) {
            log.warn("user is frozen, operatorCode:{}.", ispOperatorDTO.getOperatorCode());
            throw new DisabledAccountException("用户已被冻结");
        }

        // 包含 PLATFORM_ 角色则该用户不应该是 SP 级别的
        OperatorTypeEnum userType = ispOperatorDTO.getOperatorType();
        IspMgrFacade ispMgrFacade = RemoteServiceFactory.getService(IspMgrFacade.class);
        List<String> spCodes = ispMgrFacade.findSpCodeByOperatorCode(ispOperatorDTO.getOperatorCode());
        List<String> apiGroupCodes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(spCodes)) {
            ApiGroupFacade apiGroupFacade = RemoteServiceFactory.getService(ApiGroupFacade.class);
            apiGroupCodes = apiGroupFacade.findBySpCodes(spCodes);
        }

        Set<String> spScope = new HashSet<>(spCodes);
        Set<String> apiGroupScope = new HashSet<>(apiGroupCodes);

        String accessToken = OAuth2TokenUtils.generate(ispOperatorDTO.getOperatorCode(), ispOperatorDTO.getOperatorName(), userType, spScope, apiGroupScope);
        //此处定义shiroUser controller中判断时，可以判断用户类型 shiroUtils
        return new SimpleAuthenticationInfo(new ShiroRealm.ShiroUser(ispOperatorDTO.getOperatorCode(), ispOperatorDTO.getOperatorName(),
                userType, spScope, apiGroupScope, accessToken, Sets.newHashSet(tenantToken.getTenantCode())), ispOperatorDTO.getOperatorCode(), ispOperatorDTO.getOperatorCode());
    }
}
