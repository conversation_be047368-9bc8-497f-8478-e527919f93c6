/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.CustomSolutionQueryService;
import com.yeepay.g3.app.yop.portal.service.DocQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.yeepay.g3.app.yop.portal.utils.Constants.NON_EXISTS_SP_CODE;

/**
 * title: 自定义解决方案查询实现<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/19
 */
@Component
public class CustomSolutionQueryServiceImpl implements CustomSolutionQueryService {

    PageItemConverter<CustomSolutionPageItem> pageItemConverter = new CustomSolutionPageItemConverter();
    PageItemConverter<CustomSolutionRelatedApiPageItem> relatedApiPageItemConverter = new CustomSolutionRelatedApiPageItemConverter();
    PageItemConverter<CustomSolutionUnRelatedApiPageItem> unRelatedApiPageItemConverter = new CustomSolutionUnRelatedApiPageItemConverter();

    @Resource(name = "customSolutionQueryService")
    private QueryService queryService;

    @Autowired
    private DocQueryService docQueryService;

    @Override
    public PageQueryResult<CustomSolutionPageItem> pageQuery(CustomSolutionPageQueryParam param) {
        Map<String, Object> bizParams = getBizParams(param);
        List<Map> list = queryService.query("pageList", bizParams);

        PageQueryResult<CustomSolutionPageItem> result = new PageQueryResult<>();
        List<CustomSolutionPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> docNos = new ArrayList<>(CollectionUtils.size(list));
            list.forEach(map -> {
                final CustomSolutionPageItem pageItem = pageItemConverter.convert(map);
                docNos.add(pageItem.getSolutionCode());
                items.add(pageItem);
            });
            final List<String> solutionDocs = docQueryService.listSolutionDocs(docNos);
            if (CollectionUtils.isNotEmpty(solutionDocs)) {
                items.forEach(item -> item.setHasDoc(solutionDocs.contains(item.getSolutionCode())));
            }
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public List<CommonsVO> listForDoc() {
        Map<String, Object> bizParams = Maps.newHashMap();
        if (ShiroUtils.isSpOperator()) {
            final Set<String> spCodes = ShiroUtils.getShiroUser().getSpScopes();
            if (CollectionUtils.isEmpty(spCodes)) {
                bizParams.put("spCodes", Collections.singletonList(NON_EXISTS_SP_CODE));
            } else {
                bizParams.put("spCodes", spCodes);
            }
        }
        bizParams.put("excludeSolutionCodes", toSolutionCodes(docQueryService.listSolutionDocs(Collections.emptyList())));
        List<Map> list = queryService.query("listForDoc", bizParams);
        List<CommonsVO> result = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> result.add(new CommonsVO((String) map.get("solution_code")
                    , (String) map.get("solution_name"))));
        }
        return result;
    }

    @Override
    public PageQueryResult<CustomSolutionRelatedApiPageItem> relatedApiPageQuery(CustomSolutionRelatedApiPageQueryParam param) {
        Map<String, Object> bizParams = getRelatedApiBizParams(param);
        List<Map> list = queryService.query("relatedApiPageList", bizParams);

        PageQueryResult<CustomSolutionRelatedApiPageItem> result = new PageQueryResult<>();
        List<CustomSolutionRelatedApiPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(relatedApiPageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public PageQueryResult<CustomSolutionUnRelatedApiPageItem> unRelatedApiPageQuery(CustomSolutionUnRelatedApiPageQueryParam param) {
        Map<String, Object> bizParams = getUnRelatedApiBizParams(param);
        List<Map> list = queryService.query("unRelatedApiPageList", bizParams);

        PageQueryResult<CustomSolutionUnRelatedApiPageItem> result = new PageQueryResult<>();
        List<CustomSolutionUnRelatedApiPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(unRelatedApiPageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    private List<String> toSolutionCodes(List<String> docNos) {
        return docNos;
    }

    private Map<String, Object> getBizParams(CustomSolutionPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("solutionCode", param.getSolutionCode());
        bizParams.put("solutionName", param.getSolutionName());
        if (ShiroUtils.isSpOperator()) {
            final Set<String> spCodes = ShiroUtils.getShiroUser().getSpScopes();
            if (CollectionUtils.isEmpty(spCodes)) {
                bizParams.put("spCodes", Collections.singletonList(NON_EXISTS_SP_CODE));
            } else {
                bizParams.put("spCodes", spCodes);
            }
        }
        bizParams.put("operatorCode", param.getOperatorCode());
        bizParams.put("createdStartDate", param.getCreatedStartDate());
        bizParams.put("createdEndDate", param.getCreatedEndDate());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    private Map<String, Object> getRelatedApiBizParams(CustomSolutionRelatedApiPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiTitle", param.getApiTitle());
        bizParams.put("apiUri", param.getApiUri());
        bizParams.put("solutionCode", param.getSolutionCode());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    private Map<String, Object> getUnRelatedApiBizParams(CustomSolutionUnRelatedApiPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiGroupCode", param.getApiGroupCode());
        bizParams.put("apiTitle", param.getApiTitle());
        bizParams.put("apiUri", param.getApiUri());
        bizParams.put("apiType", param.getApiType());
        bizParams.put("apiStatus", param.getApiStatus());
        bizParams.put("solutionCode", param.getSolutionCode());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class CustomSolutionPageItemConverter extends BasePageItemConverter<CustomSolutionPageItem> {

        @Override
        public CustomSolutionPageItem convert(Map<String, Object> params) {
            CustomSolutionPageItem item = new CustomSolutionPageItem();
            item.setId((Long) params.get("id"));
            item.setSolutionCode((String) params.get("solution_code"));
            item.setSolutionName((String) params.get("solution_name"));
            item.setDescription((String) params.get("description"));
            item.setOperatorCode((String) params.get("operator_code"));
            item.setSpCode((String) params.get("sp_code"));
            item.setSpName((String) params.get("sp_name"));
            item.setVersion((Long) params.get("version"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }

    class CustomSolutionRelatedApiPageItemConverter extends BasePageItemConverter<CustomSolutionRelatedApiPageItem> {

        @Override
        public CustomSolutionRelatedApiPageItem convert(Map<String, Object> params) {
            CustomSolutionRelatedApiPageItem item = new CustomSolutionRelatedApiPageItem();
            item.setId((Long) params.get("id"));
            item.setSolutionCode((String) params.get("solution_code"));
            item.setApiId((String) params.get("api_id"));
            item.setApiTitle((String) params.get("api_title"));
            item.setApiUri((String) params.get("api_uri"));
            item.setRequired((Boolean) params.get("required"));
            return item;
        }
    }

    class CustomSolutionUnRelatedApiPageItemConverter extends BasePageItemConverter<CustomSolutionUnRelatedApiPageItem> {

        @Override
        public CustomSolutionUnRelatedApiPageItem convert(Map<String, Object> params) {
            CustomSolutionUnRelatedApiPageItem item = new CustomSolutionUnRelatedApiPageItem();
            item.setApiId((String) params.get("api_id"));
            item.setApiGroupCode((String) params.get("api_group_code"));
            item.setApiGroupName((String) params.get("api_group_name"));
            item.setApiType((String) params.get("api_type"));
            item.setApiStatus((String) params.get("api_status"));
            item.setApiUri((String) params.get("api_uri"));
            item.setApiTitle((String) params.get("api_title"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }
}
