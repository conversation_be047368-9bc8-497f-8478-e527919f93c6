/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.ClassLoadModeEnum;
import com.yeepay.g3.facade.yop.sys.enums.DeployModeEnum;
import com.yeepay.g3.facade.yop.sys.enums.RpcModeEnum;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/9/25 上午10:50
 */
public class BackendAppPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private String spCode;

    private String backendCode;

    private String backendName;

    private DeployModeEnum deployMode;

    private ClassLoadModeEnum classLoadMode;

    private RpcModeEnum rpcMode;

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getBackendCode() {
        return backendCode;
    }

    public void setBackendCode(String backendCode) {
        this.backendCode = backendCode;
    }

    public String getBackendName() {
        return backendName;
    }

    public void setBackendName(String backendName) {
        this.backendName = backendName;
    }

    public DeployModeEnum getDeployMode() {
        return deployMode;
    }

    public void setDeployMode(DeployModeEnum deployMode) {
        this.deployMode = deployMode;
    }

    public ClassLoadModeEnum getClassLoadMode() {
        return classLoadMode;
    }

    public void setClassLoadMode(ClassLoadModeEnum classLoadMode) {
        this.classLoadMode = classLoadMode;
    }

    public RpcModeEnum getRpcMode() {
        return rpcMode;
    }

    public void setRpcMode(RpcModeEnum rpcMode) {
        this.rpcMode = rpcMode;
    }
}
