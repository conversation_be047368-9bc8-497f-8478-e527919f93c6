/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.dto;

import com.yeepay.g3.facade.yop.doc.enums.DocCategoryTypeEnum;
import lombok.Data;

/**
 * title: 文档分类创建参数封装<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021-04-21
 */
@Data
public class DocCategoryRequest extends BaseDTO {
    private static final long serialVersionUID = -1L;

    private Long id;
    private Long pid;
    private String docNo;
    private DocCategoryTypeEnum type;
    private String scope;
    private String code;
    private String name;
    private String desc;
}
