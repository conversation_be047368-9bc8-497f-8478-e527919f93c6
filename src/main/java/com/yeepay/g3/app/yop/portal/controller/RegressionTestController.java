/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import cn.hutool.core.thread.NamedThreadFactory;
import com.google.common.collect.Lists;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.regression.ExecutionResult;
import com.yeepay.g3.app.yop.portal.regression.execute.engine.ExecuteEngine;
import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestAppDTO;
import com.yeepay.g3.app.yop.portal.service.ApiQueryService;
import com.yeepay.g3.app.yop.portal.service.AppQueryService;
import com.yeepay.g3.app.yop.portal.service.TestCaseQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.page.ApiDefinePageItem;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.facade.yop.api.dto.ApiDefineDTO;
import com.yeepay.g3.facade.yop.api.facade.OldApiMgrFacade;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.facade.ApiGroupFacade;
import com.yeepay.g3.facade.yop.sys.facade.RegTestAtokMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.RegTestCaseMgrFacade;
import com.yeepay.g3.facade.yop.sys.facade.SecurityReqQueryFacade;
import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.yeepay.g3.app.yop.portal.vo.RegressionTestControllerVO.*;

/**
 * title: 回归测试控制器<br/>
 * description: RegressionTestController<br/>
 * Copyright: Copyright (c)2011<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019年6月20日 下午5:34:19
 */
@Controller
@RequestMapping(value = {"/rest/regression-test"})
public class RegressionTestController {

    private static final Logger logger = LoggerFactory.getLogger(RegressionTestController.class);

    private RegTestCaseMgrFacade regTestCaseMgrFacade = RemoteServiceFactory.getService(RegTestCaseMgrFacade.class);

    private RegTestAtokMgrFacade regTestAtokMgrFacade = RemoteServiceFactory.getService(RegTestAtokMgrFacade.class);

    private OldApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(OldApiMgrFacade.class);

    private ApiGroupFacade apiGroupFacade = RemoteServiceFactory.getService(ApiGroupFacade.class);

    private SecurityReqQueryFacade securityReqQueryFacade = RemoteServiceFactory.getService(SecurityReqQueryFacade.class);

    private static final ThreadPoolExecutor TC_POOL = new ThreadPoolExecutor(1, Runtime.getRuntime().availableProcessors() + 1,
            60, TimeUnit.SECONDS, new LinkedBlockingQueue(50), new NamedThreadFactory("regression-test", false));

    @Autowired
    private ApiQueryService apiQueryService;

    @Autowired
    @Qualifier("routeExecuteEngine")
    private ExecuteEngine executeEngine;

    @Autowired
    private AppQueryService appQueryService;

    @Autowired
    private TestCaseQueryService testCaseQueryService;

    /**
     * 回归测试首页
     * 显示api列表及每个api回归用例个数
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/test/list", method = RequestMethod.GET)
    public ResponseMessage listPost(@RequestParam(value = "apiGroups", required = false) String[] apiGroups, @RequestParam(value = "apiUri", required = false) String apiUri) {
        List<ApiDefinePageItem> apis = null;
        if (ShiroUtils.isPlatformOperator()) {
            apis = apiQueryService.list(apiGroups);
        } else {
            apis = apiQueryService.listForSp(apiGroups);
        }
        List<ListPost> result = Lists.newArrayList();
        String[] apiUris;
        if (StringUtils.isNotEmpty(apiUri)) {
            apiUris = new String[1];
            apiUris[0] = apiUri;
        } else {
            apiUris = new String[apis.size()];
            for (int i = 0; i < apis.size(); i++) {
                apiUris[i] = apis.get(i).getApiUri();
            }
        }
        Map<String, Integer> caseCountMap = testCaseQueryService.statRegTestCountByApiUris(apiUris);
        for (ApiDefinePageItem api : apis) {
            Integer caseCount = caseCountMap.get(api.getApiUri());
            if (caseCount == null || caseCount == 0) {
                continue;
            }
            result.add(new ListPost(api, caseCount));
        }
        return new ResponseMessage().put("list", result);
    }

    @ResponseBody
    @RequestMapping(value = "/test/execute-batch", method = RequestMethod.POST)
    public ResponseMessage executeBatch(@RequestBody BatchExecutionVO batchExecutionVO) {
        String loginName = ShiroUtils.getOperatorCode();

        //1、查询待回归的测试用例列表
        List<RegTestCaseFindRespDTO> testCases = regTestCaseMgrFacade.findByApiUris(batchExecutionVO.apiUriList);

        //2、创建执行历史及记录
        RegTestExecCreateReqDTO reqDTO = new RegTestExecCreateReqDTO();
        reqDTO.setOperator(loginName);
        reqDTO.setEnvironment(batchExecutionVO.environment);
        reqDTO.setStartExecuteTime(new Date());
        List<RegTestExecCreateReqDTO.ExecRecordDTO> recordList = new ArrayList<>();
        for (RegTestCaseFindRespDTO respDTO : testCases) {
            RegTestExecCreateReqDTO.ExecRecordDTO execRecordDTO = new RegTestExecCreateReqDTO.ExecRecordDTO();
            execRecordDTO.setApiUri(respDTO.getApiUri());
            execRecordDTO.setTestCaseId(respDTO.getId());
            execRecordDTO.setTestCaseTitle(respDTO.getTitle());
            recordList.add(execRecordDTO);
        }
        reqDTO.setRecordList(recordList);
        RegTestExecCreateRespDTO respDTO = regTestCaseMgrFacade.createExec(reqDTO);
        Long hisId = respDTO.getHistoryId();
        Map<Object, Object> recordMap = respDTO.getRecordMap();

        //3、开始发起回归调用
        try {
            TC_POOL.submit(() -> {
                try {
                    for (RegTestCaseFindRespDTO testCase : testCases) {
                        logger.info("operator is running test case:{} with hisId:{} and recordId:{}", testCase, hisId, recordMap.get(testCase.getId()));
                        testCase.setEnvironment(batchExecutionVO.environment);
                        ExecutionResult executionResult = null;
                        RegTestExecRecordUpdateReqDTO regTestExecRecordUpdateReqDTO = new RegTestExecRecordUpdateReqDTO();
                        regTestExecRecordUpdateReqDTO.setId((Long) recordMap.get(testCase.getId()));
                        try {
                            testCase.setJsonRequestParam(null);// 走form
                            executionResult = executeEngine.execute(testCase);
                            regTestExecRecordUpdateReqDTO.setFinishExecuteTime(new Date());
                            regTestExecRecordUpdateReqDTO.setAssertionList(executionResult.getAssertionList());
                            regTestExecRecordUpdateReqDTO.setPassed(executionResult.isSuccess());
                            if (executionResult.getResponse().getError() != null) {
                                regTestExecRecordUpdateReqDTO.setErrorMsg(executionResult.getResponse().getError().getSubMessage());
                            }
                        } catch (Exception e) {
                            logger.error("error run record test case", e);
                            regTestExecRecordUpdateReqDTO.setFinishExecuteTime(new Date());
                            regTestExecRecordUpdateReqDTO.setPassed(false);
                            regTestExecRecordUpdateReqDTO.setErrorMsg(e.getMessage());
                        } finally {
                            regTestCaseMgrFacade.updateExecRecord(regTestExecRecordUpdateReqDTO);
                        }
                    }
                } catch (Exception e) {
                    logger.error("error run test case", e);
                } finally {
                    RegTestExecHisUpdateReqDTO regTestExecHisUpdateReqDTO = new RegTestExecHisUpdateReqDTO();
                    regTestExecHisUpdateReqDTO.setId(hisId);
                    regTestExecHisUpdateReqDTO.setStatus("finished");
                    regTestExecHisUpdateReqDTO.setFinishExecuteTime(new Date());
                    regTestCaseMgrFacade.updateExecHis(regTestExecHisUpdateReqDTO);
                }
            });
        } catch (RejectedExecutionException e) {
            logger.error("regression test execution rejected!", e);
            return new ResponseMessage(ResponseMessage.Status.ERROR, e.getMessage());
        }
        return new ResponseMessage().put("batchExecutionId", hisId);
    }

    @ResponseBody
    @RequestMapping(value = "/test/query-execution-result", method = RequestMethod.GET)
    public ResponseMessage queryExecutionResult(@RequestParam("batchExecutionId") Long batchExecutionId) {
        try {
            RegTestExecHisVO regTestExecHisVO = testCaseQueryService.findExecHisByHisId(batchExecutionId);
            List<ExecHisStatResult> executionList = testCaseQueryService.execHisStatByHistoryId(batchExecutionId);
            return new ResponseMessage().put("executionResult", new QueryExecutionResult(regTestExecHisVO, executionList));
        } catch (Exception e) {
            logger.error("error query execution result with hisId:" + batchExecutionId, e);
            return new ResponseMessage(ResponseMessage.Status.ERROR, e.getMessage());
        }
    }

    @RequestMapping(value = "/case/list", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public ResponseMessage queryList(@RequestParam("apiGroupCode") String apiGroupCode) {
        List<ApiGroupControllerVO.ApiGroupListItemVO.ApiVO> result = Lists.newArrayList();

        String[] apiGroupCodes = new String[]{apiGroupCode};
        List<ApiDefinePageItem> apis = null;
        if (ShiroUtils.isPlatformOperator()) {
            apis = apiQueryService.list(apiGroupCodes);
        } else {
            apis = apiQueryService.listForSp(apiGroupCodes);
        }

        if (Collections3.isNotEmpty(apis)) {
            apis.forEach(api -> {
                boolean idempotent = false;
                if (api.getTags() != null) {
                    List<String> tags = Arrays.asList(api.getTags());
                    if (Collections3.isNotEmpty(tags) && tags.contains("idempotent")) {
                        idempotent = true;
                    }
                }
                result.add(new ApiGroupControllerVO.ApiGroupListItemVO.ApiVO(api.getApiId(), api.getApiUri(), api.getApiTitle(), idempotent));
            });
        }
        return new ResponseMessage().put("api-group-query-list", result);
    }

    @ResponseBody
    @RequestMapping(value = "/test/log", method = {RequestMethod.POST, RequestMethod.GET})
    public ResponseMessage log(@RequestParam("apiUri") String apiUri,
                               @RequestParam("batchExecutionId") Long batchExecutionId) {
        try {
            List<RegTestExecRecordQryRespDTO> recordList = regTestCaseMgrFacade.findExecRecordByHisIdAndApiUri(batchExecutionId, apiUri);
            List<ExecutionLog> result = Lists.newArrayList();
            for (RegTestExecRecordQryRespDTO record : recordList) {
                result.add(new ExecutionLog(record));
            }
            return new ResponseMessage().put("log", result);
        } catch (Exception e) {
            logger.error("error query execution log with apiUri:" + apiUri + " and hisId:" + batchExecutionId, e);
            return new ResponseMessage(ResponseMessage.Status.ERROR, e.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value = "/case/list-single-api", method = {RequestMethod.POST, RequestMethod.GET})
    public ResponseMessage listSingleApiPost(@RequestParam("apiUri") String apiUri) {
        List<RegTestCaseFindRespDTO> testCases = regTestCaseMgrFacade.findByApiUri(apiUri);
        List<ListSingleApi> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(testCases)) {
            for (RegTestCaseFindRespDTO testcase : testCases) {
                result.add(new ListSingleApi(testcase));
            }
        }
        return new ResponseMessage().put("list", result);
    }

    @ResponseBody
    @RequestMapping(value = "/case/detail", method = {RequestMethod.POST, RequestMethod.GET})
    public ResponseMessage detail(@RequestParam(value = "id", required = false) Long id,
                                  @RequestParam(value = "apiUri", required = false) String apiUri) {
        RegTestCaseVO testCase = new RegTestCaseVO();
        if (id != null) {
            testCase = testCaseQueryService.findTestCaseById(id);
            if (testCase.getTokenId() != null) {
                RegTestAtokDetailFindReqDTO reqDTO = new RegTestAtokDetailFindReqDTO();
                reqDTO.setTokenId(testCase.getTokenId());
                RegTestAtokDetailFindRespDTO respDTO = regTestAtokMgrFacade.findDetailById(reqDTO);
                testCase.setTokenName(respDTO == null ? "" : respDTO.getTokenName());
            }
            apiUri = testCase.getApiUri();
        }

        ApiDefineDTO apiDefine = apiMgrFacade.findApiByUri(apiUri);
        ApiVO apiVO = new ApiVO();
        BeanUtils.copyProperties(apiDefine, apiVO);

        Map<String, SecurityReqDTO> apiSecurity = securityReqQueryFacade.findByApiUri(apiDefine.getApiUri());
        if (null == apiSecurity || MapUtils.isEmpty(apiSecurity)) {
            ApiGroupDTO apiGroup = apiGroupFacade.findByApiGroupCode(apiDefine.getApiGroup());
            List<SecurityReqDTO> security = apiGroup.getSecurity();
            apiVO.setApiSecurity(security);
        } else {
            apiVO.setApiSecurity(new ArrayList<>(apiSecurity.values()));
        }

        List<RegressionTestAppDTO> apps = appQueryService.listPlatformTest();
        return new ResponseMessage("detail", new Detail(testCase, apiVO, apps, id == null));
    }

    @ResponseBody
    @RequestMapping(value = "/case/execute-single-by-raw", method = RequestMethod.POST)
    public ResponseMessage executeSingleByRaw(@RequestBody RegressionTestCaseVO testCase, HttpServletRequest request) {
        String loginName = ShiroUtils.getOperatorCode();
        try {
            logger.info("operator:{} is running test case:{}", loginName, testCase);
            ExecutionResult executionResult = executeEngine.execute(convert(testCase));
            return new ResponseMessage().put("executionResult", new ExecuteSingleByRaw(executionResult));
        } catch (Exception e) {
            logger.error("error run test case", e);
            return new ResponseMessage(ResponseMessage.Status.ERROR, e.getMessage());
        }
    }


    @ResponseBody
    @RequestMapping(value = "/case/save", method = RequestMethod.POST)
    public ResponseMessage save(@RequestBody RegressionTestCaseVO testCase) {
        try {
            if (testCase.getId() == null) {
                regTestCaseMgrFacade.create(convertCreateDTO(testCase));
            } else {
                regTestCaseMgrFacade.update(convertUpdateDTO(testCase));
            }
        } catch (Exception e) {
            logger.error("error create/update test case", e);
            return new ResponseMessage(ResponseMessage.Status.ERROR, e.getMessage());
        }
        return new ResponseMessage();
    }

    /**
     * vo -> testCaseDTO
     *
     * @param vo
     * @return
     */
    private RegTestCaseFindRespDTO convert(RegressionTestCaseVO vo) {
        RegTestCaseFindRespDTO dto = new RegTestCaseFindRespDTO();
        BeanUtils.copyProperties(vo, dto);
        dto.setJsonRequestParam(vo.getParams().json);
        if (Collections3.isNotEmpty(vo.getParams().form)) {
            List<FormRequestParamDTO> formParams = getFormRequestParamDTOS(vo);
            dto.setFormRequestParam(formParams);
        }
        return dto;
    }

    /**
     * vo -> testCaseCreateDTO
     *
     * @param vo
     * @return
     */
    private RegTestCaseCreateReqDTO convertCreateDTO(RegressionTestCaseVO vo) {
        RegTestCaseCreateReqDTO dto = new RegTestCaseCreateReqDTO();
        BeanUtils.copyProperties(vo, dto);
        dto.setJsonRequestParam(vo.getParams().json);
        if (Collections3.isNotEmpty(vo.getParams().form)) {
            List<FormRequestParamDTO> formParams = getFormRequestParamDTOS(vo);
            dto.setFormRequestParam(formParams);
        }
        return dto;
    }

    /**
     * vo -> testCaseUpdateDTO
     *
     * @param vo
     * @return
     */
    private RegTestCaseUpdateReqDTO convertUpdateDTO(RegressionTestCaseVO vo) {
        RegTestCaseUpdateReqDTO dto = new RegTestCaseUpdateReqDTO();
        BeanUtils.copyProperties(vo, dto);
        dto.setJsonRequestParam(vo.getParams().json);
        if (Collections3.isNotEmpty(vo.getParams().form)) {
            List<FormRequestParamDTO> formParams = getFormRequestParamDTOS(vo);
            dto.setFormRequestParam(formParams);
        }
        return dto;
    }

    private List<FormRequestParamDTO> getFormRequestParamDTOS(RegressionTestCaseVO vo) {
        List<FormRequestParamDTO> formParams = new ArrayList<>();
        for (Map m : vo.getParams().form) {
            Object name = m.get("name");
            Object value = m.get("value");
            formParams.add(new FormRequestParamDTO(name.toString(), value == null ? null : value.toString()));
        }
        return formParams;
    }

    @ResponseBody
    @RequestMapping(value = "/case/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam("caseIds") Long[] caseIds) {
        logger.info("operator:{} is trying to delete test case with ids:{}", ShiroUtils.getOperatorCode(), caseIds);
        try {
            regTestCaseMgrFacade.delete(caseIds);
        } catch (Exception e) {
            logger.error("error delete test case,id:{}", caseIds);
            return new ResponseMessage(ResponseMessage.Status.ERROR, e.getMessage());
        }
        return new ResponseMessage();
    }

    /**
     * 执行环境列表
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/commons/env/list", method = RequestMethod.GET)
    public ResponseMessage envList() {
        Map<String, String> envs = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_REGRESSION_TEST_ENVS);
        List<ExecutionEnvironmentVO> result =envs.entrySet().stream()
                .map(entry -> new ExecutionEnvironmentVO(entry.getKey(), entry.getValue()))
                .collect(Collectors.toList());
        return new ResponseMessage().put("env", result);
    }

    /**
     * 查询token列表
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/commons/token/list", method = RequestMethod.GET)
    public ResponseMessage atokList(@RequestParam("apiGroup") String apiGroup) {
        RegTestAtokFindReqDTO reqDTO = new RegTestAtokFindReqDTO();
        reqDTO.setApiGroup(apiGroup);
        List<RegTestAtokFindRespDTO> list = regTestAtokMgrFacade.findByApiGroup(reqDTO);
        return new ResponseMessage().put("result", list);
    }

    /**
     * 查询token
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/token/detail", method = RequestMethod.GET)
    public ResponseMessage atokDetail(@RequestParam("tokenId") Long tokenId) {
        RegTestAtokDetailFindReqDTO reqDTO = new RegTestAtokDetailFindReqDTO();
        reqDTO.setTokenId(tokenId);
        RegTestAtokDetailFindRespDTO respDTO = regTestAtokMgrFacade.findDetailById(reqDTO);
        return new ResponseMessage().put("result", respDTO);
    }

    /**
     * 更新token
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/token/update", method = RequestMethod.POST)
    public ResponseMessage atokUpdate(@RequestBody RegTestAtokVO regTestAtokVO) {
        if (regTestAtokVO.getTokenId() == null) {
            RegTestAtokSaveReqDTO reqDTO = new RegTestAtokSaveReqDTO();
            BeanUtils.copyProperties(regTestAtokVO, reqDTO);
            regTestAtokMgrFacade.saveAtok(reqDTO);
        } else {
            RegTestAtokUpdateReqDTO reqDTO = new RegTestAtokUpdateReqDTO();
            BeanUtils.copyProperties(regTestAtokVO, reqDTO);
            regTestAtokMgrFacade.updateAtok(reqDTO);
        }
        return new ResponseMessage();
    }

    /**
     * 删除token
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/token/delete", method = RequestMethod.POST)
    public ResponseMessage atokUpdate(@RequestParam("tokenId") Long tokenId) {
        RegTestAtokDeleteReqDTO reqDTO = new RegTestAtokDeleteReqDTO();
        reqDTO.setTokenId(tokenId);
        regTestAtokMgrFacade.deleteAtok(reqDTO);
        return new ResponseMessage();
    }

    /**
     * 测试用例导出
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/case/export", method = RequestMethod.POST)
    public ResponseMessage caseExport(@RequestParam("apiUris") String[] apiUris) {
        List<RegTestCaseImExportDTO> resList = regTestCaseMgrFacade.exportByApiUris(apiUris);
        return new ResponseMessage().put("result", resList);
    }

    /**
     * 测试用例导入
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/case/import", method = RequestMethod.POST)
    public ResponseMessage caseImport(@RequestBody TestCaseImportVO testCaseImportVO) {
        RegTestCaseImportRespDTO respDTO = regTestCaseMgrFacade.importRegressiveCase(testCaseImportVO.getCaseList());
        return new ResponseMessage().put("result", respDTO);
    }

}
