/*
 * Copyright: Copyright (c)2014
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.ext.gateway.xmgj.context.param;

import com.yeepay.g3.yop.ext.gateway.context.param.ParamContext;
import com.yeepay.g3.yop.ext.gateway.context.param.ParamContextParser;
import com.yeepay.g3.yop.ext.gateway.context.protocol.ProtocolMakerEnum;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.xmgj.context.param.impl.XmgjJsonContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/8/6
 */
@Component
@Slf4j
public class XmgjParamContextParser implements ParamContextParser {

    @Override
    public ParamContext parse(ServerWebExchange exchange) {
        // 定制协议 需根据api定义组装参数，见RequestParamConvertFilter
        return new XmgjJsonContext(exchange);
    }

    @Override
    public ProtocolMakerEnum getProtocolMaker() {
        return ProtocolMakerEnum.XMGJ;
    }
}
