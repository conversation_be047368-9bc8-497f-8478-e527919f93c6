package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * title:用户有权限的资源 <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2016<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/7/23 14:20
 */
public class OperatorResourceResultVO implements Serializable {

    private static final long serialVersionUID = -1289078265304912533L;

    @JsonProperty("menus")
    private List<AclMenuRefVO> aclMenuRefVOS;

    @JsonProperty("buttons")
    private List<AclButtonVO> aclButtonVOS;

    public List<AclMenuRefVO> getAclMenuRefVOS() {
        return aclMenuRefVOS;
    }

    public void setAclMenuRefVOS(List<AclMenuRefVO> aclMenuRefVOS) {
        this.aclMenuRefVOS = aclMenuRefVOS;
    }

    public List<AclButtonVO> getAclButtonVOS() {
        return aclButtonVOS;
    }

    public void setAclButtonVOS(List<AclButtonVO> aclButtonVOS) {
        this.aclButtonVOS = aclButtonVOS;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
