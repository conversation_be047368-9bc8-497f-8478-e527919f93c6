/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/5 18:41
 */
@Data
public class CfcaCertParamVO implements Serializable {
    private static final long serialVersionUID = -1L;

    private String customerNo;
    private String serialNo;
    private String status;
    private Boolean used;
}
