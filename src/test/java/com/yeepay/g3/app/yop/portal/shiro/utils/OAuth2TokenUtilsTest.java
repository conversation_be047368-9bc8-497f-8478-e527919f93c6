package com.yeepay.g3.app.yop.portal.shiro.utils;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.shiro.authc.OAuth2Token;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import org.junit.Before;
import org.junit.Test;

import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/7/26 下午12:27
 */
public class OAuth2TokenUtilsTest {

    @Before
    public void setUp() throws Exception {
        System.setProperty("mcokconfig", "true");
        ConfigUtils.init();
    }

    @Test
    public void verify() {
        String userId = "baitao.ji";
        String userName = "柏涛";
        OperatorTypeEnum userType = OperatorTypeEnum.PLATFORM;
        Set<String> spScope = new HashSet<>();
        Set<String> apiGroupScope = new HashSet<>();
        String accessToken = OAuth2TokenUtils.generate(userId, userName, userType, spScope, apiGroupScope);

        OAuth2Token oAuth2Token = OAuth2TokenUtils.verify(accessToken);
        assertEquals(userId, oAuth2Token.getUserId());
        assertEquals(userName, oAuth2Token.getUserName());
        assertEquals(userType, oAuth2Token.getUserType());
        assertTrue(0 == oAuth2Token.getSpScopes().size());
        assertTrue(0 == oAuth2Token.getApiGroupScopes().size());
    }
}