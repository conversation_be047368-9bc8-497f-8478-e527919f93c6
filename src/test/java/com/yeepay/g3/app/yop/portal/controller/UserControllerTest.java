/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.UserSearchService;
import com.yeepay.g3.app.yop.portal.vo.UserVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * title: UserController测试<br>
 * description: UserController单元测试<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-26
 */
@RunWith(MockitoJUnitRunner.class)
public class UserControllerTest {

    @Mock
    private UserSearchService userSearchService;

    @InjectMocks
    private UserController userController;

    private List<UserVO> mockUsers;

    @Before
    public void setUp() {
        // 准备测试数据
        UserVO user1 = new UserVO("12345", "张三", "<EMAIL>", "技术部", true);
        UserVO user2 = new UserVO("67890", "李四", "<EMAIL>", "产品部", true);
        mockUsers = Arrays.asList(user1, user2);
    }

    @Test
    public void testSearchUsersWithValidKeyword() {
        // 模拟服务返回
        when(userSearchService.searchUsersForVO(eq("张三"), eq(10))).thenReturn(mockUsers);

        // 执行测试
        ResponseMessage<List<UserVO>> response = userController.searchUsers("张三", 10);

        // 验证结果
        assertNotNull(response);
        assertEquals("users", response.getState());
        assertNotNull(response.getResult());
        assertEquals(2, response.getResult().size());
        assertEquals("张三", response.getResult().get(0).getName());
        assertEquals("李四", response.getResult().get(1).getName());
    }

    @Test
    public void testSearchUsersWithEmptyKeyword() {
        // 执行测试
        ResponseMessage<List<UserVO>> response = userController.searchUsers("", 10);

        // 验证结果
        assertNotNull(response);
        assertEquals("error", response.getState());
        assertEquals("搜索关键词不能为空", response.getResult());
    }

    @Test
    public void testSearchUsersWithNullKeyword() {
        // 执行测试
        ResponseMessage<List<UserVO>> response = userController.searchUsers(null, 10);

        // 验证结果
        assertNotNull(response);
        assertEquals("error", response.getState());
        assertEquals("搜索关键词不能为空", response.getResult());
    }

    @Test
    public void testSearchUsersWithLongKeyword() {
        // 构造超长关键词（101个字符）
        String longKeyword = "a".repeat(101);

        // 执行测试
        ResponseMessage<List<UserVO>> response = userController.searchUsers(longKeyword, 10);

        // 验证结果
        assertNotNull(response);
        assertEquals("error", response.getState());
        assertEquals("搜索关键词长度不能超过100个字符", response.getResult());
    }

    @Test
    public void testSearchUsersWithDefaultLimit() {
        // 模拟服务返回
        when(userSearchService.searchUsersForVO(eq("test"), eq(10))).thenReturn(mockUsers);

        // 执行测试（不传limit参数，应该使用默认值10）
        ResponseMessage<List<UserVO>> response = userController.searchUsers("test", null);

        // 验证结果
        assertNotNull(response);
        assertEquals("users", response.getState());
        assertNotNull(response.getResult());
    }

    @Test
    public void testSearchUsersWithLargeLimit() {
        // 模拟服务返回
        when(userSearchService.searchUsersForVO(eq("test"), eq(100))).thenReturn(mockUsers);

        // 执行测试（传入超大limit，应该被限制为100）
        ResponseMessage<List<UserVO>> response = userController.searchUsers("test", 200);

        // 验证结果
        assertNotNull(response);
        assertEquals("users", response.getState());
        assertNotNull(response.getResult());
    }
}
