/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.CertPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.CertPageParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/14 上午10:34
 */
public interface CertQueryService {

    PageQueryResult<CertPageItem> pageQuery(CertPageParam param);
}
