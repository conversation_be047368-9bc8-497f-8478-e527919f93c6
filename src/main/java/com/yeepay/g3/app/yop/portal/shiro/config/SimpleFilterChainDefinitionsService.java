package com.yeepay.g3.app.yop.portal.shiro.config;


import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.yop.perm.facade.AclResourceMgrFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/7/25 下午3:25
 */
@Component
public class SimpleFilterChainDefinitionsService extends AbstractFilterChainDefinitionsService {

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();

    private AclResourceMgrFacade aclResourceMgrFacade = RemoteServiceFactory.getService(AclResourceMgrFacade.class);

    @Override
    protected Map<String, String> obtainPermissionFromDB() {
        Map<String, String> filterChainDefinitionMap = Maps.newLinkedHashMap();
        // TODO
        Map<String, String> resourceFilterChain = aclResourceMgrFacade.findAllResourceFilterChain();
        filterChainDefinitionMap.putAll(resourceFilterChain);
        return filterChainDefinitionMap;
    }

    @Override
    protected Map<String, String> obtainPermissionFromConfig() {
        String filterChainDefinition = (String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_RESOURCE_PERM_NEW);
        Map<String, String> filterChainDefinitionMap = JSON_MAPPER.fromJson(filterChainDefinition, Map.class);
        return filterChainDefinitionMap;
    }
}
