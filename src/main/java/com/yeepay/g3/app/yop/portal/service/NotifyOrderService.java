package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.NotifyOrderQueryParam;
import com.yeepay.g3.app.yop.portal.vo.notify.ResendVO;
import com.yeepay.g3.app.yop.portal.vo.notify.SyncVO;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/9 11:49 上午
 */
public interface NotifyOrderService {

    /**
     * 重发通知
     *
     * @param resendVO
     */
    void resend(ResendVO resendVO);

    /**
     * 批量重发
     *
     * @param resendVOList
     */
    void batchResend(List<ResendVO> resendVOList);

    void queryAndResend(NotifyOrderQueryParam notifyOrderQueryParam);

    /**
     * 同步订单状态
     *
     * @param syncVO
     * @return
     */
    String sync(SyncVO syncVO);
}
