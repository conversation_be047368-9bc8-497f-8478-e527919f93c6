package com.yeepay.g3.app.yop.portal.dto;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * title:
 * description:文件上传响应DTO
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  2019年5月15 上午10:57
 */
public class FileUploadResponseDTO implements Serializable {

    private static final Long serialVersionUID = -1L;

    private Integer code;

    private String message;

    private FailedUploadFileDTO data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public FailedUploadFileDTO getData() {
        return data;
    }

    public void setData(FailedUploadFileDTO data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
