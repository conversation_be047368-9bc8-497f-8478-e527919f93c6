package com.yeepay.g3.app.yop.portal.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2016<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/8/1 16:05
 */
public enum AclOperStatusEnum {

    SUCCESS("SUCCESS", "操作成功"),
    NOT_FOUND("NOT_FOUND", "找不到功能"),
    UNAUTHORIZED("UNAUTHORIZED", "操作受限"),
    FORBIDDEN("FORBIDDEN", "拒绝访问"),
    UNKNOWN("UNKNOWN", "失败");

    private static final Map<String, AclOperStatusEnum> VALUE_MAP;

    static {
        VALUE_MAP = new HashMap<String, AclOperStatusEnum>();
        for (AclOperStatusEnum authType : AclOperStatusEnum.values()) {
            VALUE_MAP.put(authType.getValue(), authType);
        }
    }

    private String value;

    private String displayName;

    AclOperStatusEnum(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static Map<String, AclOperStatusEnum> getValueMap() {
        return VALUE_MAP;
    }

    public static AclOperStatusEnum parse(String value) {
        return VALUE_MAP.get(value);
    }

    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

}
