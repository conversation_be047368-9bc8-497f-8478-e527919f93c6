package com.yeepay.g3.app.yop.portal.utils.swagger;

import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;
import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;
import com.yeepay.g3.facade.yop.sys.dto.SpiDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;
import java.util.Map;

/**
 * title: Swagger分析结果<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-06-18 16:49
 */
public class SwaggerAnalysisResult {

    private String apiGroup;

    private Map<ApiRequestKey, ApiDTO> apis;

    private List<ApiAnalysisFailedInfo> failedApiInfo;

    private Map<String, SpiDTO> callbacks;

    private Map<String, List<ApiRequestKey>> callbackApiRelations;

    private List<CallbackAnalysisFailedInfo> failedCallBackInfo;

    private Map<String, ModelDTO> models;

    private List<ModelAnalysisFailedInfo> failedModelInfo;

    private Boolean nativeSwagger;

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    public SwaggerAnalysisResult withApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public Map<ApiRequestKey, ApiDTO> getApis() {
        return apis;
    }

    public void setApis(Map<ApiRequestKey, ApiDTO> apis) {
        this.apis = apis;
    }

    public SwaggerAnalysisResult withApis(Map<ApiRequestKey, ApiDTO> apis) {
        this.apis = apis;
        return this;
    }

    public List<ApiAnalysisFailedInfo> getFailedApiInfo() {
        return failedApiInfo;
    }

    public void setFailedApiInfo(List<ApiAnalysisFailedInfo> failedApiInfo) {
        this.failedApiInfo = failedApiInfo;
    }

    public SwaggerAnalysisResult withFailedApiInfo(List<ApiAnalysisFailedInfo> failedApiInfo) {
        this.failedApiInfo = failedApiInfo;
        return this;
    }

    public Map<String, SpiDTO> getCallbacks() {
        return callbacks;
    }

    public void setCallbacks(Map<String, SpiDTO> callbacks) {
        this.callbacks = callbacks;
    }

    public SwaggerAnalysisResult withCallbacks(Map<String, SpiDTO> callbacks) {
        this.callbacks = callbacks;
        return this;
    }

    public Map<String, List<ApiRequestKey>> getCallbackApiRelations() {
        return callbackApiRelations;
    }

    public void setCallbackApiRelations(Map<String, List<ApiRequestKey>> callbackApiRelations) {
        this.callbackApiRelations = callbackApiRelations;
    }

    public SwaggerAnalysisResult withCallbackApiRelations(Map<String, List<ApiRequestKey>> callbackApiRelations) {
        this.callbackApiRelations = callbackApiRelations;
        return this;
    }

    public List<CallbackAnalysisFailedInfo> getFailedCallBackInfo() {
        return failedCallBackInfo;
    }

    public void setFailedCallBackInfo(List<CallbackAnalysisFailedInfo> failedCallBackInfo) {
        this.failedCallBackInfo = failedCallBackInfo;
    }

    public SwaggerAnalysisResult withFailedCallBackInfo(List<CallbackAnalysisFailedInfo> failedCallBackInfo) {
        this.failedCallBackInfo = failedCallBackInfo;
        return this;
    }

    public Map<String, ModelDTO> getModels() {
        return models;
    }

    public void setModels(Map<String, ModelDTO> models) {
        this.models = models;
    }

    public SwaggerAnalysisResult withModels(Map<String, ModelDTO> models) {
        this.models = models;
        return this;
    }

    public List<ModelAnalysisFailedInfo> getFailedModelInfo() {
        return failedModelInfo;
    }

    public void setFailedModelInfo(List<ModelAnalysisFailedInfo> failedModelInfo) {
        this.failedModelInfo = failedModelInfo;
    }

    public SwaggerAnalysisResult withFailedModelInfo(List<ModelAnalysisFailedInfo> failedModelInfo) {
        this.failedModelInfo = failedModelInfo;
        return this;
    }

    public Boolean isNativeSwagger() {
        return nativeSwagger;
    }

    public void setNativeSwagger(Boolean nativeSwagger) {
        this.nativeSwagger = nativeSwagger;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
