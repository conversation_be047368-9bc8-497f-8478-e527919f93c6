package com.yeepay.g3.app.yop.portal.controller.support.imports;

import java.io.Serializable;
import java.util.List;

/**
 * title: Spi导入请求<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 17:51
 */
public class SpisImportRequest implements Serializable {

    private static final long serialVersionUID = -1L;

    private String requestId;

    private List<String> spisToOverride;

    private List<String> modelsToOverride;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public List<String> getSpisToOverride() {
        return spisToOverride;
    }

    public void setSpisToOverride(List<String> spisToOverride) {
        this.spisToOverride = spisToOverride;
    }

    public List<String> getModelsToOverride() {
        return modelsToOverride;
    }

    public void setModelsToOverride(List<String> modelsToOverride) {
        this.modelsToOverride = modelsToOverride;
    }
}
