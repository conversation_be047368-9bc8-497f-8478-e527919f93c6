package com.yeepay.g3.yop.ext.gateway.context.protocol.response;

import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.ext.gateway.context.protocol.ProtocolMakerEnum;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;

import javax.servlet.http.HttpServletResponse;

/**
 * title: 返回结果序列化接口<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/10/13 11:06
 */
public interface ResponseMarshaller {

    /**
     * 序列化结果(上下文正常初始化的情况下)
     *
     * @param exchange
     * @param response        内部返回
     * @param servletResponse http返回
     */
    void marshal(ServerWebExchange exchange, Response response, HttpServletResponse servletResponse);

    /**
     * 支持的协议版本
     *
     * @return 协议版本
     */
    ProtocolMakerEnum supportProtocolMaker();

}
