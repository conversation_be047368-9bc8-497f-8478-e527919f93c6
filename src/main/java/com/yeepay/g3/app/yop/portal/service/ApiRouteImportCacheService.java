/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.dto.ApiRouteImportContext;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/7 8:50 PM
 */
public interface ApiRouteImportCacheService {

    ApiRouteImportContext storeContext(String requestId, ApiRouteImportContext context);

    ApiRouteImportContext loadContext(String requestId);

    void evictContext(String requestId);
}
