package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.app.yop.portal.vo.page.enumtype.StatisticTypeEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * @Title: StatisticInvokeTimesApiGroupQueryParam
 * @Description: 查询参数
 * <AUTHOR>
 * @date 2018年5月10日 下午2:24:26
 * @version V1.0
 */
public class StatisticInvokeTimesApiGroupQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1;

    private String spCode;

    private String apiGroup;

    private StatisticTypeEnum statisticType;

    private Date statisticStartDate;

    private Date statisticEndDate;

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    public StatisticTypeEnum getStatisticType() {
        return statisticType;
    }

    public void setStatisticType(StatisticTypeEnum statisticType) {
        this.statisticType = statisticType;
    }

    public Date getStatisticStartDate() {
        return statisticStartDate;
    }

    public void setStatisticStartDate(Date statisticStartDate) {
        this.statisticStartDate = statisticStartDate;
    }

    public Date getStatisticEndDate() {
        return statisticEndDate;
    }

    public void setStatisticEndDate(Date statisticEndDate) {
        this.statisticEndDate = statisticEndDate;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
