/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.service.UserSearchService;
import com.yeepay.g3.app.yop.portal.utils.HttpUtils;
import com.yeepay.g3.app.yop.portal.utils.PageUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.UserVO;
import com.yeepay.g3.app.yop.portal.vo.page.AclUserPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@SuppressWarnings("unchecked")

/**
 * title: 用户搜索服务实现<br>
 * description: 用户搜索服务实现类<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-26
 */
@Service
public class UserSearchServiceImpl implements UserSearchService {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserSearchServiceImpl.class);
    
    private static final ObjectMapper OBJECT_MAPPER = JsonMapper.nonEmptyMapper().getMapper();

    @Override
    public PageQueryResult<AclUserPageItem> searchUsers(String keyword, Integer pageNo, Integer pageSize) {
        if (StringUtils.isBlank(keyword)) {
            return PageUtils.getEmptyPage(pageNo != null ? pageNo : 1);
        }

        try {
            // 获取配置
            Map<String, String> searchConfig = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_USER_SEARCH_CONFIG, Map.class);
            if (searchConfig == null) {
                LOGGER.error("用户搜索配置未找到");
                return PageUtils.getEmptyPage(pageNo != null ? pageNo : 1);
            }
            
            String serviceUrl = searchConfig.get("serviceUrl");
            String systemCode = searchConfig.get("systemCode");
            String signature = searchConfig.get("signature");
            
            if (StringUtils.isBlank(serviceUrl) || StringUtils.isBlank(systemCode) || StringUtils.isBlank(signature)) {
                LOGGER.error("用户搜索配置不完整: serviceUrl={}, systemCode={}, signature={}", 
                    serviceUrl, systemCode, StringUtils.isBlank(signature) ? "空" : "已配置");
                return PageUtils.getEmptyPage(pageNo != null ? pageNo : 1);
            }

            // 构建请求参数
            Map<Object, Object> requestParams = new HashMap<>();
            requestParams.put("keyword", keyword);
            if (pageNo != null) {
                requestParams.put("pageNo", pageNo);
            }
            if (pageSize != null) {
                requestParams.put("pageSize", pageSize);
            }

            // 构建请求头
            Map<Object, Object> requestHeaders = new HashMap<>();
            requestHeaders.put("accept", "*/*");
            requestHeaders.put("systemcode", systemCode);
            requestHeaders.put("expire-time", "10");
            requestHeaders.put("request-uuid", UUID.randomUUID().toString().replace("-", ""));
            requestHeaders.put("timestamp", String.valueOf(System.currentTimeMillis()));
            requestHeaders.put("signature", signature);

            // 发起HTTP请求
            String response = HttpUtils.getGetResponseMess(serviceUrl, requestParams, requestHeaders);
            
            if (StringUtils.isBlank(response)) {
                LOGGER.warn("用户搜索服务返回空响应, keyword: {}", keyword);
                return PageUtils.getEmptyPage(pageNo != null ? pageNo : 1);
            }

            // 解析响应
            return parseUserSearchResponse(response, pageNo != null ? pageNo : 1);

        } catch (Exception e) {
            LOGGER.error("用户搜索失败, keyword: {}, error: {}", keyword, e.getMessage(), e);
            return PageUtils.getEmptyPage(pageNo != null ? pageNo : 1);
        }
    }

    @Override
    public List<UserVO> searchUsersForVO(String keyword, Integer limit) {
        if (StringUtils.isBlank(keyword)) {
            return new ArrayList<>();
        }

        // 设置默认限制
        if (limit == null || limit <= 0) {
            limit = 10;
        }

        try {
            // 获取配置
            Map<String, String> searchConfig = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_USER_SEARCH_CONFIG, Map.class);
            if (searchConfig == null) {
                LOGGER.error("用户搜索配置未找到");
                return new ArrayList<>();
            }

            String serviceUrl = searchConfig.get("serviceUrl");
            String systemCode = searchConfig.get("systemCode");
            String signature = searchConfig.get("signature");

            if (StringUtils.isBlank(serviceUrl) || StringUtils.isBlank(systemCode) || StringUtils.isBlank(signature)) {
                LOGGER.error("用户搜索配置不完整: serviceUrl={}, systemCode={}, signature={}",
                    serviceUrl, systemCode, StringUtils.isBlank(signature) ? "空" : "已配置");
                return new ArrayList<>();
            }

            // 构建请求参数
            Map<Object, Object> requestParams = new HashMap<>();
            requestParams.put("keyword", keyword);
            requestParams.put("pageNo", 1);
            requestParams.put("pageSize", limit);

            // 构建请求头
            Map<Object, Object> requestHeaders = new HashMap<>();
            requestHeaders.put("accept", "*/*");
            requestHeaders.put("systemcode", systemCode);
            requestHeaders.put("expire-time", "10");
            requestHeaders.put("request-uuid", UUID.randomUUID().toString().replace("-", ""));
            requestHeaders.put("timestamp", String.valueOf(System.currentTimeMillis()));
            requestHeaders.put("signature", signature);

            // 发起HTTP请求
            String response = HttpUtils.getGetResponseMess(serviceUrl, requestParams, requestHeaders);

            if (StringUtils.isBlank(response)) {
                LOGGER.warn("用户搜索服务返回空响应, keyword: {}", keyword);
                return new ArrayList<>();
            }

            // 解析响应并转换为UserVO
            return parseUserSearchResponseForVO(response);

        } catch (Exception e) {
            LOGGER.error("用户搜索失败, keyword: {}, error: {}", keyword, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 解析用户搜索响应并转换为UserVO列表
     */
    private List<UserVO> parseUserSearchResponseForVO(String response) {
        try {
            JsonNode rootNode = OBJECT_MAPPER.readTree(response);

            // 检查响应状态
            if (!rootNode.has("success") || !rootNode.get("success").asBoolean()) {
                LOGGER.warn("用户搜索服务返回失败状态: {}", response);
                return new ArrayList<>();
            }

            JsonNode dataNode = rootNode.get("data");
            if (dataNode == null || !dataNode.isArray()) {
                LOGGER.warn("用户搜索服务返回数据格式异常: {}", response);
                return new ArrayList<>();
            }

            // 转换用户数据
            List<UserVO> users = new ArrayList<>();
            for (JsonNode userNode : dataNode) {
                UserVO user = convertToUserVO(userNode);
                if (user != null) {
                    users.add(user);
                }
            }

            return users;

        } catch (Exception e) {
            LOGGER.error("解析用户搜索响应失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换用户数据到UserVO
     */
    private UserVO convertToUserVO(JsonNode userNode) {
        try {
            UserVO user = new UserVO();

            // 设置用户基本信息
            if (userNode.has("operatorCode")) {
                user.setId(userNode.get("operatorCode").asText());
            }
            if (userNode.has("operatorName")) {
                user.setName(userNode.get("operatorName").asText());
            }
            if (userNode.has("email")) {
                user.setEmail(userNode.get("email").asText());
            }
            if (userNode.has("position")) {
                user.setDepartment(userNode.get("position").asText());
            }

            // 根据状态判断是否在职
            if (userNode.has("status")) {
                String status = userNode.get("status").asText();
                user.setActive("ACTIVE".equalsIgnoreCase(status) || "1".equals(status));
            } else {
                user.setActive(true); // 默认为在职
            }

            return user;
        } catch (Exception e) {
            LOGGER.error("转换用户数据失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析用户搜索响应
     */
    private PageQueryResult<AclUserPageItem> parseUserSearchResponse(String response, int pageNo) {
        try {
            JsonNode rootNode = OBJECT_MAPPER.readTree(response);
            
            // 检查响应状态
            if (!rootNode.has("success") || !rootNode.get("success").asBoolean()) {
                LOGGER.warn("用户搜索服务返回失败状态: {}", response);
                return PageUtils.getEmptyPage(pageNo);
            }

            JsonNode dataNode = rootNode.get("data");
            if (dataNode == null || !dataNode.isArray()) {
                LOGGER.warn("用户搜索服务返回数据格式异常: {}", response);
                return PageUtils.getEmptyPage(pageNo);
            }

            // 转换用户数据
            List<AclUserPageItem> users = new ArrayList<>();
            for (JsonNode userNode : dataNode) {
                AclUserPageItem user = convertToAclUserPageItem(userNode);
                if (user != null) {
                    users.add(user);
                }
            }

            // 构建分页结果
            PageQueryResult<AclUserPageItem> result = new PageQueryResult<>();
            result.setItems(users);
            result.setPageNo(pageNo);
            return result;

        } catch (Exception e) {
            LOGGER.error("解析用户搜索响应失败: {}", e.getMessage(), e);
            return PageUtils.getEmptyPage(pageNo);
        }
    }

    /**
     * 转换用户数据到AclUserPageItem
     */
    private AclUserPageItem convertToAclUserPageItem(JsonNode userNode) {
        try {
            AclUserPageItem user = new AclUserPageItem();
            
            // 设置用户基本信息
            if (userNode.has("id")) {
                user.setId(userNode.get("id").asLong());
            }
            if (userNode.has("operatorCode")) {
                user.setOptCode(userNode.get("operatorCode").asText());
            }
            if (userNode.has("operatorName")) {
                user.setOptName(userNode.get("operatorName").asText());
            }
            if (userNode.has("email")) {
                user.setEmail(userNode.get("email").asText());
            }
            if (userNode.has("position")) {
                user.setPosition(userNode.get("position").asText());
            }
            if (userNode.has("status")) {
                user.setStatus(userNode.get("status").asText());
            }
            if (userNode.has("type")) {
                user.setType(userNode.get("type").asText());
            }
            
            // 设置时间字段
            if (userNode.has("createdDatetime")) {
                String createdDatetime = userNode.get("createdDatetime").asText();
                if (StringUtils.isNotBlank(createdDatetime)) {
                    user.setCreatedDate(new Date(Long.parseLong(createdDatetime)));
                }
            }
            if (userNode.has("lastModifiedDatetime")) {
                String lastModifiedDatetime = userNode.get("lastModifiedDatetime").asText();
                if (StringUtils.isNotBlank(lastModifiedDatetime)) {
                    user.setLastModifiedDate(new Date(Long.parseLong(lastModifiedDatetime)));
                }
            }

            return user;
        } catch (Exception e) {
            LOGGER.error("转换用户数据失败: {}", e.getMessage(), e);
            return null;
        }
    }
}