/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeepay.g3.app.yop.portal.vo.ApiSwaggerLocalCacheKey;
import com.yeepay.g3.facade.yop.sys.enums.SwaggerDataFormatEnum;
import com.yeepay.g3.facade.yop.sys.facade.ApiQueryFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/8 6:33 PM
 */
@Component
@Slf4j
public class ApiSwaggerLocalCache {

    private static final int CACHE_MAX_SIZE = 200;
    private static final int CACHE_EXPIRE_AFTER_ACCESS = 5;
    private static final int CACHE_REFRESH_AFTER_ACCESS = 1;

    private static final ApiQueryFacade queryFacade = RemoteServiceFactory.getService(ApiQueryFacade.class);

    private static final LoadingCache<ApiSwaggerLocalCacheKey, String> apiSwaggerLocalCache = CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE)
            .expireAfterAccess(CACHE_EXPIRE_AFTER_ACCESS, TimeUnit.MINUTES)
            .refreshAfterWrite(CACHE_REFRESH_AFTER_ACCESS, TimeUnit.MINUTES)
            .recordStats()
            .build(getCacheLoader());

    private static CacheLoader<ApiSwaggerLocalCacheKey, String> getCacheLoader() {
        return new CacheLoader<ApiSwaggerLocalCacheKey, String>() {
            @Override
            public String load(ApiSwaggerLocalCacheKey key) {
                log.info("load swagger by key:{}.", key);
                try {
                    return queryFacade.queryForSwagger(key.getPath(), key.getMethod(), SwaggerDataFormatEnum.JSON);
                } catch (Exception e) {
                    log.error("load swagger failed.", e);
                }
                return "";
            }
        };
    }

    public String get(ApiSwaggerLocalCacheKey key) {
        try {
            return apiSwaggerLocalCache.get(key);
        } catch (Exception e) {
            log.error("error when get local cache", e);
            return null;
        }
    }

}
