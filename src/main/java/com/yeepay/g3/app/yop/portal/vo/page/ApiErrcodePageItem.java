package com.yeepay.g3.app.yop.portal.vo.page;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 18:00
 */
public class ApiErrcodePageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private String apiId;

    private Long errcodeId;

    private String apiGroupCode;

    private String errorCode;

    private String subErrorCode;

    private String subErrorMsg;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApiId() {
        return apiId;
    }

    public void setApiId(String apiId) {
        this.apiId = apiId;
    }

    public Long getErrcodeId() {
        return errcodeId;
    }

    public void setErrcodeId(Long errcodeId) {
        this.errcodeId = errcodeId;
    }

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getSubErrorCode() {
        return subErrorCode;
    }

    public void setSubErrorCode(String subErrorCode) {
        this.subErrorCode = subErrorCode;
    }

    public String getSubErrorMsg() {
        return subErrorMsg;
    }

    public void setSubErrorMsg(String subErrorMsg) {
        this.subErrorMsg = subErrorMsg;
    }

}
