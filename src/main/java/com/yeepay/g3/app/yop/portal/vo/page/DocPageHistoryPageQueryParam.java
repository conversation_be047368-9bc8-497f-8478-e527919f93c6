package com.yeepay.g3.app.yop.portal.vo.page;

import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Size;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-29 15:21
 */
public class DocPageHistoryPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private String docNo;

    private String title;

    private String operator;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operDateBegin;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operDateEnd;

    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperDateBegin() {
        return operDateBegin;
    }

    public void setOperDateBegin(Date operDateBegin) {
        this.operDateBegin = operDateBegin;
    }

    public Date getOperDateEnd() {
        return operDateEnd;
    }

    public void setOperDateEnd(Date operDateEnd) {
        this.operDateEnd = operDateEnd;
    }
}
