/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeepay.g3.facade.yop.perm.dto.IspInfoDTO;
import com.yeepay.g3.facade.yop.perm.facade.IspMgrFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/10/25 10:12
 */
@Component("spLocalCache")
public class SpLocalCache {

    private static final Logger LOGGER = LoggerFactory.getLogger(SpLocalCache.class);

    public static final int CACHE_MAX_SIZE = 500;
    public static final int CACHE_EXPIRE_AFTER_ACCESS = 5;
    public static final int CACHE_REFRESH_AFTER_ACCESS = 4;

    private IspMgrFacade ispMgrFacade = RemoteServiceFactory.getService(IspMgrFacade.class);

    private LoadingCache<String, Map<String, IspInfoDTO>> spLocalCache = CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE)
            .expireAfterAccess(CACHE_EXPIRE_AFTER_ACCESS, TimeUnit.MINUTES)
            .refreshAfterWrite(CACHE_REFRESH_AFTER_ACCESS, TimeUnit.MINUTES)
            .recordStats()
            .build(getCacheLoader());

    private CacheLoader getCacheLoader() {
        return new CacheLoader<String, Map<String, IspInfoDTO>>() {
            @Override
            public Map<String, IspInfoDTO> load(String key) throws Exception {
                LOGGER.info("load spCode by apiGroup:{}.", key);
                Map<String, IspInfoDTO> ispInfoMap = new HashMap<>();
                try {
                    List<IspInfoDTO> ispInfoDTOS = ispMgrFacade.findAll();
                    ispInfoMap = ispInfoDTOS.stream().collect(Collectors.toMap(IspInfoDTO::getSpCode, Function.identity()));
                } catch (Exception e) {
                    LOGGER.error("load spCode by apiGroup failed.", e);
                }
                return ispInfoMap;
            }
        };
    }

    public Map<String, IspInfoDTO> get(String key) {
        try {
            return spLocalCache.get(key);
        } catch (Exception e) {
            LOGGER.error("error when get local cache", e);
            return null;
        }
    }

}
