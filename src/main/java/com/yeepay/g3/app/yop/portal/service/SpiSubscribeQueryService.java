/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.SpiSubscribePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.SpiSubscribePageQueryParam;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/7 17:54
 */
public interface SpiSubscribeQueryService {
    PageQueryResult<SpiSubscribePageItem> pageList(SpiSubscribePageQueryParam param);

    boolean exist(String appId, String spiName);
}
