package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.ApiCommon;
import com.yeepay.g3.app.yop.portal.validation.group.ApiCreate;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 17:19
 */
public class ApiDefineVO implements Serializable {

    private static final long serialVersionUID = 6318473265309553326L;

    @NotNull(message = "{api.basic}", groups = ApiCommon.class)
    private ApiDefineBasicVO basic;

    @NotNull(message = "{api.backend}", groups = ApiCreate.class)
    private ApiDefineBackEndVO backend;

    private ApiRequestParamsVO request;

    private List<ApiResponseParamVO> response;

    private List<ErrorCodeVO> errorCodes;

    private List<SecurityReqVO> securities;

    public ApiDefineBasicVO getBasic() {
        return basic;
    }

    public void setBasic(ApiDefineBasicVO basic) {
        this.basic = basic;
    }

    public ApiDefineBackEndVO getBackend() {
        return backend;
    }

    public void setBackend(ApiDefineBackEndVO backend) {
        this.backend = backend;
    }

    public ApiRequestParamsVO getRequest() {
        return request;
    }

    public void setRequest(ApiRequestParamsVO request) {
        this.request = request;
    }

    public List<ApiResponseParamVO> getResponse() {
        return response;
    }

    public void setResponse(List<ApiResponseParamVO> response) {
        this.response = response;
    }

    public List<ErrorCodeVO> getErrorCodes() {
        return errorCodes;
    }

    public void setErrorCodes(List<ErrorCodeVO> errorCodes) {
        this.errorCodes = errorCodes;
    }

    public List<SecurityReqVO> getSecurities() {
        return securities;
    }

    public void setSecurities(List<SecurityReqVO> securities) {
        this.securities = securities;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
