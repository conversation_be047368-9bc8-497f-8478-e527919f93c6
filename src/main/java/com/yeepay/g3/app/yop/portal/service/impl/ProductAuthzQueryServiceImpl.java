/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ProductAuthzQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.ProductAuthzPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ProductAuthzPageQueryParam;
import com.yeepay.g3.facade.yop.sys.enums.ProductAuthzStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.ProductTypeEnum;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

import static com.yeepay.g3.app.yop.portal.utils.Constants.NON_EXISTS_SP_CODE;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/6 下午5:57
 */
@Component
public class ProductAuthzQueryServiceImpl implements ProductAuthzQueryService {

    private final PageItemConverter<ProductAuthzPageItem> pageItemConverter = new ProductAuthzPageItemConverter();

    @Resource(name = "productAuthzQueryService")
    private QueryService queryService;

    @Override
    public PageQueryResult<ProductAuthzPageItem> pageQuery(ProductAuthzPageQueryParam param) {
        Map<String, Object> bizParams = getBizParams(param);
        List<Map> list = queryService.query("pageList", bizParams);
        PageQueryResult<ProductAuthzPageItem> result = new PageQueryResult<>();
        List<ProductAuthzPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    private Map<String, Object> getBizParams(ProductAuthzPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("customerNo", param.getCustomerNo());
        bizParams.put("appId", param.getAppId());
        bizParams.put("productCode", param.getProductCode());
        if (ShiroUtils.isSpOperator()) {
            final Set<String> spCodes = ShiroUtils.getShiroUser().getSpScopes();
            if (CollectionUtils.isEmpty(spCodes)) {
                bizParams.put("spCodes", Collections.singletonList(NON_EXISTS_SP_CODE));
            } else {
                bizParams.put("spCodes", spCodes);
            }
        }
        bizParams.put("status", param.getStatus());
        bizParams.put("overdueDate", param.getOverdueDate());
        bizParams.put("overdueDateStart", param.getOverdueDateStart());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        bizParams.put("type", param.getType());
        return bizParams;
    }

    class ProductAuthzPageItemConverter extends BasePageItemConverter<ProductAuthzPageItem> {

        @Override
        public ProductAuthzPageItem convert(Map<String, Object> params) {
            ProductAuthzPageItem item = new ProductAuthzPageItem();
            item.setId((Long) params.get("id"));
            item.setCustomerNo((String) params.get("customer_no"));
            item.setCustomerName((String) params.get("customer_name"));
            item.setAppId((String) params.get("app_id"));
            item.setAppName((String) params.get("app_name"));
            item.setProductCode((String) params.get("product_code"));
            item.setProductName((String) params.get("product_name"));
            item.setEffectiveDate((Date) params.get("effective_datetime"));
            item.setOverdueDate((Date) params.get("overdue_datetime"));
            item.setStatus(ProductAuthzStatusEnum.parse((String) params.get("status")));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            item.setType(ProductTypeEnum.parse(params.get("product_type") == null ? null : (String) params.get("product_type")));
            return item;
        }
    }
}
