/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.cache;

import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.yop.frame.cache.AbstractRefreshableLocalCache;
import com.yeepay.g3.yop.frame.cache.YopCacheLoader;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * title: BlacklistCache<br>
 * description: 避免多次解析统一配置<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/7/4 8:58 下午
 */
@Component
public class BlacklistConfigCache extends AbstractRefreshableLocalCache<String, Map<String, Map<String, String>>> {

    // 黑名单缓存键
    public static final String CACHE_KEY = "blacklist";

    @Override
    protected int getMaxSize() {
        return 100;
    }

    @Override
    protected int getRefreshDuration() {
        return 60;
    }

    @Override
    protected YopCacheLoader<String, Map<String, Map<String, String>>> getCacheLoader() {
        return new YopCacheLoader<String, Map<String, Map<String, String>>>() {

            @Override
            protected Map<String, Map<String, String>> doLoad(String cacheKey) {
                Map<String, String> configMap = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_NOTIFIER_BLACKLIST);

                Map<String, Map<String, String>> result = new HashMap<>();
                configMap.forEach((k, v) -> {
                    String[] configKeys = StringUtils.split(k, "#");
                    if (2 == configKeys.length) {
                        Map<String, String> urlConfigMap = result.computeIfAbsent(configKeys[0], key -> new HashMap<>());
                        urlConfigMap.put(configKeys[1], v);
                        result.put(configKeys[0], urlConfigMap);
                    }
                });
                return result;
            }

            @Override
            protected String getCacheName() {
                return "blacklist_config_cache";
            }
        };
    }
}
