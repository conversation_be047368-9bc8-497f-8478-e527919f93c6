package com.yeepay.g3.app.yop.portal.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 16:30
 */
public class EnumCommonVO implements Serializable {

    private static final long serialVersionUID = 6978363663477046395L;

    private String value;

    private String desc;

    private List<ApiParamDataFormatVO> formats;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public EnumCommonVO withValue(String value) {
        this.value = value;
        return this;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public EnumCommonVO withDesc(String desc) {
        this.desc = desc;
        return this;
    }

    public List<ApiParamDataFormatVO> getFormats() {
        return formats;
    }

    public void setFormats(List<ApiParamDataFormatVO> formats) {
        this.formats = formats;
    }

    public void addFormat(ApiParamDataFormatVO format) {
        if (formats == null) {
            formats = new ArrayList<>();
        }
        formats.add(format);
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
