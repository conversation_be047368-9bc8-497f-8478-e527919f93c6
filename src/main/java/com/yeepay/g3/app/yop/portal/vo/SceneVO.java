/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.SceneCreate;
import com.yeepay.g3.app.yop.portal.validation.group.SceneDelete;
import com.yeepay.g3.app.yop.portal.validation.group.SceneOrder;

import javax.validation.constraints.NotNull;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/7 上午10:39
 */
public class SceneVO extends BaseVO {

    private static final long serialVersionUID = -1;

    @NotNull(message = "id", groups = {SceneDelete.class, SceneOrder.class})
    private Long id;

    @NotNull(message = "name", groups = SceneCreate.class)
    private String name;

    @NotNull(message = "productCode", groups = SceneCreate.class)
    private String productCode;

    @NotNull(message = "seq", groups = {SceneOrder.class})
    private Integer seq;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }
}
