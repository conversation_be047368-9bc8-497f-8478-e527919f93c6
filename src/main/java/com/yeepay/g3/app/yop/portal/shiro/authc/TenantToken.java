/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.authc;

import lombok.Getter;
import lombok.Setter;
import org.apache.shiro.authc.AuthenticationToken;

/**
 * title: TenantToken<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/7/27 3:49 下午
 */
@Setter
@Getter
public class TenantToken implements AuthenticationToken {
    private static final long serialVersionUID = 1L;
    /**
     * jwt token
     */
    private String ticket;

    /**
     * 受众，YOP:ISP
     */
    private String aud;

    private String tenantCode;

    /**
     * 用户标识：暂定operator_code
     */
    private String operatorExternal;

    public TenantToken(String ticket) {
        this.ticket = ticket;
    }

    public TenantToken(String aud, String tenantCode, String operatorExternal) {
        this.aud = aud;
        this.tenantCode = tenantCode;
        this.operatorExternal = operatorExternal;
    }

    @Override
    public Object getPrincipal() {
        return ticket;
    }

    @Override
    public Object getCredentials() {
        return ticket;
    }
}
