package com.yeepay.g3.app.yop.portal.utils.swagger.deserialize;

import com.fasterxml.jackson.databind.module.SimpleModule;
import io.swagger.v3.oas.models.security.SecurityScheme;

/**
 * title: 自定义fanxuliehua反序列话模块<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/9/12 19:50
 */
public class CustomDeserializationModule extends SimpleModule {

    private static final long serialVersionUID = -1L;

    public CustomDeserializationModule() {
        this.addDeserializer(SecurityScheme.class, new SecuritySchemeDeserializer());
    }
}
