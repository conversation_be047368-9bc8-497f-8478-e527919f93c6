/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.AuditQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.vo.AuditRecordDetailVO;
import com.yeepay.g3.app.yop.portal.vo.page.AuditRequisitionPageQueryParam;
import com.yeepay.g3.core.yop.utils.bean.BeanConvertUtils;
import com.yeepay.g3.facade.yop.perm.dto.AclAuditRecordStageDTO;
import com.yeepay.g3.facade.yop.perm.enums.AuditOperateEnum;
import com.yeepay.g3.facade.yop.perm.facade.AclAuditFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * title:审核设置 <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-12-01 12:50
 */
@Controller
@RequestMapping("/rest/acl/audit/requisition")
public class AclAuditRequisitionController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AclAuditRequisitionController.class);

    private AclAuditFacade aclAuditFacade = RemoteServiceFactory.getService(AclAuditFacade.class);

    @Autowired
    private AuditQueryService auditQueryService;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage detail(@Validated AuditRequisitionPageQueryParam param,
                                  @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                  @RequestParam(value = "_pageSize", required = false) Integer pageSize) {
        try {
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            if (ShiroUtils.isSpOperator()) {
                return new ResponseMessage("page", auditQueryService.pageQueryRequisitionForSp(param));
            } else {
                return new ResponseMessage("page", auditQueryService.pageQueryRequisitionList(param));
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list auditRequisition with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/revoke", method = RequestMethod.POST)
    public ResponseMessage revoke(@RequestParam("code") String code, @RequestParam("cause") String cause) {
        try {
            AclAuditRecordStageDTO stageDTO = new AclAuditRecordStageDTO();
            stageDTO.setRecordCode(code);
            stageDTO.setOperate(AuditOperateEnum.REVOKE);
            stageDTO.setOperator(ShiroUtils.getOperatorCode());
            stageDTO.setCause(cause);
            aclAuditFacade.updateRecord(stageDTO);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when revoke auditRequisition with code:" + code, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam("code") String code) {
        try {
            AuditRecordDetailVO detailVO = BeanConvertUtils.convert(aclAuditFacade.findRecordWithStageByCode(code), AuditRecordDetailVO.class);
            return new ResponseMessage("result", detailVO);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when detail auditRecord with code:" + code, e);
            return new ResponseMessage(e);
        }
    }

}
