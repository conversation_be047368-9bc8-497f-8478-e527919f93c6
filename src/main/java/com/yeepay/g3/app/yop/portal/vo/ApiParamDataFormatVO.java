package com.yeepay.g3.app.yop.portal.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 16:32
 */
public class ApiParamDataFormatVO implements Serializable {

    private static final long serialVersionUID = 1824995732251364705L;

    private String value;

    private String desc;

    private Boolean defaulted;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public ApiParamDataFormatVO withValue(String value) {
        this.value = value;
        return this;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public ApiParamDataFormatVO withDesc(String desc) {
        this.desc = desc;
        return this;
    }

    public Boolean getDefaulted() {
        return defaulted;
    }

    public void setDefaulted(<PERSON>olean defaulted) {
        this.defaulted = defaulted;
    }

    public ApiParamDataFormatVO withDefaulted(Boolean defaulted) {
        this.defaulted = defaulted;
        return this;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
