/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.CustomerCreate;
import com.yeepay.g3.app.yop.portal.validation.group.CustomerEdit;
import com.yeepay.g3.facade.yop.sys.enums.CustomerTypeEnum;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/7/19 下午5:52
 */
public class CustomerVO extends BaseVO {

    private static final long serialVersionUID = -1;

    @NotNull(groups = {CustomerCreate.class, CustomerEdit.class})
    @Size(min = 1, max = 32, groups = {CustomerCreate.class})
    private String customerNo;

    @NotNull(groups = {CustomerCreate.class})
    @Size(min = 1, max = 32, groups = {CustomerCreate.class})
    private String providerCode;

    @NotNull(groups = {CustomerCreate.class})
    @Size(min = 1, max = 128, groups = {CustomerCreate.class})
    private String name;

    @NotNull(groups = {CustomerCreate.class})
    private CustomerTypeEnum type;

    private String cause;

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getProviderCode() {
        return providerCode;
    }

    public void setProviderCode(String providerCode) {
        this.providerCode = providerCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public CustomerTypeEnum getType() {
        return type;
    }

    public void setType(CustomerTypeEnum type) {
        this.type = type;
    }

    public String getCause() {
        return cause;
    }

    public void setCause(String cause) {
        this.cause = cause;
    }
}
