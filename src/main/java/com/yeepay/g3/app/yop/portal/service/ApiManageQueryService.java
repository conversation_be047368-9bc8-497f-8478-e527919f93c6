package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.*;

import java.util.List;
import java.util.Map;

/**
 * title: ApiManageQueryService<br/>
 * description: api查询service<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:43
 */
public interface ApiManageQueryService {

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<ApiManagePageItem> pageList(ApiManagePageQueryParam param);

    /**
     * sp分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<ApiManagePageItem> pageListForSp(ApiManagePageQueryParam param);

    /**
     * 查询api本表信息
     * @param apiId
     * @param apiUri
     * @return
     */
    Map<String, Object> simpleApiDetail(String apiId, String apiUri);

    /**
     * 分页查询发布记录
     *
     * @param param 查询参数
     * @return 分页查询结果
     */
    PageQueryResult<ApiPublishRecordPageItem> publishRecordList(ApiPublishRecordPageQueryParam param);

    /**
     * SP分页查询发布记录
     *
     * @param param 查询参数
     * @return 分页查询结果
     */
    PageQueryResult<ApiPublishRecordPageItem> publishRecordListForSp(ApiPublishRecordPageQueryParam param);

    /**
     * 查询子API
     *
     * @param apiId
     * @return
     */
    List<ApiManagePageItem> subRefList(String apiId);

    /**
     * 查询错误码位置
     * @param apiId
     * @return
     */
    Object errorCodeLocationDetail(String apiId);

    /**
     * 根据分组查询为配置错误码位置的接口
     * @param apiGroup
     * @return
     */
    List queryNoConfigErrorCodeLocationApi(String apiGroup);
}
