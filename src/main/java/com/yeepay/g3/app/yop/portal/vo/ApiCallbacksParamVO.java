package com.yeepay.g3.app.yop.portal.vo;

import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-24 17:42
 */
public class ApiCallbacksParamVO extends BaseVO {
    private static final long serialVersionUID = -1L;

    @NotBlank(message = "apiUri 不能为空")
    private String apiUri;

    @NotEmpty(message = "callbacks 不能为空")
    private List<String> callbacks;

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public List<String> getCallbacks() {
        return callbacks;
    }

    public void setCallbacks(List<String> callbacks) {
        this.callbacks = callbacks;
    }
}
