/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/10/9 10:48
 */
@Component
public class ProviderCodeBasedAuthorizationFilter extends CacheAbstractAuthorizationFilter {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProviderCodeBasedAuthorizationFilter.class);

    @Override
    public String shiroName() {
        return "provider_code_based";
    }

    @Override
    protected String[] getSpCodes(String[] params) {
        return new String[0];
    }

    @Override
    protected String[] getTenantCodes(String[] params) {
        String[] tenantCodes;
        if (params == null || params.length < 1) {
            tenantCodes = null;
        } else {
            tenantCodes = new String[params.length];
            try {
                for (int i = 0; i < params.length; i++) {
                    tenantCodes[i] = providerCodeLocalCache.get(params[i]);
                }
            } catch (ExecutionException e) {
                LOGGER.info("get tenantCodes by providerCode wrong, params are :{},exception is :{}", params, e);
            }
            LOGGER.debug("get tenantCodes by providerCode success, params:{}, result:{}", params, tenantCodes);
        }
        return tenantCodes;
    }
}
