package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.AclUserQueryService;
import com.yeepay.g3.app.yop.portal.utils.MapUtils;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.page.AclUserPageQueryParam;
import com.yeepay.g3.core.yop.utils.bean.BeanConvertUtils;
import com.yeepay.g3.facade.employee.facade.UserFacade;
import com.yeepay.g3.facade.employee.user.dto.UserDTO;
import com.yeepay.g3.facade.yop.perm.dto.AclIspRoleAuthResponse;
import com.yeepay.g3.facade.yop.perm.dto.IspOperatorDTO;
import com.yeepay.g3.facade.yop.perm.enums.OperatorPositionEnum;
import com.yeepay.g3.facade.yop.perm.enums.OperatorStatusEnum;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.facade.yop.perm.facade.AclRoleMgrFacade;
import com.yeepay.g3.facade.yop.perm.facade.IspOperatorMgrFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午6:05
 */

@Controller
@RequestMapping("/rest/acl/user")
public class AclUserMgrController {

    private UserFacade userFacade = RemoteServiceFactory.getService(UserFacade.class);

    private IspOperatorMgrFacade ispOperatorMgrFacade = RemoteServiceFactory.getService(IspOperatorMgrFacade.class);

    private AclRoleMgrFacade aclRoleMgrFacade = RemoteServiceFactory.getService(AclRoleMgrFacade.class);

    @Autowired
    private AclUserQueryService aclUserQueryService;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage queryUsers(@RequestParam(required = false) OperatorPositionEnum position,
                                      @RequestParam(required = false) String name,
                                      @RequestParam(required = false) OperatorStatusEnum status,
                                      @RequestParam(required = false, value = "_pageNo") Integer pageNo,
                                      @RequestParam(required = false, value = "_pageSize") Integer pageSize) {

        AclUserPageQueryParam param = AclUserPageQueryParam.Builder.anAclUserPageQueryParam()
                .withPosition(position)
                .withName(name)
                .withStatus(status)
                .withPageNo(pageNo)
                .withPageSize(pageSize)
                .build();
        if (ShiroUtils.isPlatformOperator()) {
            return new ResponseMessage("result", aclUserQueryService.pageList(param));
        } else {
            return new ResponseMessage("result", aclUserQueryService.pageListForSp(param, ShiroUtils.getShiroUser().getSpScopes()));
        }
    }

    @ResponseBody
    @RequestMapping(value = "/position/list", method = RequestMethod.GET)
    public ResponseMessage queryPositions() {
        Map<String, String> map = OperatorPositionEnum.getDisplayValueMap();
        List<TypeVO> positionType = MapUtils.mapToTypeVOs(map);
        return new ResponseMessage("result", positionType);
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage queryDetail(@RequestParam String optCode) {
        IspOperatorDTO isp = ispOperatorMgrFacade.findOrCreate(optCode);
        List<AclIspRoleAuthResponse> ispRoleAuthResponses = aclRoleMgrFacade.findByOperatorCode(optCode, optCode);
        IspOperatorVO ispOperatorVO = BeanConvertUtils.convert(isp, IspOperatorVO.class);
        ispOperatorVO.setPerms(BeanConvertUtils.convertList(ispRoleAuthResponses, AclIspRoleAuthVO.class));
        return new ResponseMessage("result", ispOperatorVO);
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage createAndAuth(@RequestBody AclUserRequestParamVO users) {
        ispOperatorMgrFacade.batchCreateAndAuth(users.getType(),
                users.getOptCodes(), users.getRoleCodes());
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public ResponseMessage edit(@RequestParam String optCode,
                                @RequestParam OperatorPositionEnum position) {
        IspOperatorDTO ispOperator = new IspOperatorDTO();
        ispOperator.setOperatorCode(optCode);
        ispOperator.setPosition(position);
        ispOperatorMgrFacade.update(ispOperator);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/frozen", method = RequestMethod.POST)
    public ResponseMessage frozenUser(@RequestParam String optCode) {
        ispOperatorMgrFacade.freeze(optCode);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/unfreeze", method = RequestMethod.POST)
    public ResponseMessage unfreezeUser(@RequestParam String optCode) {
        ispOperatorMgrFacade.unfreeze(optCode);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/auth", method = RequestMethod.POST)
    public ResponseMessage authUser(@RequestParam String optCode,
                                    @RequestParam(required = false) List<String> roleCodes) {
        if (roleCodes == null) {
            roleCodes = new ArrayList<>();
        }
        ispOperatorMgrFacade.updateRole(optCode, roleCodes);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/edit-type", method = RequestMethod.POST)
    public ResponseMessage editUserType(@RequestParam String optCode,
                                        @RequestParam OperatorTypeEnum type) {
        ispOperatorMgrFacade.updateOperatorType(optCode, type);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/by-position", method = RequestMethod.GET)
    public ResponseMessage queryUsersByPosition(@RequestParam OperatorPositionEnum position) {
        List<String> optCodes = ispOperatorMgrFacade.findOperatorCodeByPositon(position);
        return new ResponseMessage("result", optCodes);
    }

    @ResponseBody
    @RequestMapping(value = "/check-native-user", method = RequestMethod.GET)
    public ResponseMessage checkYopUser(@RequestParam String name) {
        UserDTO userDTO = userFacade.queryUserByLoginName(name);
        if (userDTO == null) {
            return new ResponseMessage("result", false);
        }
        return new ResponseMessage("result", true);
    }

    @ResponseBody
    @RequestMapping(value = "/check-boss-user", method = RequestMethod.GET)
    public ResponseMessage checkBossUser(@RequestParam String name) {
        UserDTO userDTO = userFacade.queryUserByLoginName(name);
        boolean isExist = ispOperatorMgrFacade.checkExist(name);
        AclUserExistTypeVO aclUserExistTypeVO = new AclUserExistTypeVO();
        aclUserExistTypeVO.setStatus(true);
        if (userDTO == null) {
            aclUserExistTypeVO.setRemarks("非法用户");
            aclUserExistTypeVO.setStatus(false);
        }
        if (isExist) {
            aclUserExistTypeVO.setRemarks("该用户已存在");
            aclUserExistTypeVO.setStatus(false);
        }
        return new ResponseMessage("result", aclUserExistTypeVO);
    }

    @ResponseBody
    @RequestMapping(value = "/check-portal-user", method = RequestMethod.GET)
    public ResponseMessage checkPortalUser(@RequestParam String name) {
        UserDTO userDTO = userFacade.queryUserByLoginName(name);
        boolean isExist = ispOperatorMgrFacade.checkExist(name);
        AclUserExistTypeVO aclUserExistTypeVO = new AclUserExistTypeVO();
        aclUserExistTypeVO.setStatus(true);
        if (userDTO == null) {
            aclUserExistTypeVO.setRemarks("非法用户");
            aclUserExistTypeVO.setStatus(false);
        }
        if (!isExist) {
            aclUserExistTypeVO.setRemarks("该用户不存在，请在先在用户管理中添加该用户");
            aclUserExistTypeVO.setStatus(false);
        }
        return new ResponseMessage("result", aclUserExistTypeVO);
    }

}
