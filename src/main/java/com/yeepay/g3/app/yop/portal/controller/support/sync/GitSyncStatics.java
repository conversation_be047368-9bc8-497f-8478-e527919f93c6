package com.yeepay.g3.app.yop.portal.controller.support.sync;

import com.yeepay.g3.facade.yop.sys.dto.sync.GitSyncFailedInfo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * title: Git同步统计<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-13 16:00
 */
public class GitSyncStatics implements Serializable {

    private static final long serialVersionUID = -1L;

    private Integer total;

    private List<String> success;

    private List<GitSyncFailedInfo> failed;

    private List<String> ignored;

    private List<String> unused;

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public List<String> getSuccess() {
        return success;
    }

    public void setSuccess(List<String> success) {
        this.success = success;
    }

    public GitSyncStatics addSuccessItem(String item) {
        if (this.success == null) {
            this.success = new ArrayList<>();
        }
        this.success.add(item);
        return this;
    }

    public List<GitSyncFailedInfo> getFailed() {
        return failed;
    }

    public void setFailed(List<GitSyncFailedInfo> failed) {
        this.failed = failed;
    }

    public List<String> getIgnored() {
        return ignored;
    }

    public void setIgnored(List<String> ignored) {
        this.ignored = ignored;
    }

    public GitSyncStatics addIgnoredItem(String item) {
        if (this.ignored == null) {
            this.ignored = new ArrayList<>();
        }
        this.ignored.add(item);
        return this;
    }

    public List<String> getUnused() {
        return unused;
    }

    public void setUnused(List<String> unused) {
        this.unused = unused;
    }


}
