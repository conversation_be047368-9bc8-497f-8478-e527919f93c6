/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.ext.gateway.douyin.filter.authc.douyin;

import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateStrategyEnum;

import java.util.List;

/**
 * title: 抖音认证策略<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/15 5:50 PM
 */
public interface DouyinAuthcStrategy {

    /**
     * 获取该策略支持的认证类型
     *
     * @return
     */
    List<AuthenticateStrategyEnum> supportAuthenticateStrategyEnums();

    /**
     * 认证
     *
     * @param exchange
     */
    void authenticate(ServerWebExchange exchange);

    /**
     * 校验认证信息是否过期
     *
     * @param exchange
     */
    void checkAuthenticationOverdue(ServerWebExchange exchange);
}
