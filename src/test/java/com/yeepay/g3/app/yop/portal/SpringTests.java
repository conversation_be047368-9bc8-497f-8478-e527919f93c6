package com.yeepay.g3.app.yop.portal; /**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

import com.alibaba.dubbo.common.Constants;
import com.yeepay.g3.utils.common.InitializeUtils;
import org.junit.BeforeClass;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;

/**
 * <p>Title: 不带事务的单元测试基类</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c)2011</p>
 * <p>Company: 易宝支付(YeePay)</p>
 *
 * <AUTHOR>
 * @version 0.1, 13-3-12 下午1:17
 */
@ActiveProfiles("test")
@DirtiesContext
@EnableCaching
@EnableAsync
@SpringBootTest(classes = WebApplication.class)
public class SpringTests extends AbstractJUnit4SpringContextTests {

    @BeforeClass
    public static void setUpBeforeClass() throws Exception {
        System.setProperty(Constants.DUBBO_PROPERTIES_KEY, "yeepay-soa-config/dubbo.properties");
        InitializeUtils.initComponents();
    }

}
