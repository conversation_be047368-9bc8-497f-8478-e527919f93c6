/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.context.authenticate;

import com.yeepay.g3.yop.ext.gateway.context.authenticate.AuthenticateContextParser;
import com.yeepay.g3.yop.ext.gateway.context.protocol.ProtocolMakerEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * title: 协议认证上下文解析工厂<br>
 * description: 根据不同协议，返回对应的认证解析器,协议例如：YOP，抖音，快手，银联等等<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/14 7:08 PM
 */
@Component
public class AuthenticateContextParserFactory {

    private static final Map<ProtocolMakerEnum, AuthenticateContextParser> CONTEXT_PARSER_MAP = new HashMap<>();

    public AuthenticateContextParserFactory(List<AuthenticateContextParser> authenticateContextParsers) {
        authenticateContextParsers.forEach(AuthenticateContextParserFactory::registerAuthenticateContextParser);
    }

    public static AuthenticateContextParser getAuthenticateContextParser(ProtocolMakerEnum protocolMaker) {
        return CONTEXT_PARSER_MAP.get(protocolMaker);
    }

    public static void registerAuthenticateContextParser(AuthenticateContextParser authenticateContextParser) {
        CONTEXT_PARSER_MAP.put(authenticateContextParser.getProtocolMaker(), authenticateContextParser);
    }
}
