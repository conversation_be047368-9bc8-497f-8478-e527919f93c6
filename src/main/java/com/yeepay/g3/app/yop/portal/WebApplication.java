package com.yeepay.g3.app.yop.portal;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.config.SpringApplicationRunListener;
import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.core.yop.utils.mapper.JsonMapper;
import com.yeepay.g3.utils.common.InitializeUtils;
import com.yeepay.springframework.boot.annotation.EnableSoa;
import org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ImportResource;
import org.springframework.context.annotation.PropertySource;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.Charset;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,
        RabbitAutoConfiguration.class,
        RocketMQAutoConfiguration.class})
@EnableCaching
@ImportResource(locations = {"classpath:yop-portal-spring/yop-portal-application.xml"})
@PropertySource(value = {
        "classpath:runtimecfg/codis-conf.properties", "classpath:runtimecfg/fmc-conf.properties", "classpath:runtimecfg/yuia-conf.properties",
        "classpath:runtimecfg/rocketmq.properties",
        "classpath:runtimecfg/mq_conf.properties"
})
@EnableSoa
public class WebApplication implements WebMvcConfigurer {

    public static void main(String[] args) throws Exception {
        try {
            ConfigUtils.init();
        } catch (Exception e) {
            throw new RuntimeException("init config error", e);
        }

        InitializeUtils.initComponents();

        // SpringApplication.run(WebApplication.class, args);

        SpringApplication application = new SpringApplication(WebApplication.class) {
            @Override
            protected ConfigurableApplicationContext createApplicationContext() {
                return new AnnotationConfigServletWebServerApplicationContext() {
                    @Override
                    protected Collection<ServletContextInitializer> getServletContextInitializerBeans() {
                        return super.getServletContextInitializerBeans().stream()
                                .filter(e -> !(e instanceof FilterRegistrationBean &&
                                        ((FilterRegistrationBean) e).getFilter() instanceof CustomShiroFilter))
                                .collect(Collectors.toList());
                    }
                };
            }
        };
        application.addListeners(new SpringApplicationRunListener());
        application.run(args);
    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        converters.add(new ByteArrayHttpMessageConverter());
        converters.add(new StringHttpMessageConverter(Charset.forName("UTF-8")));

        converters.forEach(converter -> {
            if (converter instanceof MappingJackson2HttpMessageConverter) {
                MappingJackson2HttpMessageConverter jsonConverter = (MappingJackson2HttpMessageConverter) converter;
                jsonConverter.setObjectMapper(JsonMapper.nonNullObjectMapper());
                jsonConverter.setPrettyPrint(true);
            }
        });
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 解决swagger无法访问
        registry.addResourceHandler("/swagger-ui.html").addResourceLocations("classpath:/META-INF/resources/");
        // 解决swagger的js文件无法访问
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
    }

}
