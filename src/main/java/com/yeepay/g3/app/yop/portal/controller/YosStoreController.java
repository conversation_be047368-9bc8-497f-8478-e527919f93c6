package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.YosStoreConfigQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.validation.group.YosStoreConfigCommon;
import com.yeepay.g3.app.yop.portal.validation.group.YosStoreConfigCreate;
import com.yeepay.g3.app.yop.portal.validation.group.YosStoreConfigEdit;
import com.yeepay.g3.app.yop.portal.vo.YosStoreConfigVO;
import com.yeepay.g3.app.yop.portal.vo.page.YosStoreConfigPageQueryParam;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.facade.yop.sys.dto.YosStoreConfigDTO;
import com.yeepay.g3.facade.yop.sys.dto.storeconfig.CephStoreConfig;
import com.yeepay.g3.facade.yop.sys.enums.YosConfigTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.YosStoreTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.YosStoreConfigFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/6/12 17:41
 */
@Controller
@RequestMapping("/rest/filestore")
public class YosStoreController {

    private static final Logger LOGGER = LoggerFactory.getLogger(YosStoreController.class);

    private YosStoreConfigFacade yosStoreConfigFacade = RemoteServiceFactory.getService(YosStoreConfigFacade.class);

    @Autowired
    private YosStoreConfigQueryService yosStoreConfigQueryService;

    private final String pattern = "yyyy-MM-dd HH:mm:ss";

    @ResponseBody
    @RequestMapping(value = "/commons/store-types", method = RequestMethod.GET)
    public ResponseMessage storeTypes() {
        try {
            return new ResponseMessage("result", yosStoreConfigQueryService.listYosStoreTypes());
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list store types:", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/api-group/create", method = RequestMethod.POST)
    public ResponseMessage addWithApiGroup(@RequestBody @Validated({YosStoreConfigCommon.class, YosStoreConfigCreate.class}) YosStoreConfigVO yosStoreConfig) {
        try {
            if (Boolean.TRUE.equals(yosStoreConfig.getIsDefault()) && !Boolean.TRUE.equals(yosStoreConfig.getForce())) {
                YosStoreConfigDTO dto = yosStoreConfigFacade.findByApiGroupCode(yosStoreConfig.getApiGroupCode());
                if (dto != null) {
                    return new ResponseMessage("result", "failed");
                }
            }
            YosStoreConfigDTO yosStoreConfigDTO = conver(yosStoreConfig);
            yosStoreConfigFacade.add(yosStoreConfigDTO);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when add yos store by api group with param:" + yosStoreConfig, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/api-group/list", method = RequestMethod.GET)
    public ResponseMessage listWithApiGroup(@RequestParam(value = "apiGroupCode", required = false) String apiGroupCode,
                                            @RequestParam(value = "createdStartDate", required = false) String createdStartDate,
                                            @RequestParam(value = "createdEndDate", required = false) String createdEndDate,
                                            @RequestParam(value = "_pageSize") Integer _pageSize,
                                            @RequestParam(value = "_pageNo") Integer _pageNo) {
        YosStoreConfigPageQueryParam param = new YosStoreConfigPageQueryParam();
        try {
            param.setPageSize(_pageSize);
            param.setPageNo(_pageNo);
            if (StringUtils.isNotBlank(apiGroupCode)) {
                param.setApiGroupCode(apiGroupCode);
            } else {
                param.setApiGroupCodes(ShiroUtils.getShiroUser().getSpScopes());
            }
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            if (StringUtils.isNotBlank(createdStartDate)) {
                param.setCreatedStartDate(sdf.parse(createdStartDate));
            }
            if (StringUtils.isNotBlank(createdEndDate)) {
                param.setCreatedEndDate(sdf.parse(createdEndDate));
            }
            if (OperatorTypeEnum.PLATFORM == ShiroUtils.getOperatorType()) {
                return new ResponseMessage("page", yosStoreConfigQueryService.pageList(param));
            } else {
                return new ResponseMessage("page", yosStoreConfigQueryService.pageListForSp(param));
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list yos store by api group with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/api-group/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam(value = "id") Long id) {
        try {
            return new ResponseMessage("result", yosStoreConfigQueryService.findById(id));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when view yos store with id:" + id, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/api-group/update", method = RequestMethod.POST)
    public ResponseMessage editWithApiGroup(@RequestBody @Validated({YosStoreConfigCommon.class, YosStoreConfigEdit.class}) YosStoreConfigVO yosStoreConfig) {
        YosStoreConfigDTO yosStoreConfigDTO = conver(yosStoreConfig);
        try {
            yosStoreConfigFacade.update(yosStoreConfigDTO);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when update yos store with param:" + yosStoreConfigDTO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam(value = "id") Long id) {
        try {
            yosStoreConfigFacade.delete(id);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when delete yos store with id:" + id, e);
            return new ResponseMessage(e);
        }
    }

    private YosStoreConfigDTO conver(YosStoreConfigVO vo) {
        YosStoreConfigDTO dto = new YosStoreConfigDTO();
        dto.setId(vo.getId());
        dto.setConfigType(YosConfigTypeEnum.API_GROUP);
        dto.setApiGroupCode(vo.getApiGroupCode());
        dto.setStoreType(YosStoreTypeEnum.CEPH);
        CephStoreConfig cephStoreConfig = new CephStoreConfig();
        cephStoreConfig.setSecretKey(vo.getSecretKey());
        cephStoreConfig.setBucket(vo.getBucket());
        cephStoreConfig.setFileName(vo.getFileName());
        dto.setStoreConfig(cephStoreConfig);
        dto.setDefault(vo.getIsDefault());
        return dto;
    }

}
