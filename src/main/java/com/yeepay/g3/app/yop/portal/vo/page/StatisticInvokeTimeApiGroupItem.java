package com.yeepay.g3.app.yop.portal.vo.page;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @Title: StatisticInvokeTimeApiGroupItem
 * @Description:
 * <AUTHOR>
 * @date 2018年5月10日 下午2:35:22
 * @version V1.0
 */
@JsonInclude(JsonInclude.Include.ALWAYS)
public class StatisticInvokeTimeApiGroupItem implements Serializable {

    private static final long serialVersionUID = -1;

    private String spCode;
    
    private String spName;

    private String apiGroupCode;

    private String apiGroupName;
    
    private Long invokeTimes;

    private String statisticDate;

    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getSpName() {
        return spName;
    }

    public void setSpName(String spName) {
        this.spName = spName;
    }

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public String getApiGroupName() {
        return apiGroupName;
    }

    public void setApiGroupName(String apiGroupName) {
        this.apiGroupName = apiGroupName;
    }

    public Long getInvokeTimes() {
        return invokeTimes;
    }

    public void setInvokeTimes(Long invokeTimes) {
        this.invokeTimes = invokeTimes;
    }

    public String getStatisticDate() {
        return statisticDate;
    }

    public void setStatisticDate(String statisticDate) {
        this.statisticDate = statisticDate;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
