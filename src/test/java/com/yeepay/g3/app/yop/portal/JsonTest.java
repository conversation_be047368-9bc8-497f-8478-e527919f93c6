package com.yeepay.g3.app.yop.portal;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.yeepay.g3.app.yop.portal.git.GitCommit;
import io.swagger.v3.core.util.Json;
import org.junit.Test;

import static org.junit.Assert.assertNotNull;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-17 18:01
 */
public class JsonTest {

    @Test
    public void test() throws JsonProcessingException {
        String src = "{\n" +
                "    \"id\": \"ed899a2f4b50b4370feeea94676502b42383c746\",\n" +
                "    \"short_id\": \"ed899a2f4b5\",\n" +
                "    \"title\": \"Replace sanitize with escape once\",\n" +
                "    \"author_name\": \"<PERSON><PERSON><PERSON><PERSON>\",\n" +
                "    \"author_email\": \"<EMAIL>\",\n" +
                "    \"authored_date\": \"2020-03-17T16:09:53.000+08:00\",\n" +
                "    \"committer_name\": \"Administrator\",\n" +
                "    \"committer_email\": \"<EMAIL>\",\n" +
                "    \"committed_date\": \"2020-03-17T16:09:53.000+08:00\",\n" +
                "    \"created_at\": \"2012-09-20T11:50:22+03:00\",\n" +
                "    \"message\": \"Replace sanitize with escape once\",\n" +
                "    \"parent_ids\": [\n" +
                "      \"6104942438c14ec7bd21c6cd5bd995272b3faff6\"\n" +
                "    ]\n" +
                "  }\n";

        GitCommit commit = Json.mapper().readValue(src, GitCommit.class);
        assertNotNull(commit);
    }
}
