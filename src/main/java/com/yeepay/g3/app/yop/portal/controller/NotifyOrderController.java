/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.exception.PortalExceptionEnum;
import com.yeepay.g3.app.yop.portal.service.ErrorCodeQueryService;
import com.yeepay.g3.app.yop.portal.service.NotifyOrderQueryService;
import com.yeepay.g3.app.yop.portal.service.NotifyOrderService;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.notify.*;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.boot.web.pojo.response.R;
import com.yeepay.g3.facade.yop.monitor.enums.OrderStatusEnum;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.*;

/**
 * title: <br>
 * description: 通知订单控制器<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/9 10:55 上午
 */
@RestController
@RequestMapping("/rest/notify/order")
@Slf4j
public class NotifyOrderController {
    private static final long MAX_TIME = 7L * 24 * 60 * 60 * 1000;
    private FastDateFormat fastDateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
    @Autowired
    private NotifyOrderService notifyOrderService;

    @Autowired
    private NotifyOrderQueryService notifyOrderQueryService;

    @Autowired
    private ErrorCodeQueryService errorCodeQueryService;

    /**
     * 查询处理的发送记录
     *
     * @param pageSize
     * @param pageNo
     * @param notifyRecordId
     * @param orderDate
     * @return
     */
    @GetMapping("/send-record/list")
    public R listNotifySendRecord(@RequestParam(value = "_pageSize", required = false) Integer pageSize, @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                  @RequestParam(value = "notifyRecordId") String notifyRecordId,
                                  @RequestParam(value = "orderDate") String orderDate) throws ParseException {
        PageQueryParam pageQueryParam = getPageQueryParam(pageSize, pageNo);
        List<NotifySendRecordVO> sendRecordVOList = notifyOrderQueryService.listNotifySendRecord(NotifySendRecordQueryParam.builder()
                .notifyRecordId(notifyRecordId)
                .orderDate(fastDateFormat.parse(orderDate))
                .build(), pageQueryParam);
        Map<String, Object> data = new HashMap<>(5);
        data.put("pageNo", pageQueryParam.getPageNo());
        data.put("pageSize", pageQueryParam.getPageSize());
        data.put("items", sendRecordVOList);
        return new R(data);
    }

    /**
     * 查询订单
     *
     * @param pageNo
     * @param pageSize
     * @param notificationId
     * @param status
     * @param appId
     * @param spiName
     * @param notifyStartDate
     * @param notifyEndDate
     * @return
     */
    @GetMapping("/list")
    public R<PageQueryResult<NotifyOrderVO>> listNotifyOrder(@RequestParam(value = "_pageNo", required = false) Integer pageNo, @RequestParam(value = "_pageSize", required = false) Integer pageSize,
                                                             @RequestParam(value = "notificationId", required = false) String notificationId,
                                                             @RequestParam(value = "customerNo", required = false) String customerNo,
                                                             @RequestParam(value = "appId", required = false) String appId,
                                                             @RequestParam(value = "status", required = false) OrderStatusEnum status,
                                                             @RequestParam(value = "spiName", required = false) String spiName,
                                                             @RequestParam(value = "notifyStartDate") String notifyStartDate,
                                                             @RequestParam(value = "notifyEndDate") String notifyEndDate,
                                                             @RequestParam(value = "backend", required = false) String backend,
                                                             @RequestParam(value = "notifyRule", required = false) String notifyRule,
                                                             @RequestParam(value = "url", required = false) String url,
                                                             @RequestParam(value = "errorCode", required = false) String errorCode,
                                                             @RequestParam(value = "guid", required = false) String guid
    ) throws ParseException {
        Date startDate = fastDateFormat.parse(notifyStartDate);
        Date endDate = fastDateFormat.parse(notifyEndDate);
        PortalExceptionEnum.TIME_INTERVAL_TOO_LONG.assertIsTrue(startDate.getTime() - startDate.getTime() <= MAX_TIME, 7);

        NotifyOrderQueryParam notifyOrderQueryParam = NotifyOrderQueryParam.builder()
                .customerNo(customerNo)
                .status(status)
                .appId(appId)
                .spiName(spiName)
                .notificationId(notificationId)
                .notifyStartDate(startDate)
                .notifyEndDate(endDate)
                .backend(backend)
                .notifyRule(notifyRule)
                .url(url)
                .errorCode(errorCode)
                .guid(guid)
                .build();

        PageQueryParam pageQueryParam = getPageQueryParam(pageSize, pageNo);
        return new R(notifyOrderQueryService.listNotifyOrder(notifyOrderQueryParam, pageQueryParam));
    }

    @GetMapping(value = "/error-code/list")
    public R listErrorCode() {
        List<NotifyErrorSolutionVO> notifyErrorSolutionVOS = errorCodeQueryService.listNotifyErrorSolution();
        List<TypeVO> typeVOList = new ArrayList<>();
        if (CheckUtils.isEmpty(notifyErrorSolutionVOS)) {
            return new R(typeVOList);
        }
        notifyErrorSolutionVOS.forEach(vo -> {
            TypeVO typeVO = new TypeVO();
            typeVO.setCode(vo.getErrorCode());
            typeVO.setName(vo.getErrorName());
            typeVOList.add(typeVO);
        });
        return new R(typeVOList);
    }

    @GetMapping("/latest-content")
    public R findNotifyContent(@RequestParam("orderId") String orderId, @RequestParam("orderDate") String orderDate) {
        return new R(notifyOrderQueryService.findNotifyContent(orderId, orderDate));
    }

    /**
     * 同步订单状态
     *
     * @param syncVO
     * @return
     */
    @PostMapping("/sync")
    public R sync(@RequestBody SyncVO syncVO) {
        return new R(notifyOrderService.sync(syncVO));
    }

    /**
     * 查询通知记录
     *
     * @param pageSize
     * @param pageNo
     * @param orderId
     * @param orderDate
     * @return
     */
    @GetMapping("/record/list")
    public R listRecord(@RequestParam(value = "_pageSize", required = false) Integer pageSize, @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                        @RequestParam(value = "orderId") String orderId,
                        @RequestParam(value = "orderDate") String orderDate) throws ParseException {
        PageQueryParam pageQueryParam = getPageQueryParam(pageSize, pageNo);
        List<NotifyRecordVO> recordVOList = notifyOrderQueryService.listNotifyRecord(NotifyRecordQueryParam.builder()
                .orderId(orderId)
                .orderDate(fastDateFormat.parse(orderDate))
                .build(), pageQueryParam);
        Map<String, Object> data = new HashMap<>(5);
        data.put("pageNo", pageQueryParam.getPageNo());
        data.put("pageSize", pageQueryParam.getPageSize());
        data.put("items", recordVOList);
        return new R(data);
    }

    @PostMapping("/resend")
    public R resend(@RequestBody List<ResendVO> resendVOList) {
        if (CollectionUtils.isNotEmpty(resendVOList)) {
            notifyOrderService.batchResend(resendVOList);
        }
        return new R();
    }

    @PostMapping("/query-resend")
    public R queryResend(@RequestBody NotifyOrderQueryParam notifyOrderQueryParam) {
        notifyOrderService.queryAndResend(notifyOrderQueryParam);
        return new R();
    }

    private PageQueryParam getPageQueryParam(Integer pageSize, Integer pageNo) {
        PageQueryParam pageQueryParam = PageQueryParam.builder().build();
        if (null != pageNo) {
            pageQueryParam.setPageNo(pageNo);
        }
        if (null != pageSize) {
            pageQueryParam.setPageSize(pageSize);
        }
        return pageQueryParam;
    }
}
