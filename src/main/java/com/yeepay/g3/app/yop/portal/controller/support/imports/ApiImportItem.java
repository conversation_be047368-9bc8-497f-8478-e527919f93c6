package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.dto.ApiDTO;

import java.io.Serializable;
import java.util.List;

/**
 * title: Api导入项<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-08-16 11:26
 */
public class ApiImportItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private ApiDTO api;

    private List<String> spisToCreate;

    private List<String> spisToOverride;

    private List<String> modelsToCreate;

    private List<String> modelsToOverride;

    public ApiDTO getApi() {
        return api;
    }

    public void setApi(ApiDTO api) {
        this.api = api;
    }

    public ApiImportItem withApi(ApiDTO api) {
        this.api = api;
        return this;
    }

    public List<String> getSpisToCreate() {
        return spisToCreate;
    }

    public void setSpisToCreate(List<String> spisToCreate) {
        this.spisToCreate = spisToCreate;
    }

    public ApiImportItem withSpisToCreate(List<String> spisToCreate) {
        this.spisToCreate = spisToCreate;
        return this;
    }

    public List<String> getSpisToOverride() {
        return spisToOverride;
    }

    public void setSpisToOverride(List<String> spisToOverride) {
        this.spisToOverride = spisToOverride;
    }

    public ApiImportItem withSpisToOverride(List<String> spisToOverride) {
        this.spisToOverride = spisToOverride;
        return this;
    }

    public List<String> getModelsToCreate() {
        return modelsToCreate;
    }

    public void setModelsToCreate(List<String> modelsToCreate) {
        this.modelsToCreate = modelsToCreate;
    }

    public ApiImportItem withModelsToCreate(List<String> modelsToCreate) {
        this.modelsToCreate = modelsToCreate;
        return this;
    }

    public List<String> getModelsToOverride() {
        return modelsToOverride;
    }

    public void setModelsToOverride(List<String> modelsToOverride) {
        this.modelsToOverride = modelsToOverride;
    }

    public ApiImportItem withModelsToOverride(List<String> modelsToOverride) {
        this.modelsToOverride = modelsToOverride;
        return this;
    }
}
