package com.yeepay.g3.app.yop.portal.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.service.YosStoreConfigQueryService;
import com.yeepay.g3.app.yop.portal.utils.PageUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.CephStoreConfigVO;
import com.yeepay.g3.app.yop.portal.vo.YosStoreConfigVO;
import com.yeepay.g3.app.yop.portal.vo.YosStoreTypeVO;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.YosStoreConfigPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.YosStoreConfigPageQueryParam;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.facade.yop.sys.dto.storeconfig.CephStoreConfig;
import com.yeepay.g3.facade.yop.sys.dto.storeconfig.FileStoreConfig;
import com.yeepay.g3.facade.yop.sys.enums.YosStoreTypeEnum;
import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Component
public class YosStoreConfigQueryServiceImpl implements YosStoreConfigQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(YosStoreConfigQueryServiceImpl.class);

    @Resource(name = "yosStoreQueryService")
    private QueryService queryService;

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    private PageItemConverter<YosStoreConfigPageItem> pageItemConverter = new YosStoreConfigItemConverter();

    private ObjectMapper objectMapper;

    {
        objectMapper = JsonMapper.nonEmptyMapper().getMapper();
        objectMapper.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
    }

    @Override
    public PageQueryResult<YosStoreConfigPageItem> pageList(YosStoreConfigPageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        QueryResult queryResult = queryService.query("apiGroupList", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
    }

    @Override
    public PageQueryResult<YosStoreConfigPageItem> pageListForSp(YosStoreConfigPageQueryParam param) {
        List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
        if (Collections3.isEmpty(apiGroupCodes)) {
            return PageUtils.getEmptyPage(param.getPageNo());
        }
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> bizParams = getBizParams(param);
        bizParams.put("apiGroupCodes", apiGroupCodes);
        queryParam.setParams(bizParams);
        QueryResult queryResult = queryService.query("apiGroupListForSp", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
    }

    @Override
    public YosStoreConfigVO findById(Long id) {
        Map<String, Object> param = Maps.newHashMap();
        param.put("id", id);
        Map map = queryService.queryUnique("findById", param, false);
        return convert(map);
    }

    @Override
    public List<YosStoreTypeVO> listYosStoreTypes() {
        List<YosStoreTypeVO> list = Lists.newArrayList();
        Map<String, YosStoreTypeEnum> valueMap = YosStoreTypeEnum.getValueMap();
        for (Map.Entry<String, YosStoreTypeEnum> entry : valueMap.entrySet()) {
            list.add(new YosStoreTypeVO(entry.getKey(), entry.getValue().getDisplayName()));
        }
        return list;
    }

    private Map<String, Object> getBizParams(YosStoreConfigPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiGroupCode", param.getApiGroupCode());
        bizParams.put("createdStartDate", param.getCreatedStartDate());
        bizParams.put("createdEndDate", param.getCreatedEndDate());
        return bizParams;
    }

    private YosStoreConfigVO convert(Map<String, Object> params) {
        YosStoreConfigVO vo = new YosStoreConfigVO();
        if (params == null || params.isEmpty()) {
            return vo;
        }
        vo.setId((Long) params.get("id"));
        vo.setApiGroupCode((String) params.get("api_group_code"));
        vo.setStoreType((String) params.get("store_type"));
        // 处理secretKey
        FileStoreConfig storeConfig = deleteSecretKey(params.get("store_config").toString());
        CephStoreConfigVO storeConfigVO = new CephStoreConfigVO();
        BeanUtils.copyProperties(storeConfig, storeConfigVO);
        vo.setStoreConfig(storeConfigVO);

        vo.setIsDefault("1".equals(params.get("is_default").toString()));
        vo.setVersion(((BigInteger) params.get("version")).longValue());
        vo.setCreatedDate((Date) params.get("created_datetime"));
        vo.setLastModifiedDate((Date) params.get("last_modified_datetime"));
        return vo;
    }

    class YosStoreConfigItemConverter extends BasePageItemConverter<YosStoreConfigPageItem> {

        @Override
        public YosStoreConfigPageItem convert(Map<String, Object> params) {
            YosStoreConfigPageItem item = new YosStoreConfigPageItem();
            item.setId((Long) params.get("id"));
            item.setApiGroupCode(params.get("api_group_code").toString());
            item.setStoreType(params.get("store_type").toString());
            // 处理secretKey
            FileStoreConfig storeConfig = hideSecretKey(params.get("store_config").toString());
            CephStoreConfigVO storeConfigVO = new CephStoreConfigVO();
            BeanUtils.copyProperties(storeConfig, storeConfigVO);
            item.setStoreConfig(storeConfigVO);

            String isDefault = params.get("is_default") == null ? null : params.get("is_default").toString();
            item.setIsDefault(StringUtils.isNotBlank(isDefault) && "1".equals(isDefault));
            item.setVersion(((BigInteger) params.get("version")).longValue());
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }

    private FileStoreConfig hideSecretKey(String storeConfig) {
        try {
            CephStoreConfig cephStoreConfig = objectMapper.readValue(storeConfig, CephStoreConfig.class);
            String secretKey = cephStoreConfig.getSecretKey();
            if (StringUtils.isNotBlank(secretKey) && secretKey.length() > 6) {
                secretKey = secretKey.subSequence(0, 3) + "********" + secretKey.substring(secretKey.length() - 4, secretKey.length() - 1);
            } else {
                secretKey = "********";
            }
            cephStoreConfig.setSecretKey(secretKey);
            return cephStoreConfig;
        } catch (IOException e) {
            LOGGER.error("read store config from json error!", e);
        }
        return null;
    }

    private FileStoreConfig deleteSecretKey(String storeConfig) {
        try {
            CephStoreConfig cephStoreConfig = objectMapper.readValue(storeConfig, CephStoreConfig.class);
            cephStoreConfig.setSecretKey(null);
            return cephStoreConfig;
        } catch (IOException e) {
            LOGGER.error("read store config from json error!", e);
        }
        return null;
    }

}
