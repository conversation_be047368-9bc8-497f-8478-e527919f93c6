/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.LimitRuleCreate;
import com.yeepay.g3.app.yop.portal.validation.group.LimitRuleEdit;
import com.yeepay.g3.facade.yop.sys.dto.limit.IpItem;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/5/11 上午10:41
 */
public class LimitRuleVO extends BaseVO {

    private static final long serialVersionUID = -1;

    @NotNull(groups = LimitRuleEdit.class)
    private Long id;

    @Size(max = 32, groups = LimitRuleCreate.class)
    private String customerNo;

    @Size(max = 64, groups = LimitRuleCreate.class)
    private String appId;

    @Size(max = 12, groups = LimitRuleCreate.class)
    private String requestMethod;

    @Size(max = 84, groups = LimitRuleCreate.class)
    private String requestPath;

    @Size(min = 1, groups = {LimitRuleCreate.class, LimitRuleEdit.class})
    private List<IpItem> whitelist;

    @Size(min = 1, groups = {LimitRuleCreate.class, LimitRuleEdit.class})
    private List<IpItem> blacklist;

    @Min(value = 1, groups = {LimitRuleCreate.class, LimitRuleEdit.class})
    private Integer rate;

    @NotNull(groups = {LimitRuleCreate.class, LimitRuleEdit.class})
    private String cause;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public String getRequestPath() {
        return requestPath;
    }

    public void setRequestPath(String requestPath) {
        this.requestPath = requestPath;
    }

    public List<IpItem> getWhitelist() {
        return whitelist;
    }

    public void setWhitelist(List<IpItem> whitelist) {
        this.whitelist = whitelist;
    }

    public List<IpItem> getBlacklist() {
        return blacklist;
    }

    public void setBlacklist(List<IpItem> blacklist) {
        this.blacklist = blacklist;
    }

    public Integer getRate() {
        return rate;
    }

    public void setRate(Integer rate) {
        this.rate = rate;
    }

    public String getCause() {
        return cause;
    }

    public void setCause(String cause) {
        this.cause = cause;
    }
}
