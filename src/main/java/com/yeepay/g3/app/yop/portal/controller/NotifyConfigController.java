/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.NotifyConfigVO;
import com.yeepay.g3.facade.yop.sys.dto.NotifyConfigDTO;
import com.yeepay.g3.facade.yop.sys.dto.NotifyProtocolDTO;
import com.yeepay.g3.facade.yop.sys.facade.NotifyConfigFacade;
import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/6/20 下午3:37
 */
@Controller
@RequestMapping("/rest/notify-config")
public class NotifyConfigController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppController.class);

    private NotifyConfigFacade notifyConfigFacade = RemoteServiceFactory.getService(NotifyConfigFacade.class);

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam(value = "appId") String appId) {
        try {
            NotifyConfigDTO notifyConfigDTO = notifyConfigFacade.find(appId);
            NotifyConfigVO notifyConfigVO = null;
            if (null != notifyConfigDTO) {
                notifyConfigVO = new NotifyConfigVO();
                BeanUtils.copyProperties(notifyConfigDTO, notifyConfigVO);
                if (notifyConfigDTO.getProtocol() != null) {
                    notifyConfigVO.setProtocol(notifyConfigDTO.getProtocol().toString());
                }
            }
            return new ResponseMessage("result", notifyConfigVO);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when detail notify config[" + appId + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody NotifyConfigVO notifyConfigVO) {
        CheckUtils.notEmpty(notifyConfigVO.getAppId(), "appId");
        CheckUtils.notEmpty(notifyConfigVO.getNotifyUrl(), "notifyUrl");
        CheckUtils.notEmpty(notifyConfigVO.getProtocol(), "protocol");
        try {
            NotifyConfigDTO notifyConfigDTO = new NotifyConfigDTO();
            BeanUtils.copyProperties(notifyConfigVO, notifyConfigDTO);
            notifyConfigDTO.setProtocol(new NotifyProtocolDTO(notifyConfigVO.getProtocol()));
            notifyConfigFacade.createOrUpdate(notifyConfigDTO);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when update notify config[" + notifyConfigVO + "]", e);
            return new ResponseMessage(e);
        }
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/commons/protocol", method = RequestMethod.GET)
    public ResponseMessage type() {
        List<CommonsVO> list = new ArrayList<>();
        Map<String, String> protocols = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_NOTIFY_PROTOCOLS);
        protocols.forEach((code, name) -> list.add(new CommonsVO(code, name)));
        return new ResponseMessage("result", list);
    }

}
