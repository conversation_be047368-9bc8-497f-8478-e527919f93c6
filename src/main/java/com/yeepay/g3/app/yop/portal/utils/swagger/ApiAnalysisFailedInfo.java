package com.yeepay.g3.app.yop.portal.utils.swagger;

/**
 * title: Api分析失败信息<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-06-20 17:08
 */
public class ApiAnalysisFailedInfo {

    private String apiUri;

    private String errorMsg;

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public ApiAnalysisFailedInfo withApiUri(String apiUri) {
        this.apiUri = apiUri;
        return this;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public ApiAnalysisFailedInfo withErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
        return this;
    }
}
