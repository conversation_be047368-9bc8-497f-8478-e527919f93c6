/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.authc.strategy;

import com.yeepay.g3.app.yop.portal.shiro.authc.BasicAuthToken;
import com.yeepay.g3.app.yop.portal.shiro.authc.Boss3gToken;
import com.yeepay.g3.app.yop.portal.shiro.authc.OAuth2Token;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.IncorrectCredentialsException;

/**
 * title: 策略模式：用于认证token<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(Yee<PERSON><PERSON>)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/7 21:13
 */
public class AuthenticationContext {
    private static final Logger LOGGER = LoggerFactory.getLogger(AuthenticationContext.class);
    private AuthenticationToken authcToken;

    public AuthenticationContext(AuthenticationToken authcToken) {
        this.authcToken = authcToken;
    }

    public AuthenticationInfo doGetAuthenticationInfo() throws AuthenticationException {
        if (authcToken instanceof OAuth2Token) {
            return ((OAuth2Token) authcToken).doGetAuthenticationInfo();
        } else if (authcToken instanceof Boss3gToken) {
            return ((Boss3gToken) authcToken).doGetAuthenticationInfo();
        } else if (authcToken instanceof BasicAuthToken) {
            return ((BasicAuthToken) authcToken).doGetAuthenticationInfo();
        } else {
            LOGGER.warn("login failed, cause: incorrect login name for user {}", authcToken);
            throw new IncorrectCredentialsException("incorrect user");
        }
    }
}
