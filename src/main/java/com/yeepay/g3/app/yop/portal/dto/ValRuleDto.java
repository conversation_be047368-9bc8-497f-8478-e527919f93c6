package com.yeepay.g3.app.yop.portal.dto;

import com.yeepay.g3.facade.yop.api.val.ValRule;
import com.yeepay.g3.facade.yop.api.val.rule.*;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.json.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by menghao.chen on 15/5/11.
 */
public class ValRuleDto implements Serializable {

    private Boolean notNull;

    private Boolean notBlank;

    private Boolean isEmail;

    private Boolean isMobile;

    private Boolean isUrl;

    private Boolean isInt;

    private String pattern;

    private Range range;

    private Length length;

    public ValRuleDto() {
    }

    public ValRuleDto(List<ValRule> valRules) {
        if (CollectionUtils.isNotEmpty(valRules)) {
            for (ValRule valRule : valRules) {
                if (valRule instanceof NotNull) {
                    notNull = true;
                } else if (valRule instanceof NotBlank) {
                    notBlank = true;
                } else if (valRule instanceof Email) {
                    isEmail = true;
                } else if (valRule instanceof Mobile) {
                    isMobile = true;
                } else if (valRule instanceof URL) {
                    isUrl = true;
                } else if (valRule instanceof RequireInt) {
                    isInt = true;
                } else if (valRule instanceof MatchPattern) {
                    pattern = ((MatchPattern) valRule).pattern();
                } else if (valRule instanceof Length) {
                    length = new Length(((Length) valRule).min(),
                            ((Length) valRule).max());
                } else if (valRule instanceof Range) {
                    range = new Range(((Range) valRule).min(),
                            ((Range) valRule).max());
                }
            }
        }
    }

    public List<ValRule> toRules() {
        List<ValRule> rules = new ArrayList<ValRule>();
        if (notNull != null && notNull) {
            rules.add(new NotNull());
        }
        if (notBlank != null && notBlank) {
            rules.add(new NotBlank());
        }
        if (isEmail != null && isEmail) {
            rules.add(new Email());
        }
        if (isMobile != null && isMobile) {
            rules.add(new Mobile());
        }
        if (isUrl != null && isUrl) {
            rules.add(new URL());
        }
        if (isInt != null && isInt) {
            rules.add(new RequireInt());
        }
        if (StringUtils.isNotBlank(pattern)) {
            rules.add(new MatchPattern(pattern));
        }
        if (range != null) {
            rules.add(new Range(range.getMin(), range.getMax()));
        }
        if (length != null) {
            rules.add(new Length(length.getMin(), length.getMax()));
        }
        return rules;
    }

    @Override
    public String toString() {
        return new JSONObject(this).toString();
    }

    public Boolean getNotNull() {
        return notNull;
    }

    public void setNotNull(Boolean notNull) {
        this.notNull = notNull;
    }

    public Boolean getNotBlank() {
        return notBlank;
    }

    public void setNotBlank(Boolean notBlank) {
        this.notBlank = notBlank;
    }

    public Boolean getIsEmail() {
        return isEmail;
    }

    public void setIsEmail(Boolean isEmail) {
        this.isEmail = isEmail;
    }

    public Boolean getIsMobile() {
        return isMobile;
    }

    public void setIsMobile(Boolean isMobile) {
        this.isMobile = isMobile;
    }

    public Boolean getIsUrl() {
        return isUrl;
    }

    public void setIsUrl(Boolean isUrl) {
        this.isUrl = isUrl;
    }

    public Boolean getIsInt() {
        return isInt;
    }

    public void setIsInt(Boolean isInt) {
        this.isInt = isInt;
    }

    public String getPattern() {
        return pattern;
    }

    public void setPattern(String pattern) {
        this.pattern = pattern;
    }

    public Range getRange() {
        return range;
    }

    public void setRange(Range range) {
        this.range = range;
    }

    public Length getLength() {
        return length;
    }

    public void setLength(Length length) {
        this.length = length;
    }

}
