package com.yeepay.g3.app.yop.portal.shiro.utils;

import com.yeepay.g3.app.yop.portal.exception.YopPortalAuthException;
import com.yeepay.g3.app.yop.portal.shiro.authc.OAuth2Token;
import com.yeepay.g3.app.yop.portal.utils.YopConfigFileUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import io.jsonwebtoken.*;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/6/8 下午2:00
 */
public class OAuth2TokenUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(OAuth2TokenUtils.class);

    private static final String ISSUER = "yop-boss";
    private static final String SP_SEPARATOR = ",";

    private static final String SIGN_KEY;

    static {
        try {
            SIGN_KEY = IOUtils.toString(YopConfigFileUtils.getFileInputStream("oauth2_key"));
        } catch (IOException e) {
            LOGGER.warn("", e);
            throw new RuntimeException(e);
        }
    }

    public static String generate(String userId, String userName, OperatorTypeEnum userType, Set<String> spScope, Set<String> apiGroupScope) {
        Long expiredSeconds = (Long) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_OAUTH2_EXPIRED_SECONDS);
        JwtBuilder jwtBuilder = Jwts.builder()
                .setIssuer(ISSUER)
                .setId(userId)
                .setSubject(userName)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expiredSeconds * 1000))
                .claim("userType", userType)
//                .claim("scope", mapJoiner.join(scope))
                .claim("spScope", StringUtils.join(spScope, SP_SEPARATOR))
                .claim("apiGroupScope", StringUtils.join(apiGroupScope, SP_SEPARATOR))
                .signWith(SignatureAlgorithm.HS512, SIGN_KEY);
        return jwtBuilder.compact();
    }

    public static OAuth2Token verify(String compactJws) {
        try {
            Jws<Claims> jws = Jwts.parser().setSigningKey(SIGN_KEY).parseClaimsJws(compactJws);
            String issuer = jws.getBody().getIssuer();
            if (!StringUtils.equals(ISSUER, issuer)) {
                throw new YopPortalAuthException("无法认证由 " + issuer + " 颁发的令牌");
            }
            if (jws.getBody().getExpiration() == null) {
                LOGGER.warn("Old expired token:{}", compactJws);
                throw new YopPortalAuthException("Expired token");
            }
            String userId = jws.getBody().getId();
            String userName = jws.getBody().getSubject();
            OperatorTypeEnum userType = OperatorTypeEnum.valueOf((String) jws.getBody().get("userType"));
            Set<String> spScope = new HashSet<>();
            Set<String> apiGroupScope = new HashSet<>();
            String spScopeStr = (String) jws.getBody().get("spScope");
            if (StringUtils.isNotBlank(spScopeStr)) {
                spScope = new HashSet<>(Arrays.asList(StringUtils.split(spScopeStr, SP_SEPARATOR)));
            }
            String apiGroupScopeStr = (String) jws.getBody().get("apiGroupScope");
            if (StringUtils.isNotBlank(apiGroupScopeStr)) {
                apiGroupScope = new HashSet<>(Arrays.asList(StringUtils.split(apiGroupScopeStr, SP_SEPARATOR)));
            }
            return new OAuth2Token(userId, userName, userType, spScope, apiGroupScope);
        } catch (ExpiredJwtException e) {
            LOGGER.warn("Expired token:{}", compactJws);
            throw new YopPortalAuthException("Expired token");
        } catch (UnsupportedJwtException e) {
            LOGGER.warn("Unsupported token:{}", compactJws);
            throw new YopPortalAuthException("Unsupported token");
        } catch (MalformedJwtException e) {
            LOGGER.warn("Malformed token:{}", compactJws);
            throw new YopPortalAuthException("Malformed token");
        } catch (SignatureException e) {
            LOGGER.warn("Invalid signed token:{}", compactJws);
            throw new YopPortalAuthException("Invalid signed token");
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Illegal argument token:{}", compactJws);
            throw new YopPortalAuthException("Illegal argument token");
        } catch (Exception e) {
            LOGGER.warn("Unsupported token caused by {}, token:{}", e.getMessage(), compactJws);
            throw e;
        }
    }

}
