/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.fasterxml.jackson.core.type.TypeReference;
import com.yeepay.g3.app.yop.portal.controller.support.imports.ApiRouteImportFailedDetail;
import com.yeepay.g3.app.yop.portal.controller.support.imports.ApiRouteImportRequest;
import com.yeepay.g3.app.yop.portal.controller.support.imports.ApiRouteImportResult;
import com.yeepay.g3.app.yop.portal.dto.ApiRouteImportContext;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.ApiRouteImportCacheService;
import com.yeepay.g3.app.yop.portal.service.ApiRouteQueryService;
import com.yeepay.g3.app.yop.portal.service.ClassLoaderService;
import com.yeepay.g3.app.yop.portal.utils.IdGenerator;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.ApiRouteImportAnalysisResult;
import com.yeepay.g3.app.yop.portal.vo.ApiRouteVO;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.SystemParameterVO;
import com.yeepay.g3.app.yop.portal.vo.page.ApiRouteListItem;
import com.yeepay.g3.core.yop.utils.Exceptions;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;
import com.yeepay.g3.facade.yop.sys.dto.route.ApiRouteArrangeRequest;
import com.yeepay.g3.facade.yop.sys.dto.route.ApiRouteDTO;
import com.yeepay.g3.facade.yop.sys.enums.AppTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.SwaggerDataFormatEnum;
import com.yeepay.g3.facade.yop.sys.facade.ApiRouteMgrFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import io.swagger.v3.core.util.Json;
import io.swagger.v3.core.util.Yaml;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020/11/9 15:07
 */
@Controller
@RequestMapping("/rest/api/route")
public class ApiRouteMgrController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiRouteMgrController.class);

    @Autowired
    private ApiRouteQueryService apiRouteQueryService;

    private ApiRouteMgrFacade apiRouteMgrFacade = RemoteServiceFactory.getService(ApiRouteMgrFacade.class);

    @Autowired
    private ClassLoaderService classLoaderService;

    @Autowired
    private ApiRouteImportCacheService apiRouteImportCacheService;

    @ResponseBody
    @RequestMapping(value = "/commons/type", method = RequestMethod.GET)
    public ResponseMessage type() {
        List<CommonsVO> list = new ArrayList<>();
        AppTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @Operation(summary = "Your GET endpoint", description = "", tags = {"route-mgr"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK", content = @Content(array = @ArraySchema(schema = @Schema(implementation = SystemParameterVO.class))))})
    @RequestMapping(value = "/commons/sys-params",
            produces = {"application/json"},
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage<List<SystemParameterVO>> getRestApiManageCommonsSysParams() {
        List<String> sysParamsConfig = (List<String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_SYSTEM_PARAMS_CONFIG);
        if (com.yeepay.g3.utils.common.CollectionUtils.isNotEmpty(sysParamsConfig)) {
            List<SystemParameterVO> list = new ArrayList<>(sysParamsConfig.size());
            sysParamsConfig.forEach(param -> {
                String[] items = StringUtils.split(param, ",");
                if (items.length == 3) {
                    list.add(new SystemParameterVO(items[0].trim(), items[1].trim(), items[2].trim()));
                }
            });
            return new ResponseMessage("result", list);
        }
        return new ResponseMessage("result", Collections.emptyList());
    }

    @Operation(summary = "映射的隐式入参地址", description = "系统参数 ", tags = {})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK", content = @Content(schema = @Schema(implementation = CommonsVO.class)))})
    @RequestMapping(value = "/commons/implicit-param-in",
            produces = {"application/json"},
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage<List<CommonsVO>> getRestApiRouteCommonsImplicitParamIn() {
        Map<String, String> config = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_API_ROUTE_IMPLICIT_PARAM_IN);
        List<CommonsVO> res = new ArrayList<>();
        config.forEach((key, value) -> res.add(new CommonsVO(key, value)));
        return new ResponseMessage("result", res);
    }


    @Operation(summary = "查询路由详情", description = "", tags = {"route-mgr"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK", content = @Content(schema = @Schema(implementation = ApiRouteDTO.class)))})
    @RequestMapping(value = "/detail",
            produces = {"application/json"},
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage<ApiRouteVO> getRestApiRouteDetail(@NotNull @Parameter(in = ParameterIn.QUERY, description = "路由id", required = true, schema = @Schema()) @Valid @RequestParam(value = "id", required = true) Long id) {
        return new ResponseMessage("result", ApiRouteVO.convert(apiRouteMgrFacade.findById(id)));
    }


    @Operation(summary = "查询api下的route列表", description = "", tags = {})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK", content = @Content(schema = @Schema(implementation = ApiRouteListItem.class)))})
    @RequestMapping(value = "/list",
            produces = {"application/json"},
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage<List<ApiRouteListItem>> getRestApiRouteList(@NotNull @Parameter(in = ParameterIn.QUERY, description = "", required = true, schema = @Schema()) @Valid @RequestParam(value = "apiId", required = true) String apiId) {
        List<ApiRouteListItem> apiRouteListItems = apiRouteQueryService.queryForApiList(apiId);
        return new ResponseMessage("result", apiRouteListItems);
    }


    @Operation(summary = "创建路由-dubbo", description = "", tags = {"route-mgr"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK")})
    @RequestMapping(value = "/create",
            consumes = {"application/json"},
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage postRestApiRouteCreate(@Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody ApiRouteVO apiRouteVO) {
        apiRouteMgrFacade.create(ApiRouteVO.convert(apiRouteVO));
        return new ResponseMessage();
    }


    @Operation(summary = "删除路由", description = "", tags = {"route-mgr"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK")})
    @RequestMapping(value = "/delete",
            consumes = {"application/x-www-form-urlencoded"},
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage postRestApiRouteDelete(@RequestParam("id") Long id) {
        apiRouteMgrFacade.delete(id);
        return new ResponseMessage();
    }


    @Operation(summary = "禁用路由", description = "", tags = {"route-mgr"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK")})
    @RequestMapping(value = "/disable",
            consumes = {"application/x-www-form-urlencoded"},
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage postRestApiRouteDisable(@RequestParam("id") Long id) {
        apiRouteMgrFacade.disable(id);
        return new ResponseMessage();
    }


    @Operation(summary = "拖动路由", description = "待改-拖动完需要保存，前端返回排序结果", tags = {"route-mgr"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK")})
    @RequestMapping(value = "/arrange",
            consumes = {"application/json"},
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage postRestApiRouteDrag(@Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody ApiRouteArrangeRequest request) {
        apiRouteMgrFacade.arrange(request);
        return new ResponseMessage();
    }


    @Operation(summary = "编辑路由", description = "", tags = {"route-mgr"})
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "OK")})
    @RequestMapping(value = "/update",
            consumes = {"application/json"},
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage<Void> postRestApiRouteEdit(@Parameter(in = ParameterIn.DEFAULT, description = "", schema = @Schema()) @Valid @RequestBody ApiRouteVO apiRouteVO) {
        apiRouteMgrFacade.update(ApiRouteVO.convert(apiRouteVO));
        return new ResponseMessage();
    }


    @RequestMapping(value = "/enable",
            consumes = {"application/x-www-form-urlencoded"},
            method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage postRestApiRouteEnable(@RequestParam("id") Long id) {
        apiRouteMgrFacade.enable(id);
        return new ResponseMessage();
    }

    @RequestMapping(value = "/end-class/list",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage postRestApiRouteEndClassList(@RequestParam(value = "serviceName") String serviceName) {
        List<String> tests = new ArrayList<>();
        return new ResponseMessage("result", tests);
    }

    @RequestMapping(value = "/end-class/method/list",
            method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage<List<String>> postRestApiRouteEndClassMethod(@RequestParam(value = "endClass") String endClass) throws Exception {
        return new ResponseMessage("result", classLoaderService.loadMethods(endClass));
    }

    @GetMapping("/export")
    @ResponseBody
    public ResponseEntity<byte[]> export(@RequestParam String apiGroup,
                                         @RequestParam List<String> apis,
                                         @RequestParam Boolean draft,
                                         @RequestParam SwaggerDataFormatEnum dataFormat) {
        try {
            RouteExportRequest request = new RouteExportRequest();
            request.setApiGroup(apiGroup);
            request.setApis(apis);
            request.setDraft(draft);
            RouteExportResponse response = apiRouteMgrFacade.exportRoute(request);
            List<RouteWithApiRequestKey> items = response.getItems();
            String data = dataFormat == SwaggerDataFormatEnum.JSON ?
                    Json.mapper().writeValueAsString(items) : Yaml.mapper().writeValueAsString(items);
            byte[] bytes = data.getBytes(StandardCharsets.UTF_8);
            String fileName = "route." + dataFormat.getValue().toLowerCase();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            LOGGER.error("export route failed.", e);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_HTML);
            return new ResponseEntity<>(Exceptions.getRootCause(e).getMessage().getBytes(StandardCharsets.UTF_8), headers,
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private static final TypeReference<List<RouteWithApiRequestKey>> typeReference = new TypeReference<List<RouteWithApiRequestKey>>() {
    };

    @PostMapping("/import/analysis")
    @ResponseBody
    public ResponseMessage importAnalysis(@RequestParam("apiGroup") String apiGroup,
                                          @RequestParam("data") MultipartFile data,
                                          @RequestParam("dataFormat") SwaggerDataFormatEnum dataFormat) {
        try {
            String requestId = IdGenerator.generate();
            Map<ApiRequestKey, List<ApiRouteDTO>> map = parseRouteImportFile(data, dataFormat);
            RouteCheckImportRequest request = new RouteCheckImportRequest();
            request.setApiGroup(apiGroup);
            request.setApiRequestKeys(new ArrayList<>(map.keySet()));
            RouteCheckImportResponse response = apiRouteMgrFacade.checkImportRoute(request);
            ApiRouteImportContext context = new ApiRouteImportContext();
            context.setApiGroup(apiGroup);
            context.setApiRequestKeyRouteMap(map);
            apiRouteImportCacheService.storeContext(requestId, context);
            ApiRouteImportAnalysisResult result = new ApiRouteImportAnalysisResult();
            result.setRequestId(requestId);
            result.setRouteToCreate(response.getRouteToCreate());
            result.setRouteToOverride(response.getRouteToOverride());
            return new ResponseMessage<>("result", result);
        } catch (Exception e) {
            LOGGER.error("route import data analysis failed.", e);
            return new ResponseMessage(e);
        }
    }

    private Map<ApiRequestKey, List<ApiRouteDTO>> parseRouteImportFile(MultipartFile data,
                                                                       SwaggerDataFormatEnum dataFormat) throws IOException {
        List<RouteWithApiRequestKey> routeWithApiRequestKeys =
                dataFormat == SwaggerDataFormatEnum.JSON ?
                        Json.mapper().readValue(data.getInputStream(), typeReference) :
                        Yaml.mapper().readValue(data.getInputStream(), typeReference);
        return routeWithApiRequestKeys.stream()
                .collect(Collectors.toMap(
                        RouteWithApiRequestKey::getApiRequestKey,
                        RouteWithApiRequestKey::getRoutes
                ));

    }

    @PostMapping("/import")
    @ResponseBody
    public ResponseMessage doImport(@RequestBody ApiRouteImportRequest request) {
        try {
            ApiRouteImportContext context = apiRouteImportCacheService.loadContext(request.getRequestId());
            Set<ApiRequestKey> apiRequestKeys = new HashSet<>(request.getRouteToCreate().size() + request.getRouteToOverride().size());
            apiRequestKeys.addAll(request.getRouteToCreate());
            apiRequestKeys.addAll(request.getRouteToOverride());
            Map<ApiRequestKey, List<ApiRouteDTO>> apiRoutesMap = context.getApiRequestKeyRouteMap()
                    .entrySet()
                    .stream()
                    .filter(entry -> apiRequestKeys.contains(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            RouteImportRequest routeImportRequest = new RouteImportRequest();
            routeImportRequest.setApiGroup(context.getApiGroup());
            routeImportRequest.setApiRoutesMap(apiRoutesMap);
            RouteImportResponse response = apiRouteMgrFacade.importRoute(routeImportRequest);
            List<RouteImportFailedInfo> failedInfos = ObjectUtils.defaultIfNull(response.getFailedInfos(), Collections.emptyList());
            ApiRouteImportResult result = wrapApiRouteImportResult(apiRequestKeys, failedInfos);
            return new ResponseMessage("result", result);
        } catch (Throwable ex) {
            LOGGER.error("route import failed.", ex);
            return new ResponseMessage(ex);
        } finally {
            apiRouteImportCacheService.evictContext(request.getRequestId());
        }
    }

    private ApiRouteImportResult wrapApiRouteImportResult(Set<ApiRequestKey> apiRequestKeys,
                                                          List<RouteImportFailedInfo> failedInfos) {
        ApiRouteImportResult apiRouteImportResult = new ApiRouteImportResult();
        int totalSize = apiRequestKeys.size();
        apiRouteImportResult.setTotal(totalSize);
        int failedSize = failedInfos.size();
        apiRouteImportResult.setFailed(failedSize);
        apiRouteImportResult.setSuccess(totalSize - failedSize);
        apiRouteImportResult.setFailedDetails(parseFailedDetails(failedInfos));
        return apiRouteImportResult;
    }

    private List<ApiRouteImportFailedDetail> parseFailedDetails(List<RouteImportFailedInfo> failedInfos) {
        return failedInfos.stream()
                .map(failedInfo -> {
                    ApiRouteImportFailedDetail info = new ApiRouteImportFailedDetail();
                    info.setApiRequestKey(failedInfo.getApiRequestKey());
                    info.setReason(failedInfo.getReason());
                    return info;
                }).collect(Collectors.toList());
    }

}
