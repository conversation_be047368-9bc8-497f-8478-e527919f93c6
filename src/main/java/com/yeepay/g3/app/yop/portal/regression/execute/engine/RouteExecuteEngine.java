/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.regression.execute.engine;

import com.yeepay.g3.app.yop.portal.regression.AssertEngine;
import com.yeepay.g3.app.yop.portal.regression.ExecutionResult;
import com.yeepay.g3.facade.yop.sys.dto.RegTestCaseFindRespDTO;
import org.springframework.stereotype.Component;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/20 下午5:31
 */
@Component
public class RouteExecuteEngine implements ExecuteEngine {

    @Override
    public ExecutionResult execute(RegTestCaseFindRespDTO testCaseDTO) {
        ExecuteEngine executeEngine = ExecuteEngineFactory.getExecuteEngine(testCaseDTO.getSecurity());
        return executeEngine.execute(testCaseDTO);
    }

    @Override
    public AssertEngine getAssertEngine() {
        return null;
    }

    @Override
    public String getSecurity() {
        return null;
    }

    @Override
    public boolean supportJson() {
        return false;
    }
}
