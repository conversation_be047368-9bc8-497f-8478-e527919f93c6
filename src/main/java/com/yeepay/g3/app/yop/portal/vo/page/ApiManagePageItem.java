package com.yeepay.g3.app.yop.portal.vo.page;

import java.io.Serializable;
import java.util.Date;

/**
 * title: ApiManagePageItem<br/>
 * description: api分页查询返回参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:38
 */
public class ApiManagePageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private String apiId;

    private Long version;

    private String name;

    private String path;

    private String httpMethod;

    private String contentType;

    private String title;

    private String apiGroup;

    private String apiGroupName;

    private String apiType;

    private String description;

    private String status;

    private String routeStatus;

    private Date createdDateTime;

    private Date lastModifiedDateTime;

    public String getApiId() {
        return apiId;
    }

    public ApiManagePageItem setApiId(String apiId) {
        this.apiId = apiId;
        return this;
    }

    public Long getVersion() {
        return version;
    }

    public ApiManagePageItem setVersion(Long version) {
        this.version = version;
        return this;
    }

    public String getName() {
        return name;
    }

    public ApiManagePageItem setName(String name) {
        this.name = name;
        return this;
    }

    public String getPath() {
        return path;
    }

    public ApiManagePageItem setPath(String path) {
        this.path = path;
        return this;
    }

    public String getHttpMethod() {
        return httpMethod;
    }

    public ApiManagePageItem setHttpMethod(String httpMethod) {
        this.httpMethod = httpMethod;
        return this;
    }

    public String getContentType() {
        return contentType;
    }

    public ApiManagePageItem setContentType(String contentType) {
        this.contentType = contentType;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public ApiManagePageItem setTitle(String title) {
        this.title = title;
        return this;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public ApiManagePageItem setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getApiGroupName() {
        return apiGroupName;
    }

    public ApiManagePageItem setApiGroupName(String apiGroupName) {
        this.apiGroupName = apiGroupName;
        return this;
    }

    public String getApiType() {
        return apiType;
    }

    public ApiManagePageItem setApiType(String apiType) {
        this.apiType = apiType;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public ApiManagePageItem setDescription(String description) {
        this.description = description;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public ApiManagePageItem setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getRouteStatus() {
        return routeStatus;
    }

    public void setRouteStatus(String routeStatus) {
        this.routeStatus = routeStatus;
    }

    public Date getCreatedDateTime() {
        return createdDateTime;
    }

    public ApiManagePageItem setCreatedDateTime(Date createdDateTime) {
        this.createdDateTime = createdDateTime;
        return this;
    }

    public Date getLastModifiedDateTime() {
        return lastModifiedDateTime;
    }

    public ApiManagePageItem setLastModifiedDateTime(Date lastModifiedDateTime) {
        this.lastModifiedDateTime = lastModifiedDateTime;
        return this;
    }

    @Override
    public String toString() {
        return "ApiManagePageItem{" +
                "apiId='" + apiId + '\'' +
                ", version=" + version +
                ", name='" + name + '\'' +
                ", path='" + path + '\'' +
                ", httpMethod='" + httpMethod + '\'' +
                ", title='" + title + '\'' +
                ", apiGroup='" + apiGroup + '\'' +
                ", apiGroupName='" + apiGroupName + '\'' +
                ", apiType='" + apiType + '\'' +
                ", description='" + description + '\'' +
                ", status='" + status + '\'' +
                ", routeStatus='" + routeStatus + '\'' +
                ", createdDateTime=" + createdDateTime +
                ", lastModifiedDateTime=" + lastModifiedDateTime +
                '}';
    }

}
