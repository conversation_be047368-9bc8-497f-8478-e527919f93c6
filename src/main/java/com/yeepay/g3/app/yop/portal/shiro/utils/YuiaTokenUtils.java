/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.utils;

import com.yeepay.g3.app.yop.portal.exception.YopPortalAuthException;
import com.yeepay.g3.app.yop.portal.utils.HttpUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/10 15:19
 */
@Component
public class YuiaTokenUtils implements InitializingBean {
    private static final Logger LOGGER = LoggerFactory.getLogger(YuiaTokenUtils.class);
    private static final String YUIA_AUTH_PRINCIPAL_URI = "/auth/principal";
    private static final String YUIA_SSO_TOKEN = "yuiassotoken";
    private static final String SYSTEM_CODE = "systemcode";
    private static final String YOP_PORTAL = "yop-portal";
    private static final String LOGIN_NAME = "loginName";
    public static String address;
    // 权限中心地址
    @Value("${yuia.address}")
    private String yuiaAddress;

    public static String verifyYuiaToken(String token) {
        HttpUtils httpUtils = new HttpUtils();
        String content = "";
        Map<String, String> headers = new HashMap<>();
        headers.put(YUIA_SSO_TOKEN, token);
        headers.put(SYSTEM_CODE, YOP_PORTAL);
        String yuiaAuthUrl = address + YUIA_AUTH_PRINCIPAL_URI;
        String responseBody = httpUtils.postJson(yuiaAuthUrl, content, headers);
        if (StringUtils.isEmpty(responseBody)) {
            LOGGER.error("invoke yuia-service-boss system failure");
            throw new YopPortalAuthException("invoke yuia-service-boss system failure");
        }
        if (!responseBody.contains(LOGIN_NAME)) {
            return null;
        }
        return responseBody;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.address = yuiaAddress;
    }
}
