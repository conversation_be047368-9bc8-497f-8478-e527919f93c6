package com.yeepay.g3.yop.frame.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import org.junit.Test;

import java.util.Optional;
import java.util.concurrent.ExecutionException;

import static java.util.concurrent.TimeUnit.SECONDS;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/7/17 16:37
 */
public class YopCacheLoaderTest {


    class CacheLoaderInstance extends YopCacheLoader<String, String> {
//        int i = 0;

        @Override
        public Optional<String> load(String s) throws Exception {
            System.out.println("load thread:" + Thread.currentThread().getName());
            return super.load(s);
        }

        @Override
        protected String doLoad(String s) throws Exception {
            System.out.println("do load thread:" + Thread.currentThread().getName());
            return null;
        }

        @Override
        protected String getCacheName() {
            return "test";
        }
    }

    @Test
    public void test() throws Exception {
        LoadingCache<String, Optional<String>> loadingCache = CacheBuilder.newBuilder()
                .refreshAfterWrite(10L, SECONDS)
                .build(new CacheLoaderInstance());

        for (int i = 0; i < 3; i++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        System.out.println("request thread:" + Thread.currentThread().getName() + ", result:" + loadingCache.get("123").orElse(null));
                    } catch (ExecutionException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
        }
        Thread.sleep(15000L);

        for (int i = 0; i < 3; i++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    ;
                    try {
                        System.out.println("request thread:" + Thread.currentThread().getName() + ", result:" + loadingCache.get("123").orElse(null));
                    } catch (ExecutionException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
        }
        Thread.sleep(2000L);
        for (int i = 0; i < 3; i++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    ;
                    try {
                        System.out.println("request thread:" + Thread.currentThread().getName() + ", result:" + loadingCache.get("123").orElse(null));
                    } catch (ExecutionException e) {
                        e.printStackTrace();
                    }
                }
            }).start();
        }
        Thread.sleep(2000L);
    }

}
