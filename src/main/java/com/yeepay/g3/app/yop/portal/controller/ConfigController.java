package com.yeepay.g3.app.yop.portal.controller;

import com.google.common.collect.Sets;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.AppConfigVO;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Map;
import java.util.Set;

/**
 * title: 应用配置控制器<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/4/10 10:55
 */
@Controller
@RequestMapping("/rest/config")
public class ConfigController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConfigController.class);

    private static final JsonMapper JSON_MAPPER = JsonMapper.nonEmptyMapper();
    private static final String YUIA = "yuia";
    private static final String BOSS3G = "boss3g";

    private static final Set<String> LOGIN_TYPES;

    static {
        LOGIN_TYPES = Sets.newHashSet();
        LOGIN_TYPES.add(YUIA);
        LOGIN_TYPES.add(BOSS3G);
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.GET)
    public ResponseMessage queryAppConfig() {
        //根据同一配置，确定登陆类型
        String filterChainDefinition = (String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_RESOURCE_PERM_NEW);
        Map<String, String> filterChainDefinitionMap = JSON_MAPPER.fromJson(filterChainDefinition, Map.class);
        String loginType = YUIA;
        if (MapUtils.isNotEmpty(filterChainDefinitionMap) && filterChainDefinitionMap.containsKey("/signin/sso/boss3g")) {
            loginType = filterChainDefinitionMap.get("/signin/sso/boss3g");
            if (!LOGIN_TYPES.contains(loginType)) {
                LOGGER.error("loginType is unsupported");
                loginType = YUIA;
            }
        }

        AppConfigVO appConfigVO = new AppConfigVO()
                .withLoginUrl((String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_LOGIN_URL))
                .withLogoutUrl((String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_BOSS_LOGOUT_URL))
                .withLinkLogCenter((String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_INVOKE_LOG_LINK_LOG_CENTER))
                .withLinkCallChain((String) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_INVOKE_LOG_LINK_CALL_CHAIN))
                .withRt((Map) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_CONFIG_PLATFORM_TEST))
                .withLoginType(loginType);
        return new ResponseMessage("config", appConfigVO);
    }
}
