/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * title: 用户视图对象<br>
 * description: 用户搜索结果的视图对象<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-01-26
 */
@Data
@ApiModel(description = "用户视图对象")
public class UserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", example = "12345")
    @JsonProperty("id")
    private String id;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名", example = "张三")
    @JsonProperty("name")
    private String name;

    /**
     * 用户邮箱
     */
    @ApiModelProperty(value = "用户邮箱", example = "<EMAIL>")
    @JsonProperty("email")
    private String email;

    /**
     * 用户部门
     */
    @ApiModelProperty(value = "用户部门", example = "技术部")
    @JsonProperty("department")
    private String department;

    /**
     * 是否在职
     */
    @ApiModelProperty(value = "是否在职", example = "true")
    @JsonProperty("active")
    private boolean active;

    /**
     * 默认构造函数
     */
    public UserVO() {
    }

    /**
     * 全参构造函数
     */
    public UserVO(String id, String name, String email, String department, boolean active) {
        this.id = id;
        this.name = name;
        this.email = email;
        this.department = department;
        this.active = active;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
