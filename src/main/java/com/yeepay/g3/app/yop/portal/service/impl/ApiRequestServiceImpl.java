package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ApiRequestService;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 11:30
 */
@Component
public class ApiRequestServiceImpl implements ApiRequestService {

    @Resource(name = "apiRequestQueryService")
    private QueryService queryService;

    @Override
    public Map<String, String> findApiIdsForOldApi(List<String> apiUris) {
        final List<Map<String, Object>> result = queryService.query("findApiIdsForOldApi", Collections.singletonMap("apiUris", apiUris));
        if (CollectionUtils.isNotEmpty(result)) {
            Map<String, String> convertedResult = Maps.newHashMapWithExpectedSize(result.size());
            result.forEach(item -> convertedResult.put((String) item.get("api_uri"), (String) item.get("api_id")));
            return convertedResult;
        }
        return Collections.emptyMap();
    }

    @Override
    public String findApiIdForNewApi(String httpMethod, String path) {
        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("httpMethod", httpMethod);
        queryMap.put("path", path);
        final List<Map<String, Object>> result = queryService.query("findApiIdForNewApi", queryMap);
        if (CollectionUtils.isNotEmpty(result)) {
            return (String) result.get(0).get("api_id");
        }
        return null;
    }

    @Override
    public String findApiIdByPath(String path) {
        Map<String, Object> queryMap = Maps.newHashMap();
        queryMap.put("path", path);
        final List<Map<String, Object>> result = queryService.query("findApiIdByPath", queryMap);
        if (CollectionUtils.isNotEmpty(result)) {
            return (String) result.get(0).get("api_id");
        }
        return null;
    }
}
