/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.BackendAppCommon;
import com.yeepay.g3.app.yop.portal.validation.group.BackendAppCreate;
import com.yeepay.g3.app.yop.portal.validation.group.BackendAppEdit;
import com.yeepay.g3.app.yop.portal.validation.group.BackendAppRebuild;
import com.yeepay.g3.facade.yop.sys.enums.ClassLoadModeEnum;
import com.yeepay.g3.facade.yop.sys.enums.DeployModeEnum;
import com.yeepay.g3.facade.yop.sys.enums.RpcModeEnum;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/9/25 上午11:18
 */
public class BackendAppVO implements Serializable {

    private static final long serialVersionUID = -1;

    @NotEmpty(message = "{backendApp.spCode}", groups = BackendAppCreate.class)
    private String spCode;

    @NotEmpty(message = "{backendApp.backendCode}", groups = BackendAppCommon.class)
    private String backendCode;

    @NotEmpty(message = "{backendApp.backendName}", groups = BackendAppCreate.class)
    private String backendName;

    @NotNull(message = "{backendApp.deployMode}", groups = BackendAppCreate.class)
    private DeployModeEnum deployMode;

    @NotNull(message = "{backendApp.classLoadMode}", groups = BackendAppCreate.class)
    private ClassLoadModeEnum classLoadMode;

    @NotNull(message = "{backendApp.rpcMode}", groups = BackendAppCreate.class)
    private RpcModeEnum rpcMode;

    private String description;

    @NotNull(message = "{backendApp.version}", groups = BackendAppEdit.class)
    private Long version;

    @NotEmpty(message = "{backendApp.reason}", groups = BackendAppRebuild.class)
    private String reason;

    private Date createdDate;

    private Date lastModifiedDate;


    public String getSpCode() {
        return spCode;
    }

    public void setSpCode(String spCode) {
        this.spCode = spCode;
    }

    public String getBackendCode() {
        return backendCode;
    }

    public void setBackendCode(String backendCode) {
        this.backendCode = backendCode;
    }

    public String getBackendName() {
        return backendName;
    }

    public void setBackendName(String backendName) {
        this.backendName = backendName;
    }

    public DeployModeEnum getDeployMode() {
        return deployMode;
    }

    public void setDeployMode(DeployModeEnum deployMode) {
        this.deployMode = deployMode;
    }

    public ClassLoadModeEnum getClassLoadMode() {
        return classLoadMode;
    }

    public void setClassLoadMode(ClassLoadModeEnum classLoadMode) {
        this.classLoadMode = classLoadMode;
    }

    public RpcModeEnum getRpcMode() {
        return rpcMode;
    }

    public void setRpcMode(RpcModeEnum rpcMode) {
        this.rpcMode = rpcMode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }
}
