package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.validation.group.ApiCommon;
import org.hibernate.validator.constraints.NotEmpty;

import java.io.Serializable;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/12 11:27
 */
public abstract class AbstractApiParamVO implements Serializable {

    private static final long serialVersionUID = 5422371485331614856L;

    @NotEmpty(message = "{api.param.paramName}", groups = ApiCommon.class)
    protected String paramName;

    @NotEmpty(message = "{api.param.paramTitle}", groups = ApiCommon.class)
    protected String paramTitle;

    @NotEmpty(message = "{api.param.paramDataType}")
    protected String paramDataType;

    @NotEmpty(message = "{api.param.paramDataFormat}")
    protected String paramDataFormat;

    protected String description;

    protected String sampleValue;

    protected Boolean sensitive;

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public String getParamTitle() {
        return paramTitle;
    }

    public void setParamTitle(String paramTitle) {
        this.paramTitle = paramTitle;
    }

    public String getParamDataType() {
        return paramDataType;
    }

    public void setParamDataType(String paramDataType) {
        this.paramDataType = paramDataType;
    }

    public String getParamDataFormat() {
        return paramDataFormat;
    }

    public void setParamDataFormat(String paramDataFormat) {
        this.paramDataFormat = paramDataFormat;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSampleValue() {
        return sampleValue;
    }

    public void setSampleValue(String sampleValue) {
        this.sampleValue = sampleValue;
    }

    public Boolean getSensitive() {
        return sensitive;
    }

    public void setSensitive(Boolean sensitive) {
        this.sensitive = sensitive;
    }
}
