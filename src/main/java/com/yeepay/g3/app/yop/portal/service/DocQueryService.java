/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.dto.DocPageRefQueryParam;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.facade.yop.doc.enums.DocCategoryTypeEnum;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-29 10:21
 */
public interface DocQueryService {

    PageQueryResult<DocPageItem> pageQuery(com.yeepay.g3.app.yop.portal.vo.page.DocPageQueryParam param);

    PageQueryResult<DocHistoryPageItem> historyPageQuery(DocPageHistoryPageQueryParam param);

    List<DocArrangeVo> listForArrange(Long categoryId);

    PageQueryResult<DocPublishPageItem> publishPageQuery(DocPublishPageQueryParam param);

    List<String> getSpDocNos();

    String findDocByPublishId(Long docPublishId);

    List<DocPageRefVO> pageListForRef(DocPageRefQueryParam queryParam);

    DocPageRefVO getPageWithPath(Long pageId);

    List<DocCategoryItemVO> listDocCategories(DocCategoryTypeEnum type, Long id);

    boolean existsDoc(String docNo);

    List<String> listSolutionDocs(List<String> docNos);

    DocCategoryVO findCategoryByParentCode(DocCategoryQueryParam queryParam);
}
