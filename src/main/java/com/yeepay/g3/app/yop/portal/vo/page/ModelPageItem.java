package com.yeepay.g3.app.yop.portal.vo.page;

import java.io.Serializable;
import java.util.Date;

/**
 * title: ModelPageItem<br/>
 * description: model分页查询返回参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:38
 */
public class ModelPageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private Long version;

    private String name;

    private String apiGroup;

    private String description;

    private Date createdDateTime;

    private Date lastModifiedDateTime;

    public Long getId() {
        return id;
    }

    public ModelPageItem setId(Long id) {
        this.id = id;
        return this;
    }

    public Long getVersion() {
        return version;
    }

    public ModelPageItem setVersion(Long version) {
        this.version = version;
        return this;
    }

    public String getName() {
        return name;
    }

    public ModelPageItem setName(String name) {
        this.name = name;
        return this;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public ModelPageItem setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getDescription() {
        return description;
    }

    public ModelPageItem setDescription(String description) {
        this.description = description;
        return this;
    }

    public Date getCreatedDateTime() {
        return createdDateTime;
    }

    public ModelPageItem setCreatedDateTime(Date createdDateTime) {
        this.createdDateTime = createdDateTime;
        return this;
    }

    public Date getLastModifiedDateTime() {
        return lastModifiedDateTime;
    }

    public ModelPageItem setLastModifiedDateTime(Date lastModifiedDateTime) {
        this.lastModifiedDateTime = lastModifiedDateTime;
        return this;
    }

    @Override
    public String toString() {
        return "ModelPageItem{" +
                "id=" + id +
                ", version=" + version +
                ", name='" + name + '\'' +
                ", apiGroup='" + apiGroup + '\'' +
                ", description='" + description + '\'' +
                ", createdDateTime=" + createdDateTime +
                ", lastModifiedDateTime=" + lastModifiedDateTime +
                '}';
    }
}
