package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.doc.enums.v2.DocPublishOperTypeEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-29 17:26
 */
public class DocPublishPageItem implements Serializable {
    private static final long serialVersionUID = -1L;

    private Long id;
    private String docNo;
    private String docTitle;
    private String docVersion;
    private DocPublishOperTypeEnum operType;
    private String operator;
    private Date createdDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDocNo() {
        return docNo;
    }

    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }

    public String getDocTitle() {
        return docTitle;
    }

    public void setDocTitle(String docTitle) {
        this.docTitle = docTitle;
    }

    public String getDocVersion() {
        return docVersion;
    }

    public void setDocVersion(String docVersion) {
        this.docVersion = docVersion;
    }

    public DocPublishOperTypeEnum getOperType() {
        return operType;
    }

    public void setOperType(DocPublishOperTypeEnum operType) {
        this.operType = operType;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }
}
