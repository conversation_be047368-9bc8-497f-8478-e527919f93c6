/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.filter.encrypt.request.yeepay;

import com.yeepay.g3.yop.gateway.filter.encrypt.request.RequestDecryptor;

import java.util.HashMap;
import java.util.Map;

/**
 * title: 请求解密器工厂<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/11 5:25 下午
 */
public class RequestDecryptorFactory {
    private static Map<String, RequestDecryptor> map = new HashMap<>();

    public static void register(String contentType, RequestDecryptor requestDecryptor) {
        map.put(contentType, requestDecryptor);
    }

    public static RequestDecryptor get(String contentType) {
        return map.get(contentType);
    }
}
