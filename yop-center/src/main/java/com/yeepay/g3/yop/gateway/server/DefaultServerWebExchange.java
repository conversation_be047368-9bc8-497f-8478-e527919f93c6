/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.server;

import com.google.common.collect.Maps;
import com.yeepay.boot.components.utils.NetworkUtils;
import com.yeepay.boot.components.utils.UUIDExt;
import com.yeepay.boot.components.utils.mapper.JsonMapper;
import com.yeepay.g3.yop.ext.gateway.context.SDKVersion;
import com.yeepay.g3.yop.ext.gateway.context.api.ApiContextParser;
import com.yeepay.g3.yop.ext.gateway.context.api.impl.ApiContext;
import com.yeepay.g3.yop.ext.gateway.context.app.AppContextParser;
import com.yeepay.g3.yop.ext.gateway.context.app.impl.AppContext;
import com.yeepay.g3.yop.ext.gateway.context.authenticate.AuthenticateContext;
import com.yeepay.g3.yop.ext.gateway.context.authenticate.AuthenticateContextParser;
import com.yeepay.g3.yop.ext.gateway.context.impl.InvokeProcessContext;
import com.yeepay.g3.yop.ext.gateway.context.param.ParamContext;
import com.yeepay.g3.yop.ext.gateway.context.param.ParamContextParser;
import com.yeepay.g3.yop.ext.gateway.context.protocol.ProtocolMakerEnum;
import com.yeepay.g3.yop.ext.gateway.context.request.ExchangeHandler;
import com.yeepay.g3.yop.ext.gateway.context.request.RequestIdParser;
import com.yeepay.g3.yop.ext.gateway.context.request.SessionIdParser;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;
import com.yeepay.g3.yop.frame.definition.SdkMetaInfo;
import com.yeepay.g3.yop.frame.exception.InternalException;
import com.yeepay.g3.yop.frame.exception.service.SystemServiceUnavailableException;
import com.yeepay.g3.yop.frame.http.Headers;
import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.frame.utils.SdkVersionParser;
import com.yeepay.g3.yop.gateway.context.api.ApiContextParserContextFactory;
import com.yeepay.g3.yop.gateway.context.app.AppContextParserFactory;
import com.yeepay.g3.yop.gateway.context.authenticate.AuthenticateContextParserFactory;
import com.yeepay.g3.yop.gateway.context.impl.StandardApiRouteContext;
import com.yeepay.g3.yop.gateway.context.param.ParamContextParserFactory;
import com.yeepay.g3.yop.gateway.context.protocol.ProtocolMakerParser;
import com.yeepay.g3.yop.gateway.context.request.ExchangeHandlerFactory;
import com.yeepay.g3.yop.gateway.context.request.RequestIdParserFactory;
import com.yeepay.g3.yop.gateway.context.request.SessionIdParserFactory;
import com.yeepay.g3.yop.router.route.RouteContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Enumeration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils.*;

;

/**
 * title: 服务网络交换器的默认实现<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/2 11:45
 */
@Slf4j
public class DefaultServerWebExchange implements ServerWebExchange {

    /**
     * 请求
     */
    private final HttpServletRequest request;

    /**
     * 响应
     */
    private final HttpServletResponse response;

    protected static final JsonMapper JSON_MAPPER = JsonMapper.defaultMapper();

    /**
     * 处理阶段的属性
     */
    private final Map<String, Object> attributes = new ConcurrentHashMap<>();

    public DefaultServerWebExchange(HttpServletRequest request, HttpServletResponse response, boolean simple) {
        Assert.notNull(request, "'request' is required");
        Assert.notNull(response, "'response' is required");
        this.request = request;
        this.response = response;
    }

    public DefaultServerWebExchange(HttpServletRequest request, HttpServletResponse response) {
        Assert.notNull(request, "'request' is required");
        Assert.notNull(response, "'response' is required");
        this.request = request;
        this.response = response;
        this.getAttributes().put(GATEWAY_IS_INVOKE_NEW_CHAIN_ATTR, true);

        Map<String, Object> properties = Maps.newHashMap();
        this.getAttributes().put(GATEWAY_PROPERTIES_ATTR, properties);
        Map<String, Object> boundParams = Maps.newHashMap();
        this.getAttributes().put(GATEWAY_BOUND_PARAMS_ATTR, boundParams);

        ProtocolMakerEnum protocolMaker = ProtocolMakerParser.parseProtocolMaker(this);
        this.getAttributes().put(GATE_PROTOCOL_MAKER_ATTR, protocolMaker);

        String sourceIp = NetworkUtils.getRemoteHost(request);
        this.getAttributes().put(GATEWAY_SOURCE_IP_ATTR, sourceIp);

        InvokeProcessContext invokeProcessContext = InvokeProcessContext.builder().build();
        this.getAttributes().put(GATE_INVOKE_PROCESS_CONTEXT_ATTR, invokeProcessContext);

        final ExchangeHandler exchangeHandler = ExchangeHandlerFactory.getHandler(protocolMaker);

        try {
            exchangeHandler.preHandler(this);
        } catch (Throwable e) {
            handleException(invokeProcessContext, e);
            return;
        }

        RequestIdParser requestIdParser = RequestIdParserFactory.getRequestIdParser(protocolMaker);
        String requestId = requestIdParser.parse(this);
        requestId = StringUtils.isBlank(requestId) ? UUIDExt.compressV4UUID() : requestId;
        this.getAttributes().put(GATEWAY_REQUEST_ID_ATTR, requestId);


        String sessionId = null;
        SessionIdParser sessionIdParser = SessionIdParserFactory.getSessionIdParser(protocolMaker);
        if (null != sessionIdParser) {
            sessionId = sessionIdParser.parse(this);
        }
        sessionId = StringUtils.defaultString(sessionId);
        this.getAttributes().put(GATEWAY_SESSION_ID_ATTR, sessionId);

        this.getAttributes().put(GATEWAY_REQUEST_METHOD_ATTR, request.getMethod());

        String apiUri = StringUtils.replace(request.getRequestURI(), request.getContextPath(), "");
        this.getAttributes().put(GATEWAY_API_URI_ATTR, apiUri);

        try {
            // 创建认证上下文的时候可能会抛异常
            AuthenticateContextParser authenticateContextParser = AuthenticateContextParserFactory.getAuthenticateContextParser(protocolMaker);
            AuthenticateContext authenticateContext = authenticateContextParser.parse(this);
            this.getAttributes().put(GATEWAY_AUTHC_CONTEXT_ATTR, authenticateContext);
        } catch (Throwable ex) {
            handleException(invokeProcessContext, ex);
            return;
        }

        AppContextParser appContextParser = AppContextParserFactory.getAppContextParser(protocolMaker);
        AppContext appContext = appContextParser.parse(this);
        this.getAttributes().put(GATEWAY_APP_CONTEXT_ATTR, appContext);

        ApiContextParser apiContextParser = ApiContextParserContextFactory.getApiContextParser(protocolMaker);
        ApiContext apiContext = apiContextParser.parse(this);
        this.getAttributes().put(GATEWAY_API_CONTEXT_ATTR, apiContext);

        ParamContextParser paramContextParser = ParamContextParserFactory.getParamContextParser(protocolMaker);
        ParamContext paramContext = paramContextParser.parse(this);
        this.getAttributes().put(GATEWAY_PARAM_CONTEXT_ATTR, paramContext);

        SDKVersion sdkVersion = getSDKVersionFromRequest(request);
        this.getAttributes().put(GATEWAY_SDK_VERSION_ATTR, sdkVersion);

        try {
            // 解析String格式的SDK版本
            SdkMetaInfo sdkMetaInfo = SdkVersionParser.parseSdkLangAndVersion(request);
            this.getAttributes().put(GATEWAY_SDK_LANG_ATTR, sdkMetaInfo.getSdkLang());
            this.getAttributes().put(GATEWAY_SDK_VERSION_STRING_ATTR, sdkMetaInfo.getSdkVersion());
            this.getAttributes().put(GATEWAY_SDK_SOURCE_ATTR, sdkMetaInfo.getSdkSource());
        } catch (Exception e) {
            log.error("parse sdk from request error", e);
        }

        RouteContext routeContext = new StandardApiRouteContext(this);
        this.getAttributes().put(GATEWAY_ROUT_CONTEXT_ATTR, routeContext);

        try {
            exchangeHandler.postHandler(this);
        } catch (Throwable e) {
            handleException(invokeProcessContext, e);
        }
    }

    private void handleException(InvokeProcessContext invokeProcessContext, Throwable ex) {
        log.error("error when build exchange, headers:{}, params:{}", getHeaders(request), getParams(request));
        if (ex instanceof InternalException) {
            log.error("internal exception happened:", ex);
            invokeProcessContext.setInternalException((InternalException) ex);
            invokeProcessContext.setResponse(new Response(ex));
        } else {
            log.error("unexpected exception happened:", ex);
            InternalException internalException = new SystemServiceUnavailableException();
            Response yopResponse = new Response(internalException);
            invokeProcessContext.setInternalException(internalException);
            invokeProcessContext.setResponse(yopResponse);
        }
    }

    @Override
    public HttpServletRequest getRequest() {
        return this.request;
    }

    @Override
    public HttpServletResponse getResponse() {
        return this.response;
    }

    @Override
    public Map<String, Object> getAttributes() {
        return this.attributes;
    }

    private SDKVersion getSDKVersionFromRequest(HttpServletRequest request) {
        String sdkVersion = request.getHeader(Headers.YOP_SDK_VERSION);
        try {
            return StringUtils.isNotBlank(sdkVersion) ? SDKVersion.parse(sdkVersion)
                    : SDKVersion.parseFromUserAgent(request.getHeader(Headers.USER_AGENT));
        } catch (Exception e) {
            log.error("exception happened when get sdkVersion,x-yop-sdk-version:" + sdkVersion, e);
            return SDKVersion.OLD;
        }
    }

    private String getHeaders(HttpServletRequest request) {
        StringBuilder headers = new StringBuilder();
        final Enumeration<String> names = request.getHeaderNames();
        String name;
        while (names.hasMoreElements()) {
            name = names.nextElement();
            headers.append(name).append(":").append(request.getHeader(name)).append("\n");
        }
        return headers.toString();
    }

    private String getParams(HttpServletRequest request) {
        String contentType = request.getContentType();
        if (StringUtils.startsWithAny(contentType, MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE)) {
            Map paramMap = request.getParameterMap();
            return JSON_MAPPER.toJson(paramMap);
        }
        if (StringUtils.startsWith(contentType, MediaType.APPLICATION_JSON_VALUE)) {
            try {
                final Object requestBody = this.getAttribute(GATEWAY_REQUEST_BODY_ATTR);
                if (null != requestBody) {
                    return JSON_MAPPER.toJson(requestBody);
                }
                return request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
            } catch (IOException e) {
                log.error("can not get params", e);
            }

        }
        return "";
    }

    @Override
    public String toString() {
        return "requestId:" +
                ServerWebExchangeUtils.getRequestId(this) +
                ",appKey:" +
                ServerWebExchangeUtils.getAppId(this) +
                ",apiGroup:" +
                ServerWebExchangeUtils.getApiContext(this).getApiGroup() +
                ",apiUri:" +
                ServerWebExchangeUtils.getApiContext(this).getApiUri();
    }

}
