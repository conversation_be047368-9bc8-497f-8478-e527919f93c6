/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.regression.execute.engine;

import com.yeepay.g3.app.yop.portal.cache.RegTestApiSecurityReqLocalCache;
import com.yeepay.g3.app.yop.portal.cache.RegTestAppIdEncryptPriKeyLocalCache;
import com.yeepay.g3.app.yop.portal.cache.RegTestAppIdInvokePriKeyLocalCache;
import com.yeepay.g3.app.yop.portal.enums.RegTestOauth2ModeEnum;
import com.yeepay.g3.app.yop.portal.exception.YopPortalException;
import com.yeepay.g3.app.yop.portal.regression.AssertEngine;
import com.yeepay.g3.app.yop.portal.regression.ExecutionResult;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.RegressionTestRequestVO;
import com.yeepay.g3.facade.yop.api.dto.ApiDefineDTO;
import com.yeepay.g3.facade.yop.api.enums.HttpMethodType;
import com.yeepay.g3.facade.yop.api.facade.OldApiMgrFacade;
import com.yeepay.g3.facade.yop.oauth2.dto.OAuth2AccessToken;
import com.yeepay.g3.facade.yop.oauth2.dto.OAuth2TokenRequestParam;
import com.yeepay.g3.facade.yop.oauth2.facade.TokenFacade;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.enums.SecurityReqNameEnum;
import com.yeepay.g3.facade.yop.sys.facade.RegTestAtokMgrFacade;
import com.yeepay.g3.sdk.yop.client.YopClient;
import com.yeepay.g3.sdk.yop.client.YopRequest;
import com.yeepay.g3.sdk.yop.client.YopResponse;
import com.yeepay.g3.sdk.yop.client.YopRsaClient;
import com.yeepay.g3.sdk.yop.exception.YopClientException;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateStrategyEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.PostConstruct;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/19 下午8:50
 */
public abstract class AbstractExecuteEngine implements ExecuteEngine {

    protected static final Logger LOGGER = LoggerFactory.getLogger(AbstractExecuteEngine.class);

    private RegTestAtokMgrFacade regTestAtokMgrFacade = RemoteServiceFactory.getService(RegTestAtokMgrFacade.class);

    private TokenFacade tokenFacade = RemoteServiceFactory.getService(TokenFacade.class);

    private OldApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(OldApiMgrFacade.class);

    private static final String SEPARATOR = "$";

    private static final String REG_TEST_ATOK_CACHE_PREFIX = "yp:rt:at:";

    private static final int REG_TEST_ACCESS_TOKEN_EXPIRE_IN = 43200;

    @Autowired
    private AssertEngine assertEngine;

    @Autowired
    private RegTestApiSecurityReqLocalCache regTestApiSecurityReqLocalCache;

    @Autowired
    private RegTestAppIdEncryptPriKeyLocalCache regTestAppIdEncryptPriKeyLocalCache;

    @Autowired
    RegTestAppIdInvokePriKeyLocalCache regTestAppIdInvokePriKeyLocalCache;

    @Autowired
    private RedisTemplate<String, String> stringRedisTemplate;

    @PostConstruct
    protected void init() {
        ExecuteEngineFactory.register(this);
    }

    @Override
    public ExecutionResult execute(RegTestCaseFindRespDTO testCaseDTO) {
        ExecutionResult executionResult = new ExecutionResult();
        executionResult.setAssertionList(testCaseDTO.getAssertionList());
        RegressionTestRequestVO request = buildRequest(testCaseDTO);
        try {
            if (request.isJsonRequest() && !this.supportJson()) {
                throw new YopPortalException("the {0} engine don't support json request", this.getSecurity());
            }
            YopResponse response = doExecute(request);
            executionResult.setResponse(response);
            if (response.getState().equals("SUCCESS")) {
                return getAssertEngine().runAssert(executionResult);
            }
            return executionResult;
        } catch (Exception e) {
            LOGGER.error("error execute test case :" + testCaseDTO.toString(), e);
            executionResult.setSuccess(false);
            return executionResult;
        }
    }

    private RegressionTestRequestVO buildRequest(RegTestCaseFindRespDTO testCaseDTO) {
        RegressionTestRequestVO.Builder builder = RegressionTestRequestVO.Builder.anRegressionTestRequestVO();
        Map<String, String> sdkConfig = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_REGRESSION_TEST_SDK_CONFIG);
        builder.withServerRoot(sdkConfig.get(testCaseDTO.getEnvironment()));
        if (StringUtils.isBlank(testCaseDTO.getAppKey())) {
            builder.withAppKey(sdkConfig.get("appKey")).withCert(sdkConfig.get("cert"));
        } else {
            if (SecurityReqNameEnum.YOP_OAUTH2.value.equals(testCaseDTO.getSecurity())) {
                String accessToken = setAccessToken(testCaseDTO);
                builder.withAppKey(testCaseDTO.getAppKey()).withAccessToken(accessToken);
            } else {
                String cert = regTestAppIdInvokePriKeyLocalCache.get(testCaseDTO.getAppKey() + SEPARATOR + testCaseDTO.getSecurity());
                builder.withAppKey(testCaseDTO.getAppKey()).withCert(cert);
            }
        }
        if (StringUtils.isNotBlank(testCaseDTO.getJsonRequestParam()) && testCaseDTO.getSecurity().equals(AuthenticateStrategyEnum.YOP_RSA2048_SHA256.getProtocolPrefix())) {
            builder.isJsonRequest().withJsonParam(testCaseDTO.getJsonRequestParam());
        } else if (CollectionUtils.isNotEmpty(testCaseDTO.getFormRequestParam())) {
            for (FormRequestParamDTO param : testCaseDTO.getFormRequestParam()) {
                builder.addFormParam(param.getName(), StringUtils.trim(param.getValue()));
            }
        }
        ApiDefineDTO apiDefineDTO = apiMgrFacade.findApiByUri(testCaseDTO.getApiUri());
        if ("SHA256".equals(apiDefineDTO.getSignAlg())) {
            builder.withSignAlg("SHA-256");
        } else {
            builder.withSignAlg(apiDefineDTO.getSignAlg());
        }
        builder.withResourcePath(testCaseDTO.getApiUri());
        if (testCaseDTO.getSecurity().equals(AuthenticateStrategyEnum.YOP_RSA2048_SHA256.getProtocolPrefix())) {
            Map<String, SecurityReqDTO> apiMap = regTestApiSecurityReqLocalCache.get(apiDefineDTO.getApiUri() + SEPARATOR + apiDefineDTO.getApiGroup());
            SecurityReqDTO securityReqDTO = null;
            if (apiMap == null || apiMap.size() == 0) {
                throw new YeepayRuntimeException("api or apigroup security req not set !");
            } else {
                securityReqDTO = apiMap.get(testCaseDTO.getSecurity());
            }
            setIsNeedEncrypt(testCaseDTO, builder, securityReqDTO);
        }
        final HttpMethodType[] supportMethods = apiDefineDTO.getHttpMethod();
        if (null != supportMethods && supportMethods.length > 0) {
            builder.withSupportMethods(apiDefineDTO.getHttpMethod());
        } else {
            builder.withSupportMethods(new HttpMethodType[]{HttpMethodType.POST});
        }
        return builder.build();
    }

    private String setAccessToken(RegTestCaseFindRespDTO testCaseDTO) {
        String accessToken = null;
        if (StringUtils.isNotEmpty(testCaseDTO.getAccessToken())) {
            accessToken = testCaseDTO.getAccessToken();
        } else {
            //目前数据库内存在一些安全需求为YOP-OAUTH2的数据，对于存量数据提示SP去维护atok信息
            if (testCaseDTO.getTokenId() == null) {
                throw new IllegalArgumentException("apiUri [ " + testCaseDTO.getApiUri() + " ] atok信息未配置");
            }
            //先从缓存取atok,无值再重新获取
            String cacheKey = REG_TEST_ATOK_CACHE_PREFIX + testCaseDTO.getTokenId();
            try {
                accessToken = stringRedisTemplate.opsForValue().get(cacheKey);
            } catch (Throwable ex) {
                LOGGER.error("token id :" + testCaseDTO.getTokenId() + " get atok from redis error", ex);
            }
            if (StringUtils.isEmpty(accessToken)) {
                accessToken = getAccessToken(testCaseDTO.getTokenId(), cacheKey);
            }
        }
        if (StringUtils.isEmpty(accessToken)) {
            throw new IllegalArgumentException("apiUri [ " + testCaseDTO.getApiUri() + " ] atok信息未配置");
        }
        return accessToken;
    }

    private String getAccessToken(Long tokenId, String cacheKey) {
        //查询atok信息
        RegTestAtokDetailFindReqDTO reqDTO = new RegTestAtokDetailFindReqDTO();
        reqDTO.setTokenId(tokenId);
        RegTestAtokDetailFindRespDTO respDTO = regTestAtokMgrFacade.findDetailById(reqDTO);
        if (respDTO == null) {
            throw new IllegalArgumentException("atok tokenId [ " + tokenId + " ] 不存在");
        }
        //重新获取accessToken
        OAuth2TokenRequestParam oAuth2TokenRequestParam = new OAuth2TokenRequestParam();
        oAuth2TokenRequestParam.setClientId(respDTO.getClientId());
        oAuth2TokenRequestParam.setAuthenticatedUserId(respDTO.getUsername());
        oAuth2TokenRequestParam.setGrantType(RegTestOauth2ModeEnum.parse(respDTO.getGrantType()).getTargetValue());
        oAuth2TokenRequestParam.setExpiresIn(REG_TEST_ACCESS_TOKEN_EXPIRE_IN);//接口里可以不传，但不传默认为0，所以还是得传 12h
        if (StringUtils.isNotEmpty(respDTO.getScope())) {
            Set<String> scope = new HashSet<>();
            scope.add(respDTO.getScope());
            oAuth2TokenRequestParam.setScope(scope);
        }
        OAuth2AccessToken oAuth2AccessToken = tokenFacade.generateToken(oAuth2TokenRequestParam);
        try {
            stringRedisTemplate.opsForValue().set(cacheKey, oAuth2AccessToken.getValue(), 12, TimeUnit.HOURS);
        } catch (Throwable ex) {
            LOGGER.error("token id :" + tokenId + " set atok to redis error", ex);
        }
        return oAuth2AccessToken.getValue();
    }

    private void setIsNeedEncrypt(RegTestCaseFindRespDTO testCaseDTO, RegressionTestRequestVO.Builder builder, SecurityReqDTO securityReqDTO) {
        if (securityReqDTO != null && securityReqDTO.isNeedEncrypt()) {
            String encryptPriKey = regTestAppIdEncryptPriKeyLocalCache.get(testCaseDTO.getAppKey());
            builder.isNeedEncrypt().withEncryptPriKey(encryptPriKey);
        }
    }

    protected abstract YopResponse doExecute(RegressionTestRequestVO regressionTestRequestVO) throws Exception;

    @Override
    public AssertEngine getAssertEngine() {
        return assertEngine;
    }

    protected YopResponse proxyToSdk(String apiUri, HttpMethodType[] supportMethods, YopRequest request, boolean isRsa) throws Exception {
        for (int i = 0; i < supportMethods.length; i++) {
            final HttpMethodType supportMethod = supportMethods[i];
            switch (supportMethod) {
                case POST:
                    return isRsa ? YopRsaClient.post(apiUri, request) : YopClient.post(apiUri, request);
                case GET:
                    return isRsa ? YopRsaClient.get(apiUri, request) : YopClient.get(apiUri, request);
                default:
                    LOGGER.warn("method not supported by sdk, apiUri:{}, method:{}", apiUri, supportMethod.name());
                    continue;
            }
        }
        throw new YopClientException("method not supported by sdk");
    }
}
