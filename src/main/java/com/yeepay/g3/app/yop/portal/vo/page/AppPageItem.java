/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/13 下午2:05
 */
public class AppPageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private String appId;

    private String name;

    private String subjectNo;

    private String customerNo;

    private String desc;

    private String type;

    private String status;

    private Date createdDate;

    private Date lastModifiedDate;

    /**
     * 对接完成时间
     */
    private Date finishManualAnnotationDate;

    /**
     * 开始对接时间
     */
    private Date startAccessDate;


    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSubjectNo() {
        return subjectNo;
    }

    public void setSubjectNo(String subjectNo) {
        this.subjectNo = subjectNo;
    }

    public String getCustomerNo() {
        return customerNo;
    }

    public void setCustomerNo(String customerNo) {
        this.customerNo = customerNo;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public Date getFinishManualAnnotationDate() {
        return finishManualAnnotationDate;
    }

    public void setFinishManualAnnotationDate(Date finishManualAnnotationDate) {
        this.finishManualAnnotationDate = finishManualAnnotationDate;
    }

    public Date getStartAccessDate() {
        return startAccessDate;
    }

    public void setStartAccessDate(Date startAccessDate) {
        this.startAccessDate = startAccessDate;
    }
}
