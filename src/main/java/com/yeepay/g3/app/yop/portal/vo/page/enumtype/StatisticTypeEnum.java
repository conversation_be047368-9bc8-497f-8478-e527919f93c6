package com.yeepay.g3.app.yop.portal.vo.page.enumtype;

import java.util.HashMap;
import java.util.Map;

/**
 * @Title: StatisticTypeEnum
 * @Description:
 * <AUTHOR>
 * @date 2018年5月10日 下午2:29:32
 * @version V1.0
 */
public enum StatisticTypeEnum {

    MONTHLY("MONTHLY", "按月"),
    DAILY("DAILY", "按天");

    private static final Map<String, StatisticTypeEnum> VALUE_MAP;

    static {
        VALUE_MAP = new HashMap<String, StatisticTypeEnum>();
        for (StatisticTypeEnum authType : StatisticTypeEnum.values()) {
            VALUE_MAP.put(authType.getValue(), authType);
        }
    }

    private String value;

    private String displayName;

    StatisticTypeEnum(String value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    public static Map<String, StatisticTypeEnum> getValueMap() {
        return VALUE_MAP;
    }

    public static StatisticTypeEnum parse(String value) {
        return VALUE_MAP.get(value);
    }

    public String getValue() {
        return value;
    }

    public String getDisplayName() {
        return displayName;
    }

}
