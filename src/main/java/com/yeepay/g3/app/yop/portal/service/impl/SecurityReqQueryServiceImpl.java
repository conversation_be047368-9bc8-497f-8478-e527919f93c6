package com.yeepay.g3.app.yop.portal.service.impl;

import com.fasterxml.jackson.databind.module.SimpleModule;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.SecurityReqQueryService;
import com.yeepay.g3.app.yop.portal.vo.page.SecurityReqQueryParam;
import com.yeepay.g3.core.yop.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqDTO;
import com.yeepay.g3.facade.yop.sys.enums.SecurityReqTypeEnum;
import com.yeepay.g3.facade.yop.sys.utils.jackson.ExtensionsDeserializer;
import com.yeepay.g3.facade.yop.sys.utils.jackson.ExtensionsSerializer;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-05-19 22:22
 */
@Component("securityReqQueryServiceImpl")
public class SecurityReqQueryServiceImpl implements SecurityReqQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SecurityReqQueryServiceImpl.class);
    private final JsonMapper mapper = JsonMapper.defaultMapper();

    {
        SimpleModule module = new SimpleModule();
        module.addSerializer(Map.class, new ExtensionsSerializer());
        module.addDeserializer(Map.class, new ExtensionsDeserializer());
        mapper.getMapper().registerModule(module);
    }

    @Resource(name = "securityReqQueryService")
    private QueryService queryService;

    @Override
    public List<SecurityReqDTO> list(SecurityReqQueryParam queryParam) {
        Map<String, Object> map = getQueryMap(queryParam);
        List result = queryService.query("list", map);
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        } else {
            return convert(result);
        }
    }

    private List<SecurityReqDTO> convert(List result) {
        if (CollectionUtils.isNotEmpty(result)) {
            List<SecurityReqDTO> securityReqs = new ArrayList<>(result.size());
            for (Object obj : result) {
                Map map = (Map) obj;
                if (MapUtils.isNotEmpty(map)) {
                    SecurityReqDTO securityReq = new SecurityReqDTO();
                    securityReq.setId((Long) map.get("id"));
                    BigInteger version = (BigInteger) map.get("version");
                    securityReq.setVersion(null != version ? version.longValue() : null);
                    securityReq.setCreatedDate((Date) map.get("created_datetime"));
                    securityReq.setLastModifiedDate((Date) map.get("last_modified_datetime"));
                    securityReq.setValue((String) map.get("value"));
                    securityReq.setType(SecurityReqTypeEnum.valueOf((String) map.get("type")));
                    securityReq.setName((String) map.get("name"));

                    securityReq.setScopes((List<String>) mapper.fromJson((String) map.get("scopes"), List.class));
                    securityReq.setExtensions((Map<String, Object>) mapper.fromJson((String) map.get("extensions"), Map.class));
                    securityReqs.add(securityReq);
                }
            }
            return securityReqs;
        }
        return Collections.emptyList();
    }

    private Map<String, Object> getQueryMap(SecurityReqQueryParam queryParam) {
        if (null != queryParam) {
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(10);
            if (CollectionUtils.isNotEmpty(queryParam.getValues())) {
                paramMap.put("valueSet", queryParam.getValues());
            }
            if (null != queryParam.getType()) {
                paramMap.put("type", queryParam.getType().name());
            }
            return paramMap;
        }
        return Collections.emptyMap();
    }
}
