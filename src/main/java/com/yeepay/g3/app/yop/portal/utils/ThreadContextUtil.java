/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.utils;

import com.yeepay.g3.utils.common.ThreadContextUtils;
import com.yeepay.g3.utils.common.threadcontext.ThreadContext;
import com.yeepay.g3.utils.common.threadcontext.ThreadContextType;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/1/16 17:11
 */
public class ThreadContextUtil {

    private static final String APP_NAME = "yop-portal";

    public static ThreadContext getContext() {
        if (!ThreadContextUtils.contextInitialized()) {
            ThreadContextUtils.initContext(APP_NAME, null, ThreadContextType.INTERFACE);
        }
        return ThreadContextUtils.getContext();
    }

}
