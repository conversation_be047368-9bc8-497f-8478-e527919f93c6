/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.BackendAppPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.BackendAppPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/9/25 上午11:48
 */
public interface BackendAppQueryService {

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<BackendAppPageItem> pageList(BackendAppPageQueryParam param);

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<BackendAppPageItem> pageListForSp(BackendAppPageQueryParam param);

}
