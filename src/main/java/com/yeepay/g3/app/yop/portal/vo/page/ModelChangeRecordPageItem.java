package com.yeepay.g3.app.yop.portal.vo.page;

import java.io.Serializable;
import java.util.Date;

/**
 * title: ModelChangeRecordPageItem<br/>
 * description: model变更记录分页查询返回参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:38
 */
public class ModelChangeRecordPageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private String name;

    private Date createdDateTime;

    private String apiGroup;

    private String operator;

    private String opType;

    private String cause;

    public Long getId() {
        return id;
    }

    public ModelChangeRecordPageItem setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public ModelChangeRecordPageItem setName(String name) {
        this.name = name;
        return this;
    }

    public Date getCreatedDateTime() {
        return createdDateTime;
    }

    public ModelChangeRecordPageItem setCreatedDateTime(Date createdDateTime) {
        this.createdDateTime = createdDateTime;
        return this;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public ModelChangeRecordPageItem setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getOperator() {
        return operator;
    }

    public ModelChangeRecordPageItem setOperator(String operator) {
        this.operator = operator;
        return this;
    }

    public String getOpType() {
        return opType;
    }

    public ModelChangeRecordPageItem setOpType(String opType) {
        this.opType = opType;
        return this;
    }

    public String getCause() {
        return cause;
    }

    public ModelChangeRecordPageItem setCause(String cause) {
        this.cause = cause;
        return this;
    }

    @Override
    public String toString() {
        return "SpiChangeRecordPageItem{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", createdDateTime=" + createdDateTime +
                ", apiGroup='" + apiGroup + '\'' +
                ", operator='" + operator + '\'' +
                ", opType='" + opType + '\'' +
                ", cause='" + cause + '\'' +
                '}';
    }
}
