package com.yeepay.g3.app.yop.portal.service.impl;


import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.AclRoleQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.AclRoleVO;
import com.yeepay.g3.app.yop.portal.vo.page.AclRolePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.AclRolePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.facade.yop.perm.enums.RoleTypeEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午5:11
 */
@Component
public class AclRoleQueryServiceImpl implements AclRoleQueryService {


    private static final Logger LOGGER = LoggerFactory.getLogger(AclRoleQueryServiceImpl.class);
    private final PageItemConverter<AclRolePageItem> pageItemConverter = new AclRolePageItemConverter();
    @Resource(name = "aclRoleQueryService")
    private QueryService queryService;

    @Override
    public List<AclRoleVO> list() {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_queryable", "true");
        QueryParam param = new QueryParam();
        param.setParams(paramMap);
        List<Map<String, String>> queryResult = queryService.queryList("list", param);
        return convert(queryResult);
    }

    @Override
    public List<AclRoleVO> listForSp(Set<String> spCodes) {
        if (Collections3.isEmpty(spCodes)) {
            return Collections.emptyList();
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_queryable", "true");
        paramMap.put("spCodes", spCodes);
        QueryParam param = new QueryParam();
        param.setParams(paramMap);
        List<Map<String, String>> queryResult = queryService.queryList("listForSp", param);
        return convert(queryResult);
    }

    @Override
    public PageQueryResult<AclRolePageItem> pageQuery(AclRolePageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        QueryResult queryResult = queryService.query("pageList", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
    }

    @Override
    public PageQueryResult<AclRolePageItem> pageQueryForSp(AclRolePageQueryParam param, Set<String> spCodes) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> bizParams = getBizParams(param);
        bizParams.put("spCodes", spCodes);
        queryParam.setParams(bizParams);
        QueryResult queryResult = queryService.query("pageListForSp", queryParam);
        return PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
    }

    private List<AclRoleVO> convert(List<Map<String, String>> queryList) {
        if (Collections3.isEmpty(queryList)) {
            return Collections.emptyList();
        }
        List<AclRoleVO> list = new ArrayList<>(queryList.size());
        for (Map<String, String> map : queryList) {
            AclRoleVO aclRoleVO = new AclRoleVO();
            aclRoleVO.setRoleCode(map.get("role_code"));
            aclRoleVO.setRoleName(map.get("role_name"));
            list.add(aclRoleVO);
        }
        return list;
    }

    private Map<String, Object> getBizParams(AclRolePageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("type", param.getType());
        bizParams.put("spCode", param.getSpCode());
        bizParams.put("roleCode", param.getRoleCode());
        bizParams.put("roleName", param.getRoleName());
        return bizParams;
    }

    class AclRolePageItemConverter extends BasePageItemConverter<AclRolePageItem> {

        @Override
        public AclRolePageItem convert(Map<String, Object> params) {
            AclRolePageItem item = new AclRolePageItem();
            item.setId((Long) params.get("id"));
            item.setCode((String) params.get("role_code"));
            item.setName((String) params.get("role_name"));
            item.setSpCode((String) params.get("sp_code"));
            item.setSpName((String) params.get("sp_name"));
            item.setType(RoleTypeEnum.parse((String) params.get("role_type")));
            item.setCreatedTime((Date) params.get("created_datetime"));
            item.setLastModifiedTime((Date) params.get("last_modified_datetime"));
            item.setVersion(Long.valueOf(String.valueOf(params.get("version"))));
            return item;
        }
    }
}
