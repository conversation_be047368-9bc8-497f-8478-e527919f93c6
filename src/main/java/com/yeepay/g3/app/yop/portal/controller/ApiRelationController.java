/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.ApiRelationService;
import com.yeepay.g3.app.yop.portal.vo.ApiRelationVO;
import com.yeepay.g3.app.yop.portal.vo.SimpleApiVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/20 11:23 上午
 */
@RestController
@RequestMapping("/rest/api-relation")
public class ApiRelationController {
    @Autowired
    private ApiRelationService apiRelationService;

    @GetMapping("/list")
    public ResponseMessage<List<ApiRelationVO>> list(@RequestParam("apiId") String apiId) {
        return new ResponseMessage<>("result", apiRelationService.list(apiId));
    }

    @PostMapping("/create")
    public ResponseMessage create(@RequestBody ApiRelationVO apiRelationVO) {
        apiRelationService.create(apiRelationVO);
        return new ResponseMessage();
    }

    @PostMapping("/edit")
    public ResponseMessage edit(@RequestBody ApiRelationVO apiRelationVO) {
        apiRelationService.edit(apiRelationVO);
        return new ResponseMessage();
    }

    @PostMapping("/delete")
    public ResponseMessage delete(@RequestBody ApiRelationVO apiRelationVO) {
        apiRelationService.delete(apiRelationVO.getId());
        return new ResponseMessage();
    }

    @GetMapping("/detail")
    public ResponseMessage<ApiRelationVO> detail(@RequestParam("id") long id) {
        return new ResponseMessage<>("result", apiRelationService.detail(id));
    }

    /**
     * 查询可关联的api
     *
     * @param apiGroup API分组编码
     * @param apiId    下单接口编号，用于排除已被关联的接口
     * @return
     */
    @GetMapping("/apis")
    public ResponseMessage<SimpleApiVO> apis(@RequestParam("apiGroup") String apiGroup,
                                             @RequestParam("apiId") String apiId) {
        return new ResponseMessage("result", apiRelationService.apis(apiGroup, apiId));
    }

}
