/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * title: 问题分页数据项<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/12/21
 */
@Data
public class DocFaqItem  implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 问题标识
     */
    private Long id;

    /**
     * 问题标题
     */
    private String title;

    /**
     * 关联的文章标题，会有多个
     */
    private List<String> relatedTitle;

    /**
     * 分值
     */
    private Integer score;

    /**
     * 是否置顶
     * true：是，false：否
     */
    private Boolean top;

    /**
     * 问题状态
     */
    private String status;

    /**
     * 问题创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdDate;

    /**
     * 问题最后修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastModifiedDate;

}
