package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutionException;

/**
 * title: 基于api发布记录id进行拦截<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-12-01 10:06
 */
@Component
public class ApiPublishIdBasedAuthorizationFilter extends CacheAbstractAuthorizationFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiPublishIdBasedAuthorizationFilter.class);

    @Override
    protected String[] getSpCodes(String[] params) {
        String[] spCodes;
        if (params == null || params.length < 1) {
            spCodes = null;
        } else {
            spCodes = new String[params.length];
            try {
                for (int i = 0; i < params.length; i++) {
                    spCodes[i] = apiPublishIdLocalCache.get(Long.valueOf(params[i]));
                }
            } catch (ExecutionException e) {
                LOGGER.info("get spCode by apiPublishId wrong, params are :{},exception is :{}", params, e);
            }
            LOGGER.debug("get spCodes by apiPublishId success, params:{}, result:{}", params, spCodes);
        }
        return spCodes;
    }

    @Override
    public String shiroName() {
        return "api_deploy_id_based";
    }
}
