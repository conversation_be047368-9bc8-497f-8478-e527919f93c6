package com.yeepay.g3.yop.frame.context;


import com.yeepay.g3.yop.frame.backend.handler.ApiHandler;
import com.yeepay.g3.yop.frame.definition.SecurityReqs;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 16/10/26 上午11:36
 */
public interface ApiContext {

    /**
     * 获取api处理器
     *
     * @return
     */
    ApiHandler getApiHandler();

    /**
     * 设置api处理器
     *
     * @param apiHandler
     */
    void setApiHandler(ApiHandler apiHandler);

    /**
     * 获取请求api uri
     *
     * @return
     */
    String getApiUri();

    String getApiGroup();

    boolean isApiV2();

    boolean isGeneric();

    String getApiId();

    String getApiKey();

    String getHttpMethod();

    SecurityReqs getSecurityReqs();
}
