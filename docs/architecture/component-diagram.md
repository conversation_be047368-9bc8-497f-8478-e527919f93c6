# YOP Portal 组件图

## 1. 组件概述

YOP Portal系统由多个组件组成，这些组件协同工作，提供完整的开放平台门户功能。本文档详细描述了系统的各个组件及其关系，包括前端组件、控制器组件、服务组件、数据访问组件、外部接口组件和基础设施组件。

## 2. 组件分类

### 2.1 前端组件

前端组件负责用户界面的展示和交互，主要包括：

- Web界面组件
- Swagger UI组件
- 开发者控制台组件

### 2.2 控制器组件

控制器组件负责处理用户请求，调用相应的服务组件，主要包括：

- API控制器
- 认证授权控制器
- 文档控制器
- 应用控制器
- 统计控制器

### 2.3 服务组件

服务组件负责实现业务逻辑，主要包括：

- API服务
- 认证授权服务
- 文档服务
- 应用服务
- 统计服务

### 2.4 数据访问组件

数据访问组件负责与数据库、缓存、消息队列等交互，主要包括：

- 数据访问对象（DAO）
- 缓存访问对象
- 消息队列访问对象

### 2.5 外部接口组件

外部接口组件负责与外部系统交互，主要包括：

- Dubbo接口
- RESTful API接口
- WebSocket接口

### 2.6 基础设施组件

基础设施组件提供系统运行的基础环境，主要包括：

- 数据库
- 缓存
- 消息队列
- 服务注册与发现

## 3. 组件图

### 3.1 整体组件图

```mermaid
graph TB
    subgraph 前端组件
        WebUI[Web界面]
        SwaggerUI[Swagger UI]
        DevConsole[开发者控制台]
    end
    
    subgraph 控制器组件
        ApiController[API控制器]
        AuthController[认证授权控制器]
        DocController[文档控制器]
        AppController[应用控制器]
        StatController[统计控制器]
    end
    
    subgraph 服务组件
        ApiService[API服务]
        AuthService[认证授权服务]
        DocService[文档服务]
        AppService[应用服务]
        StatService[统计服务]
    end
    
    subgraph 数据访问组件
        ApiDAO[API数据访问对象]
        AuthDAO[认证授权数据访问对象]
        DocDAO[文档数据访问对象]
        AppDAO[应用数据访问对象]
        StatDAO[统计数据访问对象]
        CacheAccess[缓存访问对象]
        MQAccess[消息队列访问对象]
    end
    
    subgraph 外部接口组件
        DubboInterface[Dubbo接口]
        RestInterface[RESTful API接口]
        WSInterface[WebSocket接口]
    end
    
    subgraph 基础设施组件
        DB[(数据库)]
        Cache[(缓存)]
        MQ[(消息队列)]
        ZK[(ZooKeeper)]
    end
    
    WebUI & SwaggerUI & DevConsole --> ApiController & AuthController & DocController & AppController & StatController
    
    ApiController --> ApiService
    AuthController --> AuthService
    DocController --> DocService
    AppController --> AppService
    StatController --> StatService
    
    ApiService --> ApiDAO & CacheAccess & MQAccess
    AuthService --> AuthDAO & CacheAccess
    DocService --> DocDAO & CacheAccess
    AppService --> AppDAO & CacheAccess
    StatService --> StatDAO & CacheAccess & MQAccess
    
    ApiService & AuthService & DocService & AppService & StatService --> DubboInterface
    ApiController & AuthController & DocController & AppController & StatController --> RestInterface
    ApiController & AuthController & DocController & AppController & StatController --> WSInterface
    
    ApiDAO & AuthDAO & DocDAO & AppDAO & StatDAO --> DB
    CacheAccess --> Cache
    MQAccess --> MQ
    DubboInterface --> ZK
```

### 3.2 前端组件详细图

```mermaid
graph TB
    subgraph Web界面组件
        HomePage[首页]
        ApiMgmtPage[API管理页面]
        AppMgmtPage[应用管理页面]
        DocPage[文档页面]
        StatPage[统计页面]
    end
    
    subgraph Swagger UI组件
        SwaggerUI[Swagger UI]
        ApiDoc[API文档]
        ApiTest[API测试]
    end
    
    subgraph 开发者控制台组件
        Dashboard[仪表盘]
        AppInfo[应用信息]
        ApiUsage[API使用情况]
        BillingInfo[计费信息]
    end
    
    HomePage --> ApiMgmtPage & AppMgmtPage & DocPage & StatPage
    DocPage --> SwaggerUI
    SwaggerUI --> ApiDoc & ApiTest
    Dashboard --> AppInfo & ApiUsage & BillingInfo
```

### 3.3 控制器组件详细图

```mermaid
graph TB
    subgraph API控制器
        ApiCreateCtrl[API创建控制器]
        ApiUpdateCtrl[API更新控制器]
        ApiDeleteCtrl[API删除控制器]
        ApiPublishCtrl[API发布控制器]
        ApiQueryCtrl[API查询控制器]
    end
    
    subgraph 认证授权控制器
        LoginCtrl[登录控制器]
        LogoutCtrl[登出控制器]
        PermissionCtrl[权限控制器]
        TokenCtrl[Token控制器]
    end
    
    subgraph 文档控制器
        DocGenCtrl[文档生成控制器]
        DocUpdateCtrl[文档更新控制器]
        DocPublishCtrl[文档发布控制器]
        DocQueryCtrl[文档查询控制器]
    end
    
    subgraph 应用控制器
        AppCreateCtrl[应用创建控制器]
        AppUpdateCtrl[应用更新控制器]
        AppDeleteCtrl[应用删除控制器]
        AppKeyCtrl[应用密钥控制器]
        AppPermCtrl[应用权限控制器]
    end
    
    subgraph 统计控制器
        ApiStatCtrl[API统计控制器]
        AppStatCtrl[应用统计控制器]
        PerfStatCtrl[性能统计控制器]
    end
    
    ApiCreateCtrl & ApiUpdateCtrl & ApiDeleteCtrl & ApiPublishCtrl & ApiQueryCtrl --> ApiService
    LoginCtrl & LogoutCtrl & PermissionCtrl & TokenCtrl --> AuthService
    DocGenCtrl & DocUpdateCtrl & DocPublishCtrl & DocQueryCtrl --> DocService
    AppCreateCtrl & AppUpdateCtrl & AppDeleteCtrl & AppKeyCtrl & AppPermCtrl --> AppService
    ApiStatCtrl & AppStatCtrl & PerfStatCtrl --> StatService
```

### 3.4 服务组件详细图

```mermaid
graph TB
    subgraph API服务
        ApiCreateSvc[API创建服务]
        ApiUpdateSvc[API更新服务]
        ApiDeleteSvc[API删除服务]
        ApiPublishSvc[API发布服务]
        ApiQuerySvc[API查询服务]
    end
    
    subgraph 认证授权服务
        LoginSvc[登录服务]
        LogoutSvc[登出服务]
        PermissionSvc[权限服务]
        TokenSvc[Token服务]
    end
    
    subgraph 文档服务
        DocGenSvc[文档生成服务]
        DocUpdateSvc[文档更新服务]
        DocPublishSvc[文档发布服务]
        DocQuerySvc[文档查询服务]
    end
    
    subgraph 应用服务
        AppCreateSvc[应用创建服务]
        AppUpdateSvc[应用更新服务]
        AppDeleteSvc[应用删除服务]
        AppKeySvc[应用密钥服务]
        AppPermSvc[应用权限服务]
    end
    
    subgraph 统计服务
        ApiStatSvc[API统计服务]
        AppStatSvc[应用统计服务]
        PerfStatSvc[性能统计服务]
    end
    
    ApiCreateSvc & ApiUpdateSvc & ApiDeleteSvc & ApiPublishSvc & ApiQuerySvc --> ApiDAO
    LoginSvc & LogoutSvc & PermissionSvc & TokenSvc --> AuthDAO
    DocGenSvc & DocUpdateSvc & DocPublishSvc & DocQuerySvc --> DocDAO
    AppCreateSvc & AppUpdateSvc & AppDeleteSvc & AppKeySvc & AppPermSvc --> AppDAO
    ApiStatSvc & AppStatSvc & PerfStatSvc --> StatDAO
    
    ApiCreateSvc & ApiUpdateSvc & ApiDeleteSvc & ApiPublishSvc & ApiQuerySvc & LoginSvc & LogoutSvc & PermissionSvc & TokenSvc & DocGenSvc & DocUpdateSvc & DocPublishSvc & DocQuerySvc & AppCreateSvc & AppUpdateSvc & AppDeleteSvc & AppKeySvc & AppPermSvc & ApiStatSvc & AppStatSvc & PerfStatSvc --> CacheAccess
    
    ApiPublishSvc & ApiStatSvc & AppStatSvc & PerfStatSvc --> MQAccess
```

## 4. 组件交互

### 4.1 API创建流程

```mermaid
sequenceDiagram
    participant WebUI as Web界面
    participant ApiCtrl as API控制器
    participant ApiSvc as API服务
    participant ApiDAO as API数据访问对象
    participant DB as 数据库
    participant Cache as 缓存
    
    WebUI->>ApiCtrl: 创建API请求
    ApiCtrl->>ApiSvc: 调用创建API服务
    ApiSvc->>ApiDAO: 调用数据访问对象
    ApiDAO->>DB: 存储API数据
    DB-->>ApiDAO: 返回结果
    ApiDAO-->>ApiSvc: 返回结果
    ApiSvc->>Cache: 更新缓存
    Cache-->>ApiSvc: 返回结果
    ApiSvc-->>ApiCtrl: 返回结果
    ApiCtrl-->>WebUI: 返回创建结果
```

### 4.2 用户认证流程

```mermaid
sequenceDiagram
    participant WebUI as Web界面
    participant AuthCtrl as 认证控制器
    participant AuthSvc as 认证服务
    participant AuthDAO as 认证数据访问对象
    participant DB as 数据库
    participant Cache as 缓存
    
    WebUI->>AuthCtrl: 登录请求
    AuthCtrl->>AuthSvc: 调用登录服务
    AuthSvc->>AuthDAO: 查询用户信息
    AuthDAO->>DB: 查询数据库
    DB-->>AuthDAO: 返回用户数据
    AuthDAO-->>AuthSvc: 返回用户信息
    AuthSvc->>AuthSvc: 验证密码
    AuthSvc->>Cache: 存储会话信息
    Cache-->>AuthSvc: 返回结果
    AuthSvc-->>AuthCtrl: 返回认证结果
    AuthCtrl-->>WebUI: 返回登录结果
```

### 4.3 API调用统计流程

```mermaid
sequenceDiagram
    participant Gateway as API网关
    participant MQ as 消息队列
    participant StatSvc as 统计服务
    participant StatDAO as 统计数据访问对象
    participant DB as 数据库
    
    Gateway->>MQ: 发送API调用日志
    MQ->>StatSvc: 消费API调用日志
    StatSvc->>StatSvc: 处理统计数据
    StatSvc->>StatDAO: 保存统计数据
    StatDAO->>DB: 存储到数据库
    DB-->>StatDAO: 返回结果
    StatDAO-->>StatSvc: 返回结果
```

## 5. 组件依赖

### 5.1 内部依赖

```mermaid
graph TB
    WebUI[Web界面] --> Controller[控制器]
    Controller --> Service[服务]
    Service --> DAO[数据访问对象]
    DAO --> DB[(数据库)]
    Service --> Cache[(缓存)]
    Service --> MQ[(消息队列)]
```

### 5.2 外部依赖

```mermaid
graph TB
    YopPortal[YOP Portal] --> Dubbo[Dubbo]
    YopPortal --> Redis[Redis]
    YopPortal --> RabbitMQ[RabbitMQ]
    YopPortal --> MySQL[MySQL/TiDB]
    YopPortal --> ZooKeeper[ZooKeeper]
    Dubbo --> ZooKeeper
```

## 6. 组件配置

### 6.1 Spring配置

YOP Portal使用Spring Boot进行配置，主要配置文件包括：

- `application.properties`：应用基本配置
- `yop-portal-application.xml`：应用上下文配置
- `yop-portal-datasource.xml`：数据源配置
- `yop-portal-dubbo.xml`：Dubbo配置
- `yop-portal-mq.xml`：消息队列配置

### 6.2 Dubbo配置

Dubbo配置主要包括：

- 应用名称：`yop-portal`
- 注册中心：`zookeeperx://zk.bass.3g:2181`
- 服务接口：各种Facade接口

### 6.3 数据源配置

数据源配置主要包括：

- 主数据源：`SOAYOP`
- 读数据源：`YOP_SYS_READ`
- 监控数据源：`MONITOR_TIDB_READ`

## 7. 组件扩展点

### 7.1 API扩展点

- 自定义API处理器
- 自定义API验证器
- 自定义API转换器

### 7.2 认证扩展点

- 自定义认证提供者
- 自定义权限检查器
- 自定义会话管理器

### 7.3 文档扩展点

- 自定义文档生成器
- 自定义文档模板
- 自定义文档展示器
