package com.yeepay.g3.app.yop.portal.filter;

import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @author：wang.bao
 * @since：2015年5月4日 下午2:07:04
 * @version:
 * @see org.springframework.mock.web.HeaderValueHolder
 */
public class HeaderValueHolder {
    private final List<Object> values = new LinkedList<Object>();

    HeaderValueHolder() {
    }

    public static HeaderValueHolder getByName(
            Map<String, HeaderValueHolder> headers, String name) {
        Assert.notNull(name, "Header name must not be null");
        for (String headerName : headers.keySet()) {
            if (headerName.equalsIgnoreCase(name)) {
                return (HeaderValueHolder) headers.get(headerName);
            }
        }
        return null;
    }

    public void addValue(Object value) {
        this.values.add(value);
    }

    public void addValues(Collection<?> values) {
        this.values.addAll(values);
    }

    public void addValueArray(Object values) {
        CollectionUtils.mergeArrayIntoCollection(values, this.values);
    }

    public List<Object> getValues() {
        return Collections.unmodifiableList(this.values);
    }

    public List<String> getStringValues() {
        List<String> stringList = new ArrayList<String>(this.values.size());
        for (Object value : this.values) {
            stringList.add(value.toString());
        }
        return Collections.unmodifiableList(stringList);
    }

    public Object getValue() {
        return !this.values.isEmpty() ? this.values.get(0) : null;
    }

    public void setValue(Object value) {
        this.values.clear();
        this.values.add(value);
    }

    public String getStringValue() {
        return !this.values.isEmpty() ? this.values.get(0).toString() : null;
    }
}
