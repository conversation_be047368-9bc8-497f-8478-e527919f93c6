package com.yeepay.g3.app.yop.portal.controller.support.imports;

import java.io.Serializable;
import java.util.List;

/**
 * title: Spi倚赖信息<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 14:48
 */
public class SpiDependencyInfo implements Serializable {

    private static final long serialVersionUID = -1L;

    private String spiName;

    private List<String> refModels;

    public String getSpiName() {
        return spiName;
    }

    public void setSpiName(String spiName) {
        this.spiName = spiName;
    }

    public SpiDependencyInfo withSpiName(String spiName) {
        this.spiName = spiName;
        return this;
    }

    public List<String> getRefModels() {
        return refModels;
    }

    public void setRefModels(List<String> refModels) {
        this.refModels = refModels;
    }

    public SpiDependencyInfo withRefModels(List<String> refModels) {
        this.refModels = refModels;
        return this;
    }

}
