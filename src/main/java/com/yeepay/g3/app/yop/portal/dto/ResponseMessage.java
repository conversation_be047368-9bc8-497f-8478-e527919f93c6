package com.yeepay.g3.app.yop.portal.dto;

import com.google.common.collect.Maps;
import com.yeepay.g3.core.yop.utils.Exceptions;
import com.yeepay.g3.utils.common.ThreadContextUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.threadcontext.ThreadContext;
import com.yeepay.g3.utils.common.threadcontext.ThreadContextType;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.validation.BindingResult;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Title: 响应消息<br/>
 * Description: 描述<br/>
 * Copyright: Copyright (c)2011<br/>
 * Company:<br/><br/>
 *
 * <AUTHOR>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           />
 * @version 0.1, 12-7-2 上午8:32
 */
public class ResponseMessage<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final Pattern RMI_WRAPPED_MESSAGE_PATTERN = Pattern.compile("^orgExceptionType:(.*);orgExceptionInfo:(.*)$");

    @Deprecated
    private Status status;

    private int code = 105500;

    private String message;

    private String traceId;

    @Deprecated
    private String solution;

    private Map<String, T> data = new HashMap<>();

    public ResponseMessage() {
        initSolution();
        this.status = Status.SUCCESS;
    }

    public ResponseMessage(Status status) {
        initSolution();
        this.status = status;
    }

    public ResponseMessage(T message) {
        this();
        if (message instanceof String) {
            this.message = (String) message;
        } else {
            this.status = Status.ERROR;
            this.message = message != null ? message.toString() : null;
        }
    }

    public ResponseMessage(BindingResult bindingResult) {
        this(Status.ERROR);
        this.message = bindingResult.getAllErrors().get(0).getDefaultMessage();
    }

    public ResponseMessage(Throwable t) {
        this(Status.ERROR);
        String message = Exceptions.getRootCause(t).getMessage();
        this.message = t instanceof YeepayRuntimeException ? tryUnwrapRmiMsg(message) : message;
    }

    private String tryUnwrapRmiMsg(String message) {
        Matcher matcher = RMI_WRAPPED_MESSAGE_PATTERN.matcher(message);
        if (matcher.matches()) {
            return matcher.group(2);
        }
        return message;
    }

    public ResponseMessage(Status status, T message) {
        this(status);
        if (message instanceof String) {
            this.message = (String) message;
        } else {
            this.message = message != null ? message.toString() : null;
        }
    }

    public ResponseMessage(Map<String, T> data) {
        initSolution();
        this.status = Status.SUCCESS;
        this.data = data;
    }

    /**
     * 自定义返回键值，其中value必须可序列化
     *
     * @param key
     * @param value
     */
    public ResponseMessage(String key, T value) {
        initSolution();
        this.status = Status.SUCCESS;
        this.message = "";
        this.data.put(key, value);
    }

    public ResponseMessage put(String key, T value) {
        this.data.put(key, value);
        return this;
    }

    public String getStatus() {
        return this.status.value;
    }

    public ResponseMessage setStatus(String status) {
        this.status = Status.parse(status);
        return this;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Map<String, T> getData() {
        return data;
    }

    public void initSolution() {
        ThreadContext threadContext = ThreadContextUtils.getContext();
        if (null == threadContext) {
            ThreadContextUtils.initContext("", "", ThreadContextType.WEB);
            threadContext = ThreadContextUtils.getContext();
        }
        this.solution = threadContext.getThreadUID();
        this.traceId = this.solution;
    }

    public String getSolution() {
        return solution;
    }

    public int getCode() {
        return code;
    }

    public String getTraceId() {
        return traceId;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this,
                ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public enum Status {

        /**
         * 成功
         */
        SUCCESS("success"),

        /**
         * 信息
         */
        INFO("info"),

        /**
         * 警告
         */
        WARNING("warning"),

        /**
         * 错误
         */
        ERROR("error");

        private static final Map<String, Status> VALUE_MAP;

        static {
            VALUE_MAP = Maps.newHashMapWithExpectedSize(Status.values().length);
            for (Status status : Status.values()) {
                VALUE_MAP.put(status.value, status);
            }
        }

        private final String value;

        Status(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static Status parse(String value) {
            return VALUE_MAP.get(value);
        }

    }

}
