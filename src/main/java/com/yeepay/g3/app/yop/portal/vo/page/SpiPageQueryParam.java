package com.yeepay.g3.app.yop.portal.vo.page;

import java.util.List;

/**
 * title: SpiPageQueryParam<br/>
 * description: spi分页查询参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:33
 */
public class SpiPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private String name;

    private String title;

    private String status;

    private String apiGroup;

    private String spiType;

    private List<String> apiGroupCodes;

    public String getName() {
        return name;
    }

    public SpiPageQueryParam setName(String name) {
        this.name = name;
        return this;
    }

    public String getTitle() {
        return title;
    }

    public SpiPageQueryParam setTitle(String title) {
        this.title = title;
        return this;
    }

    public String getStatus() {
        return status;
    }

    public SpiPageQueryParam setStatus(String status) {
        this.status = status;
        return this;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public SpiPageQueryParam setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getSpiType() {
        return spiType;
    }

    public SpiPageQueryParam setSpiType(String spiType) {
        this.spiType = spiType;
        return this;
    }

    public List<String> getApiGroupCodes() {
        return apiGroupCodes;
    }

    public SpiPageQueryParam setApiGroupCodes(List<String> apiGroupCodes) {
        this.apiGroupCodes = apiGroupCodes;
        return this;
    }

    @Override
    public String toString() {
        return "SpiPageQueryParam{" +
                "name='" + name + '\'' +
                ", title='" + title + '\'' +
                ", status='" + status + '\'' +
                ", apiGroup='" + apiGroup + '\'' +
                ", spiType='" + spiType + '\'' +
                ", apiGroupCodes=" + apiGroupCodes +
                '}';
    }
}
