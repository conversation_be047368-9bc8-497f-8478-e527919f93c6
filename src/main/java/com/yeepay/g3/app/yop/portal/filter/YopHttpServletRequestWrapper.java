/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.filter;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.utils.SpringContextUtil;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.frame.YopBizCodeConsts;
import com.yeepay.g3.yop.frame.error.YopException;
import com.yeepay.g3.yop.frame.http.YopDelegatingServletInputStream;
import com.yeepay.g3.yop.frame.http.YopDelegatingServletInputStream;
import com.yeepay.g3.yop.frame.xsd.SystemParamNames;
import org.apache.commons.io.IOUtils;
import org.springframework.util.Assert;
import org.springframework.util.LinkedCaseInsensitiveMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/6 19:22
 */
public class YopHttpServletRequestWrapper extends HttpServletRequestWrapper {

    private static final Logger LOGGER = LoggerFactory.getLogger(YopHttpServletRequestWrapper.class);
    public static final String ENCODING = "UTF-8";

    private ByteArrayInputStream byteArrayInputStream;
    /**
     * 用户自定义header
     */
    private final Map<String, HeaderValueHolder> customHeaders;
    /**
     * 文件map
     */
    private final MultiValueMap<String, MultipartFile> multipartFiles;
    private String contentType;
    /**
     * 参数map
     */
    private Map<String, String[]> parameterMap;
    private boolean isMultipart = false;

    /**
     * Constructs a request object wrapping the given request.
     *
     * @param request
     * @throws IllegalArgumentException if the request is null
     */
    public YopHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);

        this.contentType = request.getContentType();
        customHeaders = new LinkedCaseInsensitiveMap<>();
        parameterMap = Maps.newHashMap();

        final CommonsMultipartResolver multipartResolver = (CommonsMultipartResolver) SpringContextUtil.getBean("multipartResolver");
        isMultipart = multipartResolver.isMultipart(this);
        Map<String, String[]> rawParams;
        if (isMultipart) {
            //此处之所以没用自带的属性maxUploadSize限制上传文件的大小，是因为没法对异常做处理...
            retainInputStream(request);
            MultipartHttpServletRequest multipartHttpServletRequest = multipartResolver.resolveMultipart(this);
            rawParams = multipartHttpServletRequest.getParameterMap();
            multipartFiles = multipartHttpServletRequest.getMultiFileMap();
        } else {
            rawParams = super.getParameterMap();
            multipartFiles = null;
            retainInputStream(request);
        }

        for (Map.Entry<String, String[]> param : rawParams.entrySet()) {
            parameterMap.put(param.getKey(), escapeParam(param.getValue()));
        }

    }

    private void retainInputStream(HttpServletRequest request) {
        byte[] bytes = new byte[2048];
        try {
            bytes = IOUtils.toByteArray(request.getInputStream());
        } catch (IOException e) {
            LOGGER.error("read bodyByte error,", e);
        }
        byteArrayInputStream = new ByteArrayInputStream(bytes);
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        return parameterMap;
    }

    @Override
    public String[] getParameterValues(String parameter) {
        String[] values = parameterMap.get(parameter);
        if (values instanceof String[]) {
            return values;
        } else if (values instanceof Object[]) {
            Object[] paramValues = values;
            String[] strValues = new String[paramValues.length];
            int i = 0;
            for (Object v : paramValues) {
                strValues[i++] = (String) v;
            }
        }
        return super.getParameterValues(parameter);
    }

    @Override
    public String getParameter(String parameter) {
        Object value = parameterMap.get(parameter);
        if (value instanceof String) {
            return (String) value;
        } else if (value instanceof String[]) {
            String[] paramValues = (String[]) value;
            if (paramValues.length > 0) {
                return paramValues[0];
            }
        } else if (value instanceof Object[]) {
            Object[] paramValues = (Object[]) value;
            if (paramValues.length > 0) {
                return (String) paramValues[0];
            }
        }
        return super.getParameter(parameter);
    }

    public void setParameter(String name, String[] value) {
        parameterMap.put(name, value);
    }

    @Override
    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public void addHeader(String name, Object value) {
        doAddHeaderValue(name, value, false);
    }

    @SuppressWarnings("rawtypes")
    private void doAddHeaderValue(String name, Object value, boolean replace) {
        HeaderValueHolder header = HeaderValueHolder.getByName(this.customHeaders, name);
        Assert.notNull(value, "Header value must not be null");
        if (header == null || replace) {
            header = new HeaderValueHolder();
            this.customHeaders.put(name, header);
        }
        if (value instanceof Collection) {
            header.addValues((Collection) value);
        } else if (value.getClass().isArray()) {
            header.addValueArray(value);
        } else {
            header.addValue(value);
        }
    }

    @Override
    public String getHeader(String name) {
        HeaderValueHolder header = HeaderValueHolder.getByName(this.customHeaders, name);
        return header != null ? header.getStringValue() : ((HttpServletRequest) getRequest()).getHeader(name);
    }

    @Override
    public Enumeration<String> getHeaderNames() {
        // create a set of the custom header names
        Set<String> set = new HashSet<String>(customHeaders.keySet());

        // now add the headers from the wrapped request object
        @SuppressWarnings("unchecked")
        Enumeration<String> e = ((HttpServletRequest) getRequest()).getHeaderNames();
        while (e.hasMoreElements()) {
            // add the names of the request headers into the list
            String n = e.nextElement();
            set.add(n);
        }

        // create an enumeration from the set and return
        return Collections.enumeration(set);
    }


    /**
     * 请求参数处理
     *
     * @param value
     * @return
     */
    private String[] escapeParam(String[] value) {
        List<String> newValue = Lists.newArrayList();
        for (String item : value) {
            try {
                // fix: 用户传递>，此处虽然做了 URL 解码但是又做了 XSS 导致用户实际想传递的内容被变更
                // 考虑到网关的透明性，此处不再做 XSS 处理。
                // 例如：用户请求参数 37098219>9101186692
                // item = URLDecoder.decode(item, ENCODING);// 37098219%3E9101186692
                // newValue.add(StringUtils.trim(URIParseUtils.cleanXSS(item)));// 37098219&gt;9101186692
                newValue.add(URLDecoder.decode(item, ENCODING));
            } catch (UnsupportedEncodingException e) {
                throw new YopException(YopBizCodeConsts.INVALID_ENCODING,
                        this.getParameter(SystemParamNames.getAppKey()));
            }
        }
        return newValue.toArray(new String[]{});
    }

    public boolean isMultipart() {
        return isMultipart;
    }

    public MultiValueMap<String, MultipartFile> getMultipartFiles() {
        return multipartFiles;
    }


    @Override
    public ServletInputStream getInputStream() throws IOException {
        byteArrayInputStream.reset();
        return new YopDelegatingServletInputStream(byteArrayInputStream);
    }

}
