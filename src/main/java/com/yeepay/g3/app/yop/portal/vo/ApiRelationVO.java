/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/20 11:25 上午
 */
@Setter
@Getter
public class ApiRelationVO extends BaseVO {

    private Long id;

    private Long version;

    /**
     * APIID
     */
    private String apiId;

    /**
     * 关联类型
     */
    private String relateType;

    /**
     * 被关联API
     */
    private SimpleApiVO relateApi;


    /**
     * 关联配置
     * 关联类型为查单时记录结果通知信息
     */
    private Map<String, Object> relateConfig;
}
