package com.yeepay.g3.yop.frame.exception.authentication;

import com.yeepay.g3.yop.frame.CharacterConstants;

/**
 * title: 过期的 token<br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/6/25 下午1:12
 */
public class ExpiredTokenException extends OAuth2Exception {
    @Override
    public String getCode() {
        return super.getCode() + CharacterConstants.DOT + "expired-token";
    }
}

