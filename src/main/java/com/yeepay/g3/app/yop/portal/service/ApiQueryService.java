package com.yeepay.g3.app.yop.portal.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.yeepay.g3.app.yop.portal.dto.ApiSimpleInfoDTO;
import com.yeepay.g3.app.yop.portal.vo.ApiParamSensitiveParamVO;
import com.yeepay.g3.app.yop.portal.vo.ApiParamSensitiveVO;
import com.yeepay.g3.app.yop.portal.vo.SimpleApiVO;
import com.yeepay.g3.app.yop.portal.vo.TypeVO;
import com.yeepay.g3.app.yop.portal.vo.page.ApiDefinePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ApiDefinePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/16 11:44
 */
public interface ApiQueryService {

    /**
     * 通过apiId查询
     * 新版api
     *
     * @param apiId
     * @return
     */
    SimpleApiVO findByApiId(String apiId);

    /**
     * 分页查询
     *
     * @param queryParam 查询参数
     * @return 分页查询结果
     */
    PageQueryResult<ApiDefinePageItem> pageQuery(ApiDefinePageQueryParam queryParam);

    /**
     * 分页查询api（sp）
     *
     * @param queryParam   查询参数
     * @param operatorCode 操作员编码
     * @return 分页查询结果
     */
    PageQueryResult<ApiDefinePageItem> pageQueryForSp(ApiDefinePageQueryParam queryParam, String operatorCode);

    /**
     * 根据api分组查询所有api
     *
     * @param apiGroups api分组
     * @return api
     */
    List<ApiDefinePageItem> list(String[] apiGroups);

    /**
     * 根据分组查询api
     * 包括新版&老版
     *
     * @param apiGroups
     * @return
     */
    List<ApiDefinePageItem> listApis(String[] apiGroups);

    /**
     * 根据api分组查询所有api
     *
     * @param apiGroups api分组
     * @return api
     */
    List<ApiDefinePageItem> listForSp(String[] apiGroups);

    /**
     * 根据API分组编码查询API
     *
     * @param groupCode
     * @return
     */
    List<ApiSimpleInfoDTO> queryApiForByGroupCode(String groupCode);

    /**
     * 根据apiUri和参数类型查询参数
     *
     * @param apiParamSensitiveParamVO
     * @return
     */
    List<ApiParamSensitiveVO> listApiParam(ApiParamSensitiveParamVO apiParamSensitiveParamVO);

    /**
     * 根据apiUri和参数类型查询返回参数
     *
     * @param apiParamSensitiveParamVO
     * @return
     */
    List<ApiParamSensitiveVO> listApiReturnParam(ApiParamSensitiveParamVO apiParamSensitiveParamVO);

    JsonNode listApiServletConfig(String backendCode);


    /**
     * 查询api关联所有spi
     *
     * @param apiId
     * @return
     */
    List<TypeVO> listCallback(String apiId);

}
