spring.redis.timeout=2000
#spring.redis.sentinel.master=mymaster
#spring.redis.sentinel.nodes=redis.bass.3g:26379
spring.redis.host=qacodis.redis.yp
spring.redis.port=6379
spring.redis.pool.max-wait=5000
spring.redis.redisson.threads=5
spring.redis.redisson.nettyThreads=5
spring.redis.redisson.lockWatchdogTimeout=40000
spring.redis.redisson.checkSentinelsList=false
spring.redis.password=XjVnumoTigyY8oIxgWllMw==
spring.redis.ssl=false