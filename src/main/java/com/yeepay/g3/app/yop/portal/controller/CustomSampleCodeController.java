package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.facade.yop.sys.dto.SampleCodeDTO;
import com.yeepay.g3.facade.yop.sys.dto.SampleCodeRequest;
import com.yeepay.g3.facade.yop.sys.facade.SampleCodeFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  上午8:59
 */

@Controller
@RequestMapping("/rest/api-group")
public class CustomSampleCodeController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CustomSampleCodeController.class);

    private SampleCodeFacade sampleCodeFacade = RemoteServiceFactory.getService(SampleCodeFacade.class);

    @ResponseBody
    @RequestMapping(value = "sample-code/batch-update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody SampleCodeRequest sampleCodes) {
        try {
            sampleCodeFacade.batchAdd(sampleCodes);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("更新示例代码出现异常", ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "sample-code/list", method = RequestMethod.GET)
    public ResponseMessage query(@RequestParam String apiUri) {
        try {
            List<SampleCodeDTO> sampleCodes = sampleCodeFacade.findByApiUri(apiUri);
            return new ResponseMessage("result", sampleCodes);
        } catch (Exception ex) {
            return new ResponseMessage(ex);
        }

    }
}
