/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.yeepay.g3.facade.yop.perm.enums.ResourceTypeEnum;
import com.yeepay.g3.utils.query.QueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 20/04/08 13:12
 */
@Component("aclResourceLocalCache")
public class AclResourceLocalCache {

    public static final int CACHE_MAX_SIZE = 500;
    private static final Logger LOGGER = LoggerFactory.getLogger(AclResourceLocalCache.class);

    @Resource(name = "aclResourceQueryService")
    private QueryService resourceQueryService;

    private LoadingCache<Long, Map<String, String>> aclResourceLocalCache = CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE)
            .expireAfterAccess(3601, TimeUnit.SECONDS)
            .build(getCacheLoader());

    private CacheLoader getCacheLoader() {
        return new CacheLoader<Long, Map<String, String>>() {
            @Override
            public Map<String, String> load(Long key) {
                Map<String, String> resource = new HashMap<>();
                try {
                    Map<String, Object> resourceParams = Maps.newHashMap();
                    resourceParams.put("id", key);
                    resourceParams.put("resourceType", ResourceTypeEnum.BUTTON.getValue());
                    List<Map> resourceMap = resourceQueryService.query("resourceQuery", resourceParams);
                    for (Map map : resourceMap) {
                        resource.put((String) map.get("url"), (String) map.get("path"));
                    }
                } catch (Exception e) {
                    LOGGER.error("load aclResource by id failed,error:", e);
                }
                return resource;
            }
        };
    }

    public Map<String, String> get(Long key) {
        try {
            return aclResourceLocalCache.get(key);
        } catch (Exception e) {
            LOGGER.error("error when get local cache", e);
            return null;
        }
    }

}
