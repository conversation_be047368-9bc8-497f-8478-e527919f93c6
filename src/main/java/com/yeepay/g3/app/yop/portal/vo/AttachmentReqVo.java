package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yeepay.g3.facade.yop.doc.enums.StoreTypeEnum;
import org.springframework.web.multipart.MultipartFile;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018-12-18 14:17
 */
public class AttachmentReqVo extends BaseVO {
    private static final long serialVersionUID = -1;

    private Long id;
    private String bizCode = "doc";
    private String fileName;
    private StoreTypeEnum storeType = StoreTypeEnum.CEPH;
    @JsonIgnore
    private MultipartFile fileStream;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public StoreTypeEnum getStoreType() {
        return storeType;
    }

    public void setStoreType(StoreTypeEnum storeType) {
        this.storeType = storeType;
    }

    public MultipartFile getFileStream() {
        return fileStream;
    }

    public void setFileStream(MultipartFile fileStream) {
        this.fileStream = fileStream;
    }
}
