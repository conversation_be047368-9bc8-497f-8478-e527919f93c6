/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.regression.execute.engine;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/20 上午11:21
 */
public class ExecuteEngineFactory {

    private static final Map<String, ExecuteEngine> EXECUTE_ENGINE_MAP = new ConcurrentHashMap<>(3);

    /**
     * 获取
     *
     * @param security 安全需求
     * @return 请求适配器
     */
    public static ExecuteEngine getExecuteEngine(String security) {
        return EXECUTE_ENGINE_MAP.get(security);
    }

    /**
     * 注册
     *
     * @param executeEngine 执行引擎
     */
    public static void register(ExecuteEngine executeEngine) {
        EXECUTE_ENGINE_MAP.put(executeEngine.getSecurity(), executeEngine);
    }
}
