package com.yeepay.g3.app.yop.portal.vo.page;

import lombok.Builder;

import java.io.Serializable;
import java.util.Date;

/**
 * title: ApiManagePageItem<br/>
 * description: api分页查询返回参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:38
 */
@Builder
public class ErrorCodeExtendInfoItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private String errorCodeLocation;

    private String messageLocation;

    private String requestSuccessValue;

    private String requestSuccessType;

    private Long nonce;

    public Long getNonce() {
        return nonce;
    }

    public void setNonce(Long nonce) {
        this.nonce = nonce;
    }

    public String getErrorCodeLocation() {
        return errorCodeLocation;
    }

    public void setErrorCodeLocation(String errorCodeLocation) {
        this.errorCodeLocation = errorCodeLocation;
    }

    public String getMessageLocation() {
        return messageLocation;
    }

    public void setMessageLocation(String messageLocation) {
        this.messageLocation = messageLocation;
    }

    public String getRequestSuccessValue() {
        return requestSuccessValue;
    }

    public void setRequestSuccessValue(String requestSuccessValue) {
        this.requestSuccessValue = requestSuccessValue;
    }

    public String getRequestSuccessType() {
        return requestSuccessType;
    }

    public void setRequestSuccessType(String requestSuccessType) {
        this.requestSuccessType = requestSuccessType;
    }
}
