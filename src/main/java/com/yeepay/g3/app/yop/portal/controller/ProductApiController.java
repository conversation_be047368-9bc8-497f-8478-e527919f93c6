/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.ProductApiQueryService;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.ProductApiVO;
import com.yeepay.g3.app.yop.portal.vo.page.ProductApiPageQueryParam;
import com.yeepay.g3.facade.yop.sys.dto.ProductApiRequest;
import com.yeepay.g3.facade.yop.sys.dto.ProductApiResponse;
import com.yeepay.g3.facade.yop.sys.facade.ProductApiFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/5 下午5:57
 */
@Controller
@RequestMapping("/rest/product/api")
public class ProductApiController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductApiController.class);

    private ProductApiFacade productApiFacade = RemoteServiceFactory.getService(ProductApiFacade.class);

    @Autowired
    private ProductApiQueryService productApiQueryService;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@Validated ProductApiPageQueryParam param,
                                @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "_pageSize", required = false) Integer pageSize) {
        try {
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("product-api");
            param.setPageSize(appPageSize.intValue());
            return new ResponseMessage("page", productApiQueryService.pageQuery(param));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list product with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/batch-create", method = RequestMethod.POST)
    public ResponseMessage batchCreate(@RequestBody @Validated ProductApiVO productApiVO) {
        try {
            ProductApiRequest request = new ProductApiRequest();
            request.setProductCode(productApiVO.getProductCode());
            request.setSceneId(productApiVO.getSceneId());
            request.setType(productApiVO.getType());
            request.setValues(productApiVO.getValues());
            ProductApiResponse response = productApiFacade.batchCreate(request);
            return new ResponseMessage("result", response);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when create product api with param:" + productApiVO, e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/batch-delete", method = RequestMethod.POST)
    public ResponseMessage batchDelete(@RequestBody @Validated ProductApiVO productApiVO) {
        try {
            ProductApiRequest request = new ProductApiRequest();
            request.setProductCode(productApiVO.getProductCode());
            request.setSceneId(productApiVO.getSceneId());
            request.setType(productApiVO.getType());
            request.setValues(productApiVO.getValues());
            ProductApiResponse response = productApiFacade.batchDelete(request);
            return new ResponseMessage("result", response);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when create product api with param:" + productApiVO, e);
            return new ResponseMessage(e);
        }
    }

}
