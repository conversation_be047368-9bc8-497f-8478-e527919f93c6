/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import cfca.ra.common.vo.request.CertServiceRequestVO;
import cfca.ra.common.vo.request.QueryRequestVO;
import cfca.ra.common.vo.response.CertServiceResponseVO;
import cfca.ra.common.vo.response.QueryResponseListVO;
import cfca.ra.common.vo.response.QueryResponseVO;
import cfca.ra.toolkit.RAClient;
import cfca.ra.toolkit.exception.RATKException;
import cn.hutool.core.collection.CollectionUtil;
import com.yeepay.g3.app.yop.portal.enums.CFCACertStatusEnum;
import com.yeepay.g3.app.yop.portal.enums.CFCACertTypeEnum;
import com.yeepay.g3.app.yop.portal.service.CertService;
import com.yeepay.g3.app.yop.portal.service.CfcaCertService;
import com.yeepay.g3.app.yop.portal.vo.CfcaCertParamVO;
import com.yeepay.g3.app.yop.portal.vo.CfcaCertVO;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/3/5 18:46
 */
@Component
@Slf4j
@Profile("cfca")
public class CfcaCertServiceImpl implements CfcaCertService {
    // 普通证书
    private static final String GENERAL_CERTIFICATE = "1";
    // 企业证书
    private static final String ENTERPRISE_TYPE = "2";
    @Autowired
    private RAClient raClient;
    @Autowired
    private CertService certService;

    @Override
    public void revoke(String serialNo) {
        List<QueryResponseVO> queryResponseVOS = queryCert(null, ENTERPRISE_TYPE, GENERAL_CERTIFICATE, serialNo, null);
        if (CollectionUtil.isEmpty(queryResponseVOS)) {
            throw new YeepayRuntimeException("证书不存在");
        }
        if (!CFCACertStatusEnum.ACTIVATED.getValue().equals(queryResponseVOS.get(0).getCertStatus())) {
            throw new YeepayRuntimeException("证书状态为:{0}，不能吊销", CFCACertStatusEnum.parse(queryResponseVOS.get(0).getCertStatus()).getDecs());
        }
        // 证书是否被使用过（是否在yop配置过密钥）
        if (certService.isExisted(serialNo)) {
            throw new YeepayRuntimeException("证书已被使用，不能吊销");
        }
        CertServiceRequestVO certServiceRequestVO = new CertServiceRequestVO();
        certServiceRequestVO.setTxCode("2901");
        certServiceRequestVO.setDn(queryResponseVOS.get(0).getDn());
        try {
            CertServiceResponseVO certServiceResponseVO = (CertServiceResponseVO) raClient.process(certServiceRequestVO);
            checkRaResultCode(certServiceResponseVO);
        } catch (RATKException e) {
            log.error("", e);
            throw new YeepayRuntimeException(e);
        }
    }

    @Override
    public List<CfcaCertVO> list(CfcaCertParamVO param) {
        String status = null;
        if (StringUtils.isNotEmpty(param.getStatus())) {
            status = CFCACertStatusEnum.get(param.getStatus()).getValue();
        }
        List<QueryResponseVO> queryResponseVOS = queryCert(param.getCustomerNo(), ENTERPRISE_TYPE, GENERAL_CERTIFICATE, param.getSerialNo(), status);
        List<CfcaCertVO> cfcaCertVOS = convert2CfcaCertPageItem(queryResponseVOS);
        return cfcaCertVOS;
    }

    private void checkRaResultCode(CertServiceResponseVO certServiceResponseVO) {
        String resultCode = certServiceResponseVO.getResultCode();
        if (!RAClient.SUCCESS.equals(resultCode)) {
            String resultMsg = certServiceResponseVO.getResultMessage();
            log.error("调用RA系统异常，错误码为:{},错误信息为:{}", resultCode, resultMsg);
            throw new YeepayRuntimeException(resultMsg);
        }
    }

    private List<CfcaCertVO> convert2CfcaCertPageItem(List<QueryResponseVO> queryResponseVOS) {
        if (CollectionUtils.isEmpty(queryResponseVOS)) {
            return new ArrayList<>();
        }
        List<CfcaCertVO> cfcaCertVOS = new ArrayList<>(queryResponseVOS.size());
        String customerType = "企业";
        for (QueryResponseVO queryResponseVO : queryResponseVOS) {
            CfcaCertVO cfcaCertVO = new CfcaCertVO();
            cfcaCertVO.setCustomerNo(queryResponseVO.getUserIdent());
            cfcaCertVO.setDn(queryResponseVO.getDn());
            cfcaCertVO.setSerialNo(queryResponseVO.getSerialNo());
            cfcaCertVO.setKeyAlg(queryResponseVO.getKeyAlg());
            cfcaCertVO.setType(customerType + CFCACertTypeEnum.parse(queryResponseVO.getCertType()).getDecs());
            cfcaCertVO.setStatus(CFCACertStatusEnum.parse(queryResponseVO.getCertStatus()).getDecs());
            cfcaCertVO.setApplyDate(queryResponseVO.getApplyTime());
            cfcaCertVO.setEffectiveDate(queryResponseVO.getStartTime());
            cfcaCertVO.setExpiredDate(queryResponseVO.getEndTime());
            // 证书是否被使用过（是否在yop配置过密钥）
            cfcaCertVO.setUsed(certService.isExisted(cfcaCertVO.getSerialNo()));
            cfcaCertVOS.add(cfcaCertVO);
        }
        return cfcaCertVOS;
    }

    /**
     * ra证书查询
     *
     * @param customerNo
     * @param customerType
     * @param serialNo
     * @param certStatus
     * @return
     */
    private List<QueryResponseVO> queryCert(String customerNo, String customerType, String certType, String serialNo, String certStatus) {
        QueryRequestVO queryRequestVO = new QueryRequestVO();
        queryRequestVO.setTxCode("7101");
        if (StringUtils.isNotEmpty(customerNo)) {
            queryRequestVO.setUserIdent(customerNo);
        }
        if (StringUtils.isNotEmpty(customerType)) {
            queryRequestVO.setCustomerType(customerType);
        }
        if (StringUtils.isNotEmpty(serialNo)) {
            queryRequestVO.setSerialNo(serialNo);
        }
        if (StringUtils.isNotEmpty(certStatus)) {
            queryRequestVO.setCertStatus(certStatus);
        }
        if (StringUtils.isNotEmpty(certType)) {
            queryRequestVO.setCertType(certType);
        }
        queryRequestVO.setExactly("true");
        try {
            QueryResponseListVO queryResponseListVO = (QueryResponseListVO) raClient.process(queryRequestVO);
            List<QueryResponseVO> queryResponseVOList = queryResponseListVO.getQueryResponseVOList();
            if (CollectionUtils.isEmpty(queryResponseVOList)) {
                return new ArrayList<>();
            }
            return queryResponseVOList;
        } catch (RATKException e) {
            log.error("", e);
            throw new YeepayRuntimeException(e);
        }
    }
}

