package com.yeepay.g3.app.yop.portal.exception;

import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;

/**
 * title: <br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017/5/4 下午6:21
 */
public class YopPortalException extends YeepayRuntimeException {

    public YopPortalException() {
        // do nothing
    }

    public YopPortalException(String message, Object... args) {
        super(message, args);
    }

    public YopPortalException(Throwable throwable) {
        super(throwable);
    }

    public YopPortalException(String message, Throwable throwable, Object... args) {
        super(message, throwable, args);
    }

}
