package com.yeepay.g3.yop.frame.protocol.response.yop.v300rc1.exceptions;

import com.google.common.collect.Maps;
import com.yeepay.g3.yop.frame.chain.interceptor.annotations.ExceptionHandler;
import com.yeepay.g3.yop.frame.context.Context;
import com.yeepay.g3.yop.frame.exception.InternalException;
import com.yeepay.g3.yop.frame.exception.storage.*;
import com.yeepay.g3.yop.frame.protocol.response.yop.v300rc1.AbstractExceptionYopMarshaller;
import com.yeepay.g3.yop.frame.protocol.response.yop.v300rc1.ExceptionResult;
import com.yeepay.g3.yop.frame.response.Response;

import java.util.Map;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/1/26 下午12:53
 */

@ExceptionHandler({FileStoreException.class, IspFileStoreException.class})
public class FileStoreExceptionHandler extends AbstractExceptionYopMarshaller {

    private Map<Class, FileStoreErrorCreator> creators = Maps.newHashMap();

    @Override
    protected void postConstruct() {
        creators.put(FileStoreUnavailableException.class, () -> "文件服务不可用");
        creators.put(FileAlreadyExistedException.class, () -> "文件已存在");
        creators.put(FileCRC64FailedException.class, () -> "文件crc64校验失败");
        creators.put(FileETagFailedException.class, () -> "文件ETag计算失败");
        creators.put(FileStoreUploadFailedException.class, () -> "文件上传失败");
        creators.put(FileNotExistedException.class, () -> "文件不存在");
        creators.put(FileStoreAuthFailedException.class, () -> "文件服务认证失败");
        creators.put(FileStoreAuthorizationLimitedException.class, () -> "文件服务访问受限");
        creators.put(FileStoreCapacityLimitedException.class, () -> "文件服务容量受限");
        creators.put(FileStoreNetworkRateLimitedException.class, () -> "文件服务网络速率受限");
        creators.put(IspFileStoreNotConfiguredException.class, () -> "没找到文件存储配置");
    }

    @Override
    public ExceptionResult getErrorResult(Context context, Response response) {
        InternalException e = response.getException();
        return ExceptionResult.newBuilder().code(StatusEnum.BUSINESS_ERROR)
                .subCode(e.getCode()).subMessage(creators.get(e.getClass()).createSubMessage()).build();
    }


    private interface FileStoreErrorCreator {

        /**
         * 创建具体的error
         *
         * @return
         */
        String createSubMessage();

    }
}
