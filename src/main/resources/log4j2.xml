<?xml version="1.0" encoding="UTF-8"?>
<Configuration packages="com.yeepay.g3.utils"><!--packages参数告诉log4j2还需要额外加载哪个包下的Log4j plugin，其中YeepayMessagePatternConverter即为定制的plugin,负责输出的日志带GUID -->
    <Appenders>
        <!-- 控制台输出 -->
                <Console name="CONSOLELOG" target="SYSTEM_OUT">
                    <PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss.SSS} - %c -%-4r [%t] %-5p %x - %msg%n%throwable"/>
                </Console>
    </Appenders>
    <Loggers>
        <Logger name="com.yeepay" level="INFO"/>
        <Logger name="com.yeepay.g3.app.yop" level="DEBUG"/>
        <Logger name="com.yeepay.g3.app.yop.portal.shiro.session" level="INFO"/>
        <Logger name="com.yeepay.g3.PerformanceLogger" level="DEBUG"/>
        <Logger name="net.sf.ehcache" level="INFO"/>
        <Logger name="com.alisoft.xplatform.asf" level="WARN"/>
        <Logger name="com.mbi" level="ERROR"/>
        <Logger name="net.mlw" level="INFO"/>
        <Logger name="java.sql" level="INFO"/>
        <Logger name="com.mchange.v2.async" level="INFO"/>
        <Logger name="org.apache" level="INFO"/>
        <Logger name="org.apache.commons.httpclient" level="ERROR"/>
        <Logger name="com.yeepay.g3.utils.soa.registry.zookeeper" level="FATAL"/>
        <Logger name="org.springframework" level="INFO"/>
        <Logger name="org.hibernate.validator.internal.util.Version" level="INFO"/>
        <Logger name="org.apache.coyote.http11.Http11NioProtocol" level="INFO"/>
        <Logger name="org.apache.tomcat.util.net.NioSelectorPool" level="INFO"/>
        <Logger name="org.apache.catalina.startup.DigesterFactory" level="INFO"/>
        <Logger name="org.redisson" level="INFO"/>
        <Logger name="org.apache.dubbo" level="INFO"/>
        <Root level="WARN">
                        <AppenderRef ref="CONSOLELOG"/>
        </Root>
    </Loggers>
</Configuration>