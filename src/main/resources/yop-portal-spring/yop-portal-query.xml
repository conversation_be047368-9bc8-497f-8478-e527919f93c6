<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="customerDockingEventService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="mysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="findCustomerDockingByParam">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        *
                                    FROM
                                        tbl_customer_docking_event
                                    WHERE
                                        app_id = {appId}
                                        AND type = {type}
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>

    </bean>

    <bean id="apiQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <!--查询新版和老版API-->
                <entry key="listBothV1AndV2">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select api_id,`path`,api_title as title
                                    from
                                    ( select api_id,`path`
                                    from tbl_api_request
                                    where SUBSTRING_INDEX(SUBSTRING_INDEX(`path`,'/' , 4), '/',-1) IN {groups}
                                    and http_method='OLD_API') api left join
                                    tbl_yop_api_define api_define on
                                    api.`path`=api_define.api_uri
                                    where api_define.status='ACTIVE'
                                    union
                                    select api.api_id,`path`,title
                                    from tbl_api api left join
                                    tbl_api_request request on
                                    api.api_id=request.api_id
                                    where api.api_group in {groups}
                                    and api.status!='DISABLED'
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 根据apiId查响应-->
                <entry key="findResponseByApiId">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select
                                        id,
                                        api_id,
                                        http_code,
                                        content_type,
                                        headers,
                                        content,
                                        encrypt
                                    from tbl_api_response
                                    where api_id = {apiId}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="listCallback">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select spi.name as name,title
                                    from tbl_api_callback callback left join
                                    tbl_spi spi on callback.spi_name=spi.name
                                    where callback.api_id={apiId}
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findByApiId">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select name,title,path,http_method
                                    from tbl_api api left join
                                    tbl_api_request request on api.api_id = request.api_id
                                    where api.api_id = {apiId}
                                    limit 1
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="findOldApi">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select api_title,http_method
                                    from tbl_yop_api_define
                                    where api_uri = {path}
                                    limit 1
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="list">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                      API.ID,
                                      API.API_URI,
                                      API.API_TITLE,
                                      API.API_TYPE,
                                      API.STATUS,
                                      API.API_GROUP,
                                      API.CREATED_DATETIME,
                                      API.LAST_MODIFIED_DATETIME,
                                      API_GROUP.API_GROUP_NAME
                                    FROM
                                      TBL_YOP_API_DEFINE API
                                    LEFT JOIN
                                      TBL_API_GROUP API_GROUP
                                    ON
                                      API.API_GROUP = API_GROUP.API_GROUP_CODE
                                    WHERE
                                    1=1
                                    /~apiUri: AND API.API_URI = {apiUri}~/
                                    /~apiGroupCode: AND API.API_GROUP = {apiGroupCode}~/
                                    /~apiType: AND API.API_TYPE = {apiType}~/
                                    /~status: AND API.STATUS = {status}~/
                                    /~apiTitle: AND API.API_TITLE like concat('%',{apiTitle},'%')~/
                                    /~apiGroupCodes:AND API.API_GROUP in {apiGroupCodes}~/
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="listForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                      API.ID,
                                      API.API_URI,
                                      API.API_TITLE,
                                      API.API_TYPE,
                                      API.STATUS,
                                      API.API_GROUP,
                                      API.CREATED_DATETIME,
                                      API.LAST_MODIFIED_DATETIME,
                                      API_GROUP.API_GROUP_NAME
                                    FROM
                                      TBL_YOP_API_DEFINE API
                                    LEFT JOIN
                                      TBL_API_GROUP API_GROUP
                                    ON
                                      API.API_GROUP = API_GROUP.API_GROUP_CODE
                                    WHERE
                                    1=1
                                    /~apiUri: AND API.API_URI = {apiUri}~/
                                    /~apiGroupCode: AND API.API_GROUP = {apiGroupCode}~/
                                    /~apiType: AND API.API_TYPE = {apiType}~/
                                    /~status: AND API.STATUS = {status}~/
                                    /~apiTitle: AND API.API_TITLE like concat('%',{apiTitle},'%')~/
                                    /~apiGroupCodes:AND API.API_GROUP in {apiGroupCodes}~/
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="apiList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        API_URI,
                                        API_TITLE
                                    FROM
                                        TBL_YOP_API_DEFINE
                                    WHERE 1=1
                                      /~apiGroupCode:AND API_GROUP = {apiGroupCode}~/
                                    ORDER BY LAST_MODIFIED_DATETIME DESC
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="listApiParam">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        PARAM.ID,
                                        PARAM.PARAM_NAME,
                                        PARAM.PARAM_DATA_FORMAT,
                                        PARAM.SENSITIVE,
                                        PARAM.PARENT_ID
                                    FROM
                                        TBL_YOP_API_DEFINE API
                                    JOIN
                                        TBL_YOP_API_PARAM_DEFINE PARAM
                                    ON
                                        API.ID = PARAM.API_ID
                                    WHERE API.API_URI = {apiUri}
                                    AND PARAM.PARAM_TYPE = {paramType}
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="listApiReturnParam">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        PARAM.ID,
                                        PARAM.PARAM_NAME,
                                        PARAM.PARAM_DATA_FORMAT,
                                        PARAM.SENSITIVE,
                                        PARAM.PARENT_ID
                                    FROM
                                        TBL_YOP_API_DEFINE API
                                    JOIN
                                        TBL_YOP_API_RETURN_PARAM_DEFINE PARAM
                                    ON
                                        API.ID = PARAM.API_ID
                                    WHERE API.API_URI = {apiUri}
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="bakcendApiServletConfig">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        api.API_URI as url,
                                        api.END_CLASS as end_class,
                                        api.END_METHOD as end_method,
                                        param.PARAM_TYPE as param_type,
                                        param.END_PARAM_NAME as end_param_name,
                                        param.END_PARAM_INDEX as end_param_index
                                    FROM
                                        TBL_YOP_API_DEFINE as api
                                    left join
                                        TBL_YOP_API_PARAM_DEFINE as param
                                    on
                                        api.ID = param.API_ID
                                    WHERE
                                        API.BACKEND_APP = {backendCode}
                                    order by
                                        api.API_URI,param.END_PARAM_INDEX
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!--     根据apiUri/apiTitle模糊查询        -->
                <entry key="listApis">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select t.* from (
                                        select a.path as api_uri, b.title as api_title from tbl_api_request a
                                            inner join tbl_api b on a.api_id = b.api_id
                                            left join tbl_yop_api_define c on a.path = c.api_uri
                                        where c.id is null and b.api_group != 'OLD_API'
                                        union
                                        select a.api_uri, a.api_title from tbl_yop_api_define a
                                            inner join tbl_api_request b on a.api_uri = b.path
                                        where b.http_method = 'OLD_API'
                                        union
                                        select a.path as api_uri, b.title as api_title from tbl_api_request a
                                            inner join tbl_api b on a.api_id = b.api_id
                                            inner join tbl_yop_api_define c on a.path = c.api_uri
                                        where c.status != 'ACTIVE' and c.is_migrated = true) t
                                    where t.api_title like concat('%', {keyword},'%')
                                    union select t1.* from (
                                        select a.path as api_uri, b.title as api_title from tbl_api_request a
                                            inner join tbl_api b on a.api_id = b.api_id
                                            left join tbl_yop_api_define c on a.path = c.api_uri
                                        where c.id is null and b.api_group != 'OLD_API'
                                        union
                                        select a.api_uri, a.api_title from tbl_yop_api_define a
                                            inner join tbl_api_request b on a.api_uri = b.path
                                        where b.http_method = 'OLD_API'
                                        union
                                        select a.path as api_uri, b.title as api_title from tbl_api_request a
                                            inner join tbl_api b on a.api_id = b.api_id
                                            inner join tbl_yop_api_define c on a.path = c.api_uri
                                        where c.status != 'ACTIVE' and c.is_migrated = true) t1
                                    where t1.api_uri like concat('%', {keyword},'%')
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="apiGroupQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <!-- 根据apiId查分组-->
                <entry key="findGroupByApiId">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT api_group
                                    FROM tbl_api
                                    WHERE api_id = {apiId}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 查询apiGroup sp用户使用 -->
                <entry key="listForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        api_group_code, api_group_name, sp_code
                                    FROM
                                        tbl_api_group
                                    WHERE
                                        sp_code in {spCodes}
                                    ORDER BY api_group_code
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="querySp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT sp_code
                                    FROM
                                        tbl_api_group
                                    where api_group_code={apiGroupCode}
                                    limit 1
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="list">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        api_group_code, api_group_name, sp_code
                                    FROM
                                        tbl_api_group
                                    ORDER BY api_group_code
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT api_group_code,
                                           api_group_name,
                                           sp_code,
                                           description,
                                           created_datetime,
                                           last_modified_datetime
                                    FROM tbl_api_group
                                    WHERE    1=1
                                    /~apiGroupCode: AND api_group_code = {apiGroupCode}~/
                                    /~spCode: AND sp_code = {spCode}~/
                                    /~createdStartDate: AND created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND created_datetime <= {createdEndDate}~/
                                    ORDER BY last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="pageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT api_group_code,
                                           api_group_name,
                                           sp_code,
                                           description,
                                           created_datetime,
                                           last_modified_datetime
                                    FROM tbl_api_group
                                    WHERE
                                        sp_code in {spCodes}
                                    /~spCode: AND sp_code = {spCode}~/
                                    /~apiGroupCode: AND api_group_code = {apiGroupCode}~/
                                    /~createdStartDate: AND created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND created_datetime <= {createdEndDate}~/
                                    ORDER BY created_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 查询apiGroup下配置的灰度策略 -->
                <entry key="listApiGroupGrayPolicy">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id,policy_desc,criteria,weight,enabled
                                    FROM
                                        tbl_gray_policy
                                    WHERE
                                        api_group = {apiGroup}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="auditRecordQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageQueryRecordListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select code,status,resource_name,originator,current_operator,
                                    (case when (select max(stage.created_datetime) from tbl_acl_audit_record_stage as stage where record_code = code ) is null then record.created_datetime else (select max(stage.created_datetime) from tbl_acl_audit_record_stage as stage where record_code = code ) end) as arrived_datetime
                                    from tbl_acl_audit_record as record
                                           where code in
                                    (SELECT
                                        distinct record.code
                                    FROM
                                        tbl_acl_resource_audit as audit
                                            join tbl_acl_audit_record as record on record.stage = audit.stage and record.resource_id = audit.resource_id
                                            left join tbl_acl_role as role
                                                on (master_value = role.role_code or master_value = role.parent_role_code)
                                            left join tbl_acl_user_role as user on user.role_code = role.role_code
                                    WHERE
                                        1=1
                                    /~code: AND code = {code}~/
                                    /~operator: AND (
                                                (master='ROLE' and user.operator_code = {operator} AND record.status = 'UNAUDITED')
                                                OR (record.current_operator = {operator} AND record.status = 'AUDITING')
                                                OR (master='USER' and audit.master_value = {operator} and record.status = 'UNAUDITED')
                                                )~/
                                    /~spCodes: AND record.sp_code in {spCodes}~/)
                                    order by arrived_datetime desc
                                    LIMIT {_maxSize} OFFSET {_startIndex}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="pageQueryRecordList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select code,status,resource_name,originator,current_operator,
                                    (case when (select max(stage.created_datetime) from tbl_acl_audit_record_stage as stage where record_code = code ) is null then record.created_datetime else (select max(stage.created_datetime) from tbl_acl_audit_record_stage as stage where record_code = code ) end) as arrived_datetime
                                    from tbl_acl_audit_record as record
                                           where code in
                                    (SELECT
                                        distinct record.code
                                    FROM
                                        tbl_acl_resource_audit as audit
                                            join tbl_acl_audit_record as record on record.stage = audit.stage and record.resource_id = audit.resource_id
                                            left join tbl_acl_role as role
                                                on (master_value = role.role_code or master_value = role.parent_role_code)
                                            left join tbl_acl_user_role as user on user.role_code = role.role_code
                                    WHERE
                                        1=1
                                    /~code: AND code = {code}~/
                                    /~operator: AND (
                                                (master='ROLE' and user.operator_code = {operator} AND record.status = 'UNAUDITED')
                                                OR (record.current_operator = {operator} AND record.status = 'AUDITING')
                                                OR (master='USER' and audit.master_value = {operator} and record.status = 'UNAUDITED')
                                                )~/)
                                    order by arrived_datetime desc
                                    LIMIT {_maxSize} OFFSET {_startIndex}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="auditRequisitionQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageQueryRequisitionListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        code,resource_name,originator,current_operator,status,created_datetime
                                    FROM
                                       tbl_acl_audit_record
                                    WHERE
                                        1=1
                                    /~code: AND code = {code}~/
                                    /~createdStartDate: AND created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND created_datetime <= {createdEndDate}~/
                                    /~originator: AND originator = {originator}~/
                                    /~status: AND status = {status}~/
                                    /~spCodes: AND sp_code in {spCodes}~/
                                    ORDER BY created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="pageQueryRequisitionList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                       code,resource_name,originator,current_operator,status,created_datetime
                                    FROM
                                       tbl_acl_audit_record
                                    WHERE
                                        1=1
                                    /~code: AND code = {code}~/
                                    /~createdStartDate: AND created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND created_datetime <= {createdEndDate}~/
                                    /~originator: AND originator = {originator}~/
                                    /~status: AND status = {status}~/
                                    ORDER BY created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="backendAppQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        backend_code, backend_name, sp_code, deploy_mode, class_load_mode,
                                        rpc_mode, description, created_datetime,
                                        last_modified_datetime
                                    FROM
                                        tbl_backend
                                    WHERE
                                        1=1
                                    /~backendCode: AND backend_code = {backendCode}~/
                                    /~backendName: AND backend_name like CONCAT('%',{backendName,string},'%')~/
                                    /~deployMode: AND deploy_mode = {deployMode}~/
                                    /~classLoadMode: AND class_load_mode = {classLoadMode}~/
                                    /~rpcMode: AND rpc_mode = {rpcMode}~/
                                    /~spCode: AND sp_code = {spCode}~/
                                    ORDER BY last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="pageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        backend_code, backend_name, sp_code, deploy_mode, class_load_mode,
                                        rpc_mode, description, created_datetime,
                                        last_modified_datetime
                                    FROM
                                        tbl_backend
                                    WHERE
                                        1=1
                                    /~backendCode: AND backend_code = {backendCode}~/
                                    /~backendName: AND backend_name like CONCAT('%',{backendName,string},'%')~/
                                    /~deployMode: AND deploy_mode = {deployMode}~/
                                    /~classLoadMode: AND class_load_mode = {classLoadMode}~/
                                    /~rpcMode: AND rpc_mode = {rpcMode}~/
                                    /~spCode: AND sp_code = {spCode}~/
                                    /~spCodes: AND sp_code in {spCodes}~/
                                    ORDER BY last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="invokeLogQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="mysqlDataSource"/>
        <property name="querys">
            <map>
                <!-- 查询MySQL调用记录-->
                <entry key="list">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    id, guid, request_id, app_key, api_uri, request_ip,
                                    ifnull(data_center, 'UNKNOWN') as data_center, status, error_code, sub_error_code,
                                    security_strategy, request_datetime, response_datetime
                                    FROM tbl_invoke_log
                                    /~partition: PARTITION(p[partition])~/
                                    WHERE
                                    request_datetime >= {requestStartDate}
                                    /~requestEndDate: AND request_datetime <= {requestEndDate}~/
                                    /~apiUri: AND api_uri = {apiUri}~/
                                    /~appKey: AND app_key = {appKey}~/
                                    /~apiGroupCode: AND api_group = {apiGroupCode}~/
                                    /~requestId: AND request_id = {requestId}~/
                                    /~guid: AND guid = {guid}~/
                                    /~bizOrderNo: AND biz_order_value = {bizOrderNo}~/
                                    /~requestIp: AND request_ip = {requestIp}~/
                                    /~dataCenter: AND data_center like CONCAT({dataCenter},'%')~/
                                    /~securityStrategy: AND security_strategy = {securityStrategy}~/
                                    /~status: AND status = {status}~/
                                    /~errorCode: AND error_code = {errorCode}~/
                                    /~subErrorCode: AND sub_error_code = {subErrorCode}~/
                                    /~minBackendLatency: AND backend_latency >= {minBackendLatency}~/
                                    /~maxBackendLatency: AND backend_latency <= {maxBackendLatency}~/
                                    ORDER BY
                                    request_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="detail">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    guid, request_id, app_key, api_uri, api_group, customer_no,
                                    request_ip, ifnull(data_center, 'UNKNOWN') as data_center, status, error_code, sub_error_code,
                                    request_method, security_strategy, request_datetime, response_datetime,
                                    total_latency, backend_latency, biz_order_code, biz_order_value
                                    FROM tbl_invoke_log
                                    WHERE
                                    id = {id}
                                    AND request_datetime = {requestDatetime}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="extendDetail">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    CONVERT(request_header USING utf8) as request_header,
                                    CONVERT(request_body USING utf8) as request_body,
                                    CONVERT(response_header USING utf8) as response_header,
                                    CONVERT(response_body USING utf8) as response_body,
                                    CONVERT(stack_trace USING utf8) as stack_trace
                                    FROM tbl_invoke_log_extend
                                    WHERE
                                    invoke_log_id = {id}
                                    AND request_datetime = {requestDatetime}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="statisticInvokeTimesApiGroupQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="mysqlDataSource"/>
        <property name="querys">
            <map>
                <!-- 按日查询 -->
                <entry key="listDaily">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    api_group, SUM(invoke_times) AS invoke_times,
                                    date_format(statistic_datetime ,'%Y-%m-%d') AS statistic_date
                                    FROM tbl_stat_data_hours
                                    WHERE 1=1
                                    /~apiGroup: AND api_group = {apiGroup}~/
                                    /~apiGroups: AND api_group in {apiGroups}~/
                                    /~statisticStartDate: AND statistic_datetime >= {statisticStartDate}~/
                                    /~statisticEndDate: AND statistic_datetime < {statisticEndDate}~/
                                    GROUP BY
                                    api_group,
                                    date_format(statistic_datetime ,'%Y-%m-%d')
                                    ORDER BY
                                    date_format(statistic_datetime ,'%Y-%m-%d') ASC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 按月查询 -->
                <entry key="listMonthly">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    api_group, SUM(invoke_times) AS invoke_times,
                                    date_format(statistic_datetime ,'%Y-%m') AS statistic_date
                                    FROM tbl_stat_data_hours
                                    WHERE 1=1
                                    /~apiGroup: AND api_group = {apiGroup}~/
                                    /~apiGroups: AND api_group in {apiGroups}~/
                                    /~statisticStartDate: AND statistic_datetime >= {statisticStartDate}~/
                                    /~statisticEndDate: AND statistic_datetime < {statisticEndDate}~/
                                    GROUP BY
                                    api_group,
                                    date_format(statistic_datetime ,'%Y-%m')
                                    ORDER BY
                                    date_format(statistic_datetime ,'%Y-%m') ASC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="ispInfoQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <!--根据spcode查isp详情-->
                <entry key="findBySpCode">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    *
                                    FROM tbl_isp_info
                                    WHERE
                                    sp_code = {spCode}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 查spCodes对应的所有租户codes-->
                <entry key="listTenantCodesBySpCodes">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    distinct tenant_code
                                    FROM tbl_isp_info
                                    WHERE
                                    sp_code in {spCodes}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 查询所有sp的code和name-->
                <entry key="listAllSpCode">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    sp_code, sp_name
                                    FROM tbl_isp_info
                                    WHERE status != 'DELETED'
                                    ORDER BY SP_CODE
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 查询所有sp的code和name-->
                <entry key="listAllSpCodeForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    sp_code, sp_name
                                    FROM tbl_isp_info
                                    WHERE 1 = 1
                                    /~spCodes: AND SP_CODE in {spCodes}~/
                                    and status != 'DELETED'
                                    ORDER BY SP_CODE
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 查询所有的isp信息-->
                <entry key="ispList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    sp_code, sp_name,status,description,created_datetime, last_modified_datetime, tenant_code
                                    FROM tbl_isp_info
                                    WHERE 1 = 1
                                    /~status: AND status = {status}~/
                                    /~spCode: AND sp_code = {spCode}~/
                                    ORDER BY last_modified_datetime DESC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 查询sp的isp信息-->
                <entry key="ispListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    sp_code, sp_name,status,description,created_datetime, last_modified_datetime, tenant_code
                                    FROM tbl_isp_info
                                    WHERE 1 = 1
                                    /~status: AND status = {status}~/
                                    /~spCode: AND sp_code = {spCode}~/
                                    /~spCodes: AND sp_code in {spCodes}~/
                                    ORDER BY last_modified_datetime DESC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="yosStoreQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <!-- 查询所有apiGroup下的yopConfig-->
                <entry key="apiGroupList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    id, config_type, api_group_code, store_type, store_config, is_default, version,
                                    created_datetime, last_modified_datetime
                                    FROM tbl_yos_store_config
                                    WHERE 1=1
                                    /~apiGroupCode: AND api_group_code = {apiGroupCode}~/
                                    /~createdStartDate: AND created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND created_datetime <= {createdEndDate}~/
                                    ORDER BY
                                    is_default DESC,
                                    last_modified_datetime DESC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 查询所有apiGroup下的yopConfig-->
                <entry key="apiGroupListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    id, config_type, api_group_code, store_type, store_config, is_default, version,
                                    created_datetime, last_modified_datetime
                                    FROM tbl_yos_store_config
                                    WHERE 1=1
                                    /~apiGroupCode: AND api_group_code = {apiGroupCode}~/
                                    /~apiGroupCodes: AND api_group_code in {apiGroupCodes}~/
                                    /~createdStartDate: AND created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND created_datetime <= {createdEndDate}~/
                                    ORDER BY
                                    is_default DESC,
                                    last_modified_datetime DESC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 根据id查询yopCinfig-->
                <entry key="findById">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    id, config_type, api_group_code, api_uri, param_name, store_type, store_config,
                                    is_default, version, created_datetime, last_modified_datetime
                                    FROM tbl_yos_store_config
                                    WHERE
                                    /~id: id = {id}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>
    <bean id="errorCodeMgrQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="list">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id,type,error_code,sub_error_code, sub_error_msg, api_group_code, api_uri, created_datetime,
                                        last_modified_datetime, version
                                    FROM
                                        tbl_error_code
                                       WHERE 1=1
                                       /~type:AND type = {type}~/
                                        /~apiGroupCode:AND api_group_code = {apiGroupCode}~/
                                        /~apiGroupCodes:AND api_group_code in {apiGroupCodes}~/
                                      /~apiUri:AND api_uri like CONCAT('%',{apiUri,string},'%')~/
                                      /~errorCode:AND error_code = {errorCode}~/
                                      /~subErrorCode: AND sub_error_code like CONCAT('%', {subErrorCode,string},'%')~/
                                    ORDER BY error_code, last_modified_datetime DESC
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="aclLogQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="mysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select id,oper_datetime,url,operator_code,request_ip,status,latency
                                    from tbl_acl_log
                                    where 1=1
                                    /~operatorCode: AND operator_code = {operatorCode}~/
                                    /~resourceUrls: AND url in {resourceUrls}~/
                                    /~operatorCodes: AND operator_code in {operatorCodes}~/
                                    /~operStartDate: AND oper_datetime >= {operStartDate}~/
                                    /~operEndDate: AND oper_datetime <= {operEndDate}~/
                                    order by oper_datetime desc
                                    LIMIT {_maxSize} OFFSET {_startIndex}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="queryDetail">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select request_content,detail,status
                                    from tbl_acl_log
                                    where id={id}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

            </map>
        </property>
    </bean>

    <bean id="aclResourceQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <!-- 查询父类资源下的子资源 -->
                <entry key="resourceQuery">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select *
                                    from(select res1.url as url,res1.path as path
                                         from tbl_acl_resource as res1,tbl_acl_resource as res2
                                         where res1.`path` like concat(ifnull(res2.`path`,''),'%')
                                         and res2.id={id})as tem
                                    where url is not null
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="nextLevelResourceQuery">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select res1.url as url,res1.path as path,res1.id as id,res1.resource_name as resource_name
                                    from tbl_acl_resource as res1,tbl_acl_resource as res2
                                    where CHAR_LENGTH(res1.`path` )-CHAR_LENGTH(replace (res1.`path` ,'/',''))=CHAR_LENGTH(res2.`path` )-CHAR_LENGTH(replace (res2.`path` ,'/',''))+1
                                    and res1.`path` like concat(res2.`path`,'%')
                                    and res2.id={id}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="topResourceQuery">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select id,resource_name
                                    from tbl_acl_resource
                                    where depth = 1
                                    order by `left`
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="aclRoleQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <!-- 查询角色 平台用户使用 -->
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT role.id, role.role_code, role.role_name,role.sp_code,
                                    isp.sp_name,role.role_type,role.version,
                                    role.created_datetime, role.last_modified_datetime
                                    FROM tbl_acl_role as role left join tbl_isp_info as isp on role.sp_code = isp.sp_code
                                    WHERE 1=1
                                    /~type: AND role.role_type = {type}~/
                                    /~spCode: AND role.sp_code = {spCode}~/
                                    /~roleCode: AND role.role_code like CONCAT('%',{roleCode,string})~/
                                    /~roleName: AND role.role_name like CONCAT('%',{roleName,string},'%')~/
                                    ORDER BY role.last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 查询橘色 sp用户使用 -->
                <entry key="pageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT role.id, role.role_code, role.role_name,role.sp_code,
                                    isp.sp_name,role.role_type,role.version,
                                    role.created_datetime, role.last_modified_datetime
                                    FROM tbl_acl_role as role left join tbl_isp_info as isp on role.sp_code = isp.sp_code
                                    WHERE 1=1
                                    /~type: AND role.role_type = {type}~/
                                    /~spCode: AND role.sp_code = {spCode}~/
                                    /~spCodes: AND role.sp_code in {spCodes}~/
                                    /~roleCode: AND role.role_code like CONCAT('%',{roleCode,string})~/
                                    /~roleName: AND role.role_name like CONCAT('%',{roleName,string},'%')~/
                                    ORDER BY role.last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 查询橘色 sp用户使用 -->
                <entry key="list">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT *
                                    FROM tbl_acl_role
                                    ORDER BY last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 查询橘色 sp用户使用 -->
                <entry key="listForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT *
                                    FROM tbl_acl_role
                                    WHERE 1=1
                                    /~spCodes: AND sp_code in {spCodes}~/
                                    ORDER BY last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

            </map>
        </property>
    </bean>

    <bean id="aclUserQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <!-- 查询角色下的用户 -->
                <entry key="pageListForRole">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT distinct user.id, user.operator_code, user.operator_name,user.operator_email,
                                    user.status,user.type,user.position,user.version,
                                    user.created_datetime, user.last_modified_datetime
                                    FROM tbl_isp_operator as user left join tbl_acl_user_role as role on user.operator_code = role.operator_code
                                    WHERE 1=1
                                    /~roleCode: AND role.role_code = {roleCode}~/
                                    /~position: AND user.position = {position}~/
                                    /~name: AND user.operator_code like CONCAT('%',{name,string},'%')~/
                                    /~status: AND user.status = {status}~/
                                    ORDER BY user.last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 查询sp下的用户 -->
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT id, operator_code, operator_name,operator_email,
                                    status,type,position,version,
                                    created_datetime, last_modified_datetime
                                    FROM tbl_isp_operator
                                    WHERE 1=1
                                    /~position: AND position = {position}~/
                                    /~name: AND operator_code like CONCAT('%',{name,string},'%')~/
                                    /~status: AND status = {status}~/
                                    ORDER BY last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 查询用户 -->
                <entry key="pageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT distinct user.id, user.operator_code, user.operator_name,user.operator_email,
                                    user.status,user.type,user.position,user.version,
                                    user.created_datetime, user.last_modified_datetime
                                    FROM tbl_isp_operator as user left join tbl_acl_user_role as role on user.operator_code = role.operator_code
                                    WHERE 1=1
                                    /~spCodes: AND role.sp_code in {spCodes}~/
                                    /~position: AND user.position = {position}~/
                                    /~name: AND user.operator_code like CONCAT('%',{name,string},'%')~/
                                    /~status: AND user.status = {status}~/
                                    ORDER BY user.last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 查询角色下的所有用户code -->
                <entry key="pageCommonListForRole">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT distinct operator_code
                                    FROM tbl_acl_user_role
                                    WHERE role_code = {roleCode}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="attachmentQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopDocMysqlDataSource"/>
        <property name="querys">
            <map>
                <!-- 查询附件分页 -->
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    id,file_id,biz_code,file_name,file_type,store_type,store_path,
                                    version,created_datetime,last_modified_datetime
                                    FROM tbl_attachment
                                    WHERE 1=1
                                    /~fileId: AND file_id = {fileId}~/
                                    /~fileType: AND file_type = {fileType}~/
                                    /~fileName: AND file_name like CONCAT('%',{fileName},'%')~/
                                    ORDER BY
                                    last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>


    <bean id="appQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    app_id, app_name, subject_no, customer_no, app_desc,
                                    app_type, status, created_datetime, last_modified_datetime
                                    FROM tbl_isv_app
                                    WHERE
                                    1 = 1
                                    /~appId: AND app_id = {appId}~/
                                    /~name: AND app_name like CONCAT('%',{name,string},'%')~/
                                    /~subjectNo: AND subject_no = {subjectNo}~/
                                    /~customerNo: AND customer_no = {customerNo}~/
                                    /~type: AND app_type = {type}~/
                                    /~status: AND status = {status}~/
                                    /~createdStartDate: AND created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND created_datetime <= {createdEndDate}~/
                                    ORDER BY
                                    last_modified_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="list">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    app_id, app_name, subject_no, customer_no, app_desc,
                                    app_type, status, created_datetime, last_modified_datetime
                                    FROM tbl_isv_app
                                    WHERE
                                    1 = 1
                                    /~appId: AND app_id = {appId}~/
                                    /~name: AND app_name like CONCAT('%',{name,string},'%')~/
                                    /~subjectNo: AND subject_no = {subjectNo}~/
                                    /~customerNo: AND customer_no = {customerNo}~/
                                    /~type: AND app_type = {type}~/
                                    /~status: AND status = {status}~/
                                    /~createdStartDate: AND created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND created_datetime <= {createdEndDate}~/
                                    ORDER BY
                                    last_modified_datetime DESC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="listAlias">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                SELECT alias
                                FROM tbl_isv_app_alias
                                WHERE app_id = {appId}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="listByCustomerNo">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                SELECT app_id
                                FROM tbl_isv_app
                                WHERE customer_no = {customerNo}
                                 ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="findByConditions">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        app_id
                                    FROM tbl_isv_app
                                    WHERE
                                        1 = 1
                                        /~customerNo: AND customer_no = {customerNo}~/
                                        /~types: AND app_type in {types}~/
                                        /~status: AND status = {status}~/
                                    ORDER BY
                                        last_modified_datetime DESC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="certQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="list">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    cert.id, cert.customer_no, cert.app_id, app.app_name, app.app_type,
                                    cert.cert_alias, cert.sign_sha1, cert.sign_sha256,cert.digest, cert.cert_type,
                                    cert.cert_usage, cert.cert_source, cert.effective_date, cert.expired_date,
                                    cert.status, cert.created_datetime, cert.last_modified_datetime
                                    FROM tbl_isv_app app
                                    JOIN tbl_isv_cert cert
                                    ON app.app_id = cert.app_id
                                    WHERE
                                    1 = 1
                                    /~appId: AND cert.app_id = {appId}~/
                                    /~customerNo: AND cert.customer_no = {customerNo}~/
                                    /~appName: AND app.app_name like CONCAT('%',{appName,string},'%')~/
                                    /~type: AND cert.cert_type = {type}~/
                                    /~status: AND cert.status = {status}~/
                                    /~createdStartDate: AND cert.created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND cert.created_datetime <= {createdEndDate}~/
                                    ORDER BY
                                    last_modified_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="certService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="listBySerialNo">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    *
                                    FROM tbl_isv_cert
                                    WHERE
                                    serial_no = {serialNo}
                                    AND status != "REVOKED"
                                    AND ( CURRENT_TIMESTAMP < revoke_date OR revoke_date IS NULL )
                                    ORDER BY
                                    last_modified_datetime DESC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="certChangeLogQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="list">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        app_id, operated_datetime, type, operator, cause, detail
                                    FROM tbl_isv_cert_change_log
                                    WHERE
                                    1 = 1
                                    /~certId: AND item_key = {certId}~/
                                    /~appId: AND app_id = {appId}~/
                                    /~operatedStartDate: AND operated_datetime >= {operatedStartDate}~/
                                    /~operatedEndDate: AND operated_datetime < {operatedEndDate}~/
                                    /~type: AND type = {type}~/
                                    /~operator: AND operator = {operator}~/
                                    ORDER BY
                                    operated_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="securityReqQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="list">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id, type, value, name, scopes, extensions, version, created_datetime, last_modified_datetime
                                    FROM tbl_security_req
                                    WHERE
                                    1 = 1
                                    /~id: AND id = {id}~/
                                    /~valueSet: AND value in {valueSet}~/
                                    /~type: AND type = {type}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="testCaseQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="findTestCaseById">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id, version, api_uri, title, regressive, app_key, security, form_request_param, json_request_param,
                                        assertion_list, token_id, mongo_id, created_datetime, last_modified_datetime
                                    FROM tbl_regression_test_case
                                    WHERE id = {id}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findExecHisByHisId">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                       id, version, status, operator, environment, start_execute_time, finish_execute_time,
                                       created_datetime, last_modified_datetime
                                    FROM tbl_regression_exec_history
                                    WHERE id = {id}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="statRegTestCountByApiUris">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT api_uri,count(*) as total_count
                                    FROM tbl_regression_test_case
                                    WHERE regressive = true
                                    /~apiUris: AND api_uri in {apiUris}~/
                                    group by api_uri
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="execHisStatByHistoryId">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                   select api_uri,sum(case when passed = true  then 1 else 0 end) as success_count,sum(case when passed = false  then 1 else 0 end) as failure_count
                                   from tbl_regression_exec_record
                                   WHERE finish_execute_time is not null
                                    /~historyId: AND history_id = {historyId}~/
                                    group by api_uri
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="simpleDetail">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id, name, api_group
                                    FROM
                                        tbl_backend_service
                                    WHERE
                                        /~id: id = {id}~/
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="backendServiceQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="exist">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        1=1
                                    FROM tbl_backend_service
                                    WHERE name = {name}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        service.id, service.version, service.name, service.type, service.properties,
                                        isp.sp_code, isp.sp_name, service.created_datetime, service.last_modified_datetime
                                    FROM
                                        tbl_backend_service as service
                                        left join tbl_isp_info as isp
                                        on service.sp_code = isp.sp_code
                                    WHERE
                                        1=1
                                    /~name: AND service.name like CONCAT('%',{name},'%')~/
                                    /~type: AND service.type = {type}~/
                                    /~spCode: AND service.sp_code = {spCode}~/
                                    ORDER BY service.last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        service.id, service.version, service.name, service.type, service.properties,
                                        isp.sp_code, isp.sp_name, service.created_datetime, service.last_modified_datetime
                                    FROM
                                        tbl_backend_service as service
                                        left join tbl_isp_info as isp
                                        on service.sp_code = isp.sp_code
                                    WHERE
                                        1=1
                                    /~name: AND service.name like CONCAT('%',{name},'%')~/
                                    /~type: AND service.type = {type}~/
                                    /~spCode: AND service.sp_code = {spCode}~/
                                    /~spCodes: AND service.sp_code in {spCodes}~/
                                    ORDER BY service.last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="simpleDetail">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id, api_group
                                    FROM
                                        tbl_backend_service
                                    WHERE
                                        id = {id}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="apiManageQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        distinct api2.api_id,
                                        api2.version,
                                        api2.name,
                                        api_request2.path,
                                        api_request2.http_method,
                                        IFNULL(JSON_UNQUOTE(JSON_EXTRACT(JSON_KEYS(api_request2.request_body,'$.contents'),'$[[0]]')),'application/x-www-form-urlencoded') AS content_type,
                                        api2.title,
                                        api2.api_group,
                                        api_group.api_group_name,
                                        api2.api_type,
                                        api2.description,
                                        api2.status,
                                        api2.latest_ref,
                                        api2.created_datetime,
                                        api2.last_modified_datetime
                                    FROM
                                        tbl_api api
                                        left join tbl_api api2 on api2.api_id = (
                                            case
                                                when api.latest_ref is not null or api.latest_ref != '' then api.latest_ref
                                                else api.api_id
                                            end
                                        )
                                        left join tbl_api_request api_request on api.api_id = api_request.api_id
                                        left join tbl_api_request api_request2 on api2.api_id = api_request2.api_id
                                        left join tbl_api_group api_group on api2.api_group = api_group.api_group_code
                                    WHERE
                                        api_request.http_method != 'OLD_API'
                                    /~path: AND api_request.path like CONCAT('%',{path},'%')~/
                                    /~method: AND api_request.http_method = {method}~/
                                    /~name: AND api.name = {name}~/
                                    /~title: AND api.title like CONCAT('%',{title},'%')~/
                                    /~apiGroup: AND api.api_group = {apiGroup}~/
                                    /~apiType: AND api2.api_type = {apiType}~/
                                    /~status: AND (api2.status = {status} OR ('PUBLISHED'={status} AND api2.status = 'DOC_PUBLISHED') OR ('UNPUBLISHED'={status} AND api2.status = 'DOC_UNPUBLISHED'))~/
                                    ORDER BY api2.last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        distinct api2.api_id,
                                        api2.version,
                                        api2.name,
                                        api_request.path,
                                        api_request.http_method,
                                        IFNULL(JSON_UNQUOTE(JSON_EXTRACT(JSON_KEYS(api_request.request_body,'$.contents'),'$[[0]]')),'application/x-www-form-urlencoded') AS content_type,
                                        api2.title,
                                        api2.api_group,
                                        api_group.api_group_name,
                                        api2.api_type,
                                        api2.description,
                                        api2.status,
                                        api2.latest_ref,
                                        api2.created_datetime,
                                        api2.last_modified_datetime
                                    FROM
                                        tbl_api api
                                        left join tbl_api api2 on api2.api_id = (
                                            case
                                                when api.latest_ref is not null or api.latest_ref != '' then api.latest_ref
                                                else api.api_id
                                            end
                                        )
                                        left join tbl_api_request api_request on api2.api_id = api_request.api_id
                                        left join tbl_api_group api_group on api2.api_group = api_group.api_group_code
                                    WHERE
                                        api_request.http_method != 'OLD_API'
                                    /~path: AND api_request.path like CONCAT('%',{path},'%')~/
                                    /~method: AND api_request.http_method = {method}~/
                                    /~name: AND api.name = {name}~/
                                    /~title: AND api.title like CONCAT('%',{title},'%')~/
                                    /~apiGroup: AND api.api_group = {apiGroup}~/
                                    /~apiType: AND api2.api_type = {apiType}~/
                                    /~status: AND (api2.status = {status} OR ('PUBLISHED'={status} AND api2.status = 'DOC_PUBLISHED') OR ('UNPUBLISHED'={status} AND api2.status = 'DOC_UNPUBLISHED'))~/
                                    /~apiGroupCodes:AND api.api_group in {apiGroupCodes}~/
                                    ORDER BY api2.last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="subRefList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        distinct api.api_id,
                                        api.version,
                                        api.name,
                                        api_request.path,
                                        api_request.http_method,
                                        api.title,
                                        api.api_group,
                                        api_group.api_group_name,
                                        api.api_type,
                                        api.description,
                                        api.status,
                                        api.latest_ref,
                                        api.created_datetime,
                                        api.last_modified_datetime
                                    FROM
                                        tbl_api api left join tbl_api_request api_request on api.api_id = api_request.api_id
                                        left join tbl_api_group api_group on api.api_group = api_group.api_group_code
                                    WHERE
                                        api.latest_ref = {apiId}
                                        and api_request.http_method != 'OLD_API'
                                    ORDER BY api.last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="apiDetail">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        api.api_id,
                                        api.version,
                                        api.name,
                                        api.title,
                                        api.api_group,
                                        api.api_type,
                                        api.description,
                                        api.status,
                                        api.created_datetime,
                                        api.last_modified_datetime
                                    FROM
                                        tbl_api api join tbl_api_request req on api.api_id = req.api_id
                                    WHERE
                                        1 = 1
                                        /~apiId: AND api.api_id = {apiId}~/
                                        /~apiUri: AND req.path = {apiUri}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="listPublishRecord">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    *
                                    FROM tbl_api_publish_record record
                                    WHERE 1 = 1
                                    /~apiGroup: AND api_group = {apiGroup}~/
                                    /~path: AND request_path like CONCAT('%',{path},'%')~/
                                    /~method: AND request_method = {method}~/
                                    /~opType: AND op_type = {opType}~/
                                    /~createdStartDate: AND created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND created_datetime <= {createdEndDate}~/
                                    ORDER BY created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="listPublishRecordForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    *
                                    FROM tbl_api_publish_record record
                                    WHERE 1 = 1
                                    /~apiGroupCodes:AND api_group in {apiGroupCodes}~/
                                    /~path: AND request_path like CONCAT('%',{path},'%')~/
                                    /~method: AND request_method = {method}~/
                                    /~opType: AND op_type = {opType}~/
                                    /~createdStartDate: AND created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND created_datetime <= {createdEndDate}~/
                                    ORDER BY created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="aclByApis">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select ar.api_id,'API' as relate_type from tbl_yop_api_define api
                                        inner join tbl_api_request ar on api.api_uri = ar.path
                                            and (ar.http_method = 'OLD_API' or api.is_migrated = true)
                                            and ar.api_id in {apis}
                                            and api.api_group in(select ag.api_group_code from tbl_api_group ag
                                                where ag.sp_code in(select ur.sp_code from tbl_acl_user_role ur
                                                    where ur.operator_code = {operator}))
                                    union
                                    select api.api_id,'API' as relate_type from tbl_api api
                                        inner join tbl_api_request ar on api.api_id = ar.api_id
                                        left join tbl_yop_api_define ad on ad.api_uri = ar.path AND ad.is_migrated = true
                                    where ad.api_uri is null
                                        and api.api_id in {apis}
                                        and api.api_group in(select ag.api_group_code from tbl_api_group ag
                                            where ag.sp_code in(select ur.sp_code from tbl_acl_user_role ur
                                                where ur.operator_code = {operator}))
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="relateApiInfo">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                select b.api_id, (case when b.api_group = 'OLD_API' then c.api_title else b.title end) as api_title
                                    from tbl_api_request a
                                             inner join tbl_api b on a.api_id = b.api_id
                                             left join tbl_yop_api_define c on a.path = c.api_uri
                                where b.api_id in {apis}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="getErrorCodeLocation">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                select error_code_location errorCodeLocation ,
                                message_location messageLocation,
                                request_success_value requestSuccessValue,
                                nonce
                                from tbl_api_extend_config
                                where api_id = {apiId}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="queryNoConfigErrorCodeLocationApi">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                select ta.api_id apiId, ta.name, api_request.path, ta.title
                                from tbl_api ta
                                         left join tbl_api_extend_config taec on ta.api_id = taec.api_id
                                         left join tbl_api_request api_request on ta.api_id = api_request.api_id
                                where ta.api_group = {apiGroup}
                                  and taec.error_code_location is null
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="getTitleByApiUri">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                select a.path, (case when b.api_group = 'OLD_API' then c.api_title else b.title end) as api_title
                                    from tbl_api_request a
                                             inner join tbl_api b on a.api_id = b.api_id
                                             left join tbl_yop_api_define c on a.path = c.api_uri
                                where a.path in {apiUris}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="modelQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <!-- 根据name和分组查询模型-->
                <entry key="findByApiGroupAndName">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT model.*,
                                           sensitive_variable.variables AS sensitive_variables
                                    FROM tbl_model model
                                             LEFT JOIN tbl_model_sensitive_variable sensitive_variable ON model.id = sensitive_variable.model_id
                                    WHERE model.api_group = {apiGroup}
                                    AND model.name = {name}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <!-- 根据api分组和model名称查询模型和subref -->
                <entry key="findByApiGroupAndNameWithRef">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT model.*,
                                           sensitive_variable.variables AS sensitive_variables,
                                           ref.ref_name
                                    FROM tbl_model model
                                             LEFT JOIN tbl_model_sensitive_variable sensitive_variable ON model.id = sensitive_variable.model_id
                                             LEFT JOIN tbl_model_ref ref ON model.api_group = ref.api_group AND model.name = ref.name
                                    WHERE model.api_group = {apiGroup}
                                    AND model.name = {name}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="existed">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        1=1
                                    FROM tbl_model
                                    WHERE name = {name}
                                    AND api_group = {apiGroup}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id, version, name, api_group, description, created_datetime, last_modified_datetime
                                    FROM
                                        tbl_model
                                    WHERE
                                        1=1
                                    /~name: AND name like CONCAT('%',{name},'%')~/
                                    /~apiGroup: AND api_group = {apiGroup}~/
                                    /~description: AND description like CONCAT('%',{description},'%')~/
                                    ORDER BY last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id, version, name, api_group, description, created_datetime, last_modified_datetime
                                    FROM
                                        tbl_model
                                    WHERE
                                        1=1
                                    /~name: AND name like CONCAT('%',{name},'%')~/
                                    /~apiGroup: AND api_group = {apiGroup}~/
                                    /~description: AND description like CONCAT('%',{description},'%')~/
                                    /~apiGroupCodes:AND api_group in {apiGroupCodes}~/
                                    ORDER BY last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findModelDetailById">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        *
                                    FROM
                                        tbl_model
                                    WHERE
                                        id = {id}
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findModelDetailByNameAndGroup">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        *
                                    FROM
                                        tbl_model
                                    WHERE
                                        name = {name}
                                        and api_group = {apiGroup}
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="changeRecordPageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    id, api_group, model_name, op_type, operator, cause,
                                    created_datetime
                                    FROM tbl_model_change_record
                                    WHERE 1 = 1
                                    /~name: AND model_name like CONCAT('%',{name},'%')~/
                                    /~opType: AND op_type = {opType}~/
                                    /~apiGroup: AND api_group = {apiGroup}~/
                                    /~operator: AND operator = {operator}~/
                                    /~operatedStartDate: AND created_datetime >= {operatedStartDate}~/
                                    /~operatedEndDate: AND created_datetime <= {operatedEndDate}~/
                                    ORDER BY created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="changeRecordPageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    id, api_group, model_name, op_type, operator, cause,
                                    created_datetime
                                    FROM tbl_model_change_record
                                    WHERE 1 = 1
                                    /~name: AND model_name like CONCAT('%',{name},'%')~/
                                    /~opType: AND op_type = {opType}~/
                                    /~apiGroup: AND api_group = {apiGroup}~/
                                    /~operator: AND operator = {operator}~/
                                    /~operatedStartDate: AND created_datetime >= {operatedStartDate}~/
                                    /~operatedEndDate: AND created_datetime <= {operatedEndDate}~/
                                    /~apiGroupCodes:AND api_group in {apiGroupCodes}~/
                                    ORDER BY  created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="spiQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>

                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id, version, name, status,title, api_group, spi_type, description, created_datetime, last_modified_datetime
                                    FROM
                                        tbl_spi
                                    WHERE
                                        1=1
                                    /~name: AND name like CONCAT('%',{name},'%')~/
                                    /~spiNames: AND name in {spiNames}~/
                                    /~title: AND title like CONCAT('%',{title},'%')~/
                                    /~status: AND status = {status}~/
                                    /~apiGroup: AND api_group = {apiGroup}~/
                                    /~spiType: AND spi_type = {spiType}~/
                                    ORDER BY last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id, version, name, status,title, api_group, spi_type, description, created_datetime, last_modified_datetime
                                    FROM
                                        tbl_spi
                                    WHERE
                                        1=1
                                    /~name: AND name like CONCAT('%',{name},'%')~/
                                    /~title: AND title like CONCAT('%',{title},'%')~/
                                    /~status: AND status = {status}~/
                                    /~apiGroup: AND api_group = {apiGroup}~/
                                    /~spiType: AND spi_type = {spiType}~/
                                    /~apiGroupCodes:AND api_group in {apiGroupCodes}~/
                                    ORDER BY last_modified_datetime DESC
							]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="changeRecordPageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    id, api_group, spi_name, op_type, operator, cause,
                                    created_datetime
                                    FROM tbl_spi_change_record
                                    WHERE 1 = 1
                                    /~name: AND spi_name like CONCAT('%',{name},'%')~/
                                    /~opType: AND op_type = {opType}~/
                                    /~apiGroup: AND api_group = {apiGroup}~/
                                    /~operator: AND operator = {operator}~/
                                    /~operatedStartDate: AND created_datetime >= {operatedStartDate}~/
                                    /~operatedEndDate: AND created_datetime <= {operatedEndDate}~/
                                    ORDER BY  created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="changeRecordPageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    id, api_group, spi_name, op_type, operator, cause,
                                    created_datetime
                                    FROM tbl_spi_change_record
                                    WHERE 1 = 1
                                    /~name: AND spi_name like CONCAT('%',{name},'%')~/
                                    /~opType: AND op_type = {opType}~/
                                    /~apiGroup: AND api_group = {apiGroup}~/
                                    /~operator: AND operator = {operator}~/
                                    /~operatedStartDate: AND created_datetime >= {operatedStartDate}~/
                                    /~operatedEndDate: AND created_datetime <= {operatedEndDate}~/
                                    /~apiGroupCodes:AND api_group in {apiGroupCodes}~/
                                    ORDER BY  created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="findSpiTitle">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT title
                                    FROM tbl_spi
                                    WHERE name = {spiName}
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="apiCallbackList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id, name, title, api_group, spi_type, description
                                    FROM
                                        tbl_spi
                                    WHERE
                                        spi_type = 'CALLBACK' AND
                                        name in (SELECT spi_name FROM tbl_api_callback WHERE api_id = {apiId})
                                    ORDER BY created_datetime DESC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="listApi">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                         api.api_id as api_id, title, path,api_define.api_title as old_api_title,api.api_group as api_group,api.last_modified_datetime,callback.spi_name
                                    FROM
                                        tbl_api api
                                        inner join tbl_api_callback callback on api.api_id = callback.api_id
                                        join tbl_api_request request on api.api_id = request.api_id
                                        left join tbl_yop_api_define api_define on request.path = api_define.api_uri
                                    WHERE spi_name = {spiName}
                                         /~apiName: AND (api.title like CONCAT('%',{apiName},'%') OR api_define.api_title like CONCAT('%',{apiName},'%'))~/
                                        /~apiUri: AND request.path = {apiUri}~/
                                        ORDER BY api.last_modified_datetime DESC
                                        LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="queryContactedApis">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT path
                                    FROM tbl_api_callback callback
                                    join tbl_api_request request on callback.api_id = request.api_id
                                    WHERE spi_name = {spiName}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="spiSubscribeQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        *
                                    FROM tbl_isv_notify_subscription
                                    WHERE spi_name = {spiName}
                                    /~appId: AND app_id = {appId}~/
                                    /~customerNo: AND customer_no = {customerNo}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="exist">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT *
                                    FROM tbl_isv_notify_subscription
                                    WHERE app_id = {appId}
                                    AND spi_name = {spiName}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="notifySendRecordQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="notifierDataSource"/>
        <property name="querys">
            <map>
                <!-- 查询全量发送记录-->
                <entry key="listSendRecord">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        tnsr.id,
                                        tnsr.order_datetime,
                                        tnsr.status,
                                        tnsr.error_code,
                                        tnsr.error_msg,
                                        tnsr.notification_id,
                                        tnsr.real_notify_merchant_no,
                                        tnsr.real_app_id,
                                        tnsr.real_url,
                                        tnsr.spi_name,
                                        tnsr.real_notify_rule,
                                        tnsr.send_datetime
                                    FROM
                                        tbl_notify_send_record AS tnsr
                                    WHERE
                                        tnsr.order_datetime >= {notifyStartDate}
                                        AND tnsr.order_datetime <= {notifyEndDate}
                                        /~appId: AND tnsr.real_app_id = {appId}~/
                                        /~customerNo: AND tnsr.real_notify_merchant_no = {customerNo}~/
                                        /~notificationId: AND tnsr.notification_id = {notificationId}~/
                                        /~spiName: AND tnsr.spi_name = {spiName}~/
                                        /~notifyRule: AND tnsr.real_notify_rule = {notifyRule}~/
                                        /~url: AND tnsr.real_url = {url}~/
                                        /~errorCode: AND tnsr.error_code = {errorCode}~/
                                        /~status: AND tnsr.status = {status}~/
                                    ORDER BY tnsr.order_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 查询发送记录失败总次数和总次数 -->
                <entry key="querySendTimes">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                SELECT
                                    COUNT(*) AS times
                                FROM
                                    tbl_notify_record AS tnr
                                    INNER JOIN
                                    tbl_notify_send_record AS tnsr
                                    ON
                                        tnr.id = tnsr.notify_record_id
                                    and
                                        tnr.order_datetime = tnsr.order_datetime
                                WHERE
                                    tnsr.notify_record_id IN (select id from tbl_notify_record where order_id = {orderId})
                                    /~status: AND tnsr.status = {status}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 查询处理的发送记录-->
                <entry key="listNotifySendRecord">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        *
                                    FROM
                                        tbl_notify_send_record
                                    WHERE
                                        notify_record_id = {notifyRecordId}
                                        AND order_datetime = {orderDate}
                                    ORDER BY
                                        send_datetime DESC
                                    /~_maxSize: LIMIT {_maxSize} OFFSET {_startIndex}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="notifyOrderQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="notifierDataSource"/>
        <property name="querys">
            <map>
                <!-- 查询通知订单-->
                <entry key="listNotifyOrder">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    tno.order_id, tno.customer_no,tno.notify_merchant_no,tno.notification_id, tno.app_id,
                                    tno.url, tno.spi_name,tno.status, tno.order_datetime, tno.error_code,
                                    tno.error_msg, tno.real_notify_merchant_no, tno.real_notify_rule,guid
                                    FROM tbl_notify_order tno
                                    WHERE
                                    tno.order_datetime >= {notifyStartDate}
                                    AND tno.order_datetime <= {notifyEndDate}
                                    /~appId: AND tno.app_id = {appId}~/
                                    /~guid: AND tno.guid = {guid}~/
                                    /~customerNo: AND tno.real_notify_merchant_no={customerNo}~/
                                    /~notificationId: AND tno.notification_id = {notificationId}~/
                                    /~spiName: AND tno.spi_name = {spiName}~/
                                    /~notifyRule: AND tno.real_notify_rule = {notifyRule}~/
                                    /~url: AND tno.url = {url}~/
                                    /~errorCode: AND tno.error_code = {errorCode}~/
                                    /~status: AND (tno.status = {status} OR ('SENDING' = {status} AND tno.status = 'PENDING'))~/
                                    ORDER BY tno.order_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 查询通知订单记录-->
                <entry key="findNotifyOrder">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    customer_no,order_id, notification_id,notify_rule, app_id, url, spi_name,
                                    status, order_datetime
                                    FROM tbl_notify_order
                                    WHERE
                                    order_id = {orderId}
                                    AND order_datetime = {orderDate}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 查询最新处理报文-->
                <entry key="findRecordContent">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT record.content as content,
                                    tno.content as plaintext
                                    FROM tbl_notify_order tno
                                    left join tbl_notify_record record
                                    on tno.order_id=record.order_id and tno.order_datetime=record.order_datetime
                                    WHERE tno.order_id = {orderId}
                                    AND tno.order_datetime = {orderDate}
                                    order by record.created_datetime desc limit 1
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 查询发送记录-->
                <entry key="listNotifyRecord">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    id, notification_id, url, status,error_code,error_msg,send_datetime,order_datetime,content,backend, operator, oper_cause, oper_source
                                    FROM tbl_notify_record
                                    WHERE
                                    order_id = {orderId}
                                    AND order_datetime = {orderDate}
                                    /~status:AND status = {status}~/
                                    ORDER BY send_datetime DESC
                                    /~_maxSize: LIMIT {_maxSize} OFFSET {_startIndex}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <!-- 查询最近一次发送结果-->
                <entry key="listLatestSendResult">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    status, error_msg, created_datetime, send_datetime
                                    FROM tbl_notify_send_record
                                    WHERE
                                    notify_record_id = {notifyRecordId}
                                    /~status:AND status = {status}~/
                                    order by send_datetime desc limit 1
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="productQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        product.product_code, product.product_name, product.product_type, product.sp_code,
                                        isp.sp_name, product.status, product.created_datetime, product.last_modified_datetime
                                    FROM tbl_product_marketing product left join tbl_isp_info isp on product.sp_code = isp.sp_code
                                    WHERE
                                    1 = 1
                                    /~code: AND product.product_code = {code}~/
                                    /~name: AND product.product_name like CONCAT('%',{name},'%')~/
                                    /~type: AND product.product_type = {type}~/
                                    /~status: AND product.status = {status}~/
                                    /~spCode: AND product.sp_code = {spCode}~/
                                    /~spCodes: AND product.sp_code in {spCodes}~/
                                    /~createdStartDate: AND product.created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND product.created_datetime <= {createdEndDate}~/
                                    ORDER BY product.id DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageListByApiUri">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT distinct product.product_code, product.product_name, product.product_type, product.sp_code, isp.sp_name, product.status, product.created_datetime, product.last_modified_datetime, t.api_uri
                                    from tbl_product_marketing product
                                    join tbl_isp_info isp on product.sp_code = isp.sp_code
                                    join (
                                        SELECT pa.product_code, a.path AS api_uri
                                        FROM tbl_product_api pa
                                        join tbl_api_request a on pa.value = a.api_id
                                        UNION
                                        SELECT pa.product_code, b.path AS api_uri
                                        FROM tbl_product_api pa
                                        join tbl_api_request b on pa.value = b.path
                                        UNION
                                        SELECT pa.product_code, e.path AS api_uri
                                        FROM tbl_product_api pa
                                        join tbl_api c on pa.value = c.api_group
                                        join tbl_api_request e on c.api_id=e.api_id
                                        UNION
                                        SELECT pa.product_code, d.api_uri AS api_uri
                                        FROM tbl_product_api pa
                                        join tbl_yop_api_define d on pa.value = d.api_group
                                    ) t on t.product_code = product.product_code
                                    WHERE
                                        1 = 1
                                    /~code: AND product.product_code = {code}~/
                                    /~name: AND product.product_name like CONCAT('%',{name},'%')~/
                                    /~type: AND product.product_type = {type}~/
                                    /~status: AND product.status = {status}~/
                                    /~spCode: AND product.sp_code = {spCode}~/
                                    /~spCodes: AND product.sp_code in {spCodes}~/
                                    /~createdStartDate: AND product.created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND product.created_datetime <= {createdEndDate}~/
                                    /~apiUri: AND t.api_uri = {apiUri}~/
                                    ORDER BY product.created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="simplelist">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        product.product_code, product.product_name,product.product_type,product.sp_code,product.product_desc
                                    FROM tbl_product_marketing product
                                    WHERE
                                    1 = 1
                                    /~includes: AND product.product_code IN {includes}~/
                                    /~excludes: AND product.product_code NOT IN {excludes}~/
                                    /~status: AND product.status = {status}~/
                                    /~spCodes: AND product.sp_code in {spCodes}~/
                                    ORDER BY product.created_datetime DESC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="productApiQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageListApi">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        product_api.value AS value
                                    FROM tbl_product_api product_api
                                    LEFT JOIN tbl_product_api_tag tag
                                    ON product_api.id = tag.product_api_id
                                    WHERE
                                    1 = 1
                                    /~productCode: AND product_api.product_code = {productCode}~/
                                    /~type: AND product_api.type = {type}~/
                                    /~value: AND product_api.value = {value}~/
                                    /~sceneId: AND tag.type = 'SCENE' AND tag.value = {sceneId}~/
                                    ORDER BY product_api.created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageListNewApi">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        product_api.value AS value, api_request.http_method AS method,
                                        api_request.path AS path, api.status AS status, api.title AS name
                                    FROM tbl_product_api product_api
                                    LEFT JOIN tbl_api api
                                    ON product_api.value = api.api_id
                                    LEFT JOIN tbl_api_request api_request
                                    ON product_api.value = api_request.api_id
                                    WHERE
                                    1 = 1
                                    /~productCode: AND product_api.product_code = {productCode}~/
                                    /~type: AND product_api.type = {type}~/
                                    /~value: AND api_request.path = {value}~/
                                    ORDER BY product_api.created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageListApiGroup">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        product_api.value AS value, api_group.api_group_name AS name
                                    FROM tbl_product_api product_api
                                    LEFT JOIN tbl_product_api_tag tag
                                    ON product_api.id = tag.product_api_id
                                    LEFT JOIN tbl_api_group api_group
                                    ON product_api.value = api_group.api_group_code
                                    WHERE
                                    1 = 1
                                    /~productCode: AND product_api.product_code = {productCode}~/
                                    /~type: AND product_api.type = {type}~/
                                    /~value: AND product_api.value = {value}~/
                                    /~sceneId: AND tag.type = 'SCENE' AND tag.value = {sceneId}~/
                                    ORDER BY product_api.created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="listAllByProductCode">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        product_api.value AS value, product_api.type
                                    FROM tbl_product_api product_api
                                    WHERE product_api.product_code = {productCode}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="sceneQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="list">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        ps.*
                                    FROM tbl_product_scene ps
                                        LEFT JOIN tbl_product_marketing pm ON pm.product_code = ps.product_code
                                    WHERE
                                    1 = 1
                                    /~productCode: AND ps.product_code = {productCode}~/
                                    /~ids: AND ps.id IN {ids}~/
                                    /~spCodes: AND pm.sp_code IN {spCodes}~/
                                    ORDER BY seq ASC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="productAuthzQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        authz.id, authz.customer_no, authz.app_id, app.app_name, authz.product_code,
                                        product.product_name, authz.effective_datetime, authz.overdue_datetime,
                                        authz.status, authz.created_datetime, authz.last_modified_datetime,
                                        product.product_type
                                    FROM tbl_product_authz authz
                                    LEFT JOIN tbl_isv_app app
                                    ON authz.app_id = app.app_id
                                    LEFT JOIN tbl_product_marketing product
                                    ON authz.product_code = product.product_code
                                    WHERE
                                    1 = 1
                                    /~customerNo: AND authz.customer_no = {customerNo}~/
                                    /~appId: AND authz.app_id = {appId}~/
                                    /~productCode: AND authz.product_code = {productCode}~/
                                    /~spCodes: AND product.sp_code in {spCodes}~/
                                    /~status: AND authz.status = {status}~/
                                    /~overdueDateStart: AND authz.overdue_datetime >= {overdueDateStart}~/
                                    /~overdueDate: AND authz.overdue_datetime <= {overdueDate}~/
                                    /~type: AND product.product_type = {type}~/
                                    ORDER BY authz.last_modified_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <!--  产品变更记录  -->
    <bean id="productChangesQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        pm.product_name,pc.product_code,pc.operator,pc.oper_type,pc.oper_cause,pc.oper_detail,pc.created_datetime
                                    FROM tbl_product_changes pc left join tbl_product_marketing pm on pc.product_code = pm.product_code
                                    WHERE
                                    1 = 1
                                    /~productCode: AND pc.product_code = {productCode}~/
                                    /~type: AND pc.oper_type = {type}~/
                                    /~operator: AND pc.operator = {operator}~/
                                    /~createdStartDate: AND pc.created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND pc.created_datetime <= {createdEndDate}~/
                                    ORDER BY created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <!--  产品授权变更记录  -->
    <bean id="productAuthzChangesQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT pac.customer_no,pac.app_id,pac.operator,pac.oper_type,pac.oper_cause,pac.oper_detail,pac.created_datetime,
                                        ia.app_name

                                    FROM tbl_product_authz_changes pac
                                        left join tbl_isv_app ia on pac.app_id = ia.app_id
                                    WHERE
                                    1 = 1
                                    /~appId: AND pac.app_id = {appId}~/
                                    /~customerNo: AND pac.customer_no = {customerNo}~/
                                    /~type: AND pac.oper_type = {type}~/
                                    /~operator: AND pac.operator = {operator}~/
                                    /~createdStartDate: AND pac.created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND pac.created_datetime <= {createdEndDate}~/
                                    ORDER BY created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="customerQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        customer_no, customer_name, provider_code, customer_type, status, created_datetime,
                                        last_modified_datetime
                                    FROM tbl_isv_customer
                                    WHERE 1 = 1
                                    /~customerNo: AND customer_no = {customerNo}~/
                                    /~name: AND customer_name = {name}~/
                                    /~providerCode: AND provider_code = {providerCode}~/
                                    /~type: AND customer_type = {type}~/
                                    /~status: AND status = {status}~/
                                    ORDER BY last_modified_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        customer_no, customer_name, provider_code, customer_type, status, created_datetime,
                                        last_modified_datetime
                                    FROM tbl_isv_customer
                                    WHERE 1 = 1
                                    /~customerNo: AND customer_no = {customerNo}~/
                                    /~name: AND customer_name = {name}~/
                                    /~providerCode: AND provider_code = {providerCode}~/
                                    /~providerCodes: AND provider_code in {providerCodes}~/
                                    /~type: AND customer_type = {type}~/
                                    /~status: AND status = {status}~/
                                    ORDER BY last_modified_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findByCustomerNo">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        *
                                    FROM tbl_isv_customer
                                    WHERE 1 = 1
                                    /~customerNo: AND customer_no = {customerNo}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="customerChangeQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        tics.customer_no,
                                        tics.created_datetime,
                                        tics.oper_type,
                                        tics.operator,
                                        tics.oper_cause,
                                        tics.oper_detail,
                                        tic.provider_code
                                    FROM
                                        tbl_isv_customer AS tic
                                        JOIN tbl_isv_customer_changes AS tics ON tic.customer_no = tics.customer_no
                                    WHERE 1 = 1
                                    /~customerNo: AND tics.customer_no = {customerNo}~/
                                    /~type: AND tics.oper_type = {type}~/
                                    /~operator: AND tics.operator = {operator}~/
                                    /~createdStartDate: AND tics.created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND tics.created_datetime <= {createdEndDate}~/
                                    ORDER BY tics.created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageListForSp">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        tics.customer_no,
                                        tics.created_datetime,
                                        tics.oper_type,
                                        tics.operator,
                                        tics.oper_cause,
                                        tics.oper_detail,
                                        tic.provider_code
                                    FROM
                                        tbl_isv_customer AS tic
                                        JOIN tbl_isv_customer_changes AS tics ON tic.customer_no = tics.customer_no
                                    WHERE 1 = 1
                                    /~customerNo: AND tics.customer_no = {customerNo}~/
                                    /~type: AND tics.oper_type = {type}~/
                                    /~operator: AND tics.operator = {operator}~/
                                    /~providerCodes: AND tic.provider_code in {providerCodes}~/
                                    /~createdStartDate: AND tics.created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND tics.created_datetime <= {createdEndDate}~/
                                    ORDER BY tics.created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="operatorQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        operator.id, operator.customer_no, customer.customer_name, email_auth.identifier as email,
                                        email_auth.status as email_status, mobile_auth.identifier as mobile,
                                        mobile_auth.status as mobile_status, operator.status, operator.created_datetime,
                                        operator.last_modified_datetime
                                    FROM
                                        tbl_isv_operator operator
                                    JOIN
                                        tbl_isv_customer customer
                                    ON
                                        operator.customer_no = customer.customer_no
                                    LEFT JOIN
                                        tbl_isv_oper_auths email_auth
                                    ON
                                        operator.id = email_auth.oper_id
                                    AND
                                        email_auth.identity_type = 'EMAIL'
                                    LEFT JOIN
                                        tbl_isv_oper_auths mobile_auth
                                    ON
                                        operator.id = mobile_auth.oper_id
                                    AND
                                        mobile_auth.identity_type = 'MOBILE'
                                    WHERE
                                        1=1
                                    /~customerNo: AND operator.customer_no = {customerNo}~/
                                    /~email: AND email_auth.identifier = {email}~/
                                    /~mobile: AND mobile_auth.identifier = {mobile}~/
                                    /~status: AND operator.status = {status}~/
                                    ORDER BY
                                        operator.last_modified_datetime desc
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="docQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopDocMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        d.id, d.doc_no, d.title, d.type, d.status, d.visible, d.`desc`, d.logo, d.doc_version, d.display, d.created_datetime, d.last_modified_datetime, d.`version`,
                                        (select group_concat(dp.product_code) from tbl_doc_product_relate dp where dp.doc_no = d.doc_no) as product_codes,
                                        (select group_concat(ds.sp_code) from tbl_doc_sp_relate ds where ds.doc_no = d.doc_no) as sp_codes
                                    FROM
                                        tbl_doc d
                                    WHERE
                                        1=1
                                    /~title: AND d.title LIKE CONCAT('%',{title,string},'%')~/
                                    /~docNo: AND d.doc_no = {docNo}~/
                                    /~productCode: AND exists(select 1 from tbl_doc_product_relate dp where dp.product_code ={productCode} and dp.doc_no = d.doc_no)~/
                                    /~docNos: AND d.doc_no IN {docNos}~/
                                    /~spCode: AND exists(select 1 from tbl_doc_sp_relate ds where ds.doc_no = d.doc_no and ds.sp_code={spCode})~/
                                    /~visible: AND d.visible = {visible}~/
                                    /~status: AND d.status = {status}~/
                                    ORDER BY d.last_modified_datetime desc
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findDocsByProductCodes">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT distinct doc_no from tbl_doc_product_relate WHERE 1 = 1
                                    /~productCodes:AND product_code in {productCodes}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findDocsBySpCodes">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT distinct doc_no from tbl_doc_sp_relate WHERE 1 = 1
                                    /~spCodes:AND sp_code in {spCodes}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="listForArrange">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        dc.id, dc.seq, d.doc_no, d.title, d.status
                                    FROM
                                        tbl_doc_category_relate dc inner join tbl_doc d on d.doc_no = dc.doc_no
                                    WHERE d.status in ('PUBLISHED','EDIT') and d.display = 1 and d.visible = 'PUBLIC' and d.type = 'PRODUCT'
                                        /~docCategoryId: AND dc.category_id = {docCategoryId}~/
                                    ORDER BY
                                        dc.seq asc
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="historyPageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        h.id, h.doc_no, h.page_no, h.page_id, h.page_pid, h.title, h.oper_type, h.operator, h.version, h.created_datetime,
                                        d.title AS doc_title
                                    FROM
                                        tbl_doc_page_history h
                                        INNER JOIN tbl_doc d ON h.doc_no = d.doc_no
                                    WHERE
                                        1=1
                                    /~docNo: AND h.doc_no = {docNo}~/
                                    /~title: AND h.title LIKE CONCAT('%',{title,string},'%')~/
                                    /~operator: AND h.operator = {operator}~/
                                    /~createdDateStart: AND h.created_datetime >= {createdDateStart}~/
                                    /~createdDateEnd: AND h.created_datetime <= {createdDateEnd}~/
                                    /~docNos: AND h.doc_no IN {docNos}~/
                                    ORDER BY
                                        h.created_datetime desc
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="publishPageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id, doc_no, doc_title, doc_version, oper_type, operator, created_datetime
                                    FROM
                                        tbl_doc_publish_record
                                    WHERE
                                        1=1
                                    /~docNo: AND doc_no = {docNo}~/
                                    /~docNos: AND doc_no IN {docNos}~/
                                    /~operator: AND operator = {operator}~/
                                    /~operType: AND oper_type = {operType}~/
                                    /~createdDateStart: AND created_datetime >= {createdDateStart}~/
                                    /~createdDateEnd: AND created_datetime <= {createdDateEnd}~/
                                    ORDER BY
                                        created_datetime desc
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="publishPageOneById">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id, doc_no, doc_title, doc_version, oper_type, operator, created_datetime
                                    FROM
                                        tbl_doc_publish_record
                                    WHERE
                                        id = {id}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageListForRef">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        a.doc_no, d.title as doc_title, a.id, a.title, CONCAT(IFNULL(c.page_no, ''), '/', IFNULL(b.page_no, ''), '/', a.page_no) as page_path
                                    FROM
                                        tbl_doc_page a
                                        INNER JOIN tbl_doc d on a.doc_no = d.doc_no
                                        INNER JOIN tbl_doc_page_content ac on a.id = ac.page_id
                                        LEFT JOIN tbl_doc_page b on a.pid = b.id
                                        LEFT JOIN tbl_doc_page c on b.pid = c.id
                                    WHERE
                                        a.page_visible = 'PUBLIC' AND a.page_type = 'WIKI' AND a.ref_id is null AND a.ref_url is null
                                        AND d.visible = 'PUBLIC' AND d.status IN ('PUBLISHED','EDIT')
                                    /~docNos: AND a.doc_no IN {docNos}~/
                                    /~keywords: AND a.title LIKE CONCAT('%',{keywords,string},'%')~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="getPageWithPath">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        a.doc_no, a.id, a.title, CONCAT(IFNULL(c.page_no, ''), '/', IFNULL(b.page_no, ''), '/', a.page_no) as page_path
                                    FROM
                                        tbl_doc_page a left join tbl_doc_page b on a.pid = b.id left join tbl_doc_page c on b.pid = c.id
                                    WHERE
                                        a.id = {pageId}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="checkIfExists">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT id from tbl_doc where doc_no = {docNo}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findCategoryByParentCode">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT a.*
                                    from tbl_doc_category a
                                    inner join tbl_doc_category b on a.pid = b.id
                                    where 1=1
                                    /~code: AND a.code = {code}~/
                                    /~parentCodes: AND b.code in {parentCodes}~/
                                    /~scope: AND a.type = {type}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="listDocCategories">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT id,pid,code,name from tbl_doc_category where 1=1
                                    /~pid: AND pid = {pid}~/
                                    /~type: AND type = {type}~/
                                    /~scope: AND scope = {scope}~/
                                    order by seq asc
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="listSolutionDocs">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT doc_no from tbl_doc where type = 'SOLUTION'
                                    /~docNos: AND doc_no IN {docNos}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="docFaqQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopDocMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="listForRelate">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select dp.doc_no, doc.title as doc_title, dp.id as page_id, dp.page_no, dp.title as page_title, dp.page_type
                                    from tbl_doc_page dp
                                             inner join tbl_doc doc on dp.doc_no = doc.doc_no
                                    where dp.title LIKE CONCAT('%',{keywords,string},'%') and dp.page_control < 1024
                                        and doc.status in ('PUBLISHED','EDIT') and doc.visible = 'PUBLIC'
                                        /~queryLimit: limit {queryLimit}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select a.id
                                         , a.title
                                         , a.status
                                         , a.score
                                         , a.top
                                         , a.version
                                         , a.created_date
                                         , a.last_modified_date
                                         , (select group_concat(concat(b.relate_type, {relateSeperator}, b.relate_id))
                                            from tbl_doc_faq_relate b
                                            where b.faq_id = a.id) as relates
                                    from tbl_doc_faq a
                                    where 1 = 1
                                        /~faqIds: and a.id IN {faqIds}~/
                                        /~title: and a.title LIKE CONCAT('%',{title,string},'%')~/
                                        /~status: and a.status={status}~/
                                        /~top!=null: and a.top={top}~/
                                        /~createdDateStart: and a.created_date >= {createdDateStart}~/
                                        /~createdDateEnd: and a.created_date <= {createdDateEnd}~/
                                        order by a.last_modified_date desc
                                        LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="relatedList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select a.id
                                         , a.faq_id
                                         , a.relate_type
                                         , a.relate_id
                                    from tbl_doc_faq_relate a
                                    where 1 = 1
                                    /~faqId: and a.faq_id = {faqId}~/
                                    /~relateIds: and a.relate_id IN {relateIds}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="faqDetail">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select a.id
                                         , a.title
                                         , a.answer
                                         , a.status
                                         , a.score
                                         , a.top
                                         , a.version
                                         , a.created_date
                                         , a.last_modified_date
                                    from tbl_doc_faq a
                                    where a.id = {id}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="aclByPages">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select cast(p.id as CHAR), 'PAGE' as relate_type from tbl_doc_page p
                                    where p.id in {pages}
                                        and p.doc_no in {docNos}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="relatePageInfo">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select a.id as page_id, concat(b.title, '-', a.title) as page_title
                                    from tbl_doc_page a
                                             inner join tbl_doc b on a.doc_no = b.doc_no
                                    where a.id in {pages}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="limitRuleQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                    limit_rule.id, limit_rule.customer_no, limit_rule.app_id,
                                    IF(api_request.http_method = "OLD_API", NULL, api_request.http_method) request_method,
                                    api_request.path request_path, limit_rule.status, limit_rule.version, limit_rule.created_datetime,
                                    limit_rule.last_modified_datetime
                                    FROM tbl_limit_rule limit_rule
                                    LEFT JOIN tbl_api_request api_request
                                    ON limit_rule.api_id = api_request.api_id
                                    WHERE
                                    limit_rule.type = {type}
                                    /~customerNo: AND limit_rule.customer_no = {customerNo}~/
                                    /~appId: AND limit_rule.app_id = {appId}~/
                                    /~requestPath: AND api_request.path = {requestPath}~/
                                    /~status: AND limit_rule.status = {status}~/
                                    /~createdStartDate: AND limit_rule.created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND limit_rule.created_datetime <= {createdEndDate}~/
                                    ORDER BY
                                    limit_rule.last_modified_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="apiErrcodeQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="checkRelatedApis">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        ar.path
                                    FROM tbl_api_errcode ae
                                        INNER JOIN tbl_api_request ar ON ae.api_id = ar.api_id
                                    WHERE ae.errcode_id = {errcodeId}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findRelatedErrcodePage">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        ae.id,ae.api_id,ae.errcode_id,ec.error_code,ec.sub_error_code,
                                        ec.sub_error_msg,ec.api_group_code
                                    FROM tbl_api_errcode ae
                                        INNER JOIN tbl_error_code ec ON ae.errcode_id = ec.id
                                    WHERE ae.api_id = {apiId}
                                    /~subErrorCode: AND ec.sub_error_code like concat('%',{subErrorCode},'%')~/
                                    ORDER BY ec.sub_error_code
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findUnRelatedErrcodePage">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        ec.id,ec.error_code,ec.sub_error_code,ec.sub_error_msg,ec.api_group_code
                                    FROM tbl_error_code ec
                                    WHERE ec.api_group_code = {apiGroupCode}
                                        /~subErrorCode: AND ec.sub_error_code = {subErrorCode}~/
                                    AND ec.`type` = 'GROUP_COMMON'
                                    AND NOT EXISTS (SELECT ae.api_id from tbl_api_errcode ae WHERE ec.id = ae.errcode_id AND ae.api_id = {apiId})
                                    ORDER BY ec.sub_error_code
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="apiRequestQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <!--根据path，查API-->
                <entry key="findApiIdByPath">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        api_id
                                    FROM tbl_api_request
                                    WHERE path = {path}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findApiIdsForOldApi">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        request.api_id,api.api_uri
                                    FROM tbl_yop_api_define api
                                        INNER JOIN tbl_api_request request
                                            ON api.api_uri = request.path AND (request.http_method = 'OLD_API' OR API.IS_MIGRATED = true)
                                    WHERE api.api_uri IN {apiUris}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findApiIdForNewApi">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        api_id
                                    FROM tbl_api_request
                                    WHERE http_method = {httpMethod} AND path = {path}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="findRelatedApisPage">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        ae.id,ae.api_id,ar.path,ar.http_method
                                    FROM tbl_api_errcode ae
                                        inner join tbl_api_request ar on ae.api_id = ar.api_id
                                    WHERE ae.errcode_id = {errcodeId}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>


    <bean id="apiRouteQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="queryForApiList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id,type,service_name,status,name
                                    FROM tbl_api_route
                                    WHERE api_id = {apiId}
                                    order by `order`
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="pageQueryForDeploy">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        id,op_type,operator,cause,created_datetime
                                    FROM tbl_api_route_deploy_record
                                    WHERE api_id = {apiId}
                                    /~opType: AND op_type = {opType}~/
                                    /~operator: AND operator = {operator}~/
                                    /~createdStartDate: AND created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND created_datetime <= {createdEndDate}~/
                                    ORDER BY
                                    created_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="queryDeployStatus">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select
                                        route.api_id,
                                        not isnull(deploy_count) as has_deploy,
                                        ifnull(last_modified_datetime > last_deploy_datetime or route_count != deploy_count, false) as has_update
                                    from
                                        (
                                        select
                                            api_id,
                                            count(*) as route_count,
                                            max(last_modified_datetime) as last_modified_datetime
                                        from
                                            tbl_api_route
                                        where
                                            api_id in {apiIds}
                                        group by
                                            api_id) route
                                    left join (
                                        select
                                            api_id,
                                            JSON_LENGTH(content) as deploy_count,
                                            created_datetime as last_deploy_datetime
                                        from
                                            tbl_api_route_deploy_record route_record
                                        join tbl_api_route_deploy_record_content route_record_content
                                        on
                                            route_record.content_id = route_record_content.id
                                        where
                                            route_record.id in (
                                            select
                                                max(id)
                                            from
                                                tbl_api_route_deploy_record
                                            where
                                                api_id in {apiIds}
                                            group by
                                                api_id)) route_deploy
                                    on
                                        route.api_id = route_deploy.api_id
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>


    <bean id="v1ApiQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="findApisPage">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT request.api_id,
                                           'V1'                       as api_version,
                                           API.API_URI                as api_uri,
                                           API.API_TITLE              as api_title,
                                           API.API_TYPE               as api_type,
                                           API.STATUS                 as api_status,
                                           API.API_GROUP              as api_group_code,
                                           API_GROUP.API_GROUP_NAME   as api_group_name,
                                           API.CREATED_DATETIME       as created_datetime,
                                           API.LAST_MODIFIED_DATETIME as last_modified_datetime
                                    FROM TBL_YOP_API_DEFINE API
                                             INNER JOIN tbl_api_request request
                                                        ON API.API_URI = request.path
                                                        AND (request.http_method = 'OLD_API' OR API.IS_MIGRATED = true)
                                             LEFT JOIN TBL_API_GROUP API_GROUP ON API.API_GROUP = API_GROUP.API_GROUP_CODE
                                    WHERE 1=1
                                    /~apiUri: AND API.API_URI = {apiUri}~/
                                    /~apiGroupCode: AND API.API_GROUP = {apiGroupCode}~/
                                    /~apiType: AND API.API_TYPE = {apiType}~/
                                    /~apiStatus: AND API.STATUS = {apiStatus}~/
                                    /~apiTitle: AND API.API_TITLE like concat('%',{apiTitle},'%')~/
                                    /~joinProduct: AND NOT EXISTS(SELECT id FROM tbl_product_api pa WHERE pa.product_code = {productCode} AND (pa.value = api.api_group OR pa.value = request.api_id))~/
                                    /~joinErrcode: AND NOT EXISTS(SELECT id FROM tbl_api_errcode ae WHERE ae.api_id = request.api_id AND ae.errcode_id = {errcodeId})~/
                                    /~joinSpiName: AND NOT EXISTS(SELECT id FROM tbl_api_callback ac WHERE ac.api_id = request.api_id AND ac.spi_name = {spiName})~/
							    ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="v2ApiQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="findApisPage">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        'V2' as api_version,
                                        api.api_id,
                                        api.api_type,
                                        api.title as api_title,
                                        api.api_group as api_group_code,
                                        ag.api_group_name,
                                        api.status as api_status,
                                        ar.path as api_uri,
                                        api.created_datetime,
                                        api.last_modified_datetime
                                    FROM tbl_api api
                                        INNER JOIN tbl_api_request ar ON api.api_id = ar.api_id AND ar.http_method != 'OLD_API'
                                        INNER JOIN tbl_api_group ag ON api.api_group = ag.api_group_code
                                    WHERE 1 = 1
                                    /~apiUri: AND ar.path = {apiUri}~/
                                    /~apiGroupCode: AND api.api_group = {apiGroupCode}~/
                                    /~apiType: AND api.api_type = {apiType}~/
                                    /~apiStatus: AND api.status = {apiStatus}~/
                                    /~apiTitle: AND api.title like CONCAT('%',{apiTitle,string},'%')~/
                                    /~joinProduct: AND NOT EXISTS(SELECT id FROM tbl_product_api pa WHERE pa.product_code = {productCode} AND (pa.value = api.api_group OR pa.value = api.api_id))~/
                                    /~joinErrcode: AND NOT EXISTS(SELECT id FROM tbl_api_errcode ae WHERE ae.api_id = api.api_id AND ae.errcode_id = {errcodeId})~/
                                    /~joinSpiName: AND NOT EXISTS(SELECT id FROM tbl_api_callback ac WHERE ac.api_id = api.api_id AND ac.spi_name = {spiName})~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="apiRelationQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="listRelateId">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT relate_id
                                    FROM tbl_api_relation
                                    WHERE api_id={apiId}
                                    AND relate_type={relateType}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="list">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT id,api_id,relate_type,relate_id
                                    FROM tbl_api_relation
                                    WHERE api_id={apiId}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>

                <entry key="findById">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT id,api_id,relate_type,relate_id,relate_config,version
                                    FROM tbl_api_relation
                                    WHERE id={id}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="customSolutionQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopMysqlDataSource"/>
        <property name="querys">
            <map>
                <entry key="pageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        cs.id,cs.solution_code,cs.solution_name,cs.type,cs.status,cs.description,cs.operator_code,cs.sp_code,cs.version,cs.created_datetime,cs.last_modified_datetime,
                                        isp.sp_name
                                    FROM tbl_custom_solution cs left join tbl_isp_info isp on cs.sp_code = isp.sp_code
                                    WHERE
                                    1 = 1
                                    /~solutionName: AND cs.solution_name like CONCAT('%',{solutionName},'%')~/
                                    /~solutionCode: AND cs.solution_code like CONCAT('%',{solutionCode},'%')~/
                                    /~operatorCode: AND cs.operator_code = {operatorCode}~/
                                    /~status: AND product.status = {status}~/
                                    /~spCodes: AND cs.sp_code in {spCodes}~/
                                    /~createdStartDate: AND cs.created_datetime >= {createdStartDate}~/
                                    /~createdEndDate: AND cs.created_datetime <= {createdEndDate}~/
                                    ORDER BY cs.created_datetime DESC, cs.last_modified_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="relatedApiPageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    select csi.id,
                                           csi.solution_code,
                                           csi.api_id,
                                           csi.required,
                                           t.api_title,
                                           t.api_uri
                                    FROM tbl_custom_solution_api csi
                                             inner join
                                         (
                                             select b.api_id,
                                                    a.api_title,
                                                    a.api_uri
                                             from tbl_yop_api_define a
                                                      inner join tbl_api_request b on b.path = a.api_uri
                                             union
                                             select a.api_id,
                                                    a.title as api_title,
                                                    b.path  as api_uri
                                             from tbl_api a
                                                      inner join tbl_api_request b on a.api_id = b.api_id
                                                      left join tbl_yop_api_define c on b.path = c.api_uri and c.status = 'ACTIVE' and c.is_migrated = true
                                             where a.api_group != 'OLD_API'
                                               and c.api_uri is null
                                         ) t on csi.api_id = t.api_id
                                    WHERE
                                    1 = 1
                                    /~solutionCode: AND csi.solution_code = {solutionCode}~/
                                    /~apiTitle: AND t.api_title like CONCAT('%',{apiTitle},'%')~/
                                    /~apiUri: AND t.api_uri = {apiUri}~/
                                    ORDER BY csi.id DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="unRelatedApiPageList">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT t.*
                                    FROM (
                                             SELECT c.api_id,
                                                    a.status                AS api_status,
                                                    (CASE
                                                         WHEN a.api_type IN ('MULTI_FILE_UPLOAD', 'SINGLE_FILE_UPLOAD') THEN 'FILE_UPLOAD'
                                                         WHEN a.api_type = 'FILE_DOWNLOAD' THEN 'FILE_DOWNLOAD'
                                                         ELSE 'COMMON' END) AS api_type,
                                                    a.api_group             AS api_group_code,
                                                    ag.api_group_name,
                                                    a.api_title,
                                                    a.http_method,
                                                    a.api_uri,
                                                    'V1'                    AS api_version,
                                                    a.created_datetime,
                                                    a.last_modified_datetime
                                             FROM tbl_yop_api_define a
                                                      INNER JOIN tbl_api_request b ON b.http_method = 'OLD_API' AND b.path = a.api_uri
                                                      INNER JOIN tbl_api c ON b.api_id = c.api_id
                                                      INNER JOIN tbl_api_group ag ON a.api_group = ag.api_group_code
                                             WHERE a.is_migrated = false
                                             UNION
                                             SELECT a.api_id,
                                                    (CASE WHEN a.status in ('DISABLED','DOC_OFFLINE') THEN 'FORBID' ELSE 'ACTIVE' END) AS api_status,
                                                    a.api_type,
                                                    a.api_group AS api_group_code,
                                                    ag.api_group_name,
                                                    a.title     AS api_title,
                                                    b.http_method,
                                                    b.path      AS api_uri,
                                                    'V2'        AS api_version,
                                                    a.created_datetime,
                                                    a.last_modified_datetime
                                             FROM tbl_api a
                                                      INNER JOIN tbl_api_request b ON a.api_id = b.api_id
                                                      INNER JOIN tbl_api_group ag ON a.api_group = ag.api_group_code
                                                      LEFT JOIN tbl_yop_api_define c ON b.path = c.api_uri AND c.status = 'ACTIVE' AND c.is_migrated = true
                                             WHERE a.api_group != 'OLD_API'
                                               AND c.api_uri IS NULL
                                             UNION
                                             SELECT c.api_id,
                                                    a.status    AS api_status,
                                                    (CASE
                                                         WHEN a.api_type IN ('MULTI_FILE_UPLOAD', 'SINGLE_FILE_UPLOAD') THEN 'FILE_UPLOAD'
                                                         WHEN a.api_type = 'FILE_DOWNLOAD' THEN 'FILE_DOWNLOAD'
                                                         ELSE 'COMMON' END) AS api_type,
                                                    a.api_group AS api_group_code,
                                                    ag.api_group_name,
                                                    a.api_title,
                                                    a.http_method,
                                                    a.api_uri,
                                                    'V1'        AS api_version,
                                                    a.created_datetime,
                                                    a.last_modified_datetime
                                             FROM tbl_yop_api_define a
                                                      INNER JOIN tbl_api_request b ON b.path = a.api_uri
                                                 AND a.status = 'ACTIVE' AND a.is_migrated = true
                                                      INNER JOIN tbl_api c ON b.api_id = c.api_id
                                                      INNER JOIN tbl_api_group ag ON a.api_group = ag.api_group_code
                                         ) t
                                    WHERE NOT EXISTS(SELECT 1 FROM tbl_custom_solution_api csi WHERE t.api_id = csi.api_id AND csi.solution_code = {solutionCode})
                                    /~apiTitle: AND t.api_title like CONCAT('%',{apiTitle},'%')~/
                                    /~apiUri: AND t.api_uri = {apiUri}~/
                                    /~apiGroupCode: AND t.api_group_code = {apiGroupCode}~/
                                    /~apiType: AND t.api_type = {apiType}~/
                                    /~apiStatus: AND t.api_status = {apiStatus}~/
                                    ORDER BY t.created_datetime DESC, t.last_modified_datetime DESC
                                    LIMIT {_maxSize} OFFSET {_startIndex}
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
                <entry key="listForDoc">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        cs.solution_code,cs.solution_name
                                    FROM tbl_custom_solution cs
                                    WHERE
                                    1 = 1
                                    /~spCodes: AND cs.sp_code in {spCodes}~/
                                    /~excludeSolutionCodes: AND cs.solution_code not in {excludeSolutionCodes}~/
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="pageOwnerQueryService" class="com.yeepay.g3.utils.query.impl.QueryServiceImpl">
        <property name="dataSource" ref="yopDocMysqlDataSource"/>
        <property name="querys">
            <map>
                <!-- 根据页面ID查询负责人列表 -->
                <entry key="getPageOwners">
                    <bean class="com.yeepay.g3.utils.query.Query">
                        <property name="sql">
                            <value>
                                <![CDATA[
                                    SELECT
                                        owner_id as id,
                                        owner_name as name,
                                        created_datetime as assignedTime
                                    FROM
                                        tbl_doc_page_owner
                                    WHERE
                                        page_id = {pageId}
                                    ORDER BY created_datetime ASC
                                ]]>
                            </value>
                        </property>
                    </bean>
                </entry>
            </map>
        </property>
    </bean>
</beans>
