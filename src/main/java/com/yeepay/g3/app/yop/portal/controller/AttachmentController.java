package com.yeepay.g3.app.yop.portal.controller;

import cn.hutool.core.thread.NamedThreadFactory;
import com.fasterxml.jackson.databind.JavaType;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.AttachmentQueryService;
import com.yeepay.g3.app.yop.portal.service.AttachmentService;
import com.yeepay.g3.app.yop.portal.utils.Constants;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.utils.storage.RemoteStorageConfig;
import com.yeepay.g3.app.yop.portal.utils.storage.RemoteStorageUtils;
import com.yeepay.g3.app.yop.portal.vo.AttachmentReqVo;
import com.yeepay.g3.app.yop.portal.vo.page.AttachmentPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.doc.dto.AttachmentDTO;
import com.yeepay.g3.facade.yop.doc.dto.AttachmentDeleteRequest;
import com.yeepay.g3.facade.yop.doc.dto.AttachmentFileTypeDTO;
import com.yeepay.g3.facade.yop.doc.dto.AttachmentUploadRequest;
import com.yeepay.g3.facade.yop.doc.facade.AttachmentFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.yeepay.g3.app.yop.portal.utils.Constants.ATTACHMENT_ACCESS_PATH;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018-12-17 15:55
 */
@RestController
@RequestMapping("/rest/attachments")
public class AttachmentController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AttachmentController.class);
    private static final JsonMapper JSON_MAPPER = JsonMapper.nonNullMapper();
    private static final JavaType ATTACHMENT_SUPPORT_TYPE = JSON_MAPPER.contructCollectionType(List.class, AttachmentFileTypeDTO.class);

    private static final String EXT_SEPERATOR = ".";
    private static final String IMAGE_FILE_TYPE = "IMAGE";
    private AttachmentFacade attachmentFacade = RemoteServiceFactory.getService(AttachmentFacade.class);

    @Autowired
    private AttachmentQueryService attachmentQueryService;
    @Autowired
    private AttachmentService attachmentService;


    @GetMapping("commons/file-type")
    ResponseMessage getFileTypes() {
        Map<String, String> fileTypeConfig = (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_ATTACHMENT_EXT_CONFIG);
        List<AttachmentFileTypeDTO> result;
        String jsonStr = fileTypeConfig.get(Constants.ATTACHMENT_SUPPORTED_TYPES);
        if (StringUtils.isNotBlank(jsonStr)) {
            result = JSON_MAPPER.fromJson(jsonStr, ATTACHMENT_SUPPORT_TYPE);
        } else {
            result = Collections.emptyList();
        }
        return new ResponseMessage("result", result);
    }

    @PostMapping("upload")
    ResponseMessage upload(AttachmentReqVo reqVo) {
        Map<String, String> cephConfig = null;
        AttachmentUploadRequest uploadRequest = null;
        try {
            String fileId = attachmentService.create(cephConfig, uploadRequest, reqVo, true, "general");
            return new ResponseMessage("result", true).put("fileUrl", getFileUrl(fileId));
        } catch (Exception ex) {
            LOGGER.error("error when upload attachment with bizCode:" + reqVo.getBizCode() + ",fileName:" + reqVo.getFileName() + ".", ex);
            attachmentService.deleteCephFile(cephConfig, uploadRequest);
            return new ResponseMessage(ex);
        }
    }

    private String getFileUrl(String fileId) {
        return ATTACHMENT_ACCESS_PATH + "?fileId=" + fileId;
    }

    @GetMapping("detail")
    ResponseMessage detail(@RequestParam Long id) {
        CheckUtils.notNull(id, "id");
        AttachmentDTO dto = attachmentFacade.findById(id);
        removeExtName(dto);
        return new ResponseMessage("result", dto);
    }

    private void removeExtName(AttachmentDTO dto) {
        if (dto.getFileName().contains(EXT_SEPERATOR)) {
            dto.setFileName(dto.getFileName().substring(0, dto.getFileName().lastIndexOf(EXT_SEPERATOR)));
        }
        if (IMAGE_FILE_TYPE.equals(dto.getFileType())) {
            dto.setFileUrl(String.format("![](%s)<br>", ATTACHMENT_ACCESS_PATH + "?fileId=" + dto.getFileId()));
        } else {
            dto.setFileUrl(String.format("<a href='%s'>%s</a>", ATTACHMENT_ACCESS_PATH + "?fileId=" + dto.getFileId(), dto.getFileName()));
        }
    }

    @PostMapping("modify")
    ResponseMessage update(AttachmentReqVo reqVo) {
        Map<String, String> cephConfig = null;
        AttachmentUploadRequest uploadRequest = null;
        try {
            attachmentService.update(cephConfig, uploadRequest, reqVo, true, "general");
            return new ResponseMessage("result", true);
        } catch (Exception ex) {
            LOGGER.error("error when modify attachment.", ex);
            attachmentService.deleteCephFile(cephConfig, uploadRequest);
            return new ResponseMessage(ex);
        }
    }

    @GetMapping("list")
    ResponseMessage pageList(
            @RequestParam(value = "_pageNo", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(value = "_pageSize", required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) String fileId,
            @RequestParam(required = false) String fileType) {
        AttachmentPageQueryParam queryParam = new AttachmentPageQueryParam();
        queryParam.setPageNo(pageNo);
        queryParam.setPageSize(pageSize);
        queryParam.setFileName(fileName);
        queryParam.setFileType(fileType);
        queryParam.setFileId(fileId);
        PageQueryResult<AttachmentDTO> dtos = attachmentQueryService.pageQuery(queryParam);
        dtos.getItems().forEach(this::removeExtName);
        return new ResponseMessage("result", dtos);
    }

    @PostMapping("delete")
    ResponseMessage batchDelete(@RequestParam List<Long> ids) {
        AttachmentDeleteRequest deleteRequest = new AttachmentDeleteRequest();
        deleteRequest.setOperator(ShiroUtils.getShiroUser().getUsername());
        deleteRequest.setIds(ids);
        Object result;
        if (CollectionUtils.isNotEmpty(ids)) {
            result = attachmentFacade.batchDelete(deleteRequest);
        } else {
            result = Collections.emptyList();
        }
        return new ResponseMessage("result", result);
    }

}
