package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.ApiManageQueryService;
import com.yeepay.g3.app.yop.portal.vo.ApiExtendConfigBatchCreateVO;
import com.yeepay.g3.app.yop.portal.vo.ApiExtendConfigVO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiExtendConfigBatchCreateReqDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiExtendConfigReqDTO;
import com.yeepay.g3.facade.yop.sys.enums.RequestSuccessTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.ApiExtendConfigFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/9/1 10:56
 */
@RestController
@RequestMapping("/rest/api-extend-config/manage")
public class ApiExtendConfigController {

    private ApiExtendConfigFacade apiExtendFacade = RemoteServiceFactory.getService(ApiExtendConfigFacade.class);

    @Autowired
    private ApiManageQueryService apiManageQueryService;

    @ResponseBody
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResponseMessage errorCodeLocationSave(@RequestBody ApiExtendConfigVO apiExtendConfigVO) {
        ApiExtendConfigReqDTO apiExtendConfigReqDTO = new ApiExtendConfigReqDTO();
        apiExtendConfigReqDTO.setApiId(apiExtendConfigVO.getApiId());
        apiExtendConfigReqDTO.setErrorCodeLocation(apiExtendConfigVO.getErrorCodeLocation());
        apiExtendConfigReqDTO.setNonce(apiExtendConfigVO.getNonce());
        apiExtendConfigReqDTO.setMessageLocation(apiExtendConfigVO.getMessageLocation());
        apiExtendConfigReqDTO.setRequestSuccessValue(apiExtendConfigVO.getRequestSuccessValue());
        apiExtendConfigReqDTO.setRequestSuccessType(RequestSuccessTypeEnum.parse(apiExtendConfigVO.getRequestSuccessType()));
        apiExtendFacade.creteOrUpdate(apiExtendConfigReqDTO);
        return new ResponseMessage("result");
    }

    @ResponseBody
    @RequestMapping(value = "/batch-add", method = RequestMethod.POST)
    public ResponseMessage errorCodeLocationBatchSave(@RequestBody ApiExtendConfigBatchCreateVO apiExtendConfigBatchCreateVO) {
        ApiExtendConfigBatchCreateReqDTO apiExtendConfigBatchCreateReqDTO = new ApiExtendConfigBatchCreateReqDTO();
        apiExtendConfigBatchCreateReqDTO.setApiIds(apiExtendConfigBatchCreateVO.getApiIds());
        apiExtendConfigBatchCreateReqDTO.setErrorCodeLocation(apiExtendConfigBatchCreateVO.getErrorCodeLocation());
        apiExtendConfigBatchCreateReqDTO.setMessageLocation(apiExtendConfigBatchCreateVO.getMessageLocation());
        apiExtendConfigBatchCreateReqDTO.setRequestSuccessValue(apiExtendConfigBatchCreateVO.getRequestSuccessValue());
        apiExtendConfigBatchCreateReqDTO.setRequestSuccessType(RequestSuccessTypeEnum.parse(apiExtendConfigBatchCreateVO.getRequestSuccessType()));
        apiExtendFacade.batchCreate(apiExtendConfigBatchCreateReqDTO);
        return new ResponseMessage("result");
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage errorCodeLocationDetail(@RequestParam("apiId") String apiId) {
        Object errorCodeConfig = apiManageQueryService.errorCodeLocationDetail(apiId);
        return new ResponseMessage("errorCodeConfig", errorCodeConfig);
    }

    @ResponseBody
    @RequestMapping(value = "/not-config-apis", method = RequestMethod.GET)
    public ResponseMessage apiIds(@RequestParam("apiGroup") String apiGroup) {
        List apiList = apiManageQueryService.queryNoConfigErrorCodeLocationApi(apiGroup);
        return new ResponseMessage("apiList", apiList);
    }
}
