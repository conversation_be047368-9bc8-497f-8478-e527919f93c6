/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import com.yeepay.g3.app.yop.portal.enums.ApiVersionEnum;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.UnifyApiPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.UnifyApiPageQueryParam;
import com.yeepay.g3.utils.common.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/5 4:44 下午
 */
public class UnifyApiServiceTest extends SpringBootTests {
    @Autowired
    private UnifyApiService unifyApiService;

    @Test
    public void testListJoinSpiName() {
        String spiName = "test-wdc.self-settle.result";
        // 该api为spi已经关联的，此处通过该uri进行检索应该没有查询结果
        String apiUri = "/rest/v1.0/test-wdc/product-query/query-for-doc";
        UnifyApiPageQueryParam param = new UnifyApiPageQueryParam();
        param.setJoinCode(UnifyApiPageQueryParam.JoinCodeEnum.SPINAME);
        param.setJoinValue(spiName);
        param.setApiUri(apiUri);
        PageQueryResult<UnifyApiPageItem> resultWithUri = unifyApiService.findApisPage(param);
        Assert.assertTrue(CollectionUtils.isEmpty(resultWithUri.getItems()));

        String apiGroup = "test-wdc";
        param.setApiUri(null);
        param.setApiGroupCode(apiGroup);
        param.setApiVersion(ApiVersionEnum.V1);
        param.setPageSize(50);

        PageQueryResult<UnifyApiPageItem> resultWithApiGroup = unifyApiService.findApisPage(param);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultWithApiGroup.getItems()) && resultWithApiGroup.getItems().size() < 50);
        for (UnifyApiPageItem unifyApiPageItem : resultWithApiGroup.getItems()) {
            Assert.assertFalse(StringUtils.equals(unifyApiPageItem.getApiUri(), apiUri));
        }

    }
}
