/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.cache.NotifyErrorSolutionLocalCache;
import com.yeepay.g3.boot.web.pojo.response.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/10/29 11:22
 */
@RestController
@RequestMapping("/rest/notify/error-code")
@Slf4j
public class NotifyErrorCodeController {

    @Autowired
    private NotifyErrorSolutionLocalCache notifyErrorSolutionLocalCache;

    @GetMapping("/solution")
    public R solution(@RequestParam String errorCode) {

        return new R(notifyErrorSolutionLocalCache.get(errorCode));
    }
}
