/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.app.yop.portal.enums.ApiParamFormatEnum;

import java.io.Serializable;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/10/10 下午5:18
 */
public class ApiParamSensitiveVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private Long parentId;

    private String paramName;

    private Boolean sensitive;

    private ApiParamFormatEnum format;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getParamName() {
        return paramName;
    }

    public void setParamName(String paramName) {
        this.paramName = paramName;
    }

    public Boolean getSensitive() {
        return sensitive;
    }

    public void setSensitive(Boolean sensitive) {
        this.sensitive = sensitive;
    }

    public ApiParamFormatEnum getFormat() {
        return format;
    }

    public void setFormat(ApiParamFormatEnum format) {
        this.format = format;
    }
}
