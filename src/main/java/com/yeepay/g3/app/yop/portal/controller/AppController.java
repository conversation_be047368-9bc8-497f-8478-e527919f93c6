/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;


import com.google.common.collect.Lists;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.AppQueryService;
import com.yeepay.g3.app.yop.portal.service.SpiSubscribeQueryService;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.page.AppPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.AppPageParam;
import com.yeepay.g3.facade.yop.monitor.dto.ManualAnnotationCreateDTO;
import com.yeepay.g3.facade.yop.monitor.facade.ManualAnnotationFacade;
import com.yeepay.g3.facade.yop.sys.dto.AppDTO;
import com.yeepay.g3.facade.yop.sys.enums.AppStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.AppTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.AppFacade;
import com.yeepay.g3.utils.common.BeanUtils;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR> @version 1.0.0
 * @since 18/3/9 14:10
 */
@Controller
@RequestMapping("/rest/app")
public class AppController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AppController.class);

    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";

    private FastDateFormat dateFormat = FastDateFormat.getInstance(PATTERN);

    @Autowired
    private AppQueryService appQueryService;

    @Autowired
    private SpiSubscribeQueryService spiSubscribeQueryService;

    private AppFacade appFacade = RemoteServiceFactory.getService(AppFacade.class);

    private ManualAnnotationFacade manualAnnotationFacade = RemoteServiceFactory.getService(ManualAnnotationFacade.class);

    @ResponseBody
    @RequestMapping(value = "/manual-annotation", method = RequestMethod.POST)
    public ResponseMessage manualAnnotation(@RequestBody @Validated ManualAnnotationVO vo) {
        try {
            Date finishDate = null;
            Date startDate = null;

            if (StringUtils.isNotBlank(vo.getFinishManualAnnotationDate())) {
                finishDate = dateFormat.parse(vo.getFinishManualAnnotationDate());
            }

            if (StringUtils.isNotBlank(vo.getStartAccessDate())) {
                startDate = dateFormat.parse(vo.getStartAccessDate());
            }

            if (finishDate != null && startDate != null && finishDate.before(startDate)) {
                return new ResponseMessage("手动标注完成日期不能早于开始对接日期");
            }

            ManualAnnotationCreateDTO dto = new ManualAnnotationCreateDTO();
            dto.setCustomerNo(vo.getCustomerNo());
            dto.setAppId(vo.getAppId());
            if (finishDate != null) {
                dto.setOccurTime(finishDate);
                manualAnnotationFacade.createManualAnnotationEvent(dto);
            } else {
                manualAnnotationFacade.deleteManualAnnotationEvent(dto);
            }

            if (startDate != null) {
                dto.setOccurTime(startDate);
                manualAnnotationFacade.updateStartAccessEvent(dto);
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred when manual-annotation, request:[" + vo + "]", e);
            throw new RuntimeException(e);
        }
        return new ResponseMessage();
    }



    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@RequestParam(value = "appId", required = false) String appId,
                                @RequestParam(value = "appName", required = false) String name,
                                @RequestParam(value = "subjectNo", required = false) String subjectNo,
                                @RequestParam(value = "customerNo", required = false) String customerNo,
                                @RequestParam(value = "appType", required = false) String appType,
                                @RequestParam(value = "status", required = false) String status,
                                @RequestParam(value = "createdStartDate", required = false) String createdStartDate,
                                @RequestParam(value = "createdEndDate", required = false) String createdEndDate,
                                @RequestParam(value = "_pageNo", required = false, defaultValue = "1") Integer _pageNo) {
        AppPageParam param = new AppPageParam();
        try {
            param.setAppId(appId);
            param.setName(name);
            param.setSubjectNo(subjectNo);
            param.setCustomerNo(customerNo);
            param.setType(AppTypeEnum.parse(appType));
            param.setStatus(AppStatusEnum.parse(status));

            if (StringUtils.isNotEmpty(createdStartDate)) {
                param.setCreatedStartDate(dateFormat.parse(createdStartDate));
            }

            if (StringUtils.isNotEmpty(createdEndDate)) {
                param.setCreatedEndDate(dateFormat.parse(createdEndDate));
            }

            param.setPageNo(_pageNo);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("app");
            param.setPageSize(appPageSize.intValue());
            return new ResponseMessage("page", appQueryService.pageQuery(param));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list app with param:" + param, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "list/for-authz", method = RequestMethod.GET)
    public ResponseMessage listForAuthz(@RequestParam String customerNo) {
        AppPageParam param = new AppPageParam();
        try {
            param.setCustomerNo(customerNo);
            param.setStatus(AppStatusEnum.ACTIVE);
            param.setPageNo(1);
            param.setPageSize(Integer.MAX_VALUE);
            List<AppBasicVo> result = Lists.newArrayList();
            final List<AppPageItem> items = appQueryService.pageQuery(param).getItems();
            if (CollectionUtils.isNotEmpty(items)) {
                items.forEach(item -> result.add(new AppBasicVo(item.getAppId(), item.getName())));
            }
            return new ResponseMessage("result", result);
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list app for authz with param:" + param, ex);
            return new ResponseMessage(ex);
        }
    }

    /**
     * 根据商编查询应用，过滤已订阅的应用、过滤“沙箱、测试、移动端”应用类型
     *
     * @param customerNo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/list/for-subscribe", method = RequestMethod.GET)
    public ResponseMessage listForSubscribe(@RequestParam String customerNo,
                                            @RequestParam String spiName) {
        AppQueryParam appQueryParam = new AppQueryParam();
        try {
            appQueryParam.setCustomerNo(customerNo);
            appQueryParam.setStatus(AppStatusEnum.ACTIVE);
            appQueryParam.setTypes(Arrays.asList(AppTypeEnum.PLATFORM.name(), AppTypeEnum.PARTNER.name()));
            List<String> appIds = appQueryService.findByConditions(appQueryParam);
            List<String> filteredAppIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(appIds)) {
                //过滤已订阅的应用
                filteredAppIds = appIds.stream().filter(appId -> !spiSubscribeQueryService.exist(appId, spiName)).collect(Collectors.toList());
            }
            return new ResponseMessage("result", filteredAppIds);
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list app for subscribe with customerNo:" + customerNo, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody AppVO appVO) {
        AppDTO appDTO = new AppDTO();
        BeanUtils.copyProperties(appVO, appDTO);
        Map<String, String> map = new HashMap<>();
        try {
            AppDTO dto = appFacade.create(appDTO);
            map.put("appId", dto.getAppId());
            map.put("subjectNo", dto.getSubjectNo());
        } catch (Exception e) {
            LOGGER.error("Exception occurred when create app[" + appVO + "]", e);
            return new ResponseMessage(e);
        }
        return new ResponseMessage("result", map);
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam(value = "appId") String appId) {
        try {
            AppDTO appDTO = appFacade.find(appId);
            return new ResponseMessage("result", appDTO);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when detail app[" + appId + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/exists", method = RequestMethod.GET)
    public ResponseMessage exists(@RequestParam(value = "appId") String appId,
                                  @RequestParam(value = "ignoreDeleted", defaultValue = "true") boolean ignoreDeleted) {
        try {
            AppDTO appDTO = appFacade.find(appId);
            boolean result = appDTO != null;
            if (ignoreDeleted) {
                result = result && !AppStatusEnum.DELETED.equals(appDTO.getStatus());
            }
            return new ResponseMessage("result", result);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when exists app[" + appId + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody AppVO appVO) {
        AppDTO appDTO = new AppDTO();
        BeanUtils.copyProperties(appVO, appDTO);
        try {
            appFacade.update(appDTO);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when update app[" + appVO + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/active", method = RequestMethod.POST)
    public ResponseMessage active(@RequestParam(value = "appId") String appId) {
        try {
            appFacade.active(appId);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when active app[" + appId + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/forbid", method = RequestMethod.POST)
    public ResponseMessage forbid(@RequestParam(value = "appId") String appId) {
        try {
            appFacade.forbid(appId);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when forbid app[" + appId + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam(value = "appId") String appId) {
        try {
            appFacade.delete(appId);
            return new ResponseMessage();
        } catch (Exception e) {
            LOGGER.error("Exception occurred when delete app[" + appId + "]", e);
            return new ResponseMessage(e);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/status", method = RequestMethod.GET)
    public ResponseMessage status() {
        List<CommonsVO> list = new ArrayList<>();
        AppStatusEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/type", method = RequestMethod.GET)
    public ResponseMessage type() {
        List<CommonsVO> list = new ArrayList<>();
        AppTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }

}
