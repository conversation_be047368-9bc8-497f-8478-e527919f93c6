/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ProductAuthzChangesQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.facade.yop.sys.enums.ProductAuthzOperateTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.ProductOperateTypeEnum;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/20 上午11:58
 */
@Component
public class ProductAuthzChangesQueryServiceImpl implements ProductAuthzChangesQueryService {

    @Resource(name = "productAuthzChangesQueryService")
    private QueryService queryService;

    private final PageItemConverter<ProductAuthzChangesPageItem> pageItemConverter = new ProductAuthzChangesPageItemConverter();

    @Override
    public PageQueryResult<ProductAuthzChangesPageItem> pageQuery(ProductAuthzChangesPageQueryParam param) {
        Map<String, Object> bizParams = getBizParams(param);
        List<Map> list = queryService.query("pageList", bizParams);
        PageQueryResult<ProductAuthzChangesPageItem> result = new PageQueryResult<>();
        List<ProductAuthzChangesPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    private Map<String, Object> getBizParams(ProductAuthzChangesPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("customerNo", param.getCustomerNo());
        bizParams.put("appId", param.getAppId());
        bizParams.put("operator", param.getOperator());
        bizParams.put("type", param.getType());
        bizParams.put("createdStartDate", param.getCreatedStartDate());
        bizParams.put("createdEndDate", param.getCreatedEndDate());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class ProductAuthzChangesPageItemConverter extends BasePageItemConverter<ProductAuthzChangesPageItem> {

        @Override
        public ProductAuthzChangesPageItem convert(Map<String, Object> params) {
            ProductAuthzChangesPageItem item = new ProductAuthzChangesPageItem();
            item.setCustomerNo((String) params.get("customer_no"));
            item.setCustomerName((String) params.get("customer_name"));
            item.setAppId((String) params.get("app_id"));
            item.setAppName((String) params.get("app_name"));
            item.setType(ProductAuthzOperateTypeEnum.parse((String) params.get("oper_type")));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setOperator((String) params.get("operator"));
            item.setCause((String) params.get("oper_cause"));
            item.setDetail((String) params.get("oper_detail"));
            return item;
        }
    }
}
