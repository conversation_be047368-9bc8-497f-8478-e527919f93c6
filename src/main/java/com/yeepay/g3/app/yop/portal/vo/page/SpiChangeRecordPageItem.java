package com.yeepay.g3.app.yop.portal.vo.page;

import java.io.Serializable;
import java.util.Date;

/**
 * title: SpiChangeRecordPageItem<br/>
 * description: spi变更记录分页查询返回参数<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:38
 */
public class SpiChangeRecordPageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private String name;

    private Date createdDateTime;

    private String apiGroup;

    private String operator;

    private String opType;

    private String cause;

    public Long getId() {
        return id;
    }

    public SpiChangeRecordPageItem setId(Long id) {
        this.id = id;
        return this;
    }

    public String getName() {
        return name;
    }

    public SpiChangeRecordPageItem setName(String name) {
        this.name = name;
        return this;
    }

    public Date getCreatedDateTime() {
        return createdDateTime;
    }

    public SpiChangeRecordPageItem setCreatedDateTime(Date createdDateTime) {
        this.createdDateTime = createdDateTime;
        return this;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public SpiChangeRecordPageItem setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getOperator() {
        return operator;
    }

    public SpiChangeRecordPageItem setOperator(String operator) {
        this.operator = operator;
        return this;
    }

    public String getOpType() {
        return opType;
    }

    public SpiChangeRecordPageItem setOpType(String opType) {
        this.opType = opType;
        return this;
    }

    public String getCause() {
        return cause;
    }

    public SpiChangeRecordPageItem setCause(String cause) {
        this.cause = cause;
        return this;
    }

    @Override
    public String toString() {
        return "SpiChangeRecordPageItem{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", createdDateTime=" + createdDateTime +
                ", apiGroup='" + apiGroup + '\'' +
                ", operator='" + operator + '\'' +
                ", opType='" + opType + '\'' +
                ", cause='" + cause + '\'' +
                '}';
    }
}
