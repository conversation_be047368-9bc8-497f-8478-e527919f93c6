/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.dto;

import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;
import com.yeepay.g3.facade.yop.sys.dto.route.ApiRouteDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/7 8:50 PM
 */
@Data
public class ApiRouteImportContext implements Serializable {

    private static final long serialVersionUID = -1L;

    private String apiGroup;

    private Map<ApiRequestKey, List<ApiRouteDTO>> apiRequestKeyRouteMap;
}
