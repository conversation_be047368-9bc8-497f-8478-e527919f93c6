/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestAppDTO;
import com.yeepay.g3.app.yop.portal.service.AppQueryService;
import com.yeepay.g3.app.yop.portal.utils.DateUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.AppQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.AppPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.AppPageParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.facade.yop.monitor.enums.CustomerDockingEventType;
import com.yeepay.g3.facade.yop.sys.enums.AppStatusEnum;
import com.yeepay.g3.facade.yop.sys.enums.AppTypeEnum;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/13 下午2:10
 */

@Component
public class AppQueryServiceImpl implements AppQueryService {

    @Resource(name = "appQueryService")
    private QueryService queryService;

    @Resource(name = "customerDockingEventService")
    private QueryService customerDockingEventService;

    private final PageItemConverter<AppPageItem> pageItemConverter = new AppPageItemConverter();

    @Override
    public PageQueryResult<AppPageItem> pageQuery(AppPageParam param) {
        Map<String, Object> bizParams = getBizParams(param);
        List<Map> list = queryService.query("pageList", bizParams);
        PageQueryResult<AppPageItem> result = new PageQueryResult<>();
        List<AppPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    @Override
    public List<RegressionTestAppDTO> listPlatformTest() {
        HashMap<String, Object> map = Maps.newHashMap();
        map.put("type", AppTypeEnum.PLATFORM_TEST);
        map.put("status", AppStatusEnum.ACTIVE);
        List queryResult = queryService.query("list", map);
        return convert(queryResult);
    }

    @Override
    public List<String> listAlias(String appId) {
        Map<String, Object> param = new HashMap<>(2);
        param.put("appId", appId);
        List<Map> list = queryService.query("listAlias", param);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> aliasList = new ArrayList<>(list.size());
        list.forEach(map -> aliasList.add((String) map.get("alias")));
        return aliasList;
    }

    @Override
    public List<String> listByCustomerNo(String customerNo) {
        Map<String, Object> param = new HashMap<>(2);
        param.put("customerNo", customerNo);
        List<Map> list = queryService.query("listByCustomerNo", param);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> aliasList = new ArrayList<>(list.size());
        list.forEach(map -> aliasList.add((String) map.get("app_id")));
        return aliasList;
    }

    @Override
    public List<String> findByConditions(AppQueryParam appQueryParam) {
        Map<String, Object> param = new HashMap<>();
        param.put("customerNo", appQueryParam.getCustomerNo());
        param.put("types", appQueryParam.getTypes());
        param.put("status", appQueryParam.getStatus());
        List<Map> list = queryService.query("findByConditions", param);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> aliasList = new ArrayList<>(list.size());
        list.forEach(map -> aliasList.add((String) map.get("app_id")));
        return aliasList;
    }

    private List<RegressionTestAppDTO> convert(List queryResult) {
        if (Collections3.isEmpty(queryResult)) {
            return Collections.emptyList();
        }
        List<RegressionTestAppDTO> apps = new ArrayList<>(queryResult.size());
        for (Object result : queryResult) {
            Map resultMap = (Map) result;
            RegressionTestAppDTO dto = new RegressionTestAppDTO();
            dto.setAppKey((String) resultMap.get("app_id"));
            dto.setAppTitle((String) resultMap.get("app_name"));
            apps.add(dto);
        }
        return apps;
    }

    private Map<String, Object> getBizParams(AppPageParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("appId", param.getAppId());
        bizParams.put("name", param.getName());
        bizParams.put("subjectNo", param.getSubjectNo());
        bizParams.put("customerNo", param.getCustomerNo());
        bizParams.put("type", param.getType());
        bizParams.put("status", param.getStatus());
        bizParams.put("createdStartDate", param.getCreatedStartDate());
        bizParams.put("createdEndDate", param.getCreatedEndDate());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class AppPageItemConverter extends BasePageItemConverter<AppPageItem> {

        @Override
        public AppPageItem convert(Map<String, Object> params) {
            AppPageItem item = new AppPageItem();
            item.setAppId((String) params.get("app_id"));
            item.setName((String) params.get("app_name"));
            item.setSubjectNo((String) params.get("subject_no"));
            item.setCustomerNo((String) params.get("customer_no"));
            item.setDesc((String) params.get("app_desc"));
            item.setType((String) params.get("app_type"));
            item.setStatus((String) params.get("status"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            item.setStartAccessDate(getOccurTime(item, CustomerDockingEventType.START_ACCESS.getValue()));
            item.setFinishManualAnnotationDate(getOccurTime(item, CustomerDockingEventType.MANUAL_ANNOTATION.getValue()));
            return item;
        }

        private Date getOccurTime(AppPageItem item, String type) {
            Map<String, Object> param = Maps.newHashMap();
            param.put("appId", item.getAppId());
            param.put("type", type);
            Map map = customerDockingEventService.queryUnique("findCustomerDockingByParam", param, false);
            if (MapUtils.isEmpty(map)) {
                return null;
            }
            return DateUtils.toDate(map.get("occur_time"));
        }
    }
}
