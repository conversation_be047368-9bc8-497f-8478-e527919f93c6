/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.page.CustomerPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.CustomerPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/7/19 下午5:41
 */
public interface CustomerQueryService {

    PageQueryResult<CustomerPageItem> pageQuery(CustomerPageQueryParam param);

    PageQueryResult<CustomerPageItem> pageQueryForSp(CustomerPageQueryParam param, List<String> providerCodes);

    /**
     * 根据商编，查商户信息
     *
     * @param customerNo
     * @return
     */
    Map<String, Object> findByCustomerNo(String customerNo);
}
