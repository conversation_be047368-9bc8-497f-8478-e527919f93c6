package com.yeepay.g3.app.yop.portal.controller;


import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.yeepay.g3.app.yop.portal.controller.support.imports.*;
import com.yeepay.g3.app.yop.portal.dto.ApiErrcodeBatchAddDTO;
import com.yeepay.g3.app.yop.portal.dto.ApiImportContext;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.enums.EnvEnum;
import com.yeepay.g3.app.yop.portal.exception.PortalExceptionEnum;
import com.yeepay.g3.app.yop.portal.service.*;
import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.app.yop.portal.utils.IdGenerator;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.security.SecurityUtils;
import com.yeepay.g3.app.yop.portal.utils.swagger.SwaggerAnalysisResult;
import com.yeepay.g3.app.yop.portal.utils.swagger.SwaggerAnalyzer;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.converter.ApiExampleConverter;
import com.yeepay.g3.app.yop.portal.vo.page.*;
import com.yeepay.g3.core.yop.utils.Exceptions;
import com.yeepay.g3.facade.yop.doc.dto.api.ApiOptionPreviewRequest;
import com.yeepay.g3.facade.yop.doc.facade.ApiDocFacade;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.dto.api.*;
import com.yeepay.g3.facade.yop.sys.dto.imports.ApiImportCheckItem;
import com.yeepay.g3.facade.yop.sys.dto.imports.ImportFailedInfo;
import com.yeepay.g3.facade.yop.sys.dto.route.ApiRouteDTO;
import com.yeepay.g3.facade.yop.sys.dto.route.ParamConfig;
import com.yeepay.g3.facade.yop.sys.enums.*;
import com.yeepay.g3.facade.yop.sys.facade.*;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import com.yeepay.g3.yop.frame.utils.EnvUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * title: ApiManageController<br/>
 * description: 新版api管理<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/15 上午11:10
 */
@Controller
@RequestMapping("/rest/api/manage")
public class ApiManageController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiManageController.class);

    private ApiMgrFacade apiMgrFacade = RemoteServiceFactory.getService(ApiMgrFacade.class);

    private ApiRequestFacade apiRequestFacade = RemoteServiceFactory.getService(ApiRequestFacade.class);

    private ApiDocFacade apiDocFacade = RemoteServiceFactory.getService(ApiDocFacade.class);

    private ApiExampleMgrFacade apiExampleMgrFacade = RemoteServiceFactory.getService(ApiExampleMgrFacade.class);

    @Autowired
    private ApiManageQueryService apiManageQueryService;

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    @Autowired
    private ApiImportCacheService importCacheService;

    @Autowired
    private ApiErrcodeService apiErrcodeService;

    @Autowired
    private ApiResponseService apiResponseService;

    @Autowired
    private ApiRequestService apiRequestService;

    private ClassLoaderService classLoaderService;

    private ApiErrcodeFacade apiErrcodeFacade = RemoteServiceFactory.getService(ApiErrcodeFacade.class);

    private SecurityReqMgrFacade securityReqMgrFacade = RemoteServiceFactory.getService(SecurityReqMgrFacade.class);

    private SecurityReqQueryFacade securityReqQueryFacade = RemoteServiceFactory.getService(SecurityReqQueryFacade.class);

    private List<CommonsVO> operationTypes;

    private List<CommonsVO> apiOptions;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage<PageQueryResult<ApiManagePageItem>> list(ApiManagePageQueryParam param,
                                                                    @RequestParam(value = "_pageNo", required = false) Integer pageNo) {
        try {
            param.setPageNo(pageNo);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("api");
            param.setPageSize(appPageSize.intValue());
            if (ShiroUtils.isPlatformOperator()) {
                PageQueryResult<ApiManagePageItem> pageQueryResult = apiManageQueryService.pageList(param);
                return new ResponseMessage("page", pageQueryResult);
            } else {
                List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
                if (apiGroupCodes.size() == 0) {
                    return new ResponseMessage("page", new PageQueryResult());
                }
                param.setApiGroupCodes(apiGroupCodes);
                return new ResponseMessage("page", apiManageQueryService.pageListForSp(param));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list api with param: " + param, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage<ApiManageVO> detail(@RequestParam(value = "apiId") String apiId) {
        try {
            CheckUtils.notEmpty(apiId, "apiId");
            ApiManageVO apiManageVO = converApiManageVO(apiMgrFacade.find(apiId));
            return new ResponseMessage("result", apiManageVO);
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when query api detail with apiId: " + apiId, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/env/list", method = RequestMethod.GET)
    public ResponseMessage envList() {
        String env = EnvUtil.getCurrentEnv();
        EnvEnum envEnum = EnumUtils.getEnum(EnvEnum.class, env);
        RegressionTestControllerVO.ExecutionEnvironmentVO environmentVO = new RegressionTestControllerVO.ExecutionEnvironmentVO(envEnum.getValue(), envEnum.getDescription());
        return new ResponseMessage("result", Collections.singletonList(environmentVO));
    }

    @ResponseBody
    @RequestMapping(value = "/method/analysis", method = RequestMethod.POST)
    public ResponseMessage endService(@RequestBody MethodLoadParamVO methodLoadParamVO) {
        return new ResponseMessage("result", classLoaderService.loadEndService(methodLoadParamVO.getSwagger()));
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody ApiManageVO apiManageVO) {
        ApiDTO apiDTO = new ApiDTO();
        try {
            checkParams(apiManageVO);
            converApiDTO(apiManageVO, apiDTO, true);

            OperationInfo operationInfo = new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(null);

            if (apiManageVO.isCreatedByExitedInterface()) {
                ApiAggregationCreateRequest apiAggregationCreateRequest = new ApiAggregationCreateRequest();
                apiAggregationCreateRequest.setApi(apiDTO);
                apiAggregationCreateRequest.setDefaultApiRoute(buildApiRoutDTO(apiManageVO));
                Map<ImportOperationEnum, List<ModelDTO>> models = new HashMap<>();
                List<ModelDTO> modelDTOList = classLoaderService.buildModel(apiManageVO);
                if (CollectionUtils.isNotEmpty(modelDTOList)) {
                    models.put(ImportOperationEnum.CREATE, classLoaderService.buildModel(apiManageVO));
                    apiAggregationCreateRequest.setModels(models);
                }
                apiAggregationCreateRequest.setOperationInfo(operationInfo);
                apiMgrFacade.aggregationCreate(apiAggregationCreateRequest);
            } else {
                ApiCreateRequest apiCreateRequest = new ApiCreateRequest();
                apiCreateRequest.withApi(apiDTO).withSourceRef(apiManageVO.getSourceRef()).withOperationInfo(operationInfo);
                apiMgrFacade.create(apiCreateRequest);
            }
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when create api with param: " + apiDTO, ex);
            return new ResponseMessage(ex);
        }
    }

    private void checkParams(ApiManageVO apiManageVO) {
        CheckUtils.notNull(apiManageVO, "apiManageVO");
        CheckUtils.notNull(apiManageVO.getBasic(), "basic");
        CheckUtils.notNull(apiManageVO.getRequest(), "request");
        CheckUtils.notNull(apiManageVO.getResponse(), "response");
        CheckUtils.notNull(apiManageVO.getCallbacks(), "callbacks");
        String[] items = StringUtils.split(apiManageVO.getRequest().getPath(), "/");
        PortalExceptionEnum.API_PATH_ILLEGAL.assertIsTrue(items.length > 3 && StringUtils.equals(apiManageVO.getBasic().getApiGroup(), items[2]));
        //校验path是否存在
        String apiIdByPath = apiRequestService.findApiIdByPath(apiManageVO.getRequest().getPath());
        if (StringUtils.isNotEmpty(apiIdByPath)) {
            throw new YeepayRuntimeException("api exist, path[{0}].", apiManageVO.getRequest().getPath());
        }
    }

    private ApiRouteDTO buildApiRoutDTO(ApiManageVO apiManageVO) {
        ApiRouteDTO apiRouteDTO = new ApiRouteDTO();
        apiRouteDTO.setServiceName(apiManageVO.getAppName());
        apiRouteDTO.setName(apiManageVO.getAppName());
        apiRouteDTO.setType(ApiRouteTypeEnum.DUBBO);
        Map<String, Object> routProperties = new HashMap<>();
        routProperties.put("httpMethod", apiManageVO.getRequest().getHttpMethod());
        routProperties.put("endClass", apiManageVO.getClassName());
        routProperties.put("method", apiManageVO.getMethod());
        routProperties.put("parameterHandlingType", "PASSTHROUGH");
        apiRouteDTO.setProperties(routProperties);
        return apiRouteDTO;
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody ApiManageVO apiManageVO) {
        ApiDTO apiDTO = new ApiDTO();
        try {
            CheckUtils.notEmpty(apiManageVO.getApiId(), "apiId");
            CheckUtils.notNull(apiManageVO.getVersion(), "version");
            checkParams(apiManageVO);
            converApiDTO(apiManageVO, apiDTO, false);
            ApiUpdateRequest apiUpdateRequest = new ApiUpdateRequest();
            apiUpdateRequest.withApi(apiDTO).withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(null));
            apiMgrFacade.update(apiUpdateRequest);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when update api with param: " + apiDTO, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam(value = "apiId") String apiId, @RequestParam(value = "cause") String cause, @RequestParam(value = "latestRef", required = false) String latestRef) {
        try {
            CheckUtils.notEmpty(apiId, "apiId");
            ApiDeleteRequest apiDeleteRequest = new ApiDeleteRequest();
            apiDeleteRequest.withApiId(apiId).withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(cause)).withLatestRef(latestRef);
            apiMgrFacade.delete(apiDeleteRequest);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when delete api with apiId: " + apiId, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/enable", method = RequestMethod.POST)
    public ResponseMessage enable(@RequestParam(value = "apiId") String apiId) {
        try {
            CheckUtils.notEmpty(apiId, "apiId");
            apiMgrFacade.enable(apiId);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when enable api with apiId: " + apiId, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/disable", method = RequestMethod.POST)
    public ResponseMessage disable(@RequestParam(value = "apiId") String apiId) {
        try {
            CheckUtils.notEmpty(apiId, "apiId");
            apiMgrFacade.disable(apiId);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when disable api with apiId: " + apiId, ex);
            return new ResponseMessage(ex);
        }
    }

    private ApiManageVO converApiManageVO(ApiDTO apiDTO) {
        ApiManageVO apiManageVO = new ApiManageVO();
        apiManageVO.setApiId(apiDTO.getApiId());
        apiManageVO.setVersion(apiDTO.getVersion());
        //1、基础信息
        ApiManageVO.ApiBasicVO apiBasicVO = new ApiManageVO.ApiBasicVO();
        apiBasicVO.setName(apiDTO.getBasic().getName());
        apiBasicVO.setTitle(apiDTO.getBasic().getTitle());
        apiBasicVO.setApiType(apiDTO.getBasic().getApiType().getValue());
        apiBasicVO.setApiGroup(apiDTO.getBasic().getApiGroup());
        apiBasicVO.setDescription(apiDTO.getBasic().getDescription());
        if (apiDTO.getBasic().getStatus() != null) {
            apiBasicVO.setStatus(ApiStatusEnum.ENABLED.equals(apiDTO.getBasic().getStatus()) ? ApiStatusEnum.DOC_UNPUBLISHED.getValue() : apiDTO.getBasic().getStatus().getValue());
        }
        apiBasicVO.setOptionsRule(apiDTO.getBasic().getOptionsRule());
        apiManageVO.setBasic(apiBasicVO);

        //3、请求信息
        ApiManageVO.ApiRequestVO apiRequestVO = new ApiManageVO.ApiRequestVO();
        apiRequestVO.setPath(apiDTO.getRequest().getPath());
        apiRequestVO.setHttpMethod(apiDTO.getRequest().getHttpMethod());
        apiRequestVO.setParameters(apiDTO.getRequest().getParameters());
        apiRequestVO.setRequestBody(apiDTO.getRequest().getRequestBody());
        apiRequestVO.setEncrypt(apiDTO.getRequest().getEncrypt());
        apiManageVO.setRequest(apiRequestVO);

        //4、响应信息
        ApiManageVO.ApiResponseVO apiResponseVO = new ApiManageVO.ApiResponseVO();
        apiResponseVO.setHttpCode(apiDTO.getResponse().getHttpCode());
        apiResponseVO.setContentType(apiDTO.getResponse().getContentType());
        apiResponseVO.setHeaders(apiDTO.getResponse().getHeaders());
        apiResponseVO.setContent(apiDTO.getResponse().getContent());
        apiResponseVO.setEncrypt(apiDTO.getResponse().getEncrypt());
        apiManageVO.setResponse(apiResponseVO);

        //6、回调信息
        apiManageVO.setCallbacks(apiDTO.getCallbacks());

        //7、敏感字段+业务订单号
        apiManageVO.setSensitiveVariables(apiDTO.getSensitiveVariables());

        //8、示例
        apiManageVO.setExampleScenes(ApiExampleConverter.INSTANCE.toVOList(apiDTO.getExampleScenes()));
        return apiManageVO;
    }


    private void converApiDTO(ApiManageVO apiManageVO, ApiDTO apiDTO, Boolean isCreate) {
        //1、基础信息
        ApiBasicDTO basic = new ApiBasicDTO();

        if (isCreate) {
            basic.withApiType(ApiTypeEnum.valueOf(apiManageVO.getBasic().getApiType())).withApiGroup(apiManageVO.getBasic().getApiGroup());
        } else {
            apiDTO.setApiId(apiManageVO.getApiId());
            apiDTO.setVersion(apiManageVO.getVersion());
        }
        basic.withName(apiManageVO.getBasic().getName())
                .withTitle(apiManageVO.getBasic().getTitle())
                .withDescription(apiManageVO.getBasic().getDescription());

        basic.setOptionsRule(apiManageVO.getBasic().getOptionsRule());
        apiDTO.setBasic(basic);

        //3、请求信息
        ApiRequestDTO request = new ApiRequestDTO();
        if (isCreate) {
            request.setPath(apiManageVO.getRequest().getPath());
            request.setHttpMethod(apiManageVO.getRequest().getHttpMethod());
        }
        request.setParameters(apiManageVO.getRequest().getParameters());
        request.setRequestBody(apiManageVO.getRequest().getRequestBody());

        request.withHttpMethod(apiManageVO.getRequest().getHttpMethod()).
                withParameters(apiManageVO.getRequest().getParameters()).
                withRequestBody(apiManageVO.getRequest().getRequestBody());
        request.setEncrypt(apiManageVO.getRequest().getEncrypt());
        apiDTO.setRequest(request);

        //4、响应信息
        ApiResponseDTO response = new ApiResponseDTO();
        response.withHttpCode(apiManageVO.getResponse().getHttpCode()).
                withContentType(apiManageVO.getResponse().getContentType()).
                withHeaders(apiManageVO.getResponse().getHeaders()).
                withContent(apiManageVO.getResponse().getContent());
        response.setEncrypt(apiManageVO.getResponse().getEncrypt());
        apiDTO.setResponse(response);

        //6、回调信息
        apiDTO.withCallbacks(apiManageVO.getCallbacks());

        //7、敏感字段+业务订单号
        apiDTO.setSensitiveVariables(apiManageVO.getSensitiveVariables());

        //8、示例
        apiDTO.setExampleScenes(ApiExampleConverter.INSTANCE.toDTOList(apiManageVO.getExampleScenes()));
    }

    @ResponseBody
    @RequestMapping(value = "/commons/op-type", method = RequestMethod.GET)
    public ResponseMessage<List<CommonsVO>> opTypes() {
        return new ResponseMessage("result", operationTypes);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/api-option", method = RequestMethod.GET)
    public ResponseMessage<List<CommonsVO>> apiOptions() {
        return new ResponseMessage("result", apiOptions);
    }

    @ResponseBody
    @RequestMapping(value = "/commons/sys-params", method = RequestMethod.GET)
    public ResponseMessage<List<SystemParameterVO>> apiSysParams() {
        List<String> sysParamsConfig = (List<String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_SYSTEM_PARAMS_CONFIG);
        if (com.yeepay.g3.utils.common.CollectionUtils.isNotEmpty(sysParamsConfig)) {
            List<SystemParameterVO> list = new ArrayList<>(sysParamsConfig.size());
            sysParamsConfig.forEach(param -> {
                String[] items = StringUtils.split(param, ",");
                if (items.length == 3) {
                    list.add(new SystemParameterVO(items[0].trim(), items[1].trim(), items[2].trim()));
                }
            });
            return new ResponseMessage("result", list);
        }
        return new ResponseMessage("result", Collections.emptyList());
    }

    @ResponseBody
    @RequestMapping(value = "/parameters/list", method = RequestMethod.GET)
    public ResponseMessage<List<ParamConfig>> parametersList(@RequestParam(value = "apiId") String apiId) {
        List<ParamConfig> params = apiRequestFacade.findInputParams(apiId);
        return new ResponseMessage("result", params);
    }

    @ResponseBody
    @GetMapping("/response/list")
    public ResponseMessage<ApiResponseModelVO> responseList(@RequestParam(value = "apiId") String apiId) {
        try {
            CheckUtils.notEmpty(apiId, apiId);
            return new ResponseMessage("result", apiResponseService.findOutputParams(apiId));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when query api response parameters with apiId: " + apiId, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/import/analysis", method = RequestMethod.POST)
    public ResponseMessage importAnalysis(@RequestParam("apiGroup") String apiGroup,
                                          @RequestParam("data") MultipartFile data,
                                          @RequestParam("dataFormat") SwaggerDataFormatEnum dataFormat) {
        try {
            CheckUtils.notEmpty(apiGroup, apiGroup);
            CheckUtils.notNull(data.getInputStream(), "data");
            SwaggerAnalysisResult swaggerAnalysisResult = SwaggerAnalyzer.analysis(data.getInputStream(), dataFormat);
            if (swaggerAnalysisResult.getApiGroup() != null && !StringUtils.equals(apiGroup, swaggerAnalysisResult.getApiGroup())) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "swagger中的api分组与提交的api分组不一致");
            }
            List<ImportAnalysisFailedInfo> failedInfos = ImportsUtils.getFailedInfos(swaggerAnalysisResult);
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "swagger解析失败")
                        .put("detail", failedInfos);
            }
            if (MapUtils.isEmpty(swaggerAnalysisResult.getApis())) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "没有有效的Api");
            }
            checkPermission(apiGroup);
            swaggerAnalysisResult.getApis().values().forEach(api -> api.getBasic().setApiGroup(apiGroup));

            failedInfos = ImportsUtils.checkAnalysisResult(swaggerAnalysisResult);
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "导入数据合法性校验失败")
                        .put("detail", failedInfos);
            }
            ApiDependencyAnalysisResult dependencyAnalysisResult = new ApiDependencyAnalysisResult();
            failedInfos = ImportsUtils.apiDependencyAnalysis(new ApiDependencyAnalysisRequest(swaggerAnalysisResult.getApis(), swaggerAnalysisResult.getCallbacks(),
                    swaggerAnalysisResult.getModels()), dependencyAnalysisResult);
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "依赖分析失败")
                        .put("detail", failedInfos);
            }
            List<ApiImportCheckItem> apiImportCheckItems = new ArrayList<>(dependencyAnalysisResult.getDependencyInfos().size());
            dependencyAnalysisResult.getDependencyInfos().forEach(info -> apiImportCheckItems.add(new ApiImportCheckItem()
                    .withApiName(info.getApiName())
                    .withRequestPath(info.getRequestKey().getPath())
                    .withRequestHttpMethod(info.getRequestKey().getHttpMethod())));
            ApiImportCheckResult checkResult = apiMgrFacade.checkImport(new ApiImportCheckRequest()
                    .withApiGroup(apiGroup)
                    .withApis(apiImportCheckItems)
                    .withSpis(MapUtils.isEmpty(dependencyAnalysisResult.getSpis()) ? null : new ArrayList<>(dependencyAnalysisResult.getSpis().keySet()))
                    .withModels(MapUtils.isEmpty(dependencyAnalysisResult.getModels()) ? null : new ArrayList<>(dependencyAnalysisResult.getModels().keySet())));
            failedInfos = ImportsUtils.getImportCheckFailedInfos(checkResult);
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "导入数据与现有数据存在冲突")
                        .put("detail", failedInfos);
            }
            failedInfos = ImportsUtils.checkOverrideConsistence(dependencyAnalysisResult, checkResult, swaggerAnalysisResult.isNativeSwagger());
            if (CollectionUtils.isNotEmpty(failedInfos)) {
                return new ResponseMessage(ResponseMessage.Status.ERROR, "导入数据与现有数据存在冲突")
                        .put("detail", failedInfos);
            }
            String requestId = IdGenerator.generate();
            ApiImportContext context = new ApiImportContext().withSpis(dependencyAnalysisResult.getSpis())
                    .withCallbackApiRelations(swaggerAnalysisResult.getCallbackApiRelations())
                    .withModels(dependencyAnalysisResult.getModels())
                    .withApiGroup(swaggerAnalysisResult.getApiGroup());
            ApiImportAnalysisResult result = new ApiImportAnalysisResult().withRequestId(requestId);
            doAnalysis(dependencyAnalysisResult, checkResult, context, result);
            importCacheService.storeContext(requestId, context);
            return new ResponseMessage("result", result);
        } catch (Throwable ex) {
            LOGGER.error("api import data analysis failed.", ex);
            return new ResponseMessage(ex);
        }
    }

    private void checkPermission(String apiGroup) {
        if (ShiroUtils.isPlatformOperator()) {
            return;
        }
        ShiroRealm.ShiroUser user = ShiroUtils.getShiroUser();
        if (CollectionUtils.isNotEmpty(user.getApiGroupScopes()) && user.getApiGroupScopes().contains(apiGroup)) {
            return;
        }
        throw new YeepayRuntimeException("no permission for apiGroup:{0}.", apiGroup);
    }

    private void doAnalysis(ApiDependencyAnalysisResult dependencyAnalysisResult, ApiImportCheckResult checkResult, ApiImportContext context, ApiImportAnalysisResult result) {
        result.withUnusedSpis(CollectionUtils.isEmpty(dependencyAnalysisResult.getUnusedSpis()) ? null : dependencyAnalysisResult.getUnusedSpis())
                .withUnusedModels(CollectionUtils.isEmpty(dependencyAnalysisResult.getUnusedModels()) ? null : dependencyAnalysisResult.getUnusedModels());
        if (CollectionUtils.isNotEmpty(checkResult.getModelsToCreate())) {
            checkResult.getModelsToCreate().forEach(modelName -> {
                ModelDTO model = context.getModels().get(modelName);
                result.addModelsToCreateItem(new ModelCreateCheckItem().withModel(model));
            });
        }
        if (MapUtils.isNotEmpty(checkResult.getModelsToOverride())) {
            checkResult.getModelsToOverride().forEach((modelName, srcModel) -> {
                ModelDTO model = context.getModels().get(modelName);
                model.setId(srcModel.getId());
                model.setVersion(srcModel.getVersion());
                if (ImportCompareUtils.isEqualModel(srcModel, model)) {
                    result.addModelsToIgnoreItem(modelName);
                    context.getModels().remove(modelName);
                } else {
                    result.addModelsToOverrideItem(new ModelOverrideCheckItem().withModel(model).withSrcModel(srcModel));
                }
            });
        }
        if (CollectionUtils.isNotEmpty(checkResult.getSpisToCreate())) {
            checkResult.getSpisToCreate().forEach(spiName -> {
                SpiDTO spi = context.getSpis().get(spiName);
                result.addSpisToCreateItem(new SpiCreateCheckItem().withSpi(spi));
            });
        }
        if (MapUtils.isNotEmpty(checkResult.getSpisToOverride())) {
            checkResult.getSpisToOverride().forEach((spiName, srcSpi) -> {
                SpiDTO spi = context.getSpis().get(spiName);
                spi.setId(srcSpi.getId());
                spi.setVersion(srcSpi.getVersion());
                // spi本身没有变动，并且model也没有任何变动的，才忽略
                if (ImportCompareUtils.isEqualSpi(spi, srcSpi)
                        && CollectionUtils.isNotEmpty(result.getModelsToIgnore())
                        && CollectionUtils.containsAll(result.getModelsToIgnore(),
                        spi.getDirectedRefModels()
                                .stream()
                                .map(modelName -> spi.getBasic().getApiGroup() + "." + modelName)
                                .collect(Collectors.toList()))) {
                    result.addSpisToIgnoreItem(spiName);
                    context.getSpis().remove(spiName);
                } else {
                    result.addSpisToOverrideItem(new SpiOverrideCheckItem().withSpi(spi)
                            .withSrcSpi(srcSpi));
                }
            });
        }
        dependencyAnalysisResult.getDependencyInfos().forEach(dependencyInfo -> {
            List<String> spisToCreate = null;
            List<String> spisToOverride = null;
            if (CollectionUtils.isNotEmpty(dependencyInfo.getSpis())) {
                spisToCreate = new ArrayList<>();
                spisToOverride = new ArrayList<>();
                for (String spiName : dependencyInfo.getSpis()) {
                    if (checkResult.getSpisToOverride() != null && checkResult.getSpisToOverride().containsKey(spiName)) {
                        // 有变更的spi才覆盖
                        if (CollectionUtils.isEmpty(result.getSpisToIgnore()) || !result.getSpisToIgnore().contains(spiName)) {
                            spisToOverride.add(spiName);
                        }
                    } else {
                        spisToCreate.add(spiName);
                    }
                }
            }

            List<String> modelsToCreate = null;
            List<String> modelsToOverride = null;
            if (CollectionUtils.isNotEmpty(dependencyInfo.getRefModels())) {
                modelsToCreate = new ArrayList<>();
                modelsToOverride = new ArrayList<>();
                for (String refModel : dependencyInfo.getRefModels()) {
                    if (checkResult.getModelsToOverride() != null && checkResult.getModelsToOverride().containsKey(refModel)) {
                        // 有变更的model才覆盖
                        if (CollectionUtils.isEmpty(result.getModelsToIgnore()) || !result.getModelsToIgnore().contains(refModel)) {
                            modelsToOverride.add(refModel);
                        }
                    } else {
                        modelsToCreate.add(refModel);
                    }
                }
            }
            ApiRequestKey requestKey = dependencyInfo.getRequestKey();
            ApiDTO api = dependencyAnalysisResult.getApis().get(requestKey);
            ApiDTO srcApi = checkResult.getApisToOverride() == null ? null : checkResult.getApisToOverride().get(requestKey);
            if (srcApi == null) {
                result.addApisToCreateItem(new ApiCreateCheckItem().withApi(converApiManageVO(api)));
                context.addApisToCreateItem(new ApiImportItem()
                        .withApi(api)
                        .withSpisToCreate(spisToCreate)
                        .withSpisToOverride(spisToOverride)
                        .withModelsToCreate(modelsToCreate)
                        .withModelsToOverride(modelsToOverride));
            } else {
                // 模型没有变动，spi没有变动，并且api本身也没有变动的才忽略
                if (CollectionUtils.isEmpty(modelsToCreate)
                        && CollectionUtils.isEmpty(modelsToOverride)
                        && CollectionUtils.isEmpty(spisToCreate)
                        && CollectionUtils.isEmpty(spisToOverride)
                        && ImportCompareUtils.isEqualApi(srcApi, api)) {
                    result.addApisToIgnoreItem(ApiRequestKey.getInstance(api.getRequest()));
                } else {
                    api.setApiId(srcApi.getApiId());
                    api.setVersion(srcApi.getVersion());
                    result.addApisToOverrideItem(new ApiOverrideCheckItem().withApi(converApiManageVO(api)).withSrcApi(converApiManageVO(srcApi)));
                    context.addApiToOverrideItem(new ApiImportItem()
                            .withApi(api)
                            .withSpisToCreate(spisToCreate)
                            .withSpisToOverride(spisToOverride)
                            .withModelsToCreate(modelsToCreate)
                            .withModelsToOverride(modelsToOverride));
                }
            }

        });

    }

    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage doImport(@RequestBody ApisImportRequest request) {
        Throwable exception = null;
        try {
            CheckUtils.notEmpty(request.getRequestId(), "request.requestId");
            ApiImportContext context = importCacheService.loadContext(request.getRequestId());
            if (context == null) {
                throw new YeepayRuntimeException("ImportContext for requestId[{0}] not exist.", request.getRequestId());
            }
            checkPermission(context.getApiGroup());
            Set<String> successImportSpis = Sets.newHashSet();
            Map<String, ImportFailedInfo> failedImportSpis = Maps.newHashMap();
            Set<String> successImportModels = Sets.newHashSet();
            Map<String, ImportFailedInfo> failedImportModels = Maps.newHashMap();

            ApisImportResult result = new ApisImportResult();
            result.setTotal(CollectionUtils.size(context.getApisToCreate()) + CollectionUtils.size(request.getApisToOverride()));
            if (CollectionUtils.isNotEmpty(context.getApisToCreate())) {
                context.getApisToCreate().forEach(item -> doHandleImportItem(item, ImportOperationEnum.CREATE, request,
                        context, successImportSpis, failedImportSpis, successImportModels, failedImportModels, result));
            }
            if (CollectionUtils.isNotEmpty(context.getApisToOverride()) && CollectionUtils.isNotEmpty(request.getApisToOverride())) {
                List<ApiImportItem> targetApisToOverride = context.getApisToOverride().stream()
                        .filter(item -> request.getApisToOverride().contains(new ApiRequestKey().withPath(item.getApi().getRequest().getPath())
                                .withHttpMethod(item.getApi().getRequest().getHttpMethod())))
                        .collect(Collectors.toList());
                targetApisToOverride.forEach(item -> doHandleImportItem(item, ImportOperationEnum.OVERRIDE, request,
                        context, successImportSpis, failedImportSpis, successImportModels, failedImportModels, result));
            }
            return new ResponseMessage().put("result", result);
        } catch (Throwable ex) {
            LOGGER.error("api import failed.", ex);
            exception = ex;
            return new ResponseMessage(ex);
        } finally {
            if (exception == null) {
                importCacheService.evictContext(request.getRequestId());
            }
        }
    }

    private void doHandleImportItem(ApiImportItem item,
                                    ImportOperationEnum operation,
                                    ApisImportRequest request,
                                    ApiImportContext context,
                                    Set<String> successImportSpis,
                                    Map<String, ImportFailedInfo> failedImportSpis,
                                    Set<String> successImportModels,
                                    Map<String, ImportFailedInfo> failedImportModels,
                                    ApisImportResult result) {
        ApiRequestKey requestKey = new ApiRequestKey().withPath(item.getApi().getRequest().getPath())
                .withHttpMethod(item.getApi().getRequest().getHttpMethod());
        ApiImportRequest apiImportRequest = new ApiImportRequest().withApi(item.getApi())
                .withImportOperation(operation)
                .withOperationInfo(new OperationInfo().withOperator(ShiroUtils.getOperatorCode()).withCause(null));
        //1. 查看请求中是否包含已经失败的spi或模型
        ImportFailedInfo existSpiFailedInfo = ImportsUtils.findFailedImportItem(item.getSpisToCreate(), item.getSpisToOverride(), failedImportSpis);
        if (existSpiFailedInfo != null) {
            result.addFailedDetail(new ApiImportFailedInfo().withKey(requestKey).withReason(existSpiFailedInfo.toString()));
            return;
        }
        ImportFailedInfo existModelFailedInfo = ImportsUtils.findFailedImportItem(item.getModelsToCreate(), item.getModelsToOverride(), failedImportModels);
        if (existModelFailedInfo != null) {
            result.addFailedDetail(new ApiImportFailedInfo().withKey(requestKey).withReason(existModelFailedInfo.toString()));
            return;
        }
        //2. 过滤用户确认不覆盖的SPI、模型和已经导入成功的SPI、模型
        apiImportRequest
                .withSpis(ImportsUtils.filterImportItems(item.getSpisToCreate(),
                        item.getSpisToOverride(),
                        request.getSpisToOverride(),
                        successImportSpis,
                        context.getSpis()))
                .withModels(ImportsUtils.filterImportItems(item.getModelsToCreate(),
                        item.getModelsToOverride(),
                        request.getModelsToOverride(),
                        successImportModels,
                        context.getModels()));
        Map<String, List<ApiRequestKey>> callbackApiRelations = context.getCallbackApiRelations();
        // 没有api-spi的关联关系就不设置
        if (MapUtils.isNotEmpty(callbackApiRelations)) {
            apiImportRequest.setSpiApiRelations(callbackApiRelations);
        }

        //3. 执行导入
        ApiImportResult importResult = apiMgrFacade.importApi(apiImportRequest);
        if (StringUtils.equalsIgnoreCase(importResult.getStatus(), "success")) {
            //3. 导入成功记录成功结果，同时缓存导入成功的spis和models
            result.increaseSuccess();
            if (MapUtils.isNotEmpty(apiImportRequest.getSpis())) {
                saveSuccessImportSpis(successImportSpis, apiImportRequest);
            }
            if (MapUtils.isNotEmpty(apiImportRequest.getModels())) {
                saveSuccessImportModels(successImportModels, apiImportRequest);
            }

        } else {
            //4. 导入失败记录记录失败结果，同时缓存导入失败信息
            result.addFailedDetail(new ApiImportFailedInfo().withKey(requestKey)
                    .withReason(importResult.getFailedInfo().toString()));
            ImportFailedInfo importFailedInfo = importResult.getFailedInfo();
            if (importFailedInfo.getItem() == ImportItemEnum.SPI) {
                failedImportSpis.put(importFailedInfo.getValue(), importFailedInfo);
            }
            if (importFailedInfo.getItem() == ImportItemEnum.MODEL) {
                failedImportModels.put(importFailedInfo.getValue(), importFailedInfo);
            }
        }
    }

    private void saveSuccessImportSpis(Set<String> successImportSpis, ApiImportRequest apiImportRequest) {
        apiImportRequest.getSpis().values().forEach(spis -> spis.forEach(spi -> successImportSpis.add(spi.getBasic().getName())));
    }

    private void saveSuccessImportModels(Set<String> successImportModels, ApiImportRequest apiImportRequest) {
        apiImportRequest.getModels().values().forEach(models -> models.forEach(model -> successImportModels.add(model.getName())));
    }

    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public ResponseEntity<byte[]> export(@RequestParam("apiGroup") String apiGroup,
                                         @RequestParam("api") String[] apis,
                                         @RequestParam("dataFormat") SwaggerDataFormatEnum datFormat) {
        try {
            CheckUtils.notEmpty(apis, "api");
            ApiExportResult result = apiMgrFacade.exportApi(new ApiExportRequest().withApiGroup(apiGroup).withDataFormat(datFormat)
                    .withApis(Lists.newArrayList(apis)));
            String fileName = "swagger." + datFormat.getValue().toLowerCase();
            byte[] data = result.getData().getBytes(StandardCharsets.UTF_8);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            return new ResponseEntity<>(data, headers, HttpStatus.OK);
        } catch (Exception ex) {
            LOGGER.error("export api failed.", ex);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_HTML);
            return new ResponseEntity<>(Exceptions.getRootCause(ex).getMessage().getBytes(StandardCharsets.UTF_8), headers,
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostConstruct
    public void init() {
        operationTypes = new ArrayList<>(ApiOperationTypeEnum.values().length);
        for (ApiOperationTypeEnum type : ApiOperationTypeEnum.values()) {
            operationTypes.add(new CommonsVO(type.getValue(), type.getDisplayName()));
        }
        apiOptions = new ArrayList<>(ApiOptionEnum.values().length);
        for (ApiOptionEnum type : ApiOptionEnum.values()) {
            apiOptions.add(new CommonsVO(type.getValue(), type.getDisplayName()));
        }
    }

    @ResponseBody
    @RequestMapping(value = "error-code/list", method = RequestMethod.GET)
    public ResponseMessage relatedErrcodeList(ApiErrcodePageQueryParam pageQueryParam) {
        PageQueryResult<ApiErrcodePageItem> errcodeApis = apiErrcodeService.findRelatedErrcodePage(pageQueryParam);
        return new ResponseMessage("page", errcodeApis);
    }

    @ResponseBody
    @RequestMapping(value = "error-code/list-for-join", method = RequestMethod.GET)
    public ResponseMessage unRelatedErrcodeList(ApiErrcodePageQueryParam pageQueryParam) {
        PageQueryResult<ApiErrcodePageItem> errcodeApis = apiErrcodeService.findUnRelatedErrcodePage(pageQueryParam);
        return new ResponseMessage("page", errcodeApis);
    }

    @ResponseBody
    @RequestMapping(value = "error-code/batch-create", method = RequestMethod.POST)
    public ResponseMessage batchCreateRelatedApi(@RequestBody ApiErrcodeBatchAddDTO batchAdd) {
        apiErrcodeFacade.batchCreate(batchAdd.getErrcodeIds().stream().map(errcodeId -> {
            final ApiErrcodeDTO apiErrcode = new ApiErrcodeDTO();
            apiErrcode.setApiId(batchAdd.getApiId());
            apiErrcode.setErrcodeId(errcodeId);
            return apiErrcode;
        }).collect(Collectors.toList()));
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/sub-ref/list", method = RequestMethod.GET)
    public ResponseMessage<List<ApiManagePageItem>> oldRefList(@RequestParam("apiId") String apiId) {
        List<ApiManagePageItem> apis = apiManageQueryService.subRefList(apiId);
        return new ResponseMessage("result", apis);
    }

    @RequestMapping(value = "/security-req/update", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage updateApiSecurityReq(@RequestBody SecurityReqChangeVo apiSecurityReq) {
        CheckUtils.notNull(apiSecurityReq, "apiSecurityReq");
        CheckUtils.notNull(apiSecurityReq.getApiId(), "apiSecurityReq.apiId");

        if (null == apiSecurityReq.getSecurityReqVersion()) {
            apiSecurityReq.setSecurityReqVersion(0L);
        }
        ApiDTO api = apiMgrFacade.find(apiSecurityReq.getApiId());
        CheckUtils.notNull(api, "该api不存在！");
        checkUserPerm(api.getBasic().getApiGroup());

        SecurityReqChangeDTO securityReqChange = new SecurityReqChangeDTO();
        securityReqChange.setType(SecurityReqTypeEnum.API);
        securityReqChange.setValue(apiSecurityReq.getApiId());
        if (CollectionUtils.isNotEmpty(apiSecurityReq.getSecurities())) {
            securityReqChange.setData(apiSecurityReq.getSecurities().stream().map(vo -> {
                SecurityReqDTO dto = new SecurityReqDTO();
                dto.setName(vo.getName());
                dto.setScopes(vo.getScopes());
                dto.setExtensions(vo.getExtensions());
                dto.setVersion(apiSecurityReq.getSecurityReqVersion());
                return dto;
            }).collect(Collectors.toList()));
        }
        securityReqMgrFacade.update(securityReqChange);
        return new ResponseMessage("result", true);
    }

    private void checkUserPerm(String apiGroup) {
        List<String> apiGroupCodes = apiGroupQueryService.listApiGroupCode();
        if (ShiroUtils.isSpOperator() && (CollectionUtils.isEmpty(apiGroupCodes) || apiGroupCodes.stream().noneMatch(item -> item.equals(apiGroup)))) {
            throw new IllegalArgumentException("无权限操作该分组，" + apiGroup);
        }
    }

    @RequestMapping(value = "/security-req", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage findSecurityReq(@RequestParam(value = "apiGroupCode") String apiGroupCode,
                                           @RequestParam(value = "apiId", required = false) String apiId) {
        CheckUtils.notEmpty(apiGroupCode, "apiGroupCode");
        checkUserPerm(apiGroupCode);

        Map<String, SecurityReqChangeVo> result = Maps.newHashMapWithExpectedSize(2);

        List<String> paramList = StringUtils.isNotBlank(apiId) ?
                Lists.newArrayList(apiId, apiGroupCode) : Lists.newArrayList(apiGroupCode);
        Map<String, List<SecurityReqDTO>> securityReqMap = securityReqQueryFacade.findByValues(paramList);
        SecurityReqChangeVo apiSecurity = null, apiGroupSecurity = null;
        if (MapUtils.isNotEmpty(securityReqMap)) {
            apiSecurity = SecurityUtils.toSecurityReqVo(securityReqMap.get(apiId));
            apiGroupSecurity = SecurityUtils.toSecurityReqVo(securityReqMap.get(apiGroupCode));
        }
        result.put("apiSecurity", apiSecurity);
        result.put("apiGroupSecurity", apiGroupSecurity);
        return new ResponseMessage("result", result);
    }

    @RequestMapping(value = "/option/detail", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage<List<ApiOption>> optionDetail(@RequestParam(value = "apiId") String apiId) {
        CheckUtils.notEmpty(apiId, "apiId");
        final ApiDTO api = apiMgrFacade.find(apiId);
        List<ApiOption> apiOptions = Collections.emptyList();
        if (null != api && null != api.getBasic()) {
            apiOptions = api.getBasic().getOptionsRule();
        }
        if (CollectionUtils.isEmpty(apiOptions)) {
            apiOptions = ApiOptionEnum.defaultApiOptions();
        }
        return new ResponseMessage<>("result", apiOptions);
    }

    @RequestMapping(value = "/option/update", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage<Boolean> optionUpdate(@RequestBody ApiOptionUpdateRequest request) {
        CheckUtils.notEmpty(request.getApiId(), "apiId");
        apiMgrFacade.updateApiOption(request);
        return new ResponseMessage<>("result", true);
    }


    @RequestMapping(value = "/request-param/list", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage<List<ApiParam>> findRequestParamList(@RequestParam(value = "apiId") String apiId,
                                                            @RequestParam(value = "depth", defaultValue = "1") Integer depth) {
        CheckUtils.notEmpty(apiId, "apiId");
        final List<ApiParam> requestParams = apiMgrFacade.findRequestParams(apiId, depth);
        return new ResponseMessage<>("result", requestParams);
    }


    @RequestMapping(value = "/response-param/list", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage<List<ApiParam>> findResponseParamList(@RequestParam(value = "apiId") String apiId,
                                                            @RequestParam(value = "depth", defaultValue = "1") Integer depth) {
        CheckUtils.notEmpty(apiId, "apiId");
        final List<ApiParam> requestParams = apiMgrFacade.findResponseParams(apiId, depth);
        return new ResponseMessage<>("result", requestParams);
    }

    @RequestMapping(value = "/option-doc/preview", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage<String> previewOptionsDoc(@RequestBody ApiOptionPreviewRequest request) {
        CheckUtils.notNull(request, "request");
        CheckUtils.notEmpty(request.getApiId(), "apiId");
        CheckUtils.notNull(request.getApiOption(), "apiOption");
        return new ResponseMessage<>("result", apiDocFacade.previewApiOption(request));
    }

    @RequestMapping(value = "/example/detail", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage<Object> exampleDetail(@RequestParam(value = "apiId") String apiId) {
        CheckUtils.notEmpty(apiId, "apiId");
        // 先查询，没有则生成默认示例，名称、描述均为空
        return new ResponseMessage<>("result", apiExampleMgrFacade.findOrInitByApiId(apiId));
    }

    @ResponseBody
    @RequestMapping(value = "/example/init", method = RequestMethod.POST)
    public ResponseMessage<Object> iniExample(@RequestBody ApiManageVO apiManageVO) {
        // 基于参数生成默认示例
        ApiDTO apiDTO = new ApiDTO();
        converApiDTO(apiManageVO, apiDTO, true);
        List<ModelDTO> modelDTOList;
        if (apiManageVO.isCreatedByExitedInterface()) {
            modelDTOList = classLoaderService.buildModel(apiManageVO);
        } else {
            modelDTOList = Collections.emptyList();
        }
        ApiExampleSceneInitDTO apiExampleSceneInit = new ApiExampleSceneInitDTO();
        apiExampleSceneInit.setApiDTO(apiDTO);
        apiExampleSceneInit.setModels(modelDTOList);
        return new ResponseMessage<>("result", apiExampleMgrFacade.initApiExample(apiExampleSceneInit));
    }

    @RequestMapping(value = "/example/update", method = RequestMethod.POST)
    @ResponseBody
    public ResponseMessage<Object> exampleUpdate(@RequestBody ApiExampleSceneVO body) {
        apiExampleMgrFacade.update(ApiExampleConverter.INSTANCE.toDTO(body));
        return new ResponseMessage<>("result", true);
    }

    @Autowired
    public void setClassLoaderService(ClassLoaderService classLoaderService) {
        this.classLoaderService = classLoaderService;
    }
}
