package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.vo.AclResourceDisableResonRequestVO;
import com.yeepay.g3.app.yop.portal.vo.AclResourceRequestParamVO;
import com.yeepay.g3.app.yop.portal.vo.AclResouseDisableResonInfo;
import com.yeepay.g3.core.yop.utils.bean.BeanConvertUtils;
import com.yeepay.g3.facade.yop.perm.dto.*;
import com.yeepay.g3.facade.yop.perm.enums.RoleTypeEnum;
import com.yeepay.g3.facade.yop.perm.facade.AclPrivilegeMgrFacade;
import com.yeepay.g3.facade.yop.perm.facade.AclResourceMgrFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  上午11:46
 */

@Controller
@RequestMapping("/rest/acl/resource")
public class AclResourceMgrController {

    private AclResourceMgrFacade aclResourceMgrFacade = RemoteServiceFactory.getService(AclResourceMgrFacade.class);

    private AclPrivilegeMgrFacade aclPrivilegeMgrFacade = RemoteServiceFactory.getService(AclPrivilegeMgrFacade.class);

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage queryList() {
        List<AclResourceTreeDTO> aclResourceTreeDTOList = aclResourceMgrFacade.findTree();
        return new ResponseMessage("result", aclResourceTreeDTOList);
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage queryResourceDetail(@RequestParam Long id) {
        AclResourceDetailDTO aclResource = aclResourceMgrFacade.findDetail(id);
        return new ResponseMessage("result", aclResource);
    }

    @ResponseBody
    @RequestMapping(value = "/filter/list", method = RequestMethod.GET)
    public ResponseMessage queryResourceDetail(@RequestParam String url) {
        AclFilterChainResponse filter = aclResourceMgrFacade.findFilterChainByUrl(url);
        return new ResponseMessage("result", filter);
    }

    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody AclResourceRequestParamVO aclResource) {
        AclResourceDTO aclResourceDTO = BeanConvertUtils.convert(aclResource, AclResourceDTO.class);
        Long id = aclResourceMgrFacade.create(aclResourceDTO, aclResource.getpId());
        return new ResponseMessage("result", id);
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage batchDelete(@RequestParam List<Long> ids) {
        aclResourceMgrFacade.batchDelete(ids);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/commons/disable-reason/list", method = RequestMethod.GET)
    public ResponseMessage queryDisableReason() {
        List<String> disableReasons = (List<String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_ACL_DISABLE_REASON);
        return new ResponseMessage("result", disableReasons);
    }

    @ResponseBody
    @RequestMapping(value = "/disable", method = RequestMethod.POST)
    public ResponseMessage batchDisable(@RequestBody AclResourceDisableResonRequestVO aclResourceDisableReson) {
        List<String> failResources = aclResourceMgrFacade.batchDisable(aclResourceDisableReson.getIds(), aclResourceDisableReson.getDisableReason());
        return new ResponseMessage("result", failResources);
    }

    @ResponseBody
    @RequestMapping(value = "/batch-disable-reason", method = RequestMethod.GET)
    public ResponseMessage batchDisable(@RequestParam List<Long> ids) {
        Map<String, String> map = aclResourceMgrFacade.findDisableReason(ids);
        List<AclResouseDisableResonInfo> disableResonInfos = new ArrayList<>();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            AclResouseDisableResonInfo disableResonInfo = new AclResouseDisableResonInfo();
            disableResonInfo.setName(entry.getKey());
            disableResonInfo.setDisableReason(entry.getValue());
            disableResonInfos.add(disableResonInfo);
        }
        return new ResponseMessage("result", disableResonInfos);
    }

    @ResponseBody
    @RequestMapping(value = "/enable", method = RequestMethod.POST)
    public ResponseMessage batchEnable(@RequestParam List<Long> ids) {
        List<String> failResources = aclResourceMgrFacade.batchEnable(ids);
        return new ResponseMessage("result", failResources);
    }

    @ResponseBody
    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    public ResponseMessage batchEnable(@RequestBody AclResourceDTO aclResource) {
        aclResourceMgrFacade.update(aclResource);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/drag", method = RequestMethod.POST)
    public ResponseMessage batchEnable(@RequestBody ResourceDragRequest dragVO) {
        aclResourceMgrFacade.dragResource(dragVO);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/by-role-type", method = RequestMethod.GET)
    public ResponseMessage queryByRoleType(@RequestParam RoleTypeEnum roleType) {
        List<AclResourceAuthTreeDTO> resourceTree = aclPrivilegeMgrFacade.findAclResourceTreeByRoleType(roleType);
        return new ResponseMessage("result", resourceTree);
    }
}
