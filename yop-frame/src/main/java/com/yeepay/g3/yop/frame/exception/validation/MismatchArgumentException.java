package com.yeepay.g3.yop.frame.exception.validation;

import com.yeepay.g3.yop.frame.protocol.response.yop.v300rc1.exceptions.StatusEnum;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR> @version 1.0.0
 * @since 18/1/25 下午6:18
 */
public class MismatchArgumentException extends ValidationException {

    public MismatchArgumentException(String argumentName) {
        super(argumentName, "");
    }

    @Override
    public String getCode() {
        return super.getCode() + ".mismatch";
    }

    @Override
    public StatusEnum getStatus() {
        return StatusEnum.ILLEGAL_ARGUMENT;
    }
}

