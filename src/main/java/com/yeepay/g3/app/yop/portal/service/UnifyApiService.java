/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.ApiListItemVO;
import com.yeepay.g3.app.yop.portal.vo.ApiQueryParamVO;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.UnifyApiPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.UnifyApiPageQueryParam;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 15:07
 */
public interface UnifyApiService {

    PageQueryResult<UnifyApiPageItem> findApisPage(UnifyApiPageQueryParam param);

    List<ApiListItemVO> findApis(ApiQueryParamVO param);
}
