/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import com.yeepay.g3.app.yop.portal.vo.NotifySendRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.PageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.notify.NotifySendRecordVO;
import com.yeepay.g3.utils.common.CollectionUtils;
import com.yeepay.g3.utils.common.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/29 17:53
 */
public class NotifySendRecordQueryServiceTest extends SpringBootTests {
    @Autowired
    private NotifySendRecordQueryService notifySendRecordQueryService;

    /**
     * 全量发送记录查询
     */
    @Test
    public void testListNotifyOrder() throws ParseException {
        Date defaultNotifyEndDate = DateUtils.parseDate("2023-06-29 23:59:59", "yyyy-MM-dd HH:mm:ss");
        Date defaultNotifyStartDate = DateUtils.parseDate("2023-06-29 00:00:00", "yyyy-MM-dd HH:mm:ss");
        String customerNo = "gpt:13130247";
        List<NotifySendRecordVO> notifySendRecordVOList = notifySendRecordQueryService.listSendRecord(NotifySendRecordQueryParam.builder().notifyStartDate(defaultNotifyStartDate).notifyEndDate(defaultNotifyEndDate).build(), PageQueryParam.builder().build()).getItems();
        Assert.assertTrue(CollectionUtils.isNotEmpty(notifySendRecordVOList));
        Assert.assertEquals(20, notifySendRecordVOList.size());

        notifySendRecordVOList = notifySendRecordQueryService.listSendRecord(NotifySendRecordQueryParam.builder().notifyStartDate(defaultNotifyStartDate).notifyEndDate(defaultNotifyEndDate).customerNo(customerNo).build(), PageQueryParam.builder().build()).getItems();
        Assert.assertEquals("SUCCESS", notifySendRecordVOList.get(0).getStatus().getValue());
    }
}
