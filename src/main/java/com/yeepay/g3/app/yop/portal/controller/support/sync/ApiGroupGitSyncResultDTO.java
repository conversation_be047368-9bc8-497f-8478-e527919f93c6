package com.yeepay.g3.app.yop.portal.controller.support.sync;

import java.io.Serializable;

/**
 * title: API分组git同步结果<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-13 15:54
 */
public class ApiGroupGitSyncResultDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    private GitSyncStatics apiSyncResult;

    private GitSyncStatics spiSyncResult;

    private GitSyncStatics modelSyncResult;

    public GitSyncStatics getApiSyncResult() {
        return apiSyncResult;
    }

    public void setApiSyncResult(GitSyncStatics apiSyncResult) {
        this.apiSyncResult = apiSyncResult;
    }

    public GitSyncStatics getSpiSyncResult() {
        return spiSyncResult;
    }

    public void setSpiSyncResult(GitSyncStatics spiSyncResult) {
        this.spiSyncResult = spiSyncResult;
    }

    public GitSyncStatics getModelSyncResult() {
        return modelSyncResult;
    }

    public void setModelSyncResult(GitSyncStatics modelSyncResult) {
        this.modelSyncResult = modelSyncResult;
    }
}
