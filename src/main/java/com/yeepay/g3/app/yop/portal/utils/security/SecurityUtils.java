package com.yeepay.g3.app.yop.portal.utils.security;

import com.yeepay.g3.app.yop.portal.vo.NewSecurityReqVO;
import com.yeepay.g3.app.yop.portal.vo.SecurityReqChangeVo;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqDTO;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * title: <br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/16 18:43
 */
public class SecurityUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(SecurityUtils.class);

    public static SecurityReqChangeVo toSecurityReqVo(List<SecurityReqDTO> securityReqs) {
        if (CollectionUtils.isNotEmpty(securityReqs)) {
            SecurityReqChangeVo securityReq = new SecurityReqChangeVo();
            long securityReqVersion = 0L;
            List<NewSecurityReqVO> securities = new ArrayList<>(securityReqs.size());
            for (SecurityReqDTO securityReqDTO : securityReqs) {
                NewSecurityReqVO securityReqVo = new NewSecurityReqVO();
                securityReqVo.setName(securityReqDTO.getName());
                securityReqVo.setScopes(securityReqDTO.getScopes());
                securityReqVo.setExtensions(securityReqDTO.getExtensions());
                securities.add(securityReqVo);
                if (null != securityReqDTO.getVersion() && securityReqDTO.getVersion() > securityReqVersion) {
                    securityReqVersion = securityReqDTO.getVersion();
                }
            }
            securityReq.setSecurityReqVersion(securityReqVersion);
            securityReq.setSecurities(securities);
            return securityReq;
        }
        return null;
    }
}
