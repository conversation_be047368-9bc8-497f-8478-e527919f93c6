/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.enums;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/7/22 下午5:34
 */
public enum DataMaskEnum {
    PASSWORD("PASSWORD") {
        @Override
        public String doSensitive(String string) {
            return DEFAULT_SENSITIVE_STRING;
        }
    },
    EMAIL("EMAIL") {
        @Override
        public String doSensitive(String string) {
            int index = string.indexOf("@");
            if (index > -1) {
                return StringUtils.truncate(string, 0, 3) + getSensitiveString(3) + StringUtils.truncate(string, index, string.length() - index);
            } else {
                return DEFAULT_SENSITIVE_STRING;
            }
        }
    },
    MOBILE("MOBILE") {
        @Override
        public String doSensitive(String string) {
            if (string.startsWith("+86")) {// 大陆
                return StringUtils.truncate(string, 0, 5) + getSensitiveString(string.length() - 9) + StringUtils.truncate(string, string.length() - 4, 4);
            } else if (string.startsWith("+852") || string.startsWith("+853")) {// 香港澳门
                return StringUtils.truncate(string, 0, 6) + getSensitiveString(string.length() - 8) + StringUtils.truncate(string, string.length() - 2, 2);
            } else if (string.startsWith("+886")) {// 台湾
                return StringUtils.truncate(string, 0, 6) + getSensitiveString(string.length() - 9) + StringUtils.truncate(string, string.length() - 3, 3);
            } else {// 默认
                int i = string.length() / 3;
                return StringUtils.truncate(string, 0, i) + getSensitiveString(string.length() - 2 * i) + StringUtils.truncate(string, string.length() - i, i);
            }
        }
    },
    IDCARD("IDCARD") {
        @Override
        public String doSensitive(String string) {
            int length = string.length();
            if (length > 10) {
                return StringUtils.truncate(string, 0, 1) + getSensitiveString(length - 2) + StringUtils.truncate(string, length - 1, 1);
            } else {
                return DEFAULT_SENSITIVE_STRING;
            }
        }
    },
    BANKCARD("BANKCARD") {
        @Override
        public String doSensitive(String string) {
            final int length = string.length();
            if (length > 10) {
                return StringUtils.truncate(string, 0, 6) + getSensitiveString(length - 10) + StringUtils.truncate(string, length - 4, 4);
            } else {
                return DEFAULT_SENSITIVE_STRING;
            }
        }
    },
    CVV("CVV") {
        @Override
        public String doSensitive(String string) {
            return getSensitiveString(string.length());
        }
    },;

    public final String sensitive(String string) {
        if (StringUtils.isBlank(string)) {
            return "";
        }
        try {
            return doSensitive(StringUtils.trim(string));
        } catch (Throwable e) {
            LOGGER.error("sensitive invoke log error!", e);
        }
        return DEFAULT_SENSITIVE_STRING;
    }

    protected String doSensitive(String string) {
        return string;
    }

    private static final String getSensitiveString(int length) {
        char[] c = new char[length];
        for (int i = 0; i < length; i++) {
            c[i] = SENSITIVE_CHAR;
        }
        return new String(c);
    }

    private static final Logger LOGGER = LoggerFactory.getLogger(DataMaskEnum.class);
    private static final String DEFAULT_SENSITIVE_STRING = "********";
    private static final char SENSITIVE_CHAR = '*';

    private String value;

    private static final Map<String, DataMaskEnum> VALUE_MAP;

    static {
        VALUE_MAP = new HashMap<>();
        for (DataMaskEnum DataMaskEnum : DataMaskEnum.values()) {
            VALUE_MAP.put(DataMaskEnum.getValue(), DataMaskEnum);
        }
    }

    DataMaskEnum(String value) {
        this.value = value;
    }

    public static Map<String, DataMaskEnum> getValueMap() {
        return VALUE_MAP;
    }

    public static DataMaskEnum parse(String value) {
        return VALUE_MAP.get(value);
    }

    public String getValue() {
        return value;
    }

}
