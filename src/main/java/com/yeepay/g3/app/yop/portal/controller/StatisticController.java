/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.exception.YopPortalException;
import com.yeepay.g3.app.yop.portal.service.ApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.service.IspMgrQueryService;
import com.yeepay.g3.app.yop.portal.service.StatisticInvokeTimesApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.vo.ApiGroupVO;
import com.yeepay.g3.app.yop.portal.vo.SpCodeVO;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.StatisticInvokeTimeApiGroupItem;
import com.yeepay.g3.app.yop.portal.vo.page.StatisticInvokeTimesApiGroupQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.enumtype.StatisticTypeEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/rest/invoke-stat")
public class StatisticController {

    private static Logger LOGGER = LoggerFactory.getLogger(StatisticController.class);

    private static final String PATTERN = "yyyy-MM-dd";

    private static final long MAX_TIME = 93L * 24 * 60 * 60 * 1000;

    @Autowired
    private StatisticInvokeTimesApiGroupQueryService statisticInvokeTimesApiGroupQueryService;

    @Autowired
    private IspMgrQueryService IspMgrQueryService;

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    @RequestMapping(value = "api-group/list", method = RequestMethod.GET)
    @ResponseBody
    public ResponseMessage invokeTimesApiGroup(@RequestParam(value = "spCode", required = false) String spCode,
                                               @RequestParam(value = "apiGroup", required = false) String apiGroup,
                                               @RequestParam(value = "statisticType") StatisticTypeEnum statisticType,
                                               @RequestParam(value = "statisticStartDate") String statisticStartDate,
                                               @RequestParam(value = "statisticEndDate") String statisticEndDate,
                                               @RequestParam(value = "pageNo", required = false) Integer pageNo,
                                               @RequestParam(value = "pageSize", required = false) Integer pageSize) {
        SimpleDateFormat sdf = new SimpleDateFormat(PATTERN);
        StatisticInvokeTimesApiGroupQueryParam param = new StatisticInvokeTimesApiGroupQueryParam();
        try {
            param.setSpCode(spCode);
            param.setApiGroup(apiGroup);
            param.setStatisticType(statisticType);
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            Date start = sdf.parse(statisticStartDate);
            Date end = sdf.parse(statisticEndDate);
            param.setStatisticStartDate(start);
            param.setStatisticEndDate(new Date(end.getTime() + 24 * 3600 * 1000));
            long time = end.getTime() - start.getTime();
            if (time < 0 || time > MAX_TIME) {
                throw new YopPortalException("time range error!");
            }
            PageQueryResult<StatisticInvokeTimeApiGroupItem> list = statisticInvokeTimesApiGroupQueryService.pageQuery(param);
            List<SpCodeVO> spList = IspMgrQueryService.queryAllSpCode();
            Map<String, String> spMap = new HashMap<>();
            for (SpCodeVO spCodeVO : spList) {
                spMap.put(spCodeVO.getSpCode(), spCodeVO.getSpName());
            }
            List<ApiGroupVO> apiGroupList = apiGroupQueryService.list();
            Map<String, ApiGroupVO> apiGroupMap = new HashMap<>();
            for (ApiGroupVO apiGroupVO : apiGroupList) {
                apiGroupMap.put(apiGroupVO.getApiGroupCode(), apiGroupVO);
            }
            List<StatisticInvokeTimeApiGroupItem> items = list.getItems();
            for (StatisticInvokeTimeApiGroupItem item : items) {
                ApiGroupVO apiGroupVO = apiGroupMap.get(item.getApiGroupCode());
                if (null != apiGroupVO) {
                    item.setSpCode(apiGroupVO.getSpCode());
                    item.setSpName(spMap.get(item.getSpCode()));
                    item.setApiGroupName(apiGroupVO.getApiGroupName());
                }
            }
            return new ResponseMessage("page", list);
        } catch (Exception e) {
            LOGGER.error("Exception occurred when query statisticInvokeTimesApiGroup with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

}
