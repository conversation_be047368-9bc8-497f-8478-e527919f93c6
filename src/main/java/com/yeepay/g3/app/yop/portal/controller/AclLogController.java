/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.AclLogQueryService;
import com.yeepay.g3.app.yop.portal.vo.AclLogDetailVO;
import com.yeepay.g3.app.yop.portal.vo.AclResourceVO;
import com.yeepay.g3.app.yop.portal.vo.page.AclLogPageQueryParam;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * title:操作日志管理 <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-12-16 10:39
 */
@Controller
@RequestMapping("/rest/acl/log")
public class AclLogController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AclLogController.class);

    @Autowired
    private AclLogQueryService aclLogQueryService;

    private static final String PATTERN = "yyyy-MM-dd HH:mm:ss";

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(@Validated AclLogPageQueryParam param,
                                @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "_pageSize", required = false) Integer pageSize) {
        try {
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            return new ResponseMessage("page", aclLogQueryService.pageQuery(param));
        } catch (Exception e) {
            LOGGER.error("Exception occurred when list aclLog with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    /**
     * 查询资源树
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/commons/resources", method = RequestMethod.GET)
    public ResponseMessage nextResource() {
        AclResourceVO resources = aclLogQueryService.queryResources();
        return new ResponseMessage("result", resources);
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam(value = "operDate") String operDate,
                                  @RequestParam("id") Long id) {
        SimpleDateFormat sdf = new SimpleDateFormat(PATTERN);
        Date date;
        try {
            date = sdf.parse(operDate);
        } catch (ParseException e) {
            LOGGER.error("Exception occurred when query acl log detail with operDate:" + operDate, e);
            return new ResponseMessage(e);
        }
        AclLogDetailVO detailVO = aclLogQueryService.findDetail(id, date);
        return new ResponseMessage("result", detailVO);
    }

}
