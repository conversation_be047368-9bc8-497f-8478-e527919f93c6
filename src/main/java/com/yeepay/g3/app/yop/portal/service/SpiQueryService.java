package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.dto.spi.SpiApiModel;
import com.yeepay.g3.app.yop.portal.vo.SpiVO;
import com.yeepay.g3.app.yop.portal.vo.page.*;

import java.util.List;

/**
 * title: SpiQueryService<br/>
 * description: spi查询service<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:43
 */
public interface SpiQueryService {

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<SpiPageItem> pageList(SpiPageQueryParam param);

    /**
     * sp分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<SpiPageItem> pageListForSp(SpiPageQueryParam param);

    /**
     * spi列表查询
     *
     * @param apiGroup
     * @return
     */
    List<SpiVO.SpiListVO> simpleList(String apiGroup);

    /**
     * 根据spiNames查询spi
     *
     * @param spiNames
     * @return
     */
    List<SpiVO.SpiListVO> simpleList(List<String> spiNames);

    /**
     * 变更记录分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<SpiChangeRecordPageItem> changeRecordPageList(SpiChangeRecordPageQueryParam param);

    /**
     * 变更记录sp分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<SpiChangeRecordPageItem> changeRecordPageListForSp(SpiChangeRecordPageQueryParam param);

    /**
     * api回调列表
     *
     * @param apiId
     * @return
     */
    List<SpiVO.SpiListVO> apiCallbacks(String apiId);

    /**
     * 查询已关联的api
     *
     * @param param
     * @return
     */
    List<SpiApiModel> listApiContacted(SpiApiQueryParam param);
}
