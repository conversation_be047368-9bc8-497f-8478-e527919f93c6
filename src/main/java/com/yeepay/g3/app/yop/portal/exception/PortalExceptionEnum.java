package com.yeepay.g3.app.yop.portal.exception;

/**
 * title: 通用的异常定义枚举<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/7 11:27 上午
 */
public enum PortalExceptionEnum implements YopPortalAssert {

    // 超出时间范围最大限度
    TIME_INTERVAL_TOO_LONG("105002", "最大时间间隔不能超过{0}天"),

    // 通知订单相关
    NOTIFY_ORDER_NOT_EXIST("105001", "订单不存在"),

    // 权限相关
    PERMS_MISSING("106001", "您没有此操作的权限，请联系平台管理员"),

    SERVICE_NOT_FOUND("502001", "后端服务不存在"),

    // 审核相关 1052XX,
    SUBMIT_FAILED("105201", "提交审核失败"),

    UNKNOWN_ERROR("999999", "未知异常"),

    //api相关
    API_PATH_INVALIDATE("105100", "请求路径不合法"),

    API_GROUP_ILLEGAL("105101", "api分组不合法"),

    API_PATH_ILLEGAL("105102", "api请求路径不合法"),

    API_NOT_FOUND("105103", "api不存在, apiId:{0}"),

    API_CONTENT_TYPE_NONSTANDARD("105104", "api的content_type为空, apiId:{0}"),

    API_RESPONSE_CONTENT_NONSTANDARD("105105", "api的响应参数为空, apiId:{0}"),

    API_RESPONSE_SCHEMA_NONSTANDARD("105106", "api的响应参数的结构不标准, apiId:{0}"),

    API_GROUP_NOT_FOUND("105107", "api分组不存在, apiId:{0}"),

    API_MODEL_NOT_FOUND("105108", "api模型不存在, refName:{0}, apiGroup:{1}"),

    // 页面负责人管理相关异常 (105200-105299)
    PAGE_OWNER_NOT_FOUND("105200", "页面负责人不存在"),
    PAGE_OWNER_ALREADY_EXISTS("105201", "页面负责人已存在"),
    PAGE_OWNER_INVALID_PARAMETER("105202", "页面负责人参数无效: {0}"),
    PAGE_OWNER_OPERATION_FAILED("105203", "页面负责人操作失败: {0}"),
    PAGE_OWNER_CONCURRENT_UPDATE("105204", "页面负责人数据已被其他用户修改，请刷新后重试"),
    PAGE_OWNER_PERMISSION_DENIED("105205", "没有权限修改页面负责人"),
    PAGE_OWNER_LIST_TOO_LARGE("105206", "页面负责人数量超过限制，最大允许: {0}"),
    PAGE_OWNER_DATABASE_ERROR("105207", "页面负责人数据库操作失败"),
    
    // 用户搜索相关异常 (105300-105399)
    USER_SEARCH_FAILED("105300", "用户搜索失败: {0}"),
    USER_SEARCH_KEYWORD_INVALID("105301", "搜索关键词无效"),
    USER_SEARCH_RESULT_TOO_LARGE("105302", "搜索结果过多，请缩小搜索范围"),
    USER_NOT_FOUND("105303", "用户不存在: {0}");


    private String code;
    private String message;

    PortalExceptionEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
