package com.yeepay.g3.app.yop.portal.shiro.authc;

import com.yeepay.g3.app.yop.portal.exception.YopPortalAuthException;
import com.yeepay.g3.app.yop.portal.service.IspMgrQueryService;
import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.app.yop.portal.shiro.utils.OAuth2TokenUtils;
import com.yeepay.g3.app.yop.portal.shiro.utils.SpringContextUtil;
import com.yeepay.g3.app.yop.portal.shiro.utils.YuiaTokenUtils;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationInfo;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.SimpleAuthenticationInfo;

import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * title: OAuth2 令牌<br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/6/8 下午2:51
 */
public class OAuth2Token implements AuthenticationToken {

    private static final long serialVersionUID = 1L;
    private static final Logger LOGGER = LoggerFactory.getLogger(OAuth2Token.class);

    private String token;

    private String userId;

    private String userName;

    private OperatorTypeEnum userType;

    private Set<String> spScopes;

    private Set<String> apiGroupScopes;

    public OAuth2Token() {
        super();
    }

    public OAuth2Token(String token) {
        this.token = token;
    }

    public OAuth2Token(String userId, String userName, OperatorTypeEnum userType, Set<String> spScopes, Set<String> apiGroupScopes) {
        this.userId = userId;
        this.userName = userName;
        this.userType = userType;
        this.spScopes = spScopes;
        this.apiGroupScopes = apiGroupScopes;
    }

    public String getToken() {
        return token;
    }

    public String getUserId() {
        return userId;
    }

    public String getUserName() {
        return userName;
    }

    public OperatorTypeEnum getUserType() {
        return userType;
    }

    public Set<String> getSpScopes() {
        return spScopes;
    }

    public Set<String> getApiGroupScopes() {
        return apiGroupScopes;
    }

    @Override
    public Object getPrincipal() {
        return token;
    }

    @Override
    public Object getCredentials() {
        return token;
    }

    public AuthenticationInfo doGetAuthenticationInfo() throws AuthenticationException {
        if (null == token || StringUtils.isEmpty(token)) {
            LOGGER.error("OAuth2 unAuthenticated, token is null");
            throw new YopPortalAuthException("OAuth2 unAuthenticated");
        }
        String yuiaResponse = null;
        try {
            yuiaResponse = YuiaTokenUtils.verifyYuiaToken(token);
        } catch (Exception e) {
            LOGGER.warn("not yuia token, will try oauth2 token, ex:", e);
        }
        if (StringUtils.isEmpty(yuiaResponse)) {
            //不是权限中心的token
            OAuth2Token oAuth2Token = OAuth2TokenUtils.verify(token);
            Set<String> spCodes = oAuth2Token.getSpScopes();
            IspMgrQueryService ispMgrQueryService = (IspMgrQueryService) SpringContextUtil.getBean("ispMgrQueryServiceImpl");
            List<String> tenantCodes = ispMgrQueryService.findTenantCodesBySpCodes(spCodes);
            ShiroRealm.ShiroUser shiroUser = new ShiroRealm.ShiroUser(oAuth2Token.getUserId(), oAuth2Token.getUserId(),
                    oAuth2Token.getUserType(), oAuth2Token.getSpScopes(), oAuth2Token.getApiGroupScopes(),
                    token, new HashSet<>(tenantCodes));
            return new SimpleAuthenticationInfo(shiroUser, token, token);
        }

        //是权限中心的token
        String loginName = StringUtils.substringBetween(yuiaResponse, "<loginName>", "</loginName>");
        ShiroRealm shiroRealm = (ShiroRealm) SpringContextUtil.getBean("shiroRealm");
        ShiroRealm.ShiroUser shiroUser = shiroRealm.getShiroUserInfo(loginName, token);
        return new SimpleAuthenticationInfo(new ShiroRealm.ShiroUser(shiroUser.getUserId(), shiroUser.getUsername(), shiroUser.getUserType(), shiroUser.getSpScopes(), shiroUser.getApiGroupScopes(), shiroUser.getAccessToken(), shiroUser.getTenantScopes()), shiroUser.getUserId(), shiroUser.getUserId());
    }
}
