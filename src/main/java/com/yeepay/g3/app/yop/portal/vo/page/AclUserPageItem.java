package com.yeepay.g3.app.yop.portal.vo.page;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午5:30
 */
public class AclUserPageItem implements Serializable {

    private static final long serialVersionUID = -1;

    private Long id;

    /**
     * 用户编码
     */
    private String optCode;

    /**
     * 用户名称
     */
    private String optName;

    /**
     * 用户状态
     */
    private String status;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户职位
     */
    private String position;

    /**
     * 用户类型
     */
    private String type;

    private Date createdDate;

    private Date lastModifiedDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOptCode() {
        return optCode;
    }

    public void setOptCode(String optCode) {
        this.optCode = optCode;
    }

    public String getOptName() {
        return optName;
    }

    public void setOptName(String optName) {
        this.optName = optName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public Date getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Date lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
