/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.CustomSolutionQueryService;
import com.yeepay.g3.app.yop.portal.service.DocQueryService;
import com.yeepay.g3.app.yop.portal.utils.Constants;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.app.yop.portal.validation.group.CustomSolutionCopy;
import com.yeepay.g3.app.yop.portal.validation.group.CustomSolutionCreate;
import com.yeepay.g3.app.yop.portal.validation.group.CustomSolutionEdit;
import com.yeepay.g3.app.yop.portal.vo.*;
import com.yeepay.g3.app.yop.portal.vo.converter.CustomSolutionConverter;
import com.yeepay.g3.app.yop.portal.vo.page.CustomSolutionPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.CustomSolutionRelatedApiPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.CustomSolutionUnRelatedApiPageQueryParam;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.facade.yop.sys.facade.CustomSolutionMgrFacade;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * title: 自定义解决方案<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/19
 */
@RestController
@RequestMapping("/rest/custom-solution")
@Slf4j
public class CustomSolutionController {

    private CustomSolutionMgrFacade customSolutionMgrFacade = RemoteServiceFactory.getService(CustomSolutionMgrFacade.class);
    private CustomSolutionQueryService queryService;
    private DocQueryService docQueryService;

    @GetMapping(value = "/list")
    public ResponseMessage list(@Validated CustomSolutionPageQueryParam param,
                                @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "_pageSize", required = false) Integer pageSize
    ) {
        try {
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("custom-solution");
            param.setPageSize(appPageSize.intValue());
            return new ResponseMessage("page", queryService.pageQuery(param));
        } catch (Exception e) {
            log.error("Exception occurred when list custom-solution with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @PostMapping(value = "/create")
    public ResponseMessage<?> create(@RequestBody @Validated(CustomSolutionCreate.class) CustomSolutionVO vo) {
        try {
            if (docQueryService.existsDoc(vo.getSolutionCode())) {
                return new ResponseMessage<>(ResponseMessage.Status.ERROR,"方案编码重复，请重新设置");
            }
            String operatorCode = ShiroUtils.getOperatorCode();
            CustomSolutionDTO dto = CustomSolutionConverter.INSTANCE.toDTO(vo);
            dto.setOperatorCode(operatorCode);
            CustomSolutionCreateRequest request = new CustomSolutionCreateRequest(dto)
                    .withOperationInfo(new OperationInfo()
                            .withOperator(operatorCode)
                            .withCause("create"));
            customSolutionMgrFacade.create(request);
            return new ResponseMessage<>();
        } catch (Exception e) {
            log.error("Exception occurred when create custom-solution with param:" + vo, e);
            return new ResponseMessage<>();
        }
    }

    @PostMapping(value = "/copy")
    public ResponseMessage<?> copy(@RequestBody @Validated(CustomSolutionCopy.class) CustomSolutionCopyVO vo) {
        try {
            if (docQueryService.existsDoc(vo.getSolutionCode())) {
                return new ResponseMessage<>(ResponseMessage.Status.ERROR,"方案编码重复，请重新设置");
            }
            String operatorCode = ShiroUtils.getOperatorCode();
            CustomSolutionDTO dto = CustomSolutionConverter.INSTANCE.toDTO(vo);
            dto.setOperatorCode(operatorCode);
            CustomSolutionCopyRequest request = new CustomSolutionCopyRequest(dto)
                    .withSourceSolutionCode(vo.getSourceSolutionCode())
                    .withOperationInfo(new OperationInfo()
                            .withOperator(operatorCode)
                            .withCause("copy"));
            customSolutionMgrFacade.copy(request);
            return new ResponseMessage<>();
        } catch (Exception e) {
            log.error("Exception occurred when copy custom-solution with param:" + vo, e);
            return new ResponseMessage<>();
        }
    }

    @PostMapping(value = "/edit")
    public ResponseMessage edit(@RequestBody @Validated(CustomSolutionEdit.class) CustomSolutionVO vo) {
        try {
            CustomSolutionDTO dto = CustomSolutionConverter.INSTANCE.toDTO(vo);
            CustomSolutionUpdateRequest request = new CustomSolutionUpdateRequest(dto)
                    .withOperationInfo(new OperationInfo()
                            .withOperator(ShiroUtils.getOperatorCode())
                            .withCause("edit"));
            request.setCustomSolution(dto);
            customSolutionMgrFacade.update(request);
            return new ResponseMessage();
        } catch (Exception e) {
            log.error("Exception occurred when edit custom-solution with param:" + vo, e);
            return new ResponseMessage(e);
        }
    }

    @GetMapping(value = "/detail")
    public ResponseMessage detail(@RequestParam String solutionCode) {
        try {
            final CustomSolutionDTO dto = customSolutionMgrFacade.find(solutionCode);
            if (null != dto) {
                return new ResponseMessage("result", CustomSolutionConverter.INSTANCE.toVO(dto));
            } else {
                throw new YeepayRuntimeException("custom-solution not exists, solutionCode:[{0}]", solutionCode);
            }
        } catch (Exception e) {
            log.error("Exception occurred when find custom-solution with param:" + solutionCode, e);
            return new ResponseMessage(e);
        }
    }

    @GetMapping(value = "/share")
    public ResponseMessage share(@RequestParam String solutionCode) {
        try {
            final CustomSolutionDTO dto = customSolutionMgrFacade.find(solutionCode);
            if (null != dto) {
                CustomSolutionShareVO vo = new CustomSolutionShareVO();
                vo.setSolutionCode(solutionCode);
                vo.setDocUrl(getDocUrlForSolution(solutionCode));
                return new ResponseMessage("result", vo);
            } else {
                throw new YeepayRuntimeException("custom-solution not exists, solutionCode:[{0}]", solutionCode);
            }
        } catch (Exception e) {
            log.error("Exception occurred when share custom-solution with param:" + solutionCode, e);
            return new ResponseMessage(e);
        }
    }

    private String getDocUrlForSolution(String solutionCode) {
        Map<String, String> accessConfig = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_MBR_DOC_ACCESS_CONFIG);
        String urlPrefix = accessConfig.get(Constants.YOP_MBR_DOC_ACCESS_MBR_PREFIX_CFG_KEY),
                urlSuffix = String.format(Constants.YOP_MBR_DOC_SOLUTION_INDEX_SUFFIX_FORMAT, toDocNo(solutionCode));
        return urlPrefix + urlSuffix;
    }

    private String toDocNo(String solutionCode) {
        return solutionCode;
    }

    @GetMapping(value = "/list-for-doc")
    public ResponseMessage listForDoc() {
        try {
            final List<CommonsVO> spSolutions = queryService.listForDoc();
            return new ResponseMessage("result", spSolutions);
        } catch (Exception e) {
            log.error("Exception occurred when listForDoc, ex:", e);
            return new ResponseMessage(e);
        }
    }

    @GetMapping(value = "/related-api/list")
    public ResponseMessage relatedApiList(@Validated CustomSolutionRelatedApiPageQueryParam param,
                                @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "_pageSize", required = false) Integer pageSize
    ) {
        try {
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("custom-solution-related-api");
            param.setPageSize(appPageSize.intValue());
            return new ResponseMessage("page", queryService.relatedApiPageQuery(param));
        } catch (Exception e) {
            log.error("Exception occurred when list custom-solution-related-api with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @GetMapping(value = "/related-api/unrelated-list")
    public ResponseMessage unRelatedApiList(@Validated CustomSolutionUnRelatedApiPageQueryParam param,
                                @RequestParam(value = "_pageNo", required = false) Integer pageNo,
                                @RequestParam(value = "_pageSize", required = false) Integer pageSize
    ) {
        try {
            param.setPageNo(pageNo);
            param.setPageSize(pageSize);
            Map<String, Long> pageSizeMap = (Map<String, Long>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_PORTAL_PAGE_SIZE);
            Long appPageSize = pageSizeMap.get("custom-solution-unrelated-api");
            param.setPageSize(appPageSize.intValue());
            return new ResponseMessage("page", queryService.unRelatedApiPageQuery(param));
        } catch (Exception e) {
            log.error("Exception occurred when list custom-solution-unrelated-api with param:" + param, e);
            return new ResponseMessage(e);
        }
    }

    @PostMapping(value = "/related-api/add")
    public ResponseMessage addRelatedApi(@RequestBody @Validated CustomSolutionRelateApiVO vo) {
        try {
            CustomSolutionApiAddRequest request = new CustomSolutionApiAddRequest(vo.getSolutionCode(), vo.getApiIds())
                    .withOperationInfo(new OperationInfo()
                            .withOperator(ShiroUtils.getOperatorCode())
                            .withCause("addApi"));
            customSolutionMgrFacade.addApi(request);
            return new ResponseMessage();
        } catch (Exception e) {
            log.error("Exception occurred when add custom-solution api with param:" + vo, e);
            return new ResponseMessage(e);
        }
    }

    @PostMapping(value = "/related-api/remove")
    public ResponseMessage removeRelatedApi(@RequestBody @Validated CustomSolutionRelateApiVO vo) {
        try {
            CustomSolutionApiRemoveRequest request = new CustomSolutionApiRemoveRequest(vo.getSolutionCode(), vo.getApiIds())
                    .withOperationInfo(new OperationInfo()
                            .withOperator(ShiroUtils.getOperatorCode())
                            .withCause("removeApi"));
            customSolutionMgrFacade.removeApi(request);
            return new ResponseMessage();
        } catch (Exception e) {
            log.error("Exception occurred when remove custom-solution api with param:" + vo, e);
            return new ResponseMessage(e);
        }
    }

    @PostMapping(value = "/related-api/require")
    public ResponseMessage requireRelatedApi(@RequestBody @Validated CustomSolutionRequiredApiVO vo) {
        try {
            CustomSolutionApiRequireRequest request = new CustomSolutionApiRequireRequest(vo.getSolutionCode())
                    .withRequiredApiIds(vo.isRequired() ? Collections.singletonList(vo.getApiId()) : Collections.emptyList())
                    .withUnRequiredApiIds(vo.isRequired() ? Collections.emptyList() : Collections.singletonList(vo.getApiId()))
                    .withOperationInfo(new OperationInfo()
                            .withOperator(ShiroUtils.getOperatorCode())
                            .withCause("requireApi"));
            customSolutionMgrFacade.requireApi(request);
            return new ResponseMessage();
        } catch (Exception e) {
            log.error("Exception occurred when require custom-solution api with param:" + vo, e);
            return new ResponseMessage(e);
        }
    }

    @Autowired
    public void setQueryService(CustomSolutionQueryService queryService) {
        this.queryService = queryService;
    }

    @Autowired
    public void setDocQueryService(DocQueryService docQueryService) {
        this.docQueryService = docQueryService;
    }
}
