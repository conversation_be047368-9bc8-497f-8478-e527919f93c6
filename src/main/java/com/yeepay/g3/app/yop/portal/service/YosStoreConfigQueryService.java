package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.YosStoreConfigVO;
import com.yeepay.g3.app.yop.portal.vo.YosStoreTypeVO;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.YosStoreConfigPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.YosStoreConfigPageQueryParam;

import java.util.List;
import java.util.Set;

public interface YosStoreConfigQueryService {

    PageQueryResult<YosStoreConfigPageItem> pageList(YosStoreConfigPageQueryParam param);

    PageQueryResult<YosStoreConfigPageItem> pageListForSp(YosStoreConfigPageQueryParam param);

    YosStoreConfigVO findById(Long id);

    List<YosStoreTypeVO> listYosStoreTypes();

}
