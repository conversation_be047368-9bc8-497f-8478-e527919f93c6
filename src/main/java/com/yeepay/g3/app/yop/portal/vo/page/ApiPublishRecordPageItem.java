package com.yeepay.g3.app.yop.portal.vo.page;

import java.io.Serializable;
import java.util.Date;

/**
 * title: api发布记录项<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-02-13 16:43
 */
public class ApiPublishRecordPageItem implements Serializable {

    private static final long serialVersionUID = 6467153175588592627L;

    private Long id;

    private String apiId;

    private String apiGroup;

    private String apiGroupName;

    private String path;

    private String method;

    private String opType;

    private String operator;

    private String cause;

    private Date createdDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ApiPublishRecordPageItem withId(Long id) {
        this.id = id;
        return this;
    }

    public String getApiId() {
        return apiId;
    }

    public void setApiId(String apiId) {
        this.apiId = apiId;
    }

    public ApiPublishRecordPageItem withApiId(String apiId) {
        this.apiId = apiId;
        return this;
    }

    public String getApiGroup() {
        return apiGroup;
    }

    public void setApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
    }

    public ApiPublishRecordPageItem withApiGroup(String apiGroup) {
        this.apiGroup = apiGroup;
        return this;
    }

    public String getApiGroupName() {
        return apiGroupName;
    }

    public void setApiGroupName(String apiGroupName) {
        this.apiGroupName = apiGroupName;
    }

    public ApiPublishRecordPageItem withApiGroupName(String apiGroupName) {
        this.apiGroupName = apiGroupName;
        return this;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public ApiPublishRecordPageItem withRequestPath(String requestPath) {
        this.path = requestPath;
        return this;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public ApiPublishRecordPageItem withRequestMethod(String requestMethod) {
        this.method = requestMethod;
        return this;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public ApiPublishRecordPageItem withOpType(String opType) {
        this.opType = opType;
        return this;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public ApiPublishRecordPageItem withOperator(String operator) {
        this.operator = operator;
        return this;
    }

    public String getCause() {
        return cause;
    }

    public void setCause(String cause) {
        this.cause = cause;
    }

    public ApiPublishRecordPageItem withCause(String cause) {
        this.cause = cause;
        return this;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public ApiPublishRecordPageItem withCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
        return this;
    }
}
