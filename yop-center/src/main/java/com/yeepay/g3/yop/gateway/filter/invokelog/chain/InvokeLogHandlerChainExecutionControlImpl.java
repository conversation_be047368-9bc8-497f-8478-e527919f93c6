/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.gateway.filter.invokelog.chain;

import com.yeepay.g3.yop.gateway.filter.invokelog.InvokeLogContext;
import com.yeepay.g3.yop.gateway.filter.invokelog.handler.InvokeLogHandler;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/1/16 下午2:36
 */
public class InvokeLogHandlerChainExecutionControlImpl implements InvokeLogHandlerChainExecutionControl {

    private InvokeLogHandlerChain invokeLogHandlerChain;

    private int index;

    private InvokeLogContext invokeLogContext;

    public InvokeLogHandlerChainExecutionControlImpl(InvokeLogHandler<PERSON>hain invokeLogHandlerChain, InvokeLogContext invokeLogContext) {
        this.invokeLogHandlerChain = invokeLogHandlerChain;
        this.invokeLogContext = invokeLogContext;
        index = 0;
    }

    @Override
    public void handleNext() {
        InvokeLogHandler handler = invokeLogHandlerChain.getHandler(++index);
        if (null != handler) {
            handler.handle(invokeLogContext, this);
        }
    }
}
