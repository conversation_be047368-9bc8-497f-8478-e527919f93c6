package com.yeepay.g3.yop.frame.context.impl;

import com.yeepay.g3.yop.frame.chain.interceptor.authenticate.AuthenticateStrategyEnum;
import com.yeepay.g3.yop.frame.context.AbstractAuthenticateContext;

import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 16/11/1 下午3:23
 */
public class Oauth2Context extends AbstractAuthenticateContext {

    private static final String CLIENT_ID = "client_id";
    private static final String AUTHENTICATED_USER_ID = "authenticated_user_id";
    private static final String SCOPE = "scope";
    private static final String VALUE = "value";
    private static final String EXP = "exp";
    private static final String EXT = "ext";

    private String token;

    private Set<String> scopes;

    /**
     * clientId，一般需要与appKey匹配
     */
    private String clientId;

    /**
     * 此token对应的认证用户身份，一般是业务系统自身维护的身份标识
     */
    private String authenticatedUserId;

    private Date exp;

    private String value;

    private Map<String, String> extInfo;

    public String getToken() {
        return token;
    }

    public Set<String> getScopes() {
        return scopes;
    }

    public String getAuthenticatedUserId() {
        return authenticatedUserId;
    }

    public String getValue() {
        return value;
    }

    public Date getExp() {
        return exp;
    }

    public void setExp(Date exp) {
        this.exp = exp;
    }

    public Map<String, String> getExtInfo() {
        return extInfo;
    }

    public void parseTokenResult(Map<String, ?> tokenResult) {
        clientId = String.valueOf(tokenResult.get(CLIENT_ID));
        authenticatedUserId = String.valueOf(tokenResult.get(AUTHENTICATED_USER_ID));
        scopes = (Set<String>) tokenResult.get(SCOPE);
        value = String.valueOf(tokenResult.get(VALUE));
        exp = (Date) tokenResult.get(EXP);
        extInfo = (Map<String, String>) tokenResult.get(EXT);
    }

    @Override
    public AuthenticateStrategyEnum getAuthenticateStrategy() {
        return AuthenticateStrategyEnum.YOP_OAUTH2;
    }

    @Override
    public void initialize(String protocolPrefix, String protocolContent) {
        token = protocolContent;
    }

    public String getClientId() {
        return clientId;
    }
}
