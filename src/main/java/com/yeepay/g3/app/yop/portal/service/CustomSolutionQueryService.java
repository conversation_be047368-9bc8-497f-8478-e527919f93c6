/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.page.*;

import java.util.List;

/**
 * title: 自定义解决方案查询接口<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/9/19
 */
public interface CustomSolutionQueryService {

    PageQueryResult<CustomSolutionPageItem> pageQuery(CustomSolutionPageQueryParam param);

    /**
     * 查询尚未生成文档的解决方案
     *
     * @return 方案列表
     */
    List<CommonsVO> listForDoc();

    PageQueryResult<CustomSolutionRelatedApiPageItem> relatedApiPageQuery(CustomSolutionRelatedApiPageQueryParam param);

    PageQueryResult<CustomSolutionUnRelatedApiPageItem> unRelatedApiPageQuery(CustomSolutionUnRelatedApiPageQueryParam param);
}
