/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.yop.frame.backend;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/19 4:46 PM
 */
public enum ClassLoadMode {

    LOCAL(false),
    REMOTE(true);

    private boolean canUpgrade;

    ClassLoadMode(boolean canUpgrade) {
        this.canUpgrade = canUpgrade;
    }

}
