package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Map;

/**
 * title: 应用配置VO<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/4/10 10:57
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppConfigVO implements Serializable {

    private static final long serialVersionUID = 4051442150957919198L;

    private String loginUrl;

    private String logoutUrl;

    private String linkLogCenter;

    private String linkCallChain;

    private Map<String, Object> rt;

    /**
     * 用于区分哪种登陆类型
     */
    private String loginType;

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }

    public String getLoginUrl() {
        return loginUrl;
    }

    public void setLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
    }

    public String getLogoutUrl() {
        return logoutUrl;
    }

    public void setLogoutUrl(String logoutUrl) {
        this.logoutUrl = logoutUrl;
    }

    public String getLinkLogCenter() {
        return linkLogCenter;
    }

    public void setLinkLogCenter(String linkLogCenter) {
        this.linkLogCenter = linkLogCenter;
    }

    public String getLinkCallChain() {
        return linkCallChain;
    }

    public void setLinkCallChain(String linkCallChain) {
        this.linkCallChain = linkCallChain;
    }

    public Map<String, Object> getRt() {
        return rt;
    }

    public void setRt(Map<String, Object> rt) {
        this.rt = rt;
    }

    public AppConfigVO withLoginType(String loginType) {
        this.loginType = loginType;
        return this;
    }

    public AppConfigVO withLoginUrl(String loginUrl) {
        this.loginUrl = loginUrl;
        return this;
    }

    public AppConfigVO withLogoutUrl(String logoutUrl) {
        this.logoutUrl = logoutUrl;
        return this;
    }

    public AppConfigVO withLinkLogCenter(String linkLogCenter) {
        this.linkLogCenter = linkLogCenter;
        return this;
    }

    public AppConfigVO withLinkCallChain(String linkCallChain) {
        this.linkCallChain = linkCallChain;
        return this;
    }

    public AppConfigVO withRt(Map<String, Object> rt) {
        this.rt = rt;
        return this;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
