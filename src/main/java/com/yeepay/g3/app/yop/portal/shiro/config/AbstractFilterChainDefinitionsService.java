package com.yeepay.g3.app.yop.portal.shiro.config;

import com.google.common.collect.Maps;
import com.yeepay.g3.core.yop.utils.concurrent.threadpool.ThreadPoolBuilder;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.filter.mgt.DefaultFilterChainManager;
import org.apache.shiro.web.filter.mgt.PathMatchingFilterChainResolver;
import org.apache.shiro.web.servlet.AbstractShiroFilter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.PostConstruct;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/7/25 下午3:15
 */
public abstract class AbstractFilterChainDefinitionsService implements FilterChainDefinitionsService {

    private final static Logger log = LoggerFactory.getLogger(AbstractFilterChainDefinitionsService.class);

    @Autowired
    private ShiroFilterFactoryBean shiroFilterFactoryBean;

    private ScheduledThreadPoolExecutor executor = new ThreadPoolBuilder.ScheduledThreadPoolBuilder()
            .setThreadNamePrefix("shiroPermsPool")
            .build();

    @Override
    @PostConstruct
    public void intiPermission() {
        updatePermission();
        executor.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                updatePermission();
            }
        }, 300, 300, TimeUnit.SECONDS);
    }

    @Override
    public void updatePermission() {

        synchronized (shiroFilterFactoryBean) {

            AbstractShiroFilter shiroFilter = null;

            try {
                shiroFilter = (AbstractShiroFilter) shiroFilterFactoryBean.getObject();
            } catch (Exception e) {
                log.error(e.getMessage());
                return;
            }

            // 获取过滤管理器
            PathMatchingFilterChainResolver filterChainResolver = (PathMatchingFilterChainResolver) shiroFilter
                    .getFilterChainResolver();
            DefaultFilterChainManager manager = (DefaultFilterChainManager) filterChainResolver.getFilterChainManager();

            // 清空无用的权限
            Map<String, String> newChain = obtainPermission();
            Set<String> oldChains = manager.getFilterChains().keySet();
            Set<String> deleteChains = new HashSet<>();
            deleteChains.addAll(oldChains);
            deleteChains.removeAll(newChain.keySet());
            oldChains.removeAll(deleteChains);

            // 重新构建生成
            shiroFilterFactoryBean.setFilterChainDefinitionMap(newChain);
            Map<String, String> chains = shiroFilterFactoryBean.getFilterChainDefinitionMap();

            for (Map.Entry<String, String> entry : chains.entrySet()) {
                String url = entry.getKey();
                String chainDefinition = entry.getValue().trim().replace(" ", "");
                if (oldChains.contains(url)) {
                    oldChains.remove(url);
                }
                try {
                    manager.createChain(url, chainDefinition);
                } catch (Exception error) {
                    log.error("updatePermission error,chainDefinition is {},error:", chainDefinition, error);
                }
            }

            log.debug("update shiro permission success...");
        }
    }

    @Override
    public Map<String, String> obtainPermission() {
        Map<String, String> filterChainDefinitionMap = Maps.newLinkedHashMap();
        filterChainDefinitionMap.putAll(obtainPermissionFromDB());
        filterChainDefinitionMap.putAll(obtainPermissionFromConfig());
        return filterChainDefinitionMap;
    }

    protected abstract Map<String, String> obtainPermissionFromDB();

    protected abstract Map<String, String> obtainPermissionFromConfig();

}
