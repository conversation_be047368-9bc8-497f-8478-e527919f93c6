package com.yeepay.g3.app.yop.portal.vo.page;

import javax.validation.constraints.NotNull;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 13:42
 */
public class ErrcodeApiPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    @NotNull
    private Long errcodeId;

    public Long getErrcodeId() {
        return errcodeId;
    }

    public void setErrcodeId(Long errcodeId) {
        this.errcodeId = errcodeId;
    }
}
