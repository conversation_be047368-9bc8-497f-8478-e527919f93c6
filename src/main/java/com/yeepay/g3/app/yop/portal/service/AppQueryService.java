/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestAppDTO;
import com.yeepay.g3.app.yop.portal.vo.AppQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.AppPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.AppPageParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/3/13 下午2:10
 */
public interface AppQueryService {

    PageQueryResult<AppPageItem> pageQuery(AppPageParam param);

    List<RegressionTestAppDTO> listPlatformTest();

    /**
     * 查询应用别名
     *
     * @param appId
     * @return
     */
    List<String> listAlias(String appId);

    /**
     * 查询商编下的应用
     *
     * @param customerNo
     * @return
     */
    List<String> listByCustomerNo(String customerNo);

    /**
     * 根据查询条件查询应用
     *
     * @param appQueryParam
     * @return
     */
    List<String> findByConditions(AppQueryParam appQueryParam);
}
