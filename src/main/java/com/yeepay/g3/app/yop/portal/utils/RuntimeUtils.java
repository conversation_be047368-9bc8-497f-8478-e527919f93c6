package com.yeepay.g3.app.yop.portal.utils;


import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * title: runtime工具类<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/4/13 19:12
 */
public class RuntimeUtils {

    /**
     * 执行命令行命令，并返回标准输出
     *
     * @param cmd 命令
     * @param dir 执行目录
     * @return 标准输出
     * @throws IOException 异常
     */
    public static String exec(String cmd, File dir) throws IOException {
        // Execute a command and get its process handle
        Process proc = Runtime.getRuntime().exec(cmd, null, dir);
        // Get the handle for the processes InputStream
        InputStreamReader isReader = new InputStreamReader(proc.getInputStream());
        // Create a BufferedReader and specify it reads
        // from an input stream.

        String output = IOUtils.toString(isReader);
        try {
            proc.waitFor();
        } catch (InterruptedException ex) {
            throw new YeepayRuntimeException("InterruptedException exception occurred when handle command:{0} in path:{1}", ex,
                    cmd, dir.getPath());
        }
        // Note: proc.exitValue() returns the exit value.
        // (Use if required)
        isReader.close(); // Done.
        // Convert the list to a string and return
        return output;
    }
}
