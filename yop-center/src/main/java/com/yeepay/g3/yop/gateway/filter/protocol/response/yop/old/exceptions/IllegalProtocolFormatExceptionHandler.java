package com.yeepay.g3.yop.gateway.filter.protocol.response.yop.old.exceptions;

import com.yeepay.g3.yop.frame.error.YopError;
import com.yeepay.g3.yop.frame.exception.protocol.IllegalProtocolFormatException;
import com.yeepay.g3.yop.frame.response.Response;
import com.yeepay.g3.yop.gateway.annotations.ExceptionHandler;
import com.yeepay.g3.yop.gateway.filter.protocol.response.yop.old.AbstractExceptionYopMarshaller;
import com.yeepay.g3.yop.ext.gateway.server.ServerWebExchange;
import com.yeepay.g3.yop.ext.gateway.support.ServerWebExchangeUtils;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/1/26 下午12:53
 */
@ExceptionHandler({IllegalProtocolFormatException.class})
public class IllegalProtocolFormatExceptionHandler extends AbstractExceptionYopMarshaller {

    @Override
    protected YopError getError(ServerWebExchange exchange, Response response) {
        return new YopError("99001024", String.format("应用(%s)非法的认证协议头", ServerWebExchangeUtils.getAppId(exchange)));
    }
}
