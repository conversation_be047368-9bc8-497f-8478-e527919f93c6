package com.yeepay.g3.app.yop.portal.vo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * title: api安全定义<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/9 17:19
 */
public class SecurityDefVO implements Serializable {

    private static final long serialVersionUID = -1201907760591510217L;

    private String name;

    private String desc;

    private Map<String, Map<String, Object>> extensions;

    private List<String> scopes;

    private List<String> custom;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Map<String, Map<String, Object>> getExtensions() {
        return extensions;
    }

    public void setExtensions(Map<String, Map<String, Object>> extensions) {
        this.extensions = extensions;
    }

    public List<String> getScopes() {
        return scopes;
    }

    public void setScopes(List<String> scopes) {
        this.scopes = scopes;
    }

    public List<String> getCustom() {
        return custom;
    }

    public void setCustom(List<String> custom) {
        this.custom = custom;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
