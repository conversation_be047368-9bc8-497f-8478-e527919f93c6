package com.yeepay.g3.app.yop.portal.controller.support.imports;

import java.io.Serializable;

/**
 * title: Spi导入失败信息<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 18:42
 */
public class SpiImportFailedInfo implements Serializable {

    private static final long serialVersionUID = -1L;

    private String spiName;

    private String reason;

    public String getSpiName() {
        return spiName;
    }

    public void setSpiName(String spiName) {
        this.spiName = spiName;
    }

    public SpiImportFailedInfo withSpiName(String spiName) {
        this.spiName = spiName;
        return this;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public SpiImportFailedInfo withReason(String reason) {
        this.reason = reason;
        return this;
    }
}
