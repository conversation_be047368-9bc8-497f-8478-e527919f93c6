/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.app.yop.portal.vo.converter;

import com.yeepay.g3.app.yop.portal.vo.ApiExampleSceneItemVO;
import com.yeepay.g3.app.yop.portal.vo.ApiExampleSceneVO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiExampleSceneDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiExampleSceneItemDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/10/10
 */
@Mapper
public interface ApiExampleConverter {

    ApiExampleConverter INSTANCE = Mappers.getMapper(ApiExampleConverter.class);

    ApiExampleSceneDTO toDTO(ApiExampleSceneVO vo);

    List<ApiExampleSceneItemDTO> toDTOList(List<ApiExampleSceneItemVO> vos);

    ApiExampleSceneVO toVO(ApiExampleSceneDTO dto);

    List<ApiExampleSceneItemVO> toVOList(List<ApiExampleSceneItemDTO> vos);

}
