/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.NotifySendRecordQueryParam;
import com.yeepay.g3.app.yop.portal.vo.PageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.notify.NotifySendRecordVO;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/28 20:38
 */
public interface NotifySendRecordQueryService {

    /**
     * 查询全量发送记录
     *
     * @param notifySendRecordQueryParam
     * @param pageQueryParam
     * @return
     */
    PageQueryResult<NotifySendRecordVO> listSendRecord(NotifySendRecordQueryParam notifySendRecordQueryParam, PageQueryParam pageQueryParam);

}
