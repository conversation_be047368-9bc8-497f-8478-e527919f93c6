package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.ErrorCodeTypeEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;


/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午6:02
 * since  下午6:02
 */

public class ErrorCodePageQueryParam extends BasePageQueryParam {

    private ErrorCodeTypeEnum type;

    private String apiGroupCode;

    private List<String> apiGroupCodes;

    private String apiUri;

    private String errorCode;

    private String subErrorCode;

    public ErrorCodeTypeEnum getType() {
        return type;
    }

    public void setType(ErrorCodeTypeEnum type) {
        this.type = type;
    }

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public List<String> getApiGroupCodes() {
        return apiGroupCodes;
    }

    public void setApiGroupCodes(List<String> apiGroupCodes) {
        this.apiGroupCodes = apiGroupCodes;
    }

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getSubErrorCode() {
        return subErrorCode;
    }

    public void setSubErrorCode(String subErrorCode) {
        this.subErrorCode = subErrorCode;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public static final class Builder {

        private ErrorCodeTypeEnum type;

        private String apiGroupCode;

        private List<String> apiGroupCodes;

        private String apiUri;

        private String errorCode;

        private String subErrorCode;

        private Integer pageNo;

        private Integer pageSize;

        private Builder() {
        }

        public static Builder createBuilder() {
            return new Builder();
        }

        public Builder withType(ErrorCodeTypeEnum type) {
            this.type = type;
            return this;
        }

        public Builder withApiGroupCode(String apiGroupCode) {
            this.apiGroupCode = apiGroupCode;
            return this;
        }

        public Builder withApiGroupCodes(List<String> apiGroupCodes) {
            this.apiGroupCodes = apiGroupCodes;
            return this;
        }

        public Builder withApiUri(String apiUri) {
            this.apiUri = apiUri;
            return this;
        }

        public Builder withErrorCode(String errorCode) {
            this.errorCode = errorCode;
            return this;
        }

        public Builder withSubErrorCode(String subErrorCode) {
            this.subErrorCode = subErrorCode;
            return this;
        }

        public Builder withPageNo(Integer pageNo) {
            this.pageNo = pageNo;
            return this;
        }

        public Builder withPageSize(Integer pageSize) {
            this.pageSize = pageSize;
            return this;
        }

        public ErrorCodePageQueryParam build() {
            ErrorCodePageQueryParam errorCodePageQueryParam = new ErrorCodePageQueryParam();
            errorCodePageQueryParam.setType(type);
            errorCodePageQueryParam.setApiGroupCode(apiGroupCode);
            errorCodePageQueryParam.setApiGroupCodes(apiGroupCodes);
            errorCodePageQueryParam.setApiUri(apiUri);
            errorCodePageQueryParam.setErrorCode(errorCode);
            errorCodePageQueryParam.setSubErrorCode(subErrorCode);
            errorCodePageQueryParam.setPageNo(pageNo);
            errorCodePageQueryParam.setPageSize(pageSize);
            return errorCodePageQueryParam;
        }
    }
}
