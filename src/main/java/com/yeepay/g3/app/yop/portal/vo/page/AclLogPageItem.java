/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.app.yop.portal.enums.AclOperStatusEnum;

import java.io.Serializable;
import java.util.Date;

/**
 * title:操作记录列表项 <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/12/17 11:01
 */
public class AclLogPageItem implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;

    private String operator;

    private String requestIp;

    private AclOperStatusEnum status;

    private Integer latency;

    private String resourceName;

    private Date operDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getRequestIp() {
        return requestIp;
    }

    public void setRequestIp(String requestIp) {
        this.requestIp = requestIp;
    }

    public AclOperStatusEnum getStatus() {
        return status;
    }

    public void setStatus(AclOperStatusEnum status) {
        this.status = status;
    }

    public Integer getLatency() {
        return latency;
    }

    public void setLatency(Integer latency) {
        this.latency = latency;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public Date getOperDate() {
        return operDate;
    }

    public void setOperDate(Date operDate) {
        this.operDate = operDate;
    }
}
