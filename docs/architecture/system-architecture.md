# YOP Portal 系统架构

## 1. 系统概述

YOP Portal 是易宝支付开放平台的门户系统，作为开放平台的核心组件，它提供了API管理、应用管理、权限控制、文档管理等功能。系统采用了微服务架构，基于Spring
Boot构建，通过Dubbo实现服务间的远程调用。

## 2. 架构设计原则

- **高内聚低耦合**：系统各模块之间通过明确的接口进行交互，降低模块间的耦合度
- **分层设计**：采用经典的多层架构，包括表现层、业务逻辑层、数据访问层
- **服务化**：核心业务功能以服务的形式提供，便于复用和扩展
- **安全性**：采用多种安全机制保障系统和数据安全
- **可扩展性**：系统设计支持水平扩展和垂直扩展
- **高可用性**：关键组件支持集群部署，确保系统高可用

## 3. 系统架构图

### 3.1 整体架构

```mermaid
graph TD
    Client[客户端] --> |HTTP/HTTPS| LB[负载均衡]
    LB --> Portal[YOP Portal]
    
    subgraph YOP平台
        Portal --> |Dubbo| ApiMgr[API管理服务]
        Portal --> |Dubbo| AuthMgr[认证授权服务]
        Portal --> |Dubbo| DocMgr[文档管理服务]
        Portal --> |Dubbo| AppMgr[应用管理服务]
        Portal --> |Dubbo| StatMgr[统计监控服务]
        
        ApiMgr --> DB[(数据库)]
        AuthMgr --> DB
        DocMgr --> DB
        AppMgr --> DB
        StatMgr --> DB
        
        ApiMgr --> Cache[(缓存)]
        AuthMgr --> Cache
        DocMgr --> Cache
        AppMgr --> Cache
        StatMgr --> Cache
        
        ApiMgr --> MQ[(消息队列)]
        AuthMgr --> MQ
        DocMgr --> MQ
        AppMgr --> MQ
        StatMgr --> MQ
    end
    
    Portal --> |API调用| Gateway[API网关]
    Gateway --> |路由| Backend[后端服务]
```

### 3.2 逻辑架构

```mermaid
graph TB
    subgraph 表现层
        WebUI[Web界面]
        RESTful[RESTful API]
        Swagger[Swagger UI]
    end
    
    subgraph 业务逻辑层
        ApiService[API服务]
        AuthService[认证授权服务]
        DocService[文档服务]
        AppService[应用服务]
        StatService[统计服务]
    end
    
    subgraph 数据访问层
        DAO[数据访问对象]
        Cache[缓存]
        MQ[消息队列]
    end
    
    subgraph 基础设施层
        DB[(数据库)]
        Redis[(Redis)]
        RabbitMQ[(RabbitMQ)]
        ZK[(ZooKeeper)]
    end
    
    WebUI --> RESTful
    RESTful --> ApiService & AuthService & DocService & AppService & StatService
    Swagger --> RESTful
    
    ApiService & AuthService & DocService & AppService & StatService --> DAO
    ApiService & AuthService & DocService & AppService & StatService --> Cache
    ApiService & AuthService & DocService & AppService & StatService --> MQ
    
    DAO --> DB
    Cache --> Redis
    MQ --> RabbitMQ
    ApiService & AuthService & DocService & AppService & StatService --> ZK
```

## 4. 核心模块

### 4.1 API管理模块

负责API的全生命周期管理，包括API的创建、编辑、发布、下线等功能。

```mermaid
classDiagram
    class ApiController {
        +createApi()
        +updateApi()
        +deleteApi()
        +publishApi()
        +offlineApi()
        +queryApi()
    }
    
    class ApiService {
        +createApi()
        +updateApi()
        +deleteApi()
        +publishApi()
        +offlineApi()
        +queryApi()
    }
    
    class ApiEntity {
        -String apiId
        -String name
        -String version
        -String path
        -String method
        -String status
        -Date createTime
        -Date updateTime
    }
    
    ApiController --> ApiService
    ApiService --> ApiEntity
```

### 4.2 认证授权模块

负责用户认证和权限控制，基于Shiro实现。

```mermaid
classDiagram
    class AuthController {
        +login()
        +logout()
        +checkPermission()
    }
    
    class ShiroConfig {
        +securityManager()
        +shiroFilter()
    }
    
    class UserRealm {
        +doGetAuthenticationInfo()
        +doGetAuthorizationInfo()
    }
    
    AuthController --> ShiroConfig
    ShiroConfig --> UserRealm
```

### 4.3 文档管理模块

负责API文档的生成和管理，支持Swagger UI展示。

```mermaid
classDiagram
    class DocController {
        +generateDoc()
        +updateDoc()
        +publishDoc()
    }
    
    class SwaggerConfig {
        +docket()
        +apiInfo()
    }
    
    class DocService {
        +generateDoc()
        +updateDoc()
        +publishDoc()
    }
    
    DocController --> DocService
    DocController --> SwaggerConfig
```

### 4.4 应用管理模块

负责开发者应用的管理，包括应用创建、密钥管理、权限分配等功能。

```mermaid
classDiagram
    class AppController {
        +createApp()
        +updateApp()
        +deleteApp()
        +generateKey()
        +assignPermission()
    }
    
    class AppService {
        +createApp()
        +updateApp()
        +deleteApp()
        +generateKey()
        +assignPermission()
    }
    
    class AppEntity {
        -String appId
        -String name
        -String secretKey
        -String status
        -Date createTime
        -Date updateTime
    }
    
    AppController --> AppService
    AppService --> AppEntity
```

### 4.5 统计监控模块

负责API调用统计、性能监控等功能。

```mermaid
classDiagram
    class StatController {
        +queryApiStat()
        +queryAppStat()
        +queryPerformance()
    }
    
    class StatService {
        +queryApiStat()
        +queryAppStat()
        +queryPerformance()
    }
    
    class StatEntity {
        -String id
        -String apiId
        -String appId
        -Integer count
        -Long responseTime
        -Date statTime
    }
    
    StatController --> StatService
    StatService --> StatEntity
```

## 5. 关键业务流程

### 5.1 API发布流程

```mermaid
sequenceDiagram
    participant 开发者
    participant Portal as YOP Portal
    participant ApiMgr as API管理服务
    participant DocMgr as 文档管理服务
    participant DB as 数据库
    participant Cache as 缓存
    
    开发者->>Portal: 创建/编辑API
    Portal->>ApiMgr: 保存API信息
    ApiMgr->>DB: 存储API数据
    DB-->>ApiMgr: 返回结果
    ApiMgr-->>Portal: 返回结果
    
    开发者->>Portal: 发布API
    Portal->>ApiMgr: 发布API
    ApiMgr->>DB: 更新API状态
    ApiMgr->>DocMgr: 生成API文档
    DocMgr->>DB: 存储文档数据
    ApiMgr->>Cache: 更新API缓存
    
    DB-->>ApiMgr: 返回结果
    Cache-->>ApiMgr: 返回结果
    DocMgr-->>ApiMgr: 返回结果
    ApiMgr-->>Portal: 返回结果
    Portal-->>开发者: 显示发布成功
```

### 5.2 应用授权流程

```mermaid
sequenceDiagram
    participant 开发者
    participant Portal as YOP Portal
    participant AppMgr as 应用管理服务
    participant AuthMgr as 认证授权服务
    participant DB as 数据库
    
    开发者->>Portal: 创建应用
    Portal->>AppMgr: 保存应用信息
    AppMgr->>DB: 存储应用数据
    DB-->>AppMgr: 返回结果
    AppMgr-->>Portal: 返回结果
    
    开发者->>Portal: 申请API权限
    Portal->>AuthMgr: 创建权限申请
    AuthMgr->>DB: 存储申请数据
    DB-->>AuthMgr: 返回结果
    AuthMgr-->>Portal: 返回结果
    
    Portal->>AuthMgr: 审核权限申请
    AuthMgr->>DB: 更新权限数据
    DB-->>AuthMgr: 返回结果
    AuthMgr-->>Portal: 返回结果
    Portal-->>开发者: 显示授权成功
```

## 6. 接口定义

### 6.1 内部接口

系统内部模块之间通过Dubbo进行远程调用，主要接口包括：

- API管理接口：`ApiMgrFacade`
- 认证授权接口：`AuthMgrFacade`
- 文档管理接口：`DocMgrFacade`
- 应用管理接口：`AppMgrFacade`
- 统计监控接口：`StatMgrFacade`

### 6.2 外部接口

系统对外提供RESTful API，主要接口包括：

- API管理接口：`/api/v1/apis`
- 认证授权接口：`/api/v1/auth`
- 文档管理接口：`/api/v1/docs`
- 应用管理接口：`/api/v1/apps`
- 统计监控接口：`/api/v1/stats`

## 7. 安全设计

### 7.1 认证机制

系统采用多种认证机制，包括：

- 基于用户名密码的表单认证
- 基于JWT的Token认证
- 基于OAuth2.0的第三方认证

### 7.2 授权机制

系统采用基于角色的访问控制（RBAC）模型，通过Shiro实现权限控制。

### 7.3 数据安全

- 敏感数据加密存储
- 传输数据HTTPS加密
- 防SQL注入、XSS攻击等安全措施

## 8. 扩展性设计

### 8.1 水平扩展

- 应用服务无状态设计，支持多实例部署
- 数据库读写分离，支持主从架构
- 缓存集群部署，提高系统性能

### 8.2 垂直扩展

- 模块化设计，支持功能扩展
- 插件化架构，支持第三方插件集成
- 服务化设计，支持新服务接入
