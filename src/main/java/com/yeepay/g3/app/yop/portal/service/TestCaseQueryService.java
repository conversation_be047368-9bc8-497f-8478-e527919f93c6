package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.RegTestCaseVO;
import com.yeepay.g3.app.yop.portal.vo.RegTestExecHisVO;
import com.yeepay.g3.app.yop.portal.vo.RegressionTestControllerVO;

import java.util.List;
import java.util.Map;

public interface TestCaseQueryService {

    RegTestCaseVO findTestCaseById(Long id);

    RegTestExecHisVO findExecHisByHisId(Long id);

    Map<String, Integer> statRegTestCountByApiUris(String[] apiUris);

    List<RegressionTestControllerVO.ExecHisStatResult> execHisStatByHistoryId(Long historyId);

}
