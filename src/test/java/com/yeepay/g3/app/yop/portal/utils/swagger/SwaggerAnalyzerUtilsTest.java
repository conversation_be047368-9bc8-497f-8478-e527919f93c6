/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.utils.swagger;

import com.yeepay.g3.facade.yop.sys.dto.SpiDTO;
import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;
import com.yeepay.g3.facade.yop.sys.enums.SwaggerDataFormatEnum;
import org.junit.Test;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/1/4 5:39 下午
 */
public class SwaggerAnalyzerUtilsTest {

    @Test
    public void testImportJson() {
        String json = "{\"openapi\":\"3.0.1\",\"info\":{\"title\":\"测试YOP\",\"version\":\"1.0\"},\"security\":[{\"YOP-SM2-SM3\":[]},{\"YOP-RSA2048-SHA256\":[]}],\"tags\":[{\"name\":\"test\",\"description\":\"分组\"}],\"components\":{\"schemas\":{\"MtReport\":{\"title\":\"\",\"type\":\"object\",\"properties\":{\"orderId\":{\"maxLength\":20,\"minLength\":1,\"type\":\"string\",\"description\":\"订单ID\"},\"status\":{\"maxLength\":10,\"minLength\":1,\"type\":\"string\",\"description\":\"状态\"}},\"description\":\"状态报告\"},\"MtResultCallback\":{\"title\":\"\",\"type\":\"object\",\"properties\":{\"orderId\":{\"maxLength\":20,\"minLength\":1,\"type\":\"string\",\"description\":\"通知描述\"},\"status\":{\"maxLength\":10,\"minLength\":1,\"type\":\"string\",\"description\":\"状态\"},\"report\":{\"$ref\":\"#/components/schemas/MtReport\"}},\"description\":\"通知结果回调\"}},\"securitySchemes\":{\"YOP-SM2-SM3\":{\"type\":\"apiKey\",\"name\":\"Authorization\",\"in\":\"header\"},\"YOP-RSA2048-SHA256\":{\"type\":\"apiKey\",\"name\":\"Authorization\",\"in\":\"header\",\"x-yop-apigateway-auth-type\":\"YOP-RSA2048-SHA256\"}},\"callbacks\":{\"test.status.report\":{\"#{params.callbackUrl}\":{\"post\":{\"summary\":\"通知状态报告\",\"description\":\"通知状态报告1\",\"requestBody\":{\"description\":\"通知状态回调请求体\",\"content\":{\"application/json\":{\"schema\":{\"$ref\":\"#/components/schemas/MtResultCallback\"}}}},\"responses\":{\"200\":{\"description\":\"返回\",\"content\":{\"text/plain\":{\"schema\":{\"type\":\"string\"},\"examples\":{\"success\":{\"summary\":\"成功返回\",\"value\":\"success\"}}}}}}}},\"x-yop-callback-api-relations\":[{\"path\":\"/rest/v1.0/test/post-from\",\"httpMethod\":\"POST\"},{\"path\":\"/rest/v1.0/test/query/query-agreement-pay-order\",\"httpMethod\":\"OLD_API\"},{\"path\":\"/yos/v1.0/test/file-upload\",\"httpMethod\":\"POST\"}]}}},\"x-yop-apigateway-group\":\"test\"}";
        SwaggerAnalysisResult analysis = SwaggerAnalyzer.analysis(json, SwaggerDataFormatEnum.JSON);
        String apiGroup = analysis.getApiGroup();
        assert "test".equals(apiGroup);
        Map<String, SpiDTO> callbacks = analysis.getCallbacks();
        assert null != callbacks && callbacks.size() == 1;
        String spiName = "test.status.report";
        SpiDTO spiDTO = callbacks.get(spiName);
        assert null != spiDTO;
        Map<String, List<ApiRequestKey>> callbackApiRelations = analysis.getCallbackApiRelations();
        assert null != callbackApiRelations && !callbackApiRelations.isEmpty();
        List<ApiRequestKey> apiRequestKeys = callbackApiRelations.get(spiName);
        assert null != apiRequestKeys && apiRequestKeys.size() == 3;
        Set<ApiRequestKey> set = new HashSet<>();
        set.add(new ApiRequestKey().withPath("/rest/v1.0/test/post-from").withHttpMethod("POST"));
        set.add(new ApiRequestKey().withPath("/rest/v1.0/test/query/query-agreement-pay-order").withHttpMethod("OLD_API"));
        set.add(new ApiRequestKey().withPath("/yos/v1.0/test/file-upload").withHttpMethod("POST"));
        assert set.equals(new HashSet<>(apiRequestKeys));
    }

    @Test
    public void testImportYaml() {
        String yaml = "openapi: 3.0.1\n" +
                "info:\n" +
                "  title: 测试YOP\n" +
                "  version: \"1.0\"\n" +
                "security:\n" +
                "- YOP-SM2-SM3: []\n" +
                "- YOP-RSA2048-SHA256: []\n" +
                "tags:\n" +
                "- name: test\n" +
                "  description: 分组\n" +
                "components:\n" +
                "  schemas:\n" +
                "    MtReport:\n" +
                "      title: \"\"\n" +
                "      type: object\n" +
                "      properties:\n" +
                "        orderId:\n" +
                "          maxLength: 20\n" +
                "          minLength: 1\n" +
                "          type: string\n" +
                "          description: 订单ID\n" +
                "        status:\n" +
                "          maxLength: 10\n" +
                "          minLength: 1\n" +
                "          type: string\n" +
                "          description: 状态\n" +
                "      description: 状态报告\n" +
                "    MtResultCallback:\n" +
                "      title: \"\"\n" +
                "      type: object\n" +
                "      properties:\n" +
                "        orderId:\n" +
                "          maxLength: 20\n" +
                "          minLength: 1\n" +
                "          type: string\n" +
                "          description: 通知描述\n" +
                "        status:\n" +
                "          maxLength: 10\n" +
                "          minLength: 1\n" +
                "          type: string\n" +
                "          description: 状态\n" +
                "        report:\n" +
                "          $ref: '#/components/schemas/MtReport'\n" +
                "      description: 通知结果回调\n" +
                "  securitySchemes:\n" +
                "    YOP-SM2-SM3:\n" +
                "      type: apiKey\n" +
                "      name: Authorization\n" +
                "      in: header\n" +
                "    YOP-RSA2048-SHA256:\n" +
                "      type: apiKey\n" +
                "      name: Authorization\n" +
                "      in: header\n" +
                "      x-yop-apigateway-auth-type: YOP-RSA2048-SHA256\n" +
                "  callbacks:\n" +
                "    test.status.report:\n" +
                "      '#{params.callbackUrl}':\n" +
                "        post:\n" +
                "          summary: 通知状态报告\n" +
                "          description: 通知状态报告1\n" +
                "          requestBody:\n" +
                "            description: 通知状态回调请求体\n" +
                "            content:\n" +
                "              application/json:\n" +
                "                schema:\n" +
                "                  $ref: '#/components/schemas/MtResultCallback'\n" +
                "          responses:\n" +
                "            \"200\":\n" +
                "              description: 返回\n" +
                "              content:\n" +
                "                text/plain:\n" +
                "                  schema:\n" +
                "                    type: string\n" +
                "                  examples:\n" +
                "                    success:\n" +
                "                      summary: 成功返回\n" +
                "                      value: success\n" +
                "      x-yop-callback-api-relations:\n" +
                "      - path: /rest/v1.0/test/post-from\n" +
                "        httpMethod: POST\n" +
                "      - path: /rest/v1.0/test/query/query-agreement-pay-order\n" +
                "        httpMethod: OLD_API\n" +
                "      - path: /yos/v1.0/test/file-upload\n" +
                "        httpMethod: POST\n" +
                "x-yop-apigateway-group: test";
        SwaggerAnalysisResult analysis = SwaggerAnalyzer.analysis(yaml, SwaggerDataFormatEnum.YAML);
        String apiGroup = analysis.getApiGroup();
        assert "test".equals(apiGroup);
        Map<String, SpiDTO> callbacks = analysis.getCallbacks();
        assert null != callbacks && callbacks.size() == 1;
        String spiName = "test.status.report";
        SpiDTO spiDTO = callbacks.get(spiName);
        assert null != spiDTO;
        Map<String, List<ApiRequestKey>> callbackApiRelations = analysis.getCallbackApiRelations();
        assert null != callbackApiRelations && !callbackApiRelations.isEmpty();
        List<ApiRequestKey> apiRequestKeys = callbackApiRelations.get(spiName);
        assert null != apiRequestKeys && apiRequestKeys.size() == 3;
        Set<ApiRequestKey> set = new HashSet<>();
        set.add(new ApiRequestKey().withPath("/rest/v1.0/test/post-from").withHttpMethod("POST"));
        set.add(new ApiRequestKey().withPath("/rest/v1.0/test/query/query-agreement-pay-order").withHttpMethod("OLD_API"));
        set.add(new ApiRequestKey().withPath("/yos/v1.0/test/file-upload").withHttpMethod("POST"));
        assert set.equals(new HashSet<>(apiRequestKeys));
    }

}
