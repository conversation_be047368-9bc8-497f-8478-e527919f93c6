package com.yeepay.g3.yop.frame.exception.authentication;

import com.yeepay.g3.yop.frame.CharacterConstants;

/**
 * title: 不支持的加密算法<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-04-23 15:18
 */
public class UnSupportedEncryptAlgException extends CryptoException {

    private static final long serialVersionUID = -1L;

    @Override
    public String getCode() {
        return super.getCode() + CharacterConstants.DOT + "algorithm" + CharacterConstants.DOT + "unsupported";
    }
}
