/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ProductChangesQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.ProductChangePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.ProductChangesPageItem;
import com.yeepay.g3.facade.yop.sys.enums.ProductOperateTypeEnum;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/20 上午11:58
 */
@Component
public class ProductChangesQueryServiceImpl implements ProductChangesQueryService {

    @Resource(name = "productChangesQueryService")
    private QueryService queryService;

    private final PageItemConverter<ProductChangesPageItem> pageItemConverter = new ProductChangesPageItemConverter();

    @Override
    public PageQueryResult<ProductChangesPageItem> pageQuery(ProductChangePageQueryParam param) {
        Map<String, Object> bizParams = getBizParams(param);
        List<Map> list = queryService.query("pageList", bizParams);
        PageQueryResult<ProductChangesPageItem> result = new PageQueryResult<>();
        List<ProductChangesPageItem> items = new ArrayList<>(CollectionUtils.size(list));
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(map -> items.add(pageItemConverter.convert(map)));
        }
        result.setItems(items);
        result.setPageNo(param.getPageNo());
        return result;
    }

    private Map<String, Object> getBizParams(ProductChangePageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("productCode", param.getProductCode());
        bizParams.put("type", param.getType());
        bizParams.put("operator", param.getOperator());
        bizParams.put("createdStartDate", param.getCreatedStartDate());
        bizParams.put("createdEndDate", param.getCreatedEndDate());
        Integer pageNo = param.getPageNo() == null ? 1 : param.getPageNo();
        Integer pageSize = param.getPageSize() == null ? 10 : param.getPageSize();
        bizParams.put("_startIndex", pageNo <= 1 ? 0 : (pageNo - 1) * pageSize);
        bizParams.put("_maxSize", pageSize);
        return bizParams;
    }

    class ProductChangesPageItemConverter extends BasePageItemConverter<ProductChangesPageItem> {

        @Override
        public ProductChangesPageItem convert(Map<String, Object> params) {
            ProductChangesPageItem item = new ProductChangesPageItem();
            item.setProductCode((String) params.get("product_code"));
            item.setProductName((String) params.get("product_name"));
            item.setType(ProductOperateTypeEnum.parse((String) params.get("oper_type")));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setOperator((String) params.get("operator"));
            item.setCause((String) params.get("oper_cause"));
            item.setDetail((String) params.get("oper_detail"));
            return item;
        }
    }
}
