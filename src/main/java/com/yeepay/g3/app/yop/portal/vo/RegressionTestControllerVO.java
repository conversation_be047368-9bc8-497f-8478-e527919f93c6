package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.regression.ExecutionResult;
import com.yeepay.g3.app.yop.portal.regression.sdk.model.RegressionTestAppDTO;
import com.yeepay.g3.app.yop.portal.vo.page.ApiDefinePageItem;
import com.yeepay.g3.facade.yop.api.dto.ApiParamDefineDTO;
import com.yeepay.g3.facade.yop.api.val.ValRule;
import com.yeepay.g3.facade.yop.api.val.rule.NotBlank;
import com.yeepay.g3.facade.yop.api.val.rule.NotNull;
import com.yeepay.g3.facade.yop.sys.dto.*;
import com.yeepay.g3.sdk.yop.client.YopResponse;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.data.annotation.Id;

import java.io.Serializable;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/8/6 上午1:24
 */
public class RegressionTestControllerVO {

    /**
     * api及回归用例个数
     */
    public static class ListPost {

        @JsonProperty
        public Long apiId;

        @JsonProperty
        public String apiUri;

        @JsonProperty
        public String apiTitle;

        @JsonProperty
        public String apiGroupCode;

        @JsonProperty
        public String apiGroupTitle;

        @JsonProperty
        public Integer caseCount;

        @JsonProperty
        public final Integer[] regressionResult = {0, 0};

        @JsonProperty
        public final String lastRegressionTime = "";

        public ListPost(ApiDefinePageItem api, int caseCount) {
            this.apiId = api.getApiId();
            this.apiUri = api.getApiUri();
            this.apiTitle = api.getApiTitle();
            this.apiGroupCode = api.getApiGroupCode();
            this.apiGroupTitle = api.getApiGroupTitle();
            this.caseCount = caseCount;
        }
    }

    /**
     * 回归执行请求
     */
    public static class BatchExecutionVO {
        public String environment;
        public String[] apiUriList;
    }

    /**
     * 测试用例导入VO
     */
    public static class TestCaseImportVO implements Serializable {

        private static final long serialVersionUID = -1;

        public List<RegTestCaseImExportDTO> caseList;

        public List<RegTestCaseImExportDTO> getCaseList() {
            return caseList;
        }

        public void setCaseList(List<RegTestCaseImExportDTO> caseList) {
            this.caseList = caseList;
        }

        @Override
        public String toString() {
            return "TestCaseImportVO{" +
                    "caseList=" + caseList +
                    '}';
        }
    }

    /**
     * 查询执行结果
     */
    public static class QueryExecutionResult implements Serializable {

        private static final long serialVersionUID = -1L;

        @JsonProperty
        public String status;

        @JsonProperty
        public Date executionTime;

        @JsonProperty
        public Long batchExecutionId;

        @JsonProperty
        public List<ExecHisStatResult> executionList;

        public QueryExecutionResult(RegTestExecHisVO regTestExecHisVO, List<ExecHisStatResult> executionList) {
            this.status = regTestExecHisVO.getStatus();
            this.executionTime = regTestExecHisVO.getStartExecuteTime();
            this.batchExecutionId = regTestExecHisVO.getId();
            this.executionList = executionList;
        }
    }

    public static class BatchExecutionStatMapReduceResult {

        @Id
        @JsonProperty
        Long apiId;

        @JsonIgnore
        Value value;

        @JsonProperty("successCount")
        int getSuccessCount() {
            return value.successCount;
        }

        @JsonProperty("failureCount")
        int getFailureCount() {
            return value.failureCount;
        }

        static class Value {
            int successCount;
            int failureCount;
        }
    }

    public static class ExecHisStatResult implements Serializable {

        private static final long serialVersionUID = -1;

        private String apiUri;

        private int successCount;

        private int failureCount;

        public String getApiUri() {
            return apiUri;
        }

        public void setApiUri(String apiUri) {
            this.apiUri = apiUri;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public void setSuccessCount(int successCount) {
            this.successCount = successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public void setFailureCount(int failureCount) {
            this.failureCount = failureCount;
        }

        @Override
        public String toString() {
            return "ExecHisStatResult{" +
                    "apiUri='" + apiUri + '\'' +
                    ", successCount=" + successCount +
                    ", failureCount=" + failureCount +
                    '}';
        }
    }

    /**
     * 执行日志
     */
    public static class ExecutionLog {

        @JsonProperty
        public String title;

        @JsonProperty
        public String errorMsg;

        @JsonProperty
        public List<com.yeepay.g3.facade.yop.sys.dto.RegressionAssertionDTO> assertionList;

        public ExecutionLog(RegTestExecRecordQryRespDTO record) {
            this.title = record.getTestCaseTitle();
            this.assertionList = record.getAssertionList();
            this.errorMsg = record.getErrorMsg();
        }
    }

    /*============= list-single-api(post) ====================*/

    /**
     *
     */
    public static class ListSingleApi {

        @JsonProperty
        public Long id;

        @JsonProperty
        public String title;

        @JsonProperty
        public int assertionCount;

        @JsonProperty
        public Boolean regressive;

        @JsonProperty
        public Date lastModifiedDateTime;

        public ListSingleApi(RegTestCaseFindRespDTO testCase) {
            this.id = testCase.getId();
            this.title = testCase.getTitle();
            this.assertionCount = testCase.getAssertionList() == null ? 0 : testCase.getAssertionList().size();
            this.regressive = testCase.getRegressive();
            this.lastModifiedDateTime = testCase.getLastModifiedDateTime();
        }
    }

    /**
     * 请求参数（含form参数和json参数）
     */
    public static class RequestParamWrapper {

        @JsonProperty
        public List<Map> form;

        @JsonProperty
        public String json;
    }

    public static class AppKeyCustomerNoPair {

        @JsonProperty
        public String appKey;

        @JsonProperty
        public String customerNo;

        public AppKeyCustomerNoPair(RegressionTestAppDTO appDTO) {
            this.appKey = appDTO.getAppKey();
            this.customerNo = appDTO.getCustomerNo();
        }
    }

    /**
     * 用例详情
     */
    public static class Detail {

        @JsonProperty
        public List<SecurityItem> securities;

        @JsonProperty
        public Long tokenId;
        @JsonProperty
        public String tokenName;

        @JsonProperty
        public String security;

        @JsonProperty
        public String appKey;

        @JsonProperty
        public String env;

        @JsonProperty
        public List<AppKeyCustomerNoPair> apps;

        @JsonProperty
        public String cert;

        @JsonProperty
        public Boolean regressive;

        @JsonProperty
        public List<com.yeepay.g3.facade.yop.sys.dto.RegressionAssertionDTO> assertionList;

        @JsonProperty
        public RequestParamWrapper params;

        public Detail(RegTestCaseVO testCase, ApiVO apiVO, List<RegressionTestAppDTO> apps, boolean isCreate) {
            this.security = testCase.getSecurity();
            this.appKey = testCase.getAppKey();
            this.tokenId = testCase.getTokenId();
            this.tokenName = testCase.getTokenName();
            this.regressive = testCase.getRegressive();
            this.assertionList = testCase.getAssertionList();

            List<ApiParamDefineDTO> paramDefineDTOS = apiVO.getParamList();

            params = new RequestParamWrapper();

            params.form = Lists.newArrayList();
            for (ApiParamDefineDTO paramDefineDTO : paramDefineDTOS) {
                if (paramDefineDTO.getInternal()) {/*skip internal param*/
                    continue;
                }
                Map m = Maps.newHashMap();
                String paramName = paramDefineDTO.getParamName();
                m.put("name", paramName);
                m.put("title", paramDefineDTO.getParamTitle());
                m.put("required", isParamRequired(paramDefineDTO));
                m.put("value", isCreate ? null : getParamValueAndRemoveFromList(paramName, testCase.getFormRequestParam()));
                params.form.add(m);
            }
            params.json = testCase.getJsonRequestParam();

            this.apps = Lists.newArrayList();
            for (RegressionTestAppDTO app : apps) {
                this.apps.add(new AppKeyCustomerNoPair(app));
            }

            securities = Lists.newArrayList();
            List<SecurityReqDTO> apiSecurities = apiVO.getApiSecurity();
            if (apiSecurities != null) {
                for (SecurityReqDTO securityReq : apiSecurities) {
                    securities.add(new SecurityItem(securityReq.getName(), securityReq.getName()));
                }
            }
        }

        private boolean isParamRequired(ApiParamDefineDTO paramDefineDTO) {
            List<ValRule> valRules = paramDefineDTO.getValRules();
            if (valRules == null || valRules.size() == 0) {
                return false;
            }
            for (ValRule rule : valRules) {
                if (rule instanceof NotNull || rule instanceof NotBlank) {
                    return true;
                }
            }
            return false;
        }

        private String getParamValueAndRemoveFromList(String name, List<FormRequestParamDTO> list) {
            if (list == null || list.size() == 0) {
                return null;
            }
            Iterator<FormRequestParamDTO> it = list.iterator();
            for (; it.hasNext(); ) {
                FormRequestParamDTO param = it.next();
                if (param.getName().equals(name)) {
                    it.remove();
                    return param.getValue();
                }
            }
            return null;
        }
    }

    public static class SecurityItem {

        @JsonProperty
        public String name;

        @JsonProperty
        public String title;

        public SecurityItem() {
        }

        public SecurityItem(String name, String title) {
            this.name = name;
            this.title = title;
        }
    }


    public static class ExecuteSingleByRaw {

        @JsonProperty
        public String status;
        @JsonProperty
        public String rawResponse;
        @JsonProperty
        public List<com.yeepay.g3.facade.yop.sys.dto.RegressionAssertionDTO> assertionList;

        public ExecuteSingleByRaw(ExecutionResult executionResult) {
            this.status = executionResult.isSuccess() ? "success" : "failure";
            YopResponse response = executionResult.getResponse();
            if (response != null) {
                this.rawResponse = response.getState().equals("SUCCESS") ? response.getStringResult() : response.toString();
            }
            this.assertionList = executionResult.getAssertionList();
        }
    }

    /**
     * 环境列表
     */
    public static class ExecutionEnvironmentVO {

        @JsonProperty
        public String name;
        @JsonProperty
        public String title;

        public ExecutionEnvironmentVO(String name, String title) {
            this.name = name;
            this.title = title;
        }
    }


    public static class RegressionTestCaseVO implements Serializable {

        private String id;

        private Long apiId;

        private String apiUri;

        private String title;

        private Boolean regressive;

        private String appKey;

        private String cert;

        private String security;

        @JsonProperty("env")
        private String environment;

        private RequestParamWrapper params;

        private List<com.yeepay.g3.facade.yop.sys.dto.RegressionAssertionDTO> assertionList;

        private Date createdDateTime;

        private Date lastModifiedDateTime;

        private String accessToken;

        private Long tokenId;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public Long getApiId() {
            return apiId;
        }

        public void setApiId(Long apiId) {
            this.apiId = apiId;
        }

        public String getApiUri() {
            return apiUri;
        }

        public void setApiUri(String apiUri) {
            this.apiUri = apiUri;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public Boolean getRegressive() {
            return regressive;
        }

        public void setRegressive(Boolean regressive) {
            this.regressive = regressive;
        }

        public String getAppKey() {
            return appKey;
        }

        public void setAppKey(String appKey) {
            this.appKey = appKey;
        }

        public String getCert() {
            return cert;
        }

        public void setCert(String cert) {
            this.cert = cert;
        }

        public String getSecurity() {
            return security;
        }

        public void setSecurity(String security) {
            this.security = security;
        }

        public String getEnvironment() {
            return environment;
        }

        public void setEnvironment(String environment) {
            this.environment = environment;
        }

        public RequestParamWrapper getParams() {
            return params;
        }

        public void setParams(RequestParamWrapper params) {
            this.params = params;
        }

        public List<com.yeepay.g3.facade.yop.sys.dto.RegressionAssertionDTO> getAssertionList() {
            return assertionList;
        }

        public void setAssertionList(List<com.yeepay.g3.facade.yop.sys.dto.RegressionAssertionDTO> assertionList) {
            this.assertionList = assertionList;
        }

        public Date getCreatedDateTime() {
            return createdDateTime;
        }

        public void setCreatedDateTime(Date createdDateTime) {
            this.createdDateTime = createdDateTime;
        }

        public Date getLastModifiedDateTime() {
            return lastModifiedDateTime;
        }

        public void setLastModifiedDateTime(Date lastModifiedDateTime) {
            this.lastModifiedDateTime = lastModifiedDateTime;
        }

        public String getAccessToken() {
            return accessToken;
        }

        public void setAccessToken(String accessToken) {
            this.accessToken = accessToken;
        }

        public Long getTokenId() {
            return tokenId;
        }

        public void setTokenId(Long tokenId) {
            this.tokenId = tokenId;
        }

        @Override
        public String toString() {
            return ToStringBuilder.reflectionToString(this,
                    ToStringStyle.SHORT_PREFIX_STYLE);
        }
    }
}
