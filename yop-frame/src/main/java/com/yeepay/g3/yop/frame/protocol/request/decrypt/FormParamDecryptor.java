package com.yeepay.g3.yop.frame.protocol.request.decrypt;

import com.yeepay.g3.yop.frame.context.impl.FormDataContext;
import com.yeepay.g3.yop.frame.definition.authenticate.SecretKey;
import com.yeepay.g3.yop.frame.security.encryptor.Encryptor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * title: form表单参数解密器<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-04-19 18:28
 */
@Component
public class FormParamDecryptor extends AbstractParamDecryptor<FormDataContext> {

    @Override
    protected void doDecrypt(FormDataContext context, Encryptor encryptor, <PERSON><PERSON><PERSON> secretKey) throws Throwable {
        Map<String, String[]> paramContainer = context.getParamContainer();
        if (MapUtils.isNotEmpty(paramContainer)) {
            for (Map.Entry<String, String[]> entry : paramContainer.entrySet()) {
                String key = entry.getKey();
                String[] values = entry.getValue();
                String[] decryptValues = new String[values.length];
                for (int i = 0; i < values.length; i++) {
                    try {
                        decryptValues[i] = encryptor.decrypt(values[i], (String) secretKey.getKey());
                    } catch (Throwable ex) {
                        logger.warn("decrypt formParam:" + key + " failure, digest:" + secretKey.getDigest(), ex);
                        throw ex;
                    }
                }
                entry.setValue(decryptValues);
            }
        }
    }

    @Override
    public Class<? extends FormDataContext> supportedParamContext() {
        return FormDataContext.class;
    }
}
