/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.facade.yop.perm.dto.AclAuditDTO;
import com.yeepay.g3.facade.yop.perm.facade.AclAuditFacade;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * title:审核设置 <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-11-25 15:11
 */
@Controller
@RequestMapping("/rest/acl/audit")
public class AclAuditController {

    private AclAuditFacade aclAuditFacade = RemoteServiceFactory.getService(AclAuditFacade.class);

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam("resourceId") Long resourceId) {
        AclAuditDTO aclAuditDTO = aclAuditFacade.findAudit(resourceId);
        return new ResponseMessage("result", aclAuditDTO);
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody AclAuditDTO aclAuditDTO) {
        aclAuditFacade.updateAudit(aclAuditDTO);
        return new ResponseMessage();
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam("resourceId") Long resourceId) {
        aclAuditFacade.deleteAudit(resourceId);
        return new ResponseMessage();
    }

}
