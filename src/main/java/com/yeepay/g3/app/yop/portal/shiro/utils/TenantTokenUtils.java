/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.utils;

import com.yeepay.g3.app.yop.portal.exception.YopPortalAuthException;
import com.yeepay.g3.app.yop.portal.shiro.authc.TenantToken;
import com.yeepay.g3.core.yop.utils.Encodes;
import com.yeepay.g3.facade.mp.utils.JsonUtils;
import com.yeepay.g3.facade.yop.sys.dto.TenantCertDTO;
import com.yeepay.g3.facade.yop.sys.dto.TenantConfigDTO;
import com.yeepay.g3.facade.yop.sys.facade.TenantQueryFacade;
import com.yeepay.g3.frame.yop.ca.rsa.RSAKeyUtils;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import io.jsonwebtoken.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * title: TenantTokenUtils<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/7/27 5:36 下午
 */
public class TenantTokenUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantTokenUtils.class);
    private static final String AUD = "YOP:ISP";

    /**
     * 验证租户token
     *
     * @param token
     * @return
     */
    public static TenantToken verify(String token) {
        try {
            String tenantCode = getTenantCode(token);
            if (StringUtils.isEmpty(tenantCode)) {
                throw new YopPortalAuthException("无法认证令牌");
            }
            TenantConfigDTO tenantConfigDTO = getTenantQueryFacade().findConfig(tenantCode);
            if (Objects.isNull(tenantConfigDTO)) {
                throw new YopPortalAuthException("无法认证由" + tenantCode + "颁发的令牌");
            }
            TenantCertDTO tenantCertDTO = tenantConfigDTO.getCert();
            Jws<Claims> jws = Jwts.parser().setSigningKey(RSAKeyUtils.string2PublicKey(tenantCertDTO.getValue())).parseClaimsJws(token);
            String aud = jws.getBody().getAudience();
            if (!AUD.equals(aud)) {
                throw new YopPortalAuthException("无法认证颁发给" + aud + "令牌");
            }
            TenantToken tenantToken = new TenantToken(jws.getBody().getAudience(), jws.getBody().getIssuer(), jws.getBody().getSubject());
            tenantToken.setTicket(token);
            return tenantToken;
        } catch (ExpiredJwtException e) {
            LOGGER.warn("Expired token:{}", token);
            throw new YopPortalAuthException("Expired token");
        } catch (UnsupportedJwtException e) {
            LOGGER.warn("Unsupported token:{}", token);
            throw new YopPortalAuthException("Unsupported token");
        } catch (MalformedJwtException e) {
            LOGGER.warn("Malformed token:{}", token);
            throw new YopPortalAuthException("Malformed token");
        } catch (SignatureException e) {
            LOGGER.warn("Invalid signed token:{}", token);
            throw new YopPortalAuthException("Invalid signed token");
        } catch (IllegalArgumentException e) {
            LOGGER.warn("Illegal argument token:{}", token);
            throw new YopPortalAuthException("Illegal argument token");
        } catch (Exception e) {
            LOGGER.warn("Unsupported token caused by {}, token:{}", e.getMessage(), token);
            throw new YeepayRuntimeException();
        }

    }

    private static String getTenantCode(String token) {
        String jwtParts[] = StringUtils.split(token, ".");
        Map payload = JsonUtils.JsonString2Map(new String(Encodes.decodeBase64(jwtParts[1])));
        return (String) payload.get(Claims.ISSUER);
    }

    public static TenantQueryFacade getTenantQueryFacade() {
        return RemoteServiceFactory.getService(TenantQueryFacade.class);
    }
}
