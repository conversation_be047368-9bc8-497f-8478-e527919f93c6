package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.sys.dto.FormRequestParamDTO;
import com.yeepay.g3.facade.yop.sys.dto.RegressionAssertionDTO;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * title: <br>
 * description: 测试用例VO<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/6/18 下午4:14
 */
public class RegTestCaseVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id ;
    private String apiUri ;
    private String title ;
    private Boolean regressive ;
    private String appKey ;
    private String security ;
    private List<FormRequestParamDTO> formRequestParam;
    private String jsonRequestParam;
    private List<RegressionAssertionDTO> assertionList;
    private Long tokenId;
    private String tokenName;
    private Date createdDateTime;
    private Date lastModifiedDateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getApiUri() {
        return apiUri;
    }

    public void setApiUri(String apiUri) {
        this.apiUri = apiUri;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Boolean getRegressive() {
        return regressive;
    }

    public void setRegressive(Boolean regressive) {
        this.regressive = regressive;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getSecurity() {
        return security;
    }

    public void setSecurity(String security) {
        this.security = security;
    }

    public List<FormRequestParamDTO> getFormRequestParam() {
        return formRequestParam;
    }

    public void setFormRequestParam(List<FormRequestParamDTO> formRequestParam) {
        this.formRequestParam = formRequestParam;
    }

    public String getJsonRequestParam() {
        return jsonRequestParam;
    }

    public void setJsonRequestParam(String jsonRequestParam) {
        this.jsonRequestParam = jsonRequestParam;
    }

    public List<RegressionAssertionDTO> getAssertionList() {
        return assertionList;
    }

    public void setAssertionList(List<RegressionAssertionDTO> assertionList) {
        this.assertionList = assertionList;
    }

    public Long getTokenId() {
        return tokenId;
    }

    public void setTokenId(Long tokenId) {
        this.tokenId = tokenId;
    }

    public String getTokenName() {
        return tokenName;
    }

    public void setTokenName(String tokenName) {
        this.tokenName = tokenName;
    }

    public Date getCreatedDateTime() {
        return createdDateTime;
    }

    public void setCreatedDateTime(Date createdDateTime) {
        this.createdDateTime = createdDateTime;
    }

    public Date getLastModifiedDateTime() {
        return lastModifiedDateTime;
    }

    public void setLastModifiedDateTime(Date lastModifiedDateTime) {
        this.lastModifiedDateTime = lastModifiedDateTime;
    }

    @Override
    public String toString() {
        return "RegTestCaseVO{" +
                "id=" + id +
                ", apiUri='" + apiUri + '\'' +
                ", title='" + title + '\'' +
                ", regressive=" + regressive +
                ", appKey='" + appKey + '\'' +
                ", security='" + security + '\'' +
                ", formRequestParam=" + formRequestParam +
                ", jsonRequestParam='" + jsonRequestParam + '\'' +
                ", assertionList=" + assertionList +
                ", tokenId=" + tokenId +
                ", tokenName='" + tokenName + '\'' +
                ", createdDateTime=" + createdDateTime +
                ", lastModifiedDateTime=" + lastModifiedDateTime +
                '}';
    }
}
