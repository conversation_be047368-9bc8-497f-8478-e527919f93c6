package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.app.yop.portal.dto.AuditContentDTO;
import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.app.yop.portal.shiro.utils.Constants;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.facade.yop.perm.dto.AclAuditRecordDTO;
import com.yeepay.g3.facade.yop.perm.dto.AclResourceDTO;
import com.yeepay.g3.facade.yop.perm.facade.AclAuditFacade;
import com.yeepay.g3.facade.yop.perm.facade.AclResourceMgrFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.authz.AuthorizationFilter;
import org.apache.shiro.web.servlet.ShiroHttpServletRequest;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.net.URLDecoder;
import java.util.List;

/**
 * title: 审核拦截器，用于阶段请求并创建审核单<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/11/27 16:31
 */
@Component
public class AclAuditRecordAuthorizationFilter extends AuthorizationFilter implements CustomShiroFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(AclAuditRecordAuthorizationFilter.class);

    private static final String CAUSE = "x-yop-cause";

    private static ThreadLocal<String> threadLocalAuditThread = new ThreadLocal();

    private static final String OPTIONS = "OPTIONS";

    private static final String GET = "GET";

    @Override
    protected boolean isEnabled(ServletRequest request, ServletResponse response) throws ServletException, IOException {
        String method = ((ShiroHttpServletRequest) request).getMethod();
        // get options 不进行拦截
        if (StringUtils.equals(method, OPTIONS) || StringUtils.equals(method, GET)) {
            return false;
        }
        Object skip = request.getAttribute(Constants._PORTAL_SKIP_FILTER);
        if (skip != null && (Boolean) skip == true) {
            return false;
        }
        return true;
    }

    @Override
    protected boolean isAccessAllowed(ServletRequest servletRequest, ServletResponse servletResponse, Object o) throws Exception {
        try {
            AclResourceMgrFacade aclResourceMgrFacade = RemoteServiceFactory.getService(AclResourceMgrFacade.class);
            AclAuditFacade aclAuditFacade = RemoteServiceFactory.getService(AclAuditFacade.class);

            String uri = ((ShiroHttpServletRequest) servletRequest).getRequestURI();
            List<AclResourceDTO> aclResourceDTOS = aclResourceMgrFacade.findByResourceUrl(uri);
            AclAuditRecordDTO aclAuditRecordDTO = new AclAuditRecordDTO();
            String cause = ((ShiroHttpServletRequest) servletRequest).getHeader(CAUSE);
            cause = URLDecoder.decode(cause, "utf-8");
            if (StringUtils.isEmpty(cause)) {
                // 系统默认生成无原因
                cause = "无";
            }
            AclResourceDTO aclResourceDTO = aclResourceDTOS.get(0);
            aclAuditRecordDTO.setResourceId(aclResourceDTO.getId());
            aclAuditRecordDTO.setCause(cause);
            aclAuditRecordDTO.setOriginator(ShiroUtils.getOperatorCode());
            AuditContentDTO contentDTO = new AuditContentDTO();
            contentDTO.setContentType(servletRequest.getContentType());
            contentDTO.setParams(servletRequest.getParameterMap());
            String content = IOUtils.toString(servletRequest.getInputStream());
            contentDTO.setBody(URLDecoder.decode(content, "utf-8"));
            contentDTO.setHttpMethod(((ShiroHttpServletRequest) servletRequest).getMethod());
            aclAuditRecordDTO.setContent(JsonMapper.nonEmptyMapper().toJson(contentDTO));
            aclAuditRecordDTO.setSpCode((String) servletRequest.getAttribute(Constants._PORTAL_SP_CODE));
            aclAuditFacade.createRecord(aclAuditRecordDTO);
            threadLocalAuditThread.set("{\"status\":\"success\",\"message\":\"您已发起审核\"}");
        } catch (Exception e) {
            LOGGER.error("create audit record error,error:", e);
            threadLocalAuditThread.set("{\"status\":\"error\",\"message\":\"操作失败\"}");
        }
        return false;
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws IOException {
        Subject subject = getSubject(request, response);
        // If the subject isn't identified, redirect to login URL
        if (subject.getPrincipal() == null) {
            saveRequestAndRedirectToLogin(request, response);
        } else {
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();
            out.println(threadLocalAuditThread.get());
            out.flush();
            out.close();
        }
        return false;
    }

    @Override
    public String shiroName() {
        return "audit_record";
    }
}
