package com.yeepay.g3.app.yop.portal.servlet;


import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.facade.yop.doc.dto.AttachmentDTO;
import com.yeepay.g3.facade.yop.doc.facade.AttachmentFacade;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.yeepay.g3.app.yop.portal.utils.Constants.*;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-01-08 20:07
 */
@Component
public class AttachmentAccessServlet extends HttpServlet {
    private static final Logger LOGGER = LoggerFactory.getLogger(AttachmentAccessServlet.class);
    private static final String ATTACHMENT_CACHE_PREFIX = "yop-doc:attachments:";

    @Autowired
    private RedisTemplate<String, String> stringRedisTemplate;

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        try {
            String fileId = req.getParameter("fileId");
            String cacheKey = ATTACHMENT_CACHE_PREFIX + fileId;
            String cephPath = stringRedisTemplate.opsForValue().get(cacheKey);
            final Map<String, String> cephConfig = getCephConfig();
            if (StringUtils.isNotBlank(cephPath)) {
                resp.setStatus(HttpServletResponse.SC_MOVED_PERMANENTLY);
                resp.setHeader("Location", cephConfig.get(CEPH_NGINX_ACCESS_PREFIX) + rmBucketPrefix(cephPath, cephConfig));
            } else {
                AttachmentFacade attachmentFacade = RemoteServiceFactory.getService(AttachmentFacade.class);
                AttachmentDTO dto = attachmentFacade.findByFileId(fileId);
                if (null != dto && StringUtils.isNotBlank(dto.getStorePath())) {
                    stringRedisTemplate.opsForValue().set(cacheKey, dto.getStorePath(), 1, TimeUnit.HOURS);
                    resp.setStatus(HttpServletResponse.SC_MOVED_PERMANENTLY);
                    resp.setHeader("Location", cephConfig.get(CEPH_NGINX_ACCESS_PREFIX) + rmBucketPrefix(dto.getStorePath(), cephConfig));
                } else {
                    resp.sendError(HttpServletResponse.SC_NOT_FOUND);
                }
            }
        } catch (Exception ex) {
            LOGGER.warn("error when access attachment by servlet", ex);
            resp.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    private String rmBucketPrefix(String storePath, Map<String, String> cephConfig) {
        while (storePath.startsWith("/")) {
            storePath = storePath.substring(1);
        }
        final String attachmentProxyPrefix = getAttachmentProxyPrefix(cephConfig);
        if (storePath.startsWith(attachmentProxyPrefix)) {
            return StringUtils.substringAfter(storePath, attachmentProxyPrefix);
        }
        return storePath;
    }

    private String getAttachmentProxyPrefix(Map<String, String> cephConfig) {
        final String bucket = cephConfig.get(CEPH_BUCKET_NAME);
        final String subBucket = cephConfig.get(CEPH_SUB_BUCKET_NAME);
        if (StringUtils.isNotBlank(subBucket)) {
            return bucket + "/" + subBucket;
        }
        return bucket;
    }

    private Map<String, String> getCephConfig() {
        return (Map<String, String>) ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CEPH_CONFIG);
    }
}
