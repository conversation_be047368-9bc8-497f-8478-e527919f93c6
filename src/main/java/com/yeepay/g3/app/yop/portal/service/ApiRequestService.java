/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import java.util.List;
import java.util.Map;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-09-03 11:30
 */
public interface ApiRequestService {

    /**
     * apiUris
     *
     * @param apiUri
     * @return <apiUri:apiId>
     */
    Map<String, String> findApiIdsForOldApi(List<String> apiUri);

    /**
     * 获取apiId
     *
     * @param httpMethod
     * @param path
     * @return
     */
    String findApiIdForNewApi(String httpMethod, String path);

    /**
     * 根据path，查API
     *
     * @param path
     * @return
     */
    String findApiIdByPath(String path);
}
