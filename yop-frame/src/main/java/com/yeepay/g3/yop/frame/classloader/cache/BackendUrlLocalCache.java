/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */

package com.yeepay.g3.yop.frame.classloader.cache;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.yop.frame.cache.AbstractLocalCache;
import com.yeepay.g3.yop.frame.cache.YopCacheLoader;
import com.yeepay.g3.yop.frame.classloader.url.BackendUrlProvider;
import com.yeepay.g3.yop.frame.utils.config.ConfigEnum;
import com.yeepay.g3.yop.frame.utils.config.ConfigUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.yeepay.g3.yop.frame.CharacterConstants.HASH;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/11/20 7:26 PM
 */
@Component
public class BackendUrlLocalCache extends AbstractLocalCache<String, List<String>> {

    private static final Logger LOGGER = LoggerFactory.getLogger(BackendUrlLocalCache.class);

    @Autowired
    private BackendUrlProvider delegateBackendUrlProvider;

    @Autowired
    private BackendUrlProvider rmiBackendUrlProvider;

    @Override
    protected int getMaxSize() {
        return ConfigUtils.loadIntValue(ConfigEnum.YOP_CENTER_API_GROUP_NUM);
    }

    @Override
    protected int getExpireDuration() {
        return 3;
    }

    @Override
    protected YopCacheLoader<String, List<String>> getCacheLoader() {
        return new YopCacheLoader<String, List<String>>() {
            @Override
            public List<String> doLoad(String key) throws Exception {
                String[] split = StringUtils.split(key, HASH);
                String backendApp = split[0];
                String endClass = split[1];
                boolean fromZK = false;
                Map<String, Boolean> map = ConfigUtils.getSysConfigParam(ConfigEnum.YOP_CLASS_LOADER_URL_FROM_ZK_APP_NAME_SWITCH, Map.class);
                if (MapUtils.isNotEmpty(map)) {
                    fromZK = BooleanUtils.toBooleanDefaultIfNull(map.get(backendApp), map.getOrDefault("*", Boolean.FALSE));
                }
                if (fromZK) {
                    return delegateBackendUrlProvider.findUrls(endClass);
                } else {
                    return rmiBackendUrlProvider.findUrls(endClass);
                }
            }

            @Override
            protected String getCacheName() {
                return "backend_url_cache";
            }
        };
    }
}
