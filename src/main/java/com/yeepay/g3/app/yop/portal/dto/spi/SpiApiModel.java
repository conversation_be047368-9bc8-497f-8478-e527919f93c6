/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.dto.spi;

import lombok.Data;

import java.io.Serializable;

/**
 * title: 结果通知管理模块api展示模型<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/12/24 9:46 上午
 */
@Data
public class SpiApiModel implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * api id
     */
    private String apiId;
    /**
     * api 标题
     */
    private String title;
    /**
     * api uri
     */
    private String uri;

    /**
     * 版本
     */
    private String version;
}
