/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.controller;

import com.yeepay.g3.app.yop.portal.service.ExamineService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.vo.ProductApiExamineVO;
import com.yeepay.g3.boot.web.pojo.response.R;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * title: ExamineController<br>
 * description: 负责审核相关的操作<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/5/17 2:37 下午
 */
@Controller
@RequestMapping("/rest/examine/")
public class ExamineController {
    @Setter(onMethod_ = @Autowired)
    private ExamineService examineService;

    @ResponseBody
    @RequestMapping(value = "product-api", method = RequestMethod.POST)
    public R examine(@RequestBody ProductApiExamineVO productApiExamineVO) {
        examineService.productApi(productApiExamineVO, ShiroUtils.getOperatorCode());
        return new R();
    }
}
