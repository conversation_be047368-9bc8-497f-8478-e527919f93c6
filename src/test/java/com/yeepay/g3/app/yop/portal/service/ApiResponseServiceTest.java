package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import com.yeepay.g3.app.yop.portal.vo.ApiResponseModelVO;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/16 11:38 AM
 */
public class ApiResponseServiceTest extends SpringBootTests {
    @Autowired
    private ApiResponseService apiResponseService;

    @Test
    public void testFindOutputParams() {
        ApiResponseModelVO outputParams = apiResponseService.findOutputParams("5d94b65c565a43488be7efcacaffdfeb");
        Assert.assertNotNull(outputParams);
        Assert.assertEquals("BaseResponse", outputParams.getPrimaryName());
    }
}