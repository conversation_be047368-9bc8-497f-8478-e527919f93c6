/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.dto.EndServiceDTO;
import com.yeepay.g3.app.yop.portal.vo.ApiManageVO;
import com.yeepay.g3.facade.yop.sys.dto.ModelDTO;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/3/24 11:52 上午
 */
public interface ClassLoaderService {

    List<String> loadMethods(String className);

    EndServiceDTO loadEndService(String serviceSwaggerJson);

    /**
     * 构建等待创建模型
     *
     * @param apiManageVO
     * @return
     */
    List<ModelDTO> buildModel(ApiManageVO apiManageVO);

}
