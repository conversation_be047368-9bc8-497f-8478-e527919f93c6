package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.service.ApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.service.StatisticInvokeTimesApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.ApiGroupVO;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.app.yop.portal.vo.page.StatisticInvokeTimeApiGroupItem;
import com.yeepay.g3.app.yop.portal.vo.page.StatisticInvokeTimesApiGroupQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.enumtype.StatisticTypeEnum;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @version V1.0
 * @Title: StatisticInvokeTimesApiGroupQueryServiceImpl
 * @Description:
 * @date 2018年5月10日 下午2:42:38
 */
@Component
public class StatisticInvokeTimesApiGroupQueryServiceImpl implements StatisticInvokeTimesApiGroupQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(StatisticInvokeTimesApiGroupQueryServiceImpl.class);

    private PageItemConverter<StatisticInvokeTimeApiGroupItem> dailyPageItemConverter = new StatisticInvokeTimeApiGroupItemConverter(StatisticTypeEnum.DAILY);

    private PageItemConverter<StatisticInvokeTimeApiGroupItem> monthlyPageItemConverter = new StatisticInvokeTimeApiGroupItemConverter(StatisticTypeEnum.MONTHLY);

    @Resource(name = "statisticInvokeTimesApiGroupQueryService")
    private QueryService queryService;

    @Autowired
    private ApiGroupQueryService apiGroupQueryService;

    @Override
    public PageQueryResult<StatisticInvokeTimeApiGroupItem> pageQuery(StatisticInvokeTimesApiGroupQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        StatisticTypeEnum statisticType = param.getStatisticType();
        QueryResult queryResult = null;
        PageQueryResult<StatisticInvokeTimeApiGroupItem> convertResult = null;
        if (StatisticTypeEnum.DAILY == statisticType) {
            queryResult = queryService.query("listDaily", queryParam);
            convertResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), dailyPageItemConverter);
        } else if (StatisticTypeEnum.MONTHLY == statisticType) {
            queryResult = queryService.query("listMonthly", queryParam);
            convertResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), monthlyPageItemConverter);
        }
        return convertResult;
    }

    private Map<String, Object> getBizParams(StatisticInvokeTimesApiGroupQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        String spCode = param.getSpCode();

        String apiGroup = param.getApiGroup();
        if (StringUtils.isNotEmpty(apiGroup)) {
            bizParams.put("apiGroup", apiGroup);
        }

        if (StringUtils.isEmpty(apiGroup) && StringUtils.isNotEmpty(spCode)) {
            Set<String> apiGroups = new HashSet<>();
            List<ApiGroupVO> apiGroupVOS = apiGroupQueryService.listForSp(Collections.singleton(spCode));
            if (CollectionUtils.isNotEmpty(apiGroupVOS)) {
                apiGroupVOS.forEach(apiGroupVO -> apiGroups.add(apiGroupVO.getApiGroupCode()));
            }
            bizParams.put("apiGroups", apiGroups);
        }
        bizParams.put("statisticStartDate", param.getStatisticStartDate());
        bizParams.put("statisticEndDate", param.getStatisticEndDate());
        return bizParams;
    }

    class StatisticInvokeTimeApiGroupItemConverter extends BasePageItemConverter<StatisticInvokeTimeApiGroupItem> {

        private final String pattern = "yyyy-MM-dd";

        private final StatisticTypeEnum statisticType;

        StatisticInvokeTimeApiGroupItemConverter(StatisticTypeEnum statisticType) {
            this.statisticType = statisticType;
        }

        @Override
        public StatisticInvokeTimeApiGroupItem convert(Map<String, Object> params) {
            StatisticInvokeTimeApiGroupItem item = new StatisticInvokeTimeApiGroupItem();
            item.setSpCode((String) params.get("sp_code"));
            item.setApiGroupCode((String) params.get("api_group"));
            item.setInvokeTimes(((Number) params.get("invoke_times")).longValue());
            item.setStatisticDate((String) params.get("statistic_date"));
            return item;
        }
    }
}
