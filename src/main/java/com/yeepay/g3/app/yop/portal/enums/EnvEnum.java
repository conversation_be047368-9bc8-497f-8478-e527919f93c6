/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.enums;

/**
 * title: 环境枚举<br>
 * description: api自动解析选择环境时使用<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/11/8 6:36 下午
 */
public enum EnvEnum {
    NC("NC", "内测"),
    PRO("PRODUCT", "生产"),
    QA("QA", "测试");
    private final String description;
    private final String value;

    EnvEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public String getValue() {
        return value;
    }
}
