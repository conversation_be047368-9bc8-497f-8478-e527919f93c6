package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.SecurityReqTypeEnum;

import java.util.Set;

/**
 * title: 安全需求查询参数<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-05-19 22:20
 */
public class SecurityReqQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    private Set<String> values;
    private SecurityReqTypeEnum type;

    public Set<String> getValues() {
        return values;
    }

    public void setValues(Set<String> values) {
        this.values = values;
    }

    public SecurityReqTypeEnum getType() {
        return type;
    }

    public void setType(SecurityReqTypeEnum type) {
        this.type = type;
    }
}
