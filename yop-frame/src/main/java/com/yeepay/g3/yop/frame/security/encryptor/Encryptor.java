/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.yop.frame.security.encryptor;

import com.yeepay.g3.yop.frame.security.EncryptAlgEnum;

import javax.crypto.Cipher;
import java.security.Key;
import java.util.List;

/**
 * title: Encryptor<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/1/19 7:25 下午
 */
public interface Encryptor {

    /**
     * 返回加密算法
     *
     * @return
     */
    List<EncryptAlgEnum> encryptAlg();

    /**
     * 加密
     *
     * @param content
     * @param key
     * @return
     */
    String encrypt(String content, String key);

    /**
     * 加密
     *
     * @param content
     * @param encryptAlg
     * @return
     */
    default String encrypt(String content, EncryptAlg encryptAlg) {
        return null;
    }

    /**
     * 解密
     *
     * @param cipher
     * @param key
     * @return
     */
    String decrypt(String cipher, String key);

    /**
     * 解密
     *
     * @param encryptAlg
     * @return
     * @Param cipher 密文数据，safeUrlEncodedBase64编码
     */
    default String decrypt(String cipher, EncryptAlg encryptAlg) {
        return null;
    }

    /**
     * 获取一个已经初始化的Cipher
     *
     * @param mode
     * @param encryptAlg
     * @param isNew
     * @return
     */
    default Cipher getInitializedCipher(int mode, EncryptAlg encryptAlg, boolean isNew) {
        return null;
    }

}
