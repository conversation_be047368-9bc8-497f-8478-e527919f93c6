# 文档页面负责人管理功能设计文档

## 概述

本设计文档基于需求文档，为YOP文档系统的页面级负责人管理功能提供详细的技术设计方案。该功能将在现有的`tbl_doc_page`表基础上扩展负责人字段，并提供完整的负责人管理、搜索、展示和变更追踪能力。

## 架构

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端服务      │    │   数据存储      │
│                 │    │                 │    │                 │
│ - 负责人选择器  │◄──►│ - 负责人管理API │◄──►│ - 页面负责人表  │
│ - 搜索下拉框    │    │ - 人员搜索API   │    │ - 负责人变更日志│
│ - 信息展示组件  │    │ - 权限验证服务  │    │ - 用户信息表    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 模块架构
```
yop-portal-fe (前端)
├── components/
│   ├── OwnerSelector/          # 负责人选择器组件
│   └── OwnerDisplay/           # 负责人展示组件
└── api/
    └── ownerManagement.js      # 负责人管理API调用

yop-portal (门户后端)
├── controller/
│   └── DocController           # 文档控制器（扩展负责人管理接口）
└── service/
    ├── PageOwnerQueryService   # 页面负责人查询服务
    └── UserSearchService       # 用户搜索服务

janus/yop-doc-facade (服务接口层)
├── PageOwnerFacade             # 页面负责人门面接口
└── dto/
    └── PageOwnerDTO            # 页面负责人传输对象

janus/yop-doc-core (核心业务层)
├── service/
│   └── PageOwnerService        # 页面负责人服务
├── domain/
│   └── PageOwner               # 页面负责人领域对象
└── infrastructure/
    └── PageOwnerRepository     # 页面负责人仓储
```

## 组件和接口

### 数据模型

#### 1. 页面负责人表 (tbl_doc_page_owner)
```sql
CREATE TABLE `tbl_doc_page_owner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `page_id` bigint(20) NOT NULL COMMENT '页面ID',
  `owner_id` varchar(32) COLLATE utf8_bin NOT NULL COMMENT '负责人ID',
  `owner_name` varchar(64) COLLATE utf8_bin NOT NULL COMMENT '负责人姓名',
  `created_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_datetime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '乐观锁',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_idx_page_owner` (`page_id`, `owner_id`),
  KEY `idx_page_id` (`page_id`),
  KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='文档页面负责人';
```



### 后端接口设计

#### 1. Facade接口定义 (janus/yop-doc-facade)

```java
public interface PageOwnerFacade {
    
    /**
     * 更新页面负责人
     */
    void updatePageOwners(PageOwnerUpdateDTO updateDTO);
}
```

#### 2. 页面负责人管理API (yop-portal DocController扩展)

```java
@RestController
@RequestMapping("/rest/doc")
public class DocController {
    
    @Autowired
    private PageOwnerFacade pageOwnerFacade;
    
    // 现有的文档相关接口...
    
    /**
     * 获取页面负责人列表
     */
    @GetMapping("/page/owners")
    public ResponseMessage<PageOwnerVO> getPageOwners(PageOwnerQueryRequest request) {
        // 直接通过查询组件查询，不调用facade服务
        List<PageOwnerVO> owners = pageOwnerQueryService.getPageOwners(request.getPageId());
        return new ResponseMessage<>("result", owners);
    }
    
    /**
     * 更新页面负责人
     */
    @PostMapping("/page/owners/update")
    public ResponseMessage<String> updatePageOwners(@RequestBody PageOwnerUpdateRequest request) {
        PageOwnerUpdateDTO updateDTO = new PageOwnerUpdateDTO();
        updateDTO.setPageId(request.getPageId());
        updateDTO.setOwnerIds(request.getOwnerIds());
        pageOwnerFacade.updatePageOwners(updateDTO);
        return new ResponseMessage<>("负责人更新成功");
    }
}
```

#### 3. 页面负责人查询服务 (yop-portal)

```java
@Service
public class PageOwnerQueryService {
    
    /**
     * 获取页面负责人列表
     * 直接查询数据库，不调用facade服务
     */
    public List<PageOwnerVO> getPageOwners(Long pageId) {
        // 通过yop-portal-query.xml配置的查询组件查询
        // 查询tbl_doc_page_owner表
        return pageOwnerMapper.selectByPageId(pageId);
    }
}
```

#### 4. 用户搜索API

```java
@RestController
@RequestMapping("/rest/user")
public class UserController {
    
    /**
     * 搜索用户
     */
    @GetMapping("/search")
    public ResponseMessage<UserVO> searchUsers(
        @RequestParam String keyword,
        @RequestParam(defaultValue = "10") int limit) {
        // 实现用户搜索逻辑
        List<UserVO> users = userSearchService.searchUsers(keyword, limit);
        return new ResponseMessage<>("users", users);
    }
}
```



### 前端组件设计

#### 1. 负责人选择器组件 (OwnerSelector.vue)

```vue
<template>
  <div class="owner-selector">
    <el-select
      v-model="selectedOwners"
      multiple
      filterable
      remote
      reserve-keyword
      placeholder="搜索并选择负责人"
      :remote-method="searchUsers"
      :loading="loading"
      @change="handleOwnerChange"
    >
      <el-option
        v-for="user in userOptions"
        :key="user.id"
        :label="user.name"
        :value="user.id"
      >
        <span>{{ user.name }}</span>
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'OwnerSelector',
  props: {
    pageId: {
      type: Number,
      required: true
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedOwners: [],
      userOptions: [],
      loading: false
    }
  },
  methods: {
    async searchUsers(keyword) {
      if (keyword) {
        this.loading = true
        try {
          const response = await this.$api.userSearch.searchUsers(keyword)
          this.userOptions = response.data
        } finally {
          this.loading = false
        }
      }
    },
    
    async handleOwnerChange(ownerIds) {
      try {
        await this.$api.pageOwner.updatePageOwners(this.pageId, ownerIds)
        this.$emit('change', ownerIds)
        this.$message.success('负责人更新成功')
      } catch (error) {
        this.$message.error('负责人更新失败')
      }
    }
  }
}
</script>
```

#### 2. 负责人展示组件 (OwnerDisplay.vue)

```vue
<template>
  <div class="owner-display">
    <div v-if="owners.length > 0" class="owner-list">
      <el-tag
        v-for="owner in owners"
        :key="owner.id"
        type="info"
        size="small"
        class="owner-tag"
      >
        {{ owner.name }}
      </el-tag>
    </div>
    <span v-else class="no-owner">未指定</span>
  </div>
</template>

<script>
export default {
  name: 'OwnerDisplay',
  props: {
    owners: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped>
.owner-tag {
  margin-right: 8px;
  margin-bottom: 4px;
}

.no-owner {
  color: #999;
  font-style: italic;
}
</style>
```

## 数据模型

### 领域对象

#### 1. PageOwner (页面负责人)
```java
@Entity
@Table(name = "tbl_doc_page_owner")
public class PageOwner {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "page_id", nullable = false)
    private Long pageId;
    
    @Column(name = "owner_id", nullable = false)
    private String ownerId;
    
    @Column(name = "owner_name", nullable = false)
    private String ownerName;
    
    @Column(name = "created_datetime", nullable = false)
    private LocalDateTime createdDatetime;
    
    @Column(name = "last_modified_datetime", nullable = false)
    private LocalDateTime lastModifiedDatetime;
    
    @Version
    @Column(name = "version", nullable = false)
    private Long version;
    
    // getters and setters
}
```

### DTO对象 (janus/yop-doc-facade)

#### 1. PageOwnerDTO
```java
public class PageOwnerDTO {
    private String id;
    private String name;
    private LocalDateTime assignedTime;
    
    // getters and setters
}
```

#### 2. PageOwnerUpdateDTO
```java
public class PageOwnerUpdateDTO {
    private Long pageId;
    private List<String> ownerIds;
    
    // getters and setters
}
```



### VO对象 (yop-portal)

#### 1. PageOwnerVO
```java
public class PageOwnerVO {
    private String id;
    private String name;
    private LocalDateTime assignedTime;
    
    // getters and setters
}
```

#### 2. UserVO
```java
public class UserVO {
    private String id;
    private String name;
    private String email;
    private String department;
    private boolean active;
    
    // getters and setters
}
```

### 请求包装类 (yop-portal)

#### 1. PageOwnerQueryRequest
```java
public class PageOwnerQueryRequest {
    private Long pageId;
    
    // getters and setters
}
```

#### 2. PageOwnerUpdateRequest
```java
public class PageOwnerUpdateRequest {
    private Long pageId;
    private List<String> ownerIds;
    
    // getters and setters
}
```

## 错误处理

### 异常类型定义

### 错误处理策略

1. **数据库操作失败**: 返回500状态码，记录错误日志
2. **并发更新冲突**: 返回409状态码，提示数据已被其他用户修改
3. **参数验证失败**: 返回400状态码，提示参数错误

### 前端错误处理

```javascript
// API调用错误处理
const handleApiError = (error) => {
  switch (error.response?.status) {
    case 400:
      this.$message.error('参数错误，请检查输入')
      break
    case 409:
      this.$message.error('数据已被其他用户修改，请刷新后重试')
      break
    default:
      this.$message.error('操作失败，请稍后重试')
  }
}
```

## 测试策略

### 单元测试

#### 1. 后端服务测试
```java
@ExtendWith(MockitoExtension.class)
class PageOwnerServiceTest {
    
    @Mock
    private PageOwnerRepository pageOwnerRepository;
    
    @Mock
    private OwnerChangeLogRepository ownerChangeLogRepository;
    
    @InjectMocks
    private PageOwnerService pageOwnerService;
    
    @Test
    void shouldAddPageOwnerSuccessfully() {
        // 测试添加页面负责人
    }
    
    @Test
    void shouldThrowExceptionWhenOwnerAlreadyExists() {
        // 测试重复添加负责人的异常处理
    }
    
    @Test
    void shouldLogOwnerChangeWhenUpdating() {
        // 测试负责人变更日志记录
    }
}
```

#### 2. 前端组件测试
```javascript
import { shallowMount } from '@vue/test-utils'
import OwnerSelector from '@/components/OwnerSelector.vue'

describe('OwnerSelector.vue', () => {
  it('should search users when keyword is entered', async () => {
    const wrapper = shallowMount(OwnerSelector, {
      propsData: { pageId: 1 }
    })
    
    await wrapper.vm.searchUsers('test')
    
    expect(wrapper.vm.loading).toBe(false)
    expect(wrapper.vm.userOptions).toBeDefined()
  })
  
  it('should emit change event when owners are updated', async () => {
    const wrapper = shallowMount(OwnerSelector, {
      propsData: { pageId: 1 }
    })
    
    await wrapper.vm.handleOwnerChange(['user1', 'user2'])
    
    expect(wrapper.emitted().change).toBeTruthy()
  })
})
```

### 端到端测试

使用Cypress进行前端功能的端到端测试：

```javascript
describe('Page Owner Management', () => {
  it('should allow user to search and select page owners', () => {
    cy.visit('/doc/edit?pageId=1')
    
    cy.get('[data-cy=owner-selector]').click()
    cy.get('[data-cy=owner-search-input]').type('张三')
    
    cy.get('[data-cy=user-option]').first().click()
    cy.get('[data-cy=save-button]').click()
    
    cy.contains('负责人更新成功').should('be.visible')
  })
  
  it('should display owner change history', () => {
    cy.visit('/doc/edit?pageId=1')
    
    cy.get('[data-cy=owner-history-button]').click()
    cy.get('[data-cy=change-log-modal]').should('be.visible')
    cy.get('[data-cy=change-log-item]').should('have.length.greaterThan', 0)
  })
})
```