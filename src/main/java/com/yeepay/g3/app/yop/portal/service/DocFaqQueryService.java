/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.DocFaqRelateItemVO;
import com.yeepay.g3.app.yop.portal.vo.DocFaqVO;
import com.yeepay.g3.app.yop.portal.vo.page.DocFaqItem;
import com.yeepay.g3.app.yop.portal.vo.page.DocFaqPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;

import java.util.List;

/**
 * title: 常见问题服务<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-10-29 10:21
 */
public interface DocFaqQueryService {

    /**
     * 分页查询问题列表
     *
     * @param param
     * @return
     */
    PageQueryResult<DocFaqItem> faqPageQuery(DocFaqPageQueryParam param);

    /**
     * 该接口做个限制，仅返回50条数据，防止查询太慢
     *
     * @param keyword
     * @return
     */
    List<DocFaqRelateItemVO> listForRelate(String keyword);

    /**
     * 问题详情
     *
     * @param faqId
     * @return
     */
    DocFaqVO faqDetail(Long faqId);

    /**
     * 权限校验
     *
     * @param docFaqId 问题标识
     * @return
     */
    boolean checkAcl(Long docFaqId);
}
