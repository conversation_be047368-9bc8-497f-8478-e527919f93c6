package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.vo.ModelVO;
import com.yeepay.g3.app.yop.portal.vo.page.*;

import java.util.List;

/**
 * title: ModelQueryService<br/>
 * description: model查询service<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午5:43
 */
public interface ModelQueryService {

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<ModelPageItem> pageList(ModelPageQueryParam param);

    /**
     * sp分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<ModelPageItem> pageListForSp(ModelPageQueryParam param);

    /**
     * model详情查询
     *
     * @param id
     * @return
     */
    String findModelDetailById(Long id);

    /**
     * model列表查询
     *
     * @param apiGroup
     * @return
     */
    List<ModelVO> simpleList(String apiGroup);

    /**
     * 变更记录分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<ModelChangeRecordPageItem> changeRecordPageList(ModelChangeRecordPageQueryParam param);

    /**
     * 变更记录sp分页查询
     *
     * @param param
     * @return
     */
    PageQueryResult<ModelChangeRecordPageItem> changeRecordPageListForSp(ModelChangeRecordPageQueryParam param);

    /**
     * 模型是否已存在
     *
     * @param name
     * @param apiGroup
     * @return
     */
    boolean exist(String name, String apiGroup);

    /**
     * 错误码位置下拉框
     * @param apiId
     * @return
     */
    Object codes(String apiId);
}
