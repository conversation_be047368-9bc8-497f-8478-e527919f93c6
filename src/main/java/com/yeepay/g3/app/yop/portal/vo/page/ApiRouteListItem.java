package com.yeepay.g3.app.yop.portal.vo.page;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;

import java.io.Serializable;
import java.util.Objects;

/**
 * ApiRouteListItem
 */
@Validated
@javax.annotation.Generated(value = "io.swagger.codegen.v3.generators.java.SpringCodegen", date = "2020-11-03T07:28:20.629Z[GMT]")


public class ApiRouteListItem implements Serializable {

  private static final long serialVersionUID = -1L;
  
  @JsonProperty("id")
  private Long id = null;

  private String name;

  /**
   * 类型
   */
  @JsonProperty("type")
  private String type = null;

  @JsonProperty("serviceName")
  private String serviceName = null;

  /**
   * 状态
   */
  @JsonProperty("status")
  private String status = null;

  public ApiRouteListItem id(Long id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   *
   * @return id
   **/
  @Schema(description = "")

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public ApiRouteListItem name(String name) {
    this.name = name;
    return this;
  }

  public ApiRouteListItem type(String type) {
    this.type = type;
    return this;
  }

  /**
   * 类型
   *
   * @return type
   **/
  @Schema(description = "类型")

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public ApiRouteListItem serviceName(String serviceName) {
    this.serviceName = serviceName;
    return this;
  }

  /**
   * 后端服务名
   *
   * @return serviceName
   **/
  @Schema(description = "后端服务名")

  public String getServiceName() {
    return serviceName;
  }

  public void setServiceName(String serviceName) {
    this.serviceName = serviceName;
  }

  public ApiRouteListItem status(String status) {
    this.status = status;
    return this;
  }

  /**
   * 状态
   *
   * @return status
   **/
  @Schema(description = "状态")

  public String getStatus() {
    return status;
  }

  public void setStatus(String status) {
    this.status = status;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ApiRouteListItem apiRouteListItem = (ApiRouteListItem) o;
    return Objects.equals(this.id, apiRouteListItem.id) &&
            Objects.equals(this.type, apiRouteListItem.type) &&
            Objects.equals(this.serviceName, apiRouteListItem.serviceName) &&
            Objects.equals(this.status, apiRouteListItem.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, type, serviceName, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ApiRouteListItem {\n");

    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    serviceName: ").append(toIndentedString(serviceName)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
