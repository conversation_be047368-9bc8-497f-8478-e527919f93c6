package com.yeepay.g3.app.yop.portal.config;

import com.yeepay.g3.app.yop.portal.servlet.AttachmentAccessServlet;
import com.yeepay.g3.app.yop.portal.servlet.CaptchaContentServlet;
import org.springframework.boot.web.servlet.ServletRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.yeepay.g3.app.yop.portal.utils.Constants.ATTACHMENT_ACCESS_PATH;

/**
 * title: <br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2017/4/19 下午6:12
 */
@Configuration
public class ServletConfig {

    @Bean
    public ServletRegistrationBean testServletRegistrationBean(CaptchaContentServlet captchaContentServlet) {
        ServletRegistrationBean registration = new ServletRegistrationBean(captchaContentServlet);
        registration.setEnabled(true);
        registration.addUrlMappings("/captcha.png");
        return registration;
    }

    @Bean
    public ServletRegistrationBean attachmentAccessServletConfig(AttachmentAccessServlet attachmentAccessServlet) {
        ServletRegistrationBean registration = new ServletRegistrationBean(attachmentAccessServlet);
        registration.setEnabled(true);
        registration.addUrlMappings(ATTACHMENT_ACCESS_PATH);
        return registration;
    }

}
