/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.notify;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yeepay.g3.facade.yop.monitor.enums.OrderStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/5/31
 */
@Data
public class NotifySendRecordVO implements Serializable {
    private static final long serialVersionUID = -1L;
    private String id;
    private OrderStatusEnum status;
    private String errorCode;
    private String errorMsg;
    private String notifyRule;
    private String notifyMerchantNo;
    private String appId;
    private String url;
    private String spiName;
    private String spiTitle;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDate;
    private String notificationId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private Date sendDate;
    /**
     * 失败原因
     */
    private String errorName;
}
