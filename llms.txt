# SOA 4.0升级指南

## 元数据
- **组件名称**: SOA 4.0升级指南
- **版本**: 4.0
- **最后更新**: 2025-07-02
- **关键词**: SOA升级, Dubbo, Spring Boot, 微服务
- **相关组件**: 无
- **环境信息**: 支持Spring Boot 1.x/2.x和传统Tomcat项目
- **依赖服务**: Zookeeper注册中心

## 1. 系统概述

SOA 4.0升级指南提供了从SOA 2.x版本升级到4.0版本的完整步骤和配置说明。升级后可以获得更好的性能、稳定性和新特性支持。

### 1.1 核心功能

- **版本升级**: 从SOA 2.x升级到4.0
- **协议支持**: 支持hessianx和trix协议
- **多项目类型**: 支持Spring Boot和传统Tomcat项目
- **依赖管理**: 统一管理相关依赖版本

### 1.2 技术架构

```
业务应用 -> SOA 4.0 -> Dubbo 3.x -> Zookeeper -> 服务提供者
```

> **关键依赖**: 升级需要Zookeeper注册中心和相关基础组件支持

## 2. 升级准备

### 2.1 前置条件准备
- 移除dubbo协议`httpx`,`restx`的配置

### 2.2 处理组件冲突
- 降低`fastjson2`版本以解决Dal 1.0的ASM版本冲突
- 项目中若手动依赖了soa、rmi的依赖包可以进行去除依赖，避免造成项目冲突
- 项目若是多模块推荐使用maven项目<dependencyManagement/>强制控制jar版本 避免冲突。

## 3. 接入准备

### 3.1 项目依赖升级与改造

#### 3.1.1 通用部分-(tomcat项目、spring boot项目均需要)
```xml
<dependency>
    <groupId>com.yeepay.g3.utils</groupId>
    <artifactId>yeepay-thread-context</artifactId>
    <version>1.8</version>
</dependency>
<dependency>
    <groupId>com.yeepay.g3.utils</groupId>
    <artifactId>yeepay-utils-common</artifactId>
    <version>4.2.1</version>
    <exclusions>
        <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
        </exclusion>
        <exclusion>
            <groupId>com.yeepay.infra</groupId>
            <artifactId>yeepay-atlas-agent</artifactId>
        </exclusion>
    </exclusions>
</dependency>
<dependency>
    <artifactId>yeepay-rmi</artifactId>
    <groupId>com.yeepay.g3.utils</groupId>
    <version>4.0</version>
    <exclusions>
        <exclusion>
            <artifactId>servlet-api</artifactId>
            <groupId>javax.servlet</groupId>
        </exclusion>
    </exclusions>
</dependency>
<dependency>
    <artifactId>yeepay-soa</artifactId>
    <groupId>com.yeepay.g3.utils</groupId>
    <version>4.0</version>
    <exclusions>
        <exclusion>
            <groupId>org.jboss.netty</groupId>
            <artifactId>netty</artifactId>
        </exclusion>
        <exclusion>
            <artifactId>slf4j-log4j12</artifactId>
            <groupId>org.slf4j</groupId>
        </exclusion>
    </exclusions>
</dependency>
<dependency>
  <groupId>org.apache.zookeeper</groupId>
  <artifactId>zookeeper</artifactId>
  <version>3.4.8</version>
  <exclusions>
      <exclusion>
          <artifactId>slf4j-log4j12</artifactId>
          <groupId>org.slf4j</groupId>
      </exclusion>
  </exclusions>
</dependency>
<dependency>
    <groupId>io.netty</groupId>
    <artifactId>netty-all</artifactId>
    <version>4.1.56.Final</version>
</dependency>
<dependency>
    <groupId>com.caucho</groupId>
    <artifactId>hessian</artifactId>
    <version>4.3.5-yeepay</version>
</dependency>
<dependency>
    <groupId>com.alibaba</groupId>
    <artifactId>fastjson</artifactId>
    <version>2.0.53</version>
</dependency>
<dependency>
    <groupId>org.javassist</groupId>
    <artifactId>javassist</artifactId>
    <version>3.28.0-GA</version>
</dependency>
<dependency>
    <groupId>com.google.protobuf</groupId>
    <artifactId>protobuf-java</artifactId>
    <version>3.9.1</version>
</dependency>
<dependency>
    <groupId>com.google.guava</groupId>
    <artifactId>guava</artifactId>
    <version>27.0.1-jre</version>
</dependency>
<dependency>
    <groupId>org.apache.curator</groupId>
    <artifactId>curator-framework</artifactId>
    <version>4.2.0</version>
</dependency>
<dependency>
    <groupId>org.apache.curator</groupId>
    <artifactId>curator-recipes</artifactId>
    <version>4.2.0</version>
</dependency>
<dependency>
    <groupId>org.apache.curator</groupId>
    <artifactId>curator-x-discovery</artifactId>
    <version>4.2.0</version>
</dependency>
<dependency>
    <groupId>org.apache.curator</groupId>
    <artifactId>curator-client</artifactId>
    <version>4.2.0</version>
    <exclusions>
       <exclusion>
          <artifactId>guava</artifactId>
          <groupId>com.google.guava</groupId>
       </exclusion>
       <exclusion>
          <artifactId>zookeeper</artifactId>
          <groupId>org.apache.zookeeper</groupId>
       </exclusion>
    </exclusions>
</dependency>
<dependency>
    <groupId>org.aspectj</groupId>
    <artifactId>aspectjweaver</artifactId>
    <version>1.9.7</version>
</dependency>
<dependency>
    <groupId>org.aspectj</groupId>
    <artifactId>aspectjrt</artifactId>
    <version>1.9.7</version>
</dependency>
<dependency>
    <groupId>com.yeepay.infra</groupId>
    <artifactId>sentinel-soa-adapter</artifactId>
    <version>3.2-SNAPSHOT</version>
</dependency>

<dependency>
    <groupId>com.googlecode.concurrentlinkedhashmap</groupId>
    <artifactId>concurrentlinkedhashmap-lru</artifactId>
    <version>1.4.2</version>
</dependency>
<dependency>
    <groupId>com.yeepay.infra</groupId>
    <artifactId>fluent-log4j2-appender</artifactId>
     <version>2.1.1</version>
</dependency>

<dependency> <!-- 负责在日志中添加GUID -->
    <groupId>com.yeepay.g3.utils</groupId>
    <artifactId>yeepay-log4j2-pattern</artifactId>
    <version>2.1.0</version>
</dependency>
<dependency>
    <groupId>com.yeepay.infra</groupId>
    <artifactId>fluent-sender-base</artifactId>
    <version>2.1.4</version>

</dependency>
<dependency>
    <groupId>com.yeepay.infra</groupId>
    <artifactId>stream-sender-api</artifactId>
    <version>2.1.1</version>
</dependency>
<dependency>
    <groupId>com.yeepay.infra</groupId>
    <artifactId>metrics-agent-core</artifactId>
    <version>2.1.1</version>
</dependency>
```

#### 3.1.2 特殊部分-针对tomcat项目

##### 配置dubbo协议，保留hessianx协议，增加trix，去除其他协议

**注意：** 仅dubbo服务提供着需要添加此配置

```xml
<dubbo:protocol name="hessianx"/>

<dubbo:protocol name="trix"/>

<bean class="com.yeepay.g3.utils.soa.exporter.ServicePackageExporter">
    <property name="packageName" value="facade所在包路径"/>
</bean>
```
示例：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans.xsd
	http://code.alibabatech.com/schema/dubbo
	http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:protocol name="hessianx"/>
    <dubbo:protocol name="trix"/>
	<bean class="com.yeepay.g3.utils.soa.exporter.ServicePackageExporter">
		<property name="packageName" value="com.yeepay.g3.facade.notifier" />
	</bean>
</beans>
```

#### 3.1.3 特殊部分-针对spring boot项目引入soa starter

##### spring boot 2.x
```xml
<dependency>
    <groupId>com.yeepay.g3.starter</groupId>
    <artifactId>yeepay-soa-starter</artifactId>
    <version>4.0.3.RELEASE</version>
</dependency>
<dependency>
    <groupId>com.yeepay.infra</groupId>
    <artifactId>metrics-agent-core</artifactId>
</dependency>
```

##### spring boot 1.x

**注意：** 仅在启动类所在maven模块添加该依赖

```xml
<dependency>
    <groupId>com.yeepay.g3.starter</groupId>
    <artifactId>yeepay-soa-starter</artifactId>
    <version>3.0.3.RELEASE</version>
</dependency>
<dependency>
    <groupId>com.yeepay.infra</groupId>
    <artifactId>metrics-agent-core</artifactId>
</dependency>
```
#### 配置application.yml

**注意：** 仅dubbo服务提供着需要添加此配置

```yml
yeepay:
  rmi:
    package-name: facade所在的包路径，多个用","隔开
server:
  servlet:
    context-parameters:
      soa_app_name: 应用名称
    context-path: /应用名称
```

示例：
```yml
yeepay:
  rmi:
    package-name: com.yeepay.g3.facade.yop.ca.facade,com.yeepay.g3.yop.ca.client.facade
```

#### 调整spring xml dubbo相关配置
移除 spring配置文件中的soa、rmi相关配置，例如：删除如下文件和内容
```xml
<bean class="com.yeepay.g3.core.yop.utils.rmi.HessianServicePackageExporter"/>

<dubbo:application name="xxx"/>
<dubbo:monitor protocol="registry"/>
<dubbo:protocol name="hessianx" server="servletx"/>
<dubbo:protocol name="httpx" server="servletx"/>
<dubbo:protocol name="restx" server="servletx"/>

<bean class="com.yeepay.g3.utils.soa.exporter.ServicePackageExporter">
    <property name="packageName" value="xxx"/>
</bean>
```

#### 在启动类添加注解 @EnableSoa
**注意：** 仅dubbo服务提供着需要添加此注解

```java

示例
```java
import com.yeepay.springframework.boot.annotation.EnableSoa;

@SpringBootApplication
@EnableSoa
public class WebApplication {
    public static void main(String[] args) {
        SpringApplication.run(WebApplication.class, args);
    }
}
```

## 4. 接口说明

### 4.1 核心配置接口

SOA 4.0升级主要涉及以下配置接口和注解：

| 配置项 | 类型 | 必填 | 描述 |
|-------|------|-----|------|
| `@EnableSoa` | 注解 | 是 | 启用SOA功能的注解 |
| `dubbo:protocol` | XML配置 | 是 | Dubbo协议配置 |
| `ServicePackageExporter` | Bean配置 | 是 | 服务包导出器 |

### 4.2 dubbo3日志配置

根据项目实际情况，调整日志配置文件，添加如下内容：

```xml
<Logger name="org.apache.dubbo" level="INFO"/>
```


## 5. 使用示例

### 5.1 Spring Boot项目升级示例

```java
import com.yeepay.springframework.boot.annotation.EnableSoa;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
@EnableSoa
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

### 5.2 配置文件示例

```yaml
yeepay:
  rmi:
    package-name: com.example.facade
server:
  servlet:
    context-parameters:
      soa_app_name: example-app
    context-path: /example-app
```

## 6. 常见问题与错误处理

### 6.1 常见错误及解决方案

| 错误情况 | 可能原因 | 解决方案 |
|---------|---------|---------|
| 依赖冲突 | 版本不兼容 | 使用dependencyManagement统一管理版本 |
| 编译失败 | API变更 | 替换过时的API，如JSON处理类 |
| 服务注册失败 | 配置错误 | 检查@EnableSoa注解和包路径配置 |
| 协议不支持 | 协议配置错误 | 确保使用hessianx和trix协议 |

### 6.2 排查步骤

1. 确保pom调整后，没有重复定义，出现冲突时，以该文档提供的版本为准
2. 升级改造后的代码需要保证编译通过
3. 在父模块定义了依赖版本后，子模块不需要再指定版本号
4. 检查日志配置是否正确添加

### 6.3 API替换示例

如果用到了com.alibaba.dubbo.common.json.JSON，需要替换为com.fasterxml.jackson.databind.ObjectMapper：

```java
import java.text.SimpleDateFormat;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.DeserializationFeature;

private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
static {
    // 此处与com.alibaba.dubbo.common.json.JSON保持一致
    OBJECT_MAPPER.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
}
```

## 7. 术语表

| 术语 | 描述 |
|-----|------|
| SOA | 面向服务的架构 |
| Dubbo | 阿里巴巴开源的RPC框架 |
| hessianx | 基于Hessian协议的扩展 |
| trix | 易宝自定义的RPC协议 |
| ServicePackageExporter | 服务包导出器，用于自动发布服务 |

## 8. 版本历史

| 版本 | 日期 | 变更内容 |
|-----|------|---------|
| 4.0 | 2025-07-02 | SOA 4.0升级指南，支持Dubbo 3.x |