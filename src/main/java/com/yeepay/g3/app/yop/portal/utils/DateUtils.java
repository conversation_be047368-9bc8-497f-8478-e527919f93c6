/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

/**
 * title: <br>
 * description: 某些版本的数据库驱动查询出来的datetime类型的数据为LocalDateTime
 * 需要Date类型是需要简单转换一下
 * <br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/6/16 下午6:11
 */
public class DateUtils {
    public static Date toDate(Object date) {
        if (null == date) {
            return null;
        }
        if (date instanceof Date) {
            return (Date) date;
        }
        LocalDateTime localDateTime = (LocalDateTime) date;
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = localDateTime.atZone(zoneId);
        return Date.from(zdt.toInstant());
    }
}
