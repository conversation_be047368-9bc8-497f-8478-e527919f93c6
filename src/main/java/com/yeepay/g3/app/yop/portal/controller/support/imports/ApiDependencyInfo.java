package com.yeepay.g3.app.yop.portal.controller.support.imports;

import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;

import java.io.Serializable;
import java.util.List;

/**
 * title: Spi倚赖信息<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2019<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019-07-29 14:48
 */
public class ApiDependencyInfo implements Serializable {

    private static final long serialVersionUID = -1L;

    private String apiName;

    private ApiRequestKey requestKey;

    private List<String> spis;

    private List<String> refModels;

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public ApiDependencyInfo withApiName(String apiName) {
        this.apiName = apiName;
        return this;
    }

    public ApiRequestKey getRequestKey() {
        return requestKey;
    }

    public void setRequestKey(ApiRequestKey requestKey) {
        this.requestKey = requestKey;
    }

    public ApiDependencyInfo withRequestKey(ApiRequestKey requestKey) {
        this.requestKey = requestKey;
        return this;
    }

    public List<String> getSpis() {
        return spis;
    }

    public void setSpis(List<String> spis) {
        this.spis = spis;
    }

    public ApiDependencyInfo withSpis(List<String> spis) {
        this.spis = spis;
        return this;
    }

    public List<String> getRefModels() {
        return refModels;
    }

    public void setRefModels(List<String> refModels) {
        this.refModels = refModels;
    }

    public ApiDependencyInfo withRefModels(List<String> refModels) {
        this.refModels = refModels;
        return this;
    }

}
