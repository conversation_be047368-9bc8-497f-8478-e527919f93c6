package com.yeepay.g3.app.yop.portal.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yeepay.g3.facade.yop.perm.enums.ResourceStatusEnum;
import com.yeepay.g3.facade.yop.perm.enums.ResourceTypeEnum;
import com.yeepay.g3.facade.yop.perm.enums.ResourceVisibleTypeEnum;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * title:
 * description:
 * Copyright: Copyright (c) 2018
 * Company: 易宝支付（Yeepay)
 *
 * <AUTHOR>
 * @version 1.0.0
 * since  下午12:46
 */
public class AclResourceRequestParamVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long pId;

    @JsonProperty(value = "name")
    private String resourceName;

    @JsonProperty(value = "type")
    private ResourceTypeEnum resourceType;

    private String url;

    private Long permId;

    private String description;

    private String icon;

    private ResourceStatusEnum status;

    private ResourceVisibleTypeEnum visibleType;

    public Long getpId() {
        return pId;
    }

    public void setpId(Long pId) {
        this.pId = pId;
    }

    public String getResourceName() {
        return resourceName;
    }

    public void setResourceName(String resourceName) {
        this.resourceName = resourceName;
    }

    public ResourceTypeEnum getResourceType() {
        return resourceType;
    }

    public void setResourceType(ResourceTypeEnum resourceType) {
        this.resourceType = resourceType;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Long getPermId() {
        return permId;
    }

    public void setPermId(Long permId) {
        this.permId = permId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public ResourceStatusEnum getStatus() {
        return status;
    }

    public void setStatus(ResourceStatusEnum status) {
        this.status = status;
    }

    public ResourceVisibleTypeEnum getVisibleType() {
        return visibleType;
    }

    public void setVisibleType(ResourceVisibleTypeEnum visibleType) {
        this.visibleType = visibleType;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
