package com.yeepay.g3.app.yop.portal.shiro.authc.filter;

import com.yeepay.g3.app.yop.portal.shiro.CustomShiroFilter;
import com.yeepay.g3.app.yop.portal.shiro.authc.Boss3gToken;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.web.filter.authc.AuthenticatingFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * title: 基于 BOSS3G Token 的授权拦截器<br>
 * description:描述<br>
 * Copyright: Copyright (c)2011<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018/5/31 下午2:51
 */
@Component
public class Boss3gTokenAuthenticationFilter extends AuthenticatingFilter implements CustomShiroFilter {

    private static final Logger LOGGER = LoggerFactory.getLogger(Boss3gTokenAuthenticationFilter.class);

    private static final String TOKEN = "token";

    private static final String SSO_LOGIN_URL_PREFIX = "/signin/sso/boss3g";

    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response) {
        String token = getBoss3gTokenStr(request);
        return new Boss3gToken(token);
    }

    protected String getBoss3gTokenStr(ServletRequest request) {
        return WebUtils.getCleanParam(request, TOKEN);
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        if (isLoginRequest(request, response)) {
            if (isLoginSubmission(request, response) || isSSOLogin(request, response)) {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.trace("Login submission detected.  Attempting to execute login.");
                }
                return executeLogin(request, response);
            } else {
                if (LOGGER.isDebugEnabled()) {
                    LOGGER.trace("Login page view.");
                }
                //allow them to see the login page ;)
                return true;
            }
        } else {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.trace("Attempting to access a path which requires authentication.  Forwarding to the " +
                        "Authentication url [" + getLoginUrl() + "]");
            }

            saveRequestAndRedirectToLogin(request, response);
            return false;
        }
    }

    @Override
    protected boolean onLoginFailure(AuthenticationToken token, AuthenticationException e, ServletRequest request, ServletResponse response) {
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        try {
            PrintWriter out = response.getWriter();
            LOGGER.error("boss3g AuthenticationException username:{}, detail:{}", token.getPrincipal(), e.getClass().getSimpleName());
            out.println("{\"status\":\"error\",\"message\":\"" + e.getMessage() + "\",\"detail\":\"" + e.getClass().getSimpleName() + "\"}");
            out.flush();
            out.close();
        } catch (IOException e1) {
            LOGGER.error(" boss3g onLoginFailure exception", e1);
        }
        return super.onLoginFailure(token, e, request, response);
    }

    @SuppressWarnings({"UnusedDeclaration"})
    protected boolean isLoginSubmission(ServletRequest request, ServletResponse response) {
        return (request instanceof HttpServletRequest) && WebUtils.toHttp(request).getMethod().equalsIgnoreCase(POST_METHOD);
    }

    protected boolean isSSOLogin(ServletRequest request, ServletResponse response) {
        return (request instanceof HttpServletRequest) && WebUtils.toHttp(request).getRequestURI().startsWith(SSO_LOGIN_URL_PREFIX);
    }

    @Override
    public String shiroName() {
        return "boss3g";
    }
}
