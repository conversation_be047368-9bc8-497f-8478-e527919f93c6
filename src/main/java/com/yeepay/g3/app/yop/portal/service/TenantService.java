/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import java.util.List;
import java.util.Set;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/10/20 16:09
 */
public interface TenantService {

    /**
     * 根据租户code,查支持的提供方code
     *
     * @param tenantCodes
     * @return
     */
    List<String> getProviderCodesByTenantCodes(Set<String> tenantCodes);

}
