/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.service;

import com.yeepay.g3.app.yop.portal.SpringBootTests;
import com.yeepay.g3.app.yop.portal.utils.Assert;
import com.yeepay.g3.app.yop.portal.vo.ProductSimpleVO;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/8/3 6:14 下午
 */
public class ProductQueryServiceTest extends SpringBootTests {
    @Autowired
    private ProductQueryService productQueryService;

    @Test
    public void testAllProducts() {
        List<ProductSimpleVO> result = productQueryService.allNormalProducts();
        Assert.isTrue(CollectionUtils.isNotEmpty(result));
    }
}
