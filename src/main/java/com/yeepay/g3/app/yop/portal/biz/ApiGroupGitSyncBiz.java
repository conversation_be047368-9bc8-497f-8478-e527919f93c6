package com.yeepay.g3.app.yop.portal.biz;

import com.yeepay.g3.app.yop.portal.controller.support.sync.ApiGroupGitSyncDiffDTO;
import com.yeepay.g3.app.yop.portal.dto.ApiGroupGitSyncCommitDTO;
import com.yeepay.g3.app.yop.portal.dto.ApiGroupGitSyncContext;
import com.yeepay.g3.facade.yop.sys.dto.ApiGroupGitSyncInfoDTO;

/**
 * title: Api分组Git同步Biz<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-26 11:11
 */
public interface ApiGroupGitSyncBiz {


    /**
     * 获取新旧diff
     *
     * @param syncInfo        同步信息
     * @param currentCommitId 当前commitId
     * @param latestCommitId  最新commitId
     * @return diff详情
     */
    ApiGroupGitSyncDiffDTO diff(ApiGroupGitSyncInfoDTO syncInfo, String currentCommitId, String latestCommitId);

    /**
     * 提交到生产
     *
     * @param commit 提交
     */
    void commitToProduction(ApiGroupGitSyncCommitDTO commit);

    /**
     * 处理提交
     *
     * @param commit 提交
     */
    void handleCommit(ApiGroupGitSyncCommitDTO commit);

    /**
     * 查询同步上下文
     *
     * @param apiGroup  api分组
     * @param requestId 请求id
     * @return 文件内容
     */
    ApiGroupGitSyncContext findSyncRequest(String apiGroup, String requestId);

    /**
     * 删除同步请求
     *
     * @param apiGroup  api分组
     * @param requestId 请求id
     */
    void deleteSyncRequest(String apiGroup, String requestId);

    /**
     * 刷新所有
     */
    void refreshAll();
}
