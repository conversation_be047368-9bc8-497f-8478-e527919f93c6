package com.yeepay.g3.app.yop.portal.git;

import java.util.List;

/**
 * title: GitClient<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2020<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2020-03-12 14:13
 */
public interface GitClient {

    /**
     * 获取最近的提交
     *
     * @param gitRepository gitRepository
     * @param gitBranch     gitBranch
     * @param filePath      file路径
     * @return 最新的commitId
     */
    String getLatestCommit(String gitRepository, String gitBranch, String filePath);


    /**
     * 获取指定repository下的所有分支
     *
     * @param gitRepository gitRepository
     * @return 分支列表
     */
    List<GitBranch> getBranches(String gitRepository);


    /**
     * 获取文件
     *
     * @param gitRepository repository
     * @param filePath      文件路径
     * @param commitId      commitId
     * @return git文件
     */
    GitFile getFile(String gitRepository, String filePath, String commitId);

}
