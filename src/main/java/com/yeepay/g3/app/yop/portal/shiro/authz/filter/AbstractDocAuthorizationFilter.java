/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.shiro.authz.filter;

import com.yeepay.g3.app.yop.portal.shiro.realm.ShiroRealm;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Set;

/**
 * title: 文档权限特殊处理<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2021/11/20
 */
public abstract class AbstractDocAuthorizationFilter extends CacheAbstractAuthorizationFilter {
    protected static final String[] EMPTY = new String[0];

    /**
     * 处理资源的spCodes
     * 文档数据权限特殊处理，取当前用户的spCode与文档(关联产品)的spCode交集
     * @param resourceSpCodes
     * @return
     */
    protected String[] resolveSpCodes(Set<String> resourceSpCodes) {
        if (CollectionUtils.isNotEmpty(resourceSpCodes)) {
            final ShiroRealm.ShiroUser shiroUser = ShiroUtils.getShiroUser();
            if (shiroUser.isPlatformOperator()) {
                return resourceSpCodes.toArray(new String[]{});
            } else {
                final Set<String> userSpCodes = shiroUser.getSpScopes();
                if (CollectionUtils.isNotEmpty(userSpCodes)) {
                    return CollectionUtils.intersection(resourceSpCodes, userSpCodes).toArray(new String[]{});
                }
            }
        }
        return EMPTY;
    }
}
