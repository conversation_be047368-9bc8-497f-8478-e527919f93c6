package com.yeepay.g3.app.yop.portal.servlet;

import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.session.Session;
import org.patchca.color.ColorFactory;
import org.patchca.color.SingleColorFactory;
import org.patchca.filter.predefined.CurvesRippleFilterFactory;
import org.patchca.service.ConfigurableCaptchaService;
import org.patchca.utils.encoder.EncoderHelper;
import org.patchca.word.RandomWordFactory;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Random;

/**
 * 验证码生成Servlet
 * <p/>
 * User: <EMAIL>
 * Date: 12-4-15
 * Time: 下午7:37
 */
@org.springframework.stereotype.Component
public class CaptchaContentServlet extends HttpServlet {

    private static final Logger LOGGER = LoggerFactory.getLogger(CaptchaContentServlet.class);

    private static ConfigurableCaptchaService captchaService = null;
    private static ColorFactory cf = null;                   // 颜色
    private static RandomWordFactory wf = null;              // 字体
    private static Random r = new Random();
    private static final int WIDTH = 120;                   // 默认160
    private static final int HEIGHT = 50;                   // 默认70

    private static final Color TEXT_COLOR = new Color(0, 27, 220);
    //private static final Color BACKGROUND_COLOR = new Color(25, 60, 170);

    @Override
    public void init() throws ServletException {
        super.init();

        if (null == wf) {
            // 设置字体
            this.wf = new RandomWordFactory();
            this.wf.setMaxLength(4);
            this.wf.setMinLength(4);
            this.wf.setCharacters("1234567890");
        }
        if (null == cf) {
            this.cf = new SingleColorFactory(TEXT_COLOR);
        }
        if (null == captchaService) {
            captchaService = new ConfigurableCaptchaService();
            captchaService.setWordFactory(wf);
            captchaService.setColorFactory(cf);
            captchaService.setWidth(WIDTH);
            captchaService.setHeight(HEIGHT);
        }

        CurvesRippleFilterFactory crff = new CurvesRippleFilterFactory(captchaService.getColorFactory());
        captchaService.setFilterFactory(crff);
    }

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        doGet(req, resp);
    }

    @Override
    protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        resp.setContentType("image/png");
        resp.setHeader("cache", "no-cache");

        OutputStream outputStream = resp.getOutputStream();
        try {
            Session session = SecurityUtils.getSubject().getSession();
            String patchca = EncoderHelper.getChallangeAndWriteImage(captchaService, "png", outputStream);
            session.setAttribute("PATCHCA", patchca);
        } finally {
            outputStream.flush();
            outputStream.close();
        }
    }
}
