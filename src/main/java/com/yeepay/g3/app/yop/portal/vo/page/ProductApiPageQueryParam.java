/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo.page;

import com.yeepay.g3.facade.yop.sys.enums.ProductApiTypeEnum;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2019/8/5 下午6:12
 */
public class ProductApiPageQueryParam extends BasePageQueryParam {

    private static final long serialVersionUID = -1L;

    @Size(min = 1, max = 32)
    private String productCode;

    private Long sceneId;

    @NotNull
    private ProductApiTypeEnum type;

    @Size(min = 1, max = 84)
    private String value;

    @NotNull
    private Boolean related;

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Long getSceneId() {
        return sceneId;
    }

    public void setSceneId(Long sceneId) {
        this.sceneId = sceneId;
    }

    public ProductApiTypeEnum getType() {
        return type;
    }

    public void setType(ProductApiTypeEnum type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Boolean isRelated() {
        return related;
    }

    public void setRelated(Boolean related) {
        this.related = related;
    }
}
