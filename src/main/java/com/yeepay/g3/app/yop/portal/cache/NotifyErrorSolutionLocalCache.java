/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeepay.boot.components.config.utils.ConfigUtils;
import com.yeepay.g3.app.yop.portal.utils.config.ConfigEnum;
import com.yeepay.g3.app.yop.portal.utils.mapper.JsonMapper;
import com.yeepay.g3.app.yop.portal.vo.NotifyErrorSolutionVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/11/15 11:26
 */
@Component("notifyErrorSolutionLocalCache")
public class NotifyErrorSolutionLocalCache {
    public static final int CACHE_MAX_SIZE = 500;
    public static final int CACHE_EXPIRE_AFTER_ACCESS = 5;
    private static final Logger LOGGER = LoggerFactory.getLogger(NotifyErrorSolutionLocalCache.class);
    protected JsonMapper jsonMapper = JsonMapper.nonEmptyMapper();

    private LoadingCache<String, NotifyErrorSolutionVO> notifyErrorSolutionLocalCache = CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE)
            .expireAfterAccess(CACHE_EXPIRE_AFTER_ACCESS, TimeUnit.MINUTES)
            .recordStats()
            .build(getCacheLoader());

    private CacheLoader getCacheLoader() {
        return new CacheLoader<String, NotifyErrorSolutionVO>() {
            @Override
            public NotifyErrorSolutionVO load(String key) throws Exception {
                LOGGER.info("load notifyErrorSolutionVO by errorCode:{}.", key);
                if (StringUtils.isEmpty(key)) {
                    return null;
                }
                Map<String, String> errorCodeSolutions = (Map<String, String>) ConfigUtils.getAppConfigParam(ConfigEnum.YOP_NOTIFY_ORDER_ERROR_CODE_SOLUTION);
                NotifyErrorSolutionVO notifyErrorSolutionVO = new NotifyErrorSolutionVO();
                for (Map.Entry<String, String> entry : errorCodeSolutions.entrySet()) {
                    String errorCode = entry.getKey();
                    String value = entry.getValue();
                    if (key.equals(errorCode)) {
                        Map<String, String> map = jsonMapper.fromJson(value, Map.class);
                        notifyErrorSolutionVO = convertToNotifyErrorSolutionVO(map);
                        notifyErrorSolutionVO.setErrorCode(key);
                        return notifyErrorSolutionVO;
                    }
                }
                return notifyErrorSolutionVO;
            }
        };
    }

    private NotifyErrorSolutionVO convertToNotifyErrorSolutionVO(Map<String, String> map) {
        NotifyErrorSolutionVO vo = new NotifyErrorSolutionVO();
        vo.setErrorName(map.get("errorName"));
        vo.setInnerSolution(map.get("innerSolution"));
        vo.setOuterSolution(map.get("outerSolution"));
        return vo;
    }

    public NotifyErrorSolutionVO get(String key) {
        try {
            return notifyErrorSolutionLocalCache.get(key);
        } catch (Exception e) {
            LOGGER.error("error when get local cache", e);
            return null;
        }
    }
}
