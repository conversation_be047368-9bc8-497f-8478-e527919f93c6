package com.yeepay.g3.app.yop.portal.service.impl;

import com.google.common.collect.Maps;
import com.yeepay.g3.app.yop.portal.exception.PortalExceptionEnum;
import com.yeepay.g3.app.yop.portal.service.ApiGroupQueryService;
import com.yeepay.g3.app.yop.portal.utils.PageUtils;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.utils.page.query.BasePageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageItemConverter;
import com.yeepay.g3.app.yop.portal.utils.page.query.PageQueryUtils;
import com.yeepay.g3.app.yop.portal.vo.ApiGroupVO;
import com.yeepay.g3.app.yop.portal.vo.GrayPolicyInfoVO;
import com.yeepay.g3.app.yop.portal.vo.page.ApiGroupPageItem;
import com.yeepay.g3.app.yop.portal.vo.page.ApiGroupPageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.core.yop.utils.Collections3;
import com.yeepay.g3.facade.yop.perm.enums.OperatorTypeEnum;
import com.yeepay.g3.facade.yop.sys.dto.SecurityReqDTO;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.query.QueryParam;
import com.yeepay.g3.utils.query.QueryResult;
import com.yeepay.g3.utils.query.QueryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * title: api分组service<br/>
 * description: <br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 18/3/9 14:48
 */
@Component
public class ApiGroupQueryServiceImpl implements ApiGroupQueryService {

    private static final Logger LOGGER = LoggerFactory.getLogger(ApiGroupQueryServiceImpl.class);

    @Resource(name = "apiGroupQueryService")
    private QueryService queryService;

    private PageItemConverter<ApiGroupPageItem> pageItemConverter = new ApiGroupItemConverter();

    @Override
    public List<ApiGroupVO> list() {
        List queryResult = queryService.query("list", Maps.newHashMap());
        if (CollectionUtils.isEmpty(queryResult)) {
            return Collections.emptyList();
        } else {
            return convert(queryResult);
        }
    }

    @Override
    public List<ApiGroupVO> listForSp(Set<String> spCodes) {
        if (Collections3.isEmpty(spCodes)) {
            return Collections.emptyList();
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("spCodes", spCodes);
        List queryResult = queryService.query("listForSp", params);
        return convert(queryResult);
    }

    @Override
    public List<String> listApiGroupCode() {
        List<ApiGroupVO> apiGroupList;
        if (OperatorTypeEnum.PLATFORM == ShiroUtils.getOperatorType()) {
            apiGroupList = list();
        } else {
            apiGroupList = listForSp(ShiroUtils.getShiroUser().getSpScopes());
        }
        return Collections3.extractToList(apiGroupList, "apiGroupCode");
    }

    @Override
    public PageQueryResult<ApiGroupPageItem> pageList(ApiGroupPageQueryParam param) {
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        queryParam.setParams(getBizParams(param));
        QueryResult queryResult = queryService.query("pageList", queryParam);
        PageQueryResult<ApiGroupPageItem> pageQueryResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        return pageQueryResult;
    }

    @Override
    public PageQueryResult<ApiGroupPageItem> pageListForSp(ApiGroupPageQueryParam param, Set<String> spCodes) {
        if (Collections3.isEmpty(spCodes)) {
            return PageUtils.getEmptyPage(param.getPageNo());
        }
        QueryParam queryParam = PageQueryUtils.getBaseQueryParam(param);
        Map<String, Object> map = getBizParams(param);
        map.put("spCodes", spCodes);
        queryParam.setParams(map);
        QueryResult queryResult = queryService.query("pageListForSp", queryParam);
        PageQueryResult<ApiGroupPageItem> pageQueryResult = PageQueryUtils.convertResult(queryResult, param.getPageNo(), pageItemConverter);
        return pageQueryResult;
    }

    /**
     * 查询api分组下灰度策略
     *
     * @return 灰度策略列表
     */
    @Override
    public List<GrayPolicyInfoVO> listApiGroupGrayPolicy(String apiGroup) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("apiGroup", apiGroup);
        List<Map<String, Object>> queryResult = queryService.query("listApiGroupGrayPolicy", params);
        if (CollectionUtils.isEmpty(queryResult)) {
            return Collections.emptyList();
        } else {
            return convertGrayPolicyInfoVO(queryResult);
        }


    }

    @Override
    public String findSp(String apiGroupCode) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("apiGroupCode", apiGroupCode);
        List<Map<String, Object>> queryResult = queryService.query("querySp", params);
        PortalExceptionEnum.API_GROUP_ILLEGAL.assertIsFalse(CollectionUtils.isEmpty(queryResult));
        return (String) queryResult.get(0).get("sp_code");
    }

    private List<Map<String, Object>> getApiGroupSecurities(String apiGroupCode, Map<String, List<SecurityReqDTO>> securityReqMap) {
        List<SecurityReqDTO> securityReqsForGroup = securityReqMap.get(apiGroupCode);
        if (CollectionUtils.isNotEmpty(securityReqsForGroup)) {
            return securityReqsForGroup.stream().map(securityReq -> {
                Map<String, Object> map = Maps.newHashMapWithExpectedSize(3);
                map.put("name", securityReq.getName());
                map.put("scopes", securityReq.getScopes());
                map.put("extensions", securityReq.getExtensions());
                return map;
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private Map<String, Object> getBizParams(ApiGroupPageQueryParam param) {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("apiGroupCode", param.getApiGroupCode());
        bizParams.put("spCode", param.getSpCode());
        bizParams.put("createdStartDate", param.getCreatedStartDate());
        bizParams.put("createdEndDate", param.getCreatedEndDate());
        return bizParams;
    }

    private List<ApiGroupVO> convert(List queryResult) {
        if (Collections3.isEmpty(queryResult)) {
            return Collections.emptyList();
        }
        List<ApiGroupVO> apiGroups = new ArrayList<>(queryResult.size());
        for (Object result : queryResult) {
            Map resultMap = (Map) result;
            ApiGroupVO apiGroupVO = new ApiGroupVO((String) resultMap.get("api_group_code"), (String) resultMap.get("api_group_name"));
            apiGroupVO.setSpCode((String) resultMap.get("sp_code"));
            apiGroups.add(apiGroupVO);
        }
        return apiGroups;
    }

    private List<GrayPolicyInfoVO> convertGrayPolicyInfoVO(List<Map<String, Object>> queryResult) {
        List<GrayPolicyInfoVO> grayPolicyList = new ArrayList<>(queryResult.size());
        for (Map<String, Object> result : queryResult) {
            GrayPolicyInfoVO apiGroupGrayPolicyInfoVO = new GrayPolicyInfoVO();
            apiGroupGrayPolicyInfoVO.setId((Long) result.get("id"));
            apiGroupGrayPolicyInfoVO.setPolicyDesc((String) result.get("policy_desc"));
            //apiGroupGrayPolicyInfoVO.setCriteria((String) result.get("criteria"));
            apiGroupGrayPolicyInfoVO.setEnabled(Integer.parseInt(String.valueOf(result.get("enabled"))) == 1 ? true : false);
            apiGroupGrayPolicyInfoVO.setWeight(Integer.parseInt(String.valueOf(result.get("weight"))));
            grayPolicyList.add(apiGroupGrayPolicyInfoVO);
        }
        return grayPolicyList;
    }

    class ApiGroupItemConverter extends BasePageItemConverter<ApiGroupPageItem> {

        @Override
        public ApiGroupPageItem convert(Map<String, Object> params) {
            ApiGroupPageItem item = new ApiGroupPageItem();
            item.setId((Long) params.get("id"));
            item.setApiGroupCode((String) params.get("api_group_code"));
            item.setApiGroupName((String) params.get("api_group_name"));
            item.setSpCode((String) params.get("sp_code"));
            item.setDescription((String) params.get("description"));
            item.setVersion((Long) params.get("version"));
            item.setCreatedDate((Date) params.get("created_datetime"));
            item.setLastModifiedDate((Date) params.get("last_modified_datetime"));
            return item;
        }
    }

}
