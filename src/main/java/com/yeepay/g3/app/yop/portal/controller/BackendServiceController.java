package com.yeepay.g3.app.yop.portal.controller;


import com.yeepay.g3.app.yop.portal.dto.ResponseMessage;
import com.yeepay.g3.app.yop.portal.service.BackendServiceQueryService;
import com.yeepay.g3.app.yop.portal.utils.ShiroUtils;
import com.yeepay.g3.app.yop.portal.vo.CommonsVO;
import com.yeepay.g3.app.yop.portal.vo.page.BackendServicePageItem;
import com.yeepay.g3.app.yop.portal.vo.page.BackendServicePageQueryParam;
import com.yeepay.g3.app.yop.portal.vo.page.PageQueryResult;
import com.yeepay.g3.facade.yop.sys.dto.BackendServiceDTO;
import com.yeepay.g3.facade.yop.sys.enums.BackendServiceTypeEnum;
import com.yeepay.g3.facade.yop.sys.facade.BackendServiceMgrFacade;
import com.yeepay.g3.utils.common.CheckUtils;
import com.yeepay.g3.utils.common.log.Logger;
import com.yeepay.g3.utils.common.log.LoggerFactory;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * title: BackendServiceController<br/>
 * description: 后端服务管理<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午3:30
 */
@Controller
@RequestMapping("/rest/backend-service")
public class BackendServiceController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BackendServiceController.class);

    private BackendServiceMgrFacade backendServiceMgrFacade = RemoteServiceFactory.getService(BackendServiceMgrFacade.class);

    @Autowired
    private BackendServiceQueryService backendServiceQueryService;

    @ResponseBody
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public ResponseMessage list(BackendServicePageQueryParam param) {
        try {
            if (ShiroUtils.isPlatformOperator()) {
                PageQueryResult<BackendServicePageItem> pageQueryResult = backendServiceQueryService.pageList(param);
                return new ResponseMessage("page", pageQueryResult);
            } else {
                param.setSpCodes(new ArrayList<>(ShiroUtils.getShiroUser().getSpScopes()));
                return new ResponseMessage("page", backendServiceQueryService.pageListForSp(param));
            }
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list backend service with param: " + param, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public ResponseMessage detail(@RequestParam(value = "id") Long id) {
        try {
            CheckUtils.notNull(id, "id");
            return new ResponseMessage("result", backendServiceMgrFacade.find(id));
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when query backend-service detail with id: " + id, ex);
            return new ResponseMessage(ex);
        }
    }


    @ResponseBody
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public ResponseMessage create(@RequestBody BackendServiceDTO backendServiceDTO) {
        try {
            backendServiceMgrFacade.create(backendServiceDTO);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when create backend service with param: " + backendServiceDTO, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseMessage update(@RequestBody BackendServiceDTO backendServiceDTO) {
        try {
            backendServiceMgrFacade.update(backendServiceDTO);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when update backend service with param: " + backendServiceDTO, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public ResponseMessage delete(@RequestParam(value = "id") Long id) {
        try {
            CheckUtils.notNull(id, "id");
            backendServiceMgrFacade.remove(id);
            return new ResponseMessage();
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when delete backend service with id: " + id, ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/simple-list", method = RequestMethod.GET)
    public ResponseMessage simpleList() {
        try {
            return new ResponseMessage("result", backendServiceQueryService.simpleList());
        } catch (Exception ex) {
            LOGGER.error("Exception occurred when list backend service ", ex);
            return new ResponseMessage(ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/commons/type", method = RequestMethod.GET)
    public ResponseMessage type() {
        List<CommonsVO> list = new ArrayList<>();
        BackendServiceTypeEnum.getDisplayValueMap().forEach((value, displayName) -> list.add(new CommonsVO(value, displayName)));
        return new ResponseMessage("result", list);
    }


}
