/**
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.cache;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yeepay.g3.facade.yop.sys.dto.CertDTO;
import com.yeepay.g3.facade.yop.sys.dto.CertSpec;
import com.yeepay.g3.facade.yop.sys.dto.SymmetricCertSpec;
import com.yeepay.g3.facade.yop.sys.dto.UnSymmetricCertSpec;
import com.yeepay.g3.facade.yop.sys.enums.CertTypeEnum;
import com.yeepay.g3.facade.yop.sys.enums.CertUsageEnum;
import com.yeepay.g3.facade.yop.sys.enums.SecurityReqNameEnum;
import com.yeepay.g3.facade.yop.sys.facade.CertFacade;
import com.yeepay.g3.utils.common.exception.YeepayRuntimeException;
import com.yeepay.g3.utils.rmi.RemoteServiceFactory;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * title: regTestAppIdInvokePriKeyLocalCache<br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 19/05/23 10:12
 */
@Component("regTestAppIdInvokePriKeyLocalCache")
public class RegTestAppIdInvokePriKeyLocalCache {

    private static final Logger LOGGER = LoggerFactory.getLogger(RegTestAppIdInvokePriKeyLocalCache.class);

    private static final String SEPARATOR = "$";
    public static final int CACHE_MAX_SIZE = 500;
    public static final int CACHE_EXPIRE_AFTER_WRITE = 2;

    private CertFacade certFacade = RemoteServiceFactory.getService(CertFacade.class);

    private LoadingCache<String, String> regTestAppIdInvokePriKeyLocalCache = CacheBuilder.newBuilder()
            .maximumSize(CACHE_MAX_SIZE)
            .expireAfterWrite(CACHE_EXPIRE_AFTER_WRITE, TimeUnit.HOURS)
            .recordStats()
            .build(getCacheLoader());

    private CacheLoader getCacheLoader() {
        return new CacheLoader<String, String>() {
            @Override
            public String load(String key) throws Exception {
                String[] array = StringUtils.split(key, SEPARATOR);
                String appId = array[0];
                String security = array[1];
                LOGGER.info("load appid invoke private key by appid and security:{} ", key);
                String cert = null;
                if (SecurityReqNameEnum.YOP_HMAC_AES128.value.equals(security)) {
                    cert = getCert(appId, CertTypeEnum.AES128);
                } else if (SecurityReqNameEnum.YOP_RSA2048_SHA256.value.equals(security)) {
                    cert = getCert(appId, CertTypeEnum.RSA2048);
                }
                return cert;
            }
        };
    }

    public String get(String key) {
        try {
            return regTestAppIdInvokePriKeyLocalCache.get(key);
        } catch (Exception e) {
            LOGGER.error("error when get local cache", e);
            throw new YeepayRuntimeException(e.getMessage());
        }
    }

    private String getCert(String appId, CertTypeEnum certTypeEnum) {
        List<CertDTO> certs = certFacade.findByCondition(appId, certTypeEnum, CertUsageEnum.API_INVOKE);
        CertDTO cert = null;
        for (CertDTO certDTO : certs) {
            if (certDTO.isActive()) {
                cert = certDTO;
                break;
            }
        }
        if (null == cert) {
            throw new IllegalArgumentException("回归测试应用 [ " + appId + " ] 签名密钥未配置");
        }
        CertSpec certSpec = cert.getCertSpec();
        if (certTypeEnum.isSymmetric()) {
            return ((SymmetricCertSpec) certSpec).getSecretKey();
        } else {
            return ((UnSymmetricCertSpec) certSpec).getPriKey();
        }
    }

}
