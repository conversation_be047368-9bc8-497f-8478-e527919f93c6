/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import com.yeepay.g3.facade.yop.sys.dto.api.ApiRequestKey;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2022/4/7 9:11 PM
 */
@Data
public class ApiRouteImportAnalysisResult implements Serializable {

    private static final long serialVersionUID = -1L;

    private String requestId;

    private List<ApiRequestKey> routeToCreate;

    private List<ApiRequestKey> routeToOverride;
}
