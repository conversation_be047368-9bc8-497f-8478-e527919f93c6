package com.yeepay.g3.app.yop.portal.regression;

import com.yeepay.g3.facade.yop.sys.dto.RegressionAssertionDTO;
import com.yeepay.g3.sdk.yop.client.YopResponse;

import java.util.List;

/**
 * title: <br/>
 * description: 描述<br/>
 * Copyright: Copyright (c)2014<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 17/8/5 下午4:14
 */
public class ExecutionResult {

    private YopResponse response;

    private List<RegressionAssertionDTO> assertionList;

    private boolean success;

    public YopResponse getResponse() {
        return response;
    }

    public void setResponse(YopResponse response) {
        this.response = response;
    }

    public List<RegressionAssertionDTO> getAssertionList() {
        return assertionList;
    }

    public void setAssertionList(List<RegressionAssertionDTO> assertionList) {
        this.assertionList = assertionList;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
