package com.yeepay.g3.app.yop.portal.vo.page;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

public class YosStoreConfigPageQueryParam extends BasePageQueryParam implements Serializable {

    private static final long serialVersionUID = 5602832331204880025L;

    private String apiGroupCode;

    private Set<String> apiGroupCodes;

    private Date createdStartDate;

    private Date createdEndDate;

    public String getApiGroupCode() {
        return apiGroupCode;
    }

    public void setApiGroupCode(String apiGroupCode) {
        this.apiGroupCode = apiGroupCode;
    }

    public Set<String> getApiGroupCodes() {
        return apiGroupCodes;
    }

    public void setApiGroupCodes(Set<String> apiGroupCodes) {
        this.apiGroupCodes = apiGroupCodes;
    }

    public Date getCreatedStartDate() {
        return createdStartDate;
    }

    public void setCreatedStartDate(Date createdStartDate) {
        this.createdStartDate = createdStartDate;
    }

    public Date getCreatedEndDate() {
        return createdEndDate;
    }

    public void setCreatedEndDate(Date createdEndDate) {
        this.createdEndDate = createdEndDate;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
