/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/6/20 11:54 上午
 */
@Setter
@Getter
public class SimpleApiVO extends BaseVO {
    /**
     * APIID
     */
    private String apiId;
    /**
     * API标题
     */
    private String apiTitle;
    /**
     * API名称
     */
    private String apiName;
    /**
     * API请求路径
     */
    private String apiUri;

    /**
     * httpMethod
     */
    private String httpMethod;
}
