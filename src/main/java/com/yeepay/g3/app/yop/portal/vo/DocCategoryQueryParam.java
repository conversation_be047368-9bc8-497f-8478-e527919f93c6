/*
 * Copyright: Copyright (c)2011
 * Company: 易宝支付(YeePay)
 */
package com.yeepay.g3.app.yop.portal.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * title: <br>
 * description: 描述<br>
 * Copyright: Copyright (c)2014<br>
 * Company: 易宝支付(YeePay)<br>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024/7/23 11:55
 */
@Data
@Accessors(chain = true)
public class DocCategoryQueryParam extends BaseVO {
    private String code;
    private List<String> parentCodes;
    private String type;
}
