package com.yeepay.g3.app.yop.portal.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * title: ModelVO<br/>
 * description: ModelVO<br/>
 * Copyright: Copyright (c) 2018<br/>
 * Company: 易宝支付(YeePay)<br/>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019/7/11 下午7:32
 */
@Data
public class ModelVO implements Serializable {

    private static final long serialVersionUID = 6318473265309553326L;

    private Long id;

    private String name;

    private String description;

    @Override
    public String toString() {
        return "ModelVO{" +
                "id='" + id + '\'' +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
